<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link
      rel="stylesheet"
      href="//oss.ienmore.com/resources/public/css/flex.css"
    />
    <link
      rel="stylesheet"
      href="//at.alicdn.com/t/font_2010163_eoeeepmzmna.css"
    />
    <title></title>
    <script src="https://oss.ienmore.com/resources/public/js/libs/ckeditor/ckeditor.js"></script>
  </head>

  <body>
    <div id="app"></div>
  </body>

  <script>
    window.onload = function () {
      setTimeout(() => {
        let platformId = sessionStorage.getItem('platform')
        let clientInfo
        try {
          clientInfo = JSON.parse(localStorage.getItem('clientInfo')) || {
            templateName: '',
          }
        } catch (e) {
          console.log('获取模版失败')
        }
        let $favicon = document.createElement('link')
        $favicon.rel = 'shortcut icon'
        $favicon.type = 'image/x-icon'
        $favicon.href =  clientInfo.managerFavicon
        document.getElementsByTagName('head')[0].appendChild($favicon)
        document.title = clientInfo.templateName
      }, 1200)
      function isMobile() {
        if (window.navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)) {
            return true; // 移动端
        } else {
            return false; // PC端
        }
    }

    // mobile环境 跳转mobile页面
    if (isMobile()) {
      // pc端
      let thisOrigin = ''
      if ('<%= VUE_APP_CURRENTMODE %>' === "production") {
        // 正式
        thisOrigin = ''
      } else {
        // 测试
        thisOrigin = 'http://testbio.ienmore.com:809'
      }
      if (location.href.indexOf("liveRoom") != -1) {
        if(location.href.indexOf('https://www.morebio.cn') != -1){
                  thisOrigin='https://live.morebio.cn/info/#/'
        }
        // 企业直播间
        window.location.replace(thisOrigin + window.location.href.split("#")[1]);
      }else if(location.href.indexOf('https://www.morebio.cn') != -1){
        window.location.replace("https://live.morebio.cn/info/#/home");
      }
    }
    }
   
  </script>
</html>
