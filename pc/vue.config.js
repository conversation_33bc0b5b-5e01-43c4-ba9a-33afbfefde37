const path = require('path');
const WebpackAliyunOss = require('webpack-aliyun-oss');
const OSS = require('./AliOss');
const CompressionWebpackPlugin = require('compression-webpack-plugin');
const getDay = function () {
    var date = new Date();
    return date.getFullYear() + '' + (date.getMonth() + 1) + '' + date.getDate();
}
const fileName = path.basename(path.resolve(__dirname, '.')) || 'oss';


function resolve(dir) {
    return path.join(__dirname, dir)
}

module.exports = {
    baseUrl: process.env.VUE_APP_CURRENTMODE !== 'production' ?
        './' : `//oss.ienmore.com/${fileName}/${getDay()}/`,
    // 将构建好的文件输出
    outputDir: process.env.outputDir,
    // 是否为生产环境构建生成 source map？
    productionSourceMap: false,
    chainWebpack: config => {
        config.resolve.alias
            .set('@', resolve('src'));
        config.plugins.delete('prefetch');
        const imagesRule = config.module.rule('images')
        imagesRule.use('image-webpack-loader').loader('image-webpack-loader')
            .options({
                bypassOnDebug: true
            }).end()
    },
    // CSS 相关选项
    css: {
        // extract: true,
        // sourceMap: false,
        // modules: false,
        loaderOptions: {
            sass: {
                data: `@import "@/assets/css/base.scss";
                @import "@/assets/css/starxder.scss";
                @import "@enmore/enmore-common-front/element-primary-color.scss";
                `,
            }
        }
    },


    // 配置 webpack-dev-server 行为。
    devServer: {
        port: 8080,
        https: false,
        hotOnly: true,
        open: true
    },


    configureWebpack: config => {
        if (process.env.VUE_APP_CURRENTMODE == 'production') {
            config.plugins.push(new WebpackAliyunOss(OSS));
        }
        return {
            plugins: [new CompressionWebpackPlugin({
                test: /\.(js|css)(\?.*)?$/i, //需要压缩的文件正则
                threshold: 10240, //文件大小大于这个值时启用压缩
                deleteOriginalAssets: false //压缩后保留原文件
            })]
        };
    },

    // 第三方插件的选项
    pluginOptions: {}
}