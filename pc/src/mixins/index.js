import starxder from '@/assets/css/starxder.scss'
import moment from "moment";
export default {
    data() {
        return {
            btnType: {
                add: 'add',
                addBatch: 'addBatch',
                deleteBatch: 'deleteBatch',
                tabEdit: 'tabEdit',
                tabDelete: 'tabDelete',
                tabReset: 'tabReset',
                save: 'save',
                publish: 'publish',
                tabPublish: 'tabPublish',
                cancel: 'cancel',
                home_banner: 'home_banner',
                hot_live: 'hot_live',
                video_course: 'video_course',
                audio_course: 'audio_course',
                conference: 'conference',
                play_back: 'play_back',
                search_key: 'search_key',
                home_content: 'home_content',
                channel_content: 'channel_content',
                addRank: 'addRank',
                addRoles:'addRoles'
            },
            ...starxder,
            sysCodeStr: 'info', //组件使用
            platformInfoData:sessionStorage.getItem('platform')
        }
    },
    computed: {
        routerNames() {
            return this.$store.state.routerNames;
        },
        routerQuery() {
            return this.$store.state.routerQuery;
        },
        loginWayList(){
            if(JSON.parse(sessionStorage.getItem('sysInfo'))){
                return JSON.parse(sessionStorage.getItem('sysInfo')).loginWayList;
            }else{
                return [];
            }
        }
    },
    methods: {
        moment() {
            return moment()
        },
        formatTime(time, type) {
            return moment(time).format(type)
        }
    }
}