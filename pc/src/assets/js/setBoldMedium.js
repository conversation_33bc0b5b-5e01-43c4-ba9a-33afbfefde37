
window.onload = function () {
    let isMac = /macintosh|mac os x/i.test(navigator.userAgent);
    let boldMedium = isMac ? '.bold-medium{font-weight:500 !important;}' : '.bold-medium{font-weight:700 !important;}'
    let style = document.createElement("style");
    style.type = 'text/css';
    try {
        style.appendChild(document.createTextNode(boldMedium));
    } catch (ex) {
        style.styleSheet.cssText = boldMedium;//针对IE
    }
    var head = document.getElementsByTagName("head")[0];

    head.appendChild(style);
}