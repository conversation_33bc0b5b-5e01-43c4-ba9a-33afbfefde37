/* 改变变量 */
$--border-radius-base: 3px !default;
$--size-base: 12px !default;
$--font-size-base: 12px !default;
$--font-size-small: 11px !default;
$--font-size-large: 16px !default;
$--collapse-header-size: 12px !default;
$--pagination-font-size: 12px !default;
$--dialog-font-size: 12px !default;
$--checkbox-font-size: 12px !default;
$--radio-font-size: 12px !default;
$--select-input-font-size: 12px !default;
$--button-font-size: 12px !default;
$--color-primary: #ff6b00;
$--all-transition: all .3s cubic-bezier(.645,.045,.355,1) !default;
$--fade-transition: opacity 300ms cubic-bezier(0.23, 1, 0.32, 1) !default;
$--fade-linear-transition: opacity 200ms linear !default;
$--md-fade-transition: transform 300ms cubic-bezier(0.23, 1, 0.32, 1), opacity 300ms cubic-bezier(0.23, 1, 0.32, 1) !default;
$--border-transition-base: border-color .2s cubic-bezier(.645,.045,.355,1) !default;
$--color-transition-base: color .2s cubic-bezier(.645,.045,.355,1) !default;
$--font-path: '~element-ui/lib/theme-chalk/fonts';

@import "~element-ui/packages/theme-chalk/src/index";
