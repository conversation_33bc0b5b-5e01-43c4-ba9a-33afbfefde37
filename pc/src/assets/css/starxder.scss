$enmore-color:#ff6b00;
$healthcare-color:#065B98;
$sub-color:#383d4a;
$success-color:#52c41a;
$warning-color:#faad14;
$error-color:#ff4d4f;
$text-color-0:#333;
$text-color-1:#666;
$text-color-2:#999;
$text-color-3:#ddd;
$text-color-light:#fff;

.main-title {
    font-size: 24px;
    line-height: 32px;
    vertical-align: middle;
}

.article-title {
    font-size: 20px;
    line-height: 28px;
    vertical-align: middle;
}

.main-text {
    font-size: 16px;
    line-height: 24px;
    vertical-align: middle;
}

.normal-text {
    font-size: 14px;
    line-height: 22px;
    vertical-align: middle;
}

.sub-text {
    font-size: 12px;
    line-height: 20px;
    vertical-align: middle;
}

.perfix_line {
    &:before {
        content: "|";
        font-size: 12px;
        line-height: 16px;
        margin-right: 10px;
        width: 3px;
        height: 16px;
        background: var(--color-primary);
        color: var(--color-primary);
        position: relative;
        top: -2px;
    }
}

.vertical-line {
    width: 100%;
    height: 1px;
    top: 49px;
    left: 0px;
    background-color: #f2f3fa;
    position: absolute;
}

.flex-row {
    display: flex;
    flex-direction: row;
}

.single-line {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.double-line {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.triple-line {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
}

.el-dialog__header {
    padding: 15px 20px 15px 20px;
    border-bottom: 1px solid #F2F3FA;
}


// 修改toolTip
.el-tooltip__popper {
    max-width: 240px !important;
    padding: 8px;
    &.is-dark {
        font-size: 12px;
        line-height: 18px;
        &[x-placement='top'] {
            margin-bottom: -6px;
        }
        &[x-placement='bottom'] {
            margin-top: -6px;
        }
    }

    &.is-light {
        font-size: 14px;
        line-height: 22px;
        border: none;
        color: rgba(0, 0, 0, 0.65);
        box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
        .popper__arrow {
            border-width: 0;
        }
        &[x-placement='top-start'] {
            transform: translateX(-12px);
            .popper__arrow {
                margin-left: 12px;
            }
        }
    }
}

:export {
    enmoreColor: $enmore-color;
    subColor: $sub-color;
    successColor: $success-color;
    warningColor: $warning-color;
    errorColor: $error-color;
    textColor0: $text-color-0;
    textColor1: $text-color-1;
    textColor2: $text-color-2;
    textColor3: $text-color-3;
    textColorLight: $text-color-light;
}