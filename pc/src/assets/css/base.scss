
body {
  background: #f6f6f6;
}

.clearfix:after,
.clearfix:before {
  content: '';
  display: block;
  clear: both;
}

.pull-left {
  float: left;
}

.pull-right {
  float: right;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb10 {
  margin-bottom: 10px;
}

.mr5 {
  margin-right: 5px;
}

.mr10 {
  margin-right: 10px;
}

.pd10 {
  padding: 10px;
}

.pdlr10 {
  padding: 0 10px;
}

.pdlr5 {
  padding: 0 5px;
}

.pdl15 {
  padding-left: 15px;
}

.text-small {
  font-size: .8em !important;
}

a {
  text-decoration: none;
}

.default-bg {
  background: #f0f3f8;
}

/*vue-router transition*/
.v-enter {
  opacity: 0;
}

.v-enter-active {
  transition: 2s;

}

.v-enter-to {
  opacity: 1;
}

/*表格上方的按钮容器*/
.table-tools {
  margin-bottom: 15px;
  margin-top: 15px;
  overflow: hidden;
}

.table-tools .btnTitle {
  margin-top: 5px;
}

/*左侧带蓝边的标题*/
.btnTitle {
  padding-left: 10px;
  display: inline-block;
}

/*搜索框*/
.el-select {
  display: block !important;
}

/*左端对齐，因为label的宽度不固定，在页面中使用margin-left为负值设置*/

/*文字按钮:彩色*/
.text-info {
  color: #909399 !important;
}

.text-warning {
  color: #E6A23C !important;
}

.text-success {
  color: #67C23A !important;
}

.text-danger {
  color: #F56C6C !important;
}

/*页面中一般的列表，table添加class main-table  size:mini  内部的操作按钮 size:mini*/
/*size:mini的表格，字体设置为14px*/
.main-table.el-table--mini {
  font-size: 14px;
}

.main-table .el-button--mini {
  font-size: 14px;
}

.el-collapse-item__header {
  font-size: 17px !important;
}

.el-colH {
  height: 100%;
}

.border {
  border: thin solid #eee;
  padding: 7px 10px;
  border-radius: 4px;
}

.el-collapse-item__header.is-active {
  margin-left: 10px;
}

//表格表头错位问题解决
.el-table th.gutter {
  display: table-cell !important;
}

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: #fff;
  border-radius: 4px;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 3px rgba(216, 216, 216, 0.3);
  border-radius: 4px;
  background-color: rgba(216, 216, 216, 0.3);
}
textarea::-webkit-input-placeholder {
  font-family: PingFangSC-Regular, PingFang SC !important; 
  font-weight: 400 !important;
  color: #AAA  !important;
  font-size: 16px !important;
}
/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 4px;
  -webkit-box-shadow: inset 0 0 3px rgba(216, 216, 216, 0.3);
  background-color: #D8D8D8;
}

$--color-primary:var(--color-primary);

// 弹窗顶部的样式结构
.el-dialog__header {
  border-bottom: 1px solid #F2F3FA !important;
  padding: 0 0 0 20px !important;
  height: 50px !important;
  line-height: 50px !important;
}

.el-dialog__title {
  font-family: PingFangSC-Regular, PingFang SC !important;
  font-weight: 400 !important;
  color: rgba(51, 51, 51, 1) !important;
}

.el-dialog__body {
  padding: 10px 20px 20px 20px !important;
}


//  表单的样式控制
.el-form-item__label {
  min-width: 80px !important;
}


.el-radio__label {
  padding-left: 8px;
}

// 级联选择器
.el-cascader__tags,
.el-select__tags {
  padding-left: 5px;

  .el-tag.el-tag--info {
    color: #333;
    margin: 5px 0 3px 10px;
    border-radius: 10px;
    height: 20px;
    line-height: 20px;
    border: none;
    padding: 0 10px;

    .el-icon-close {
      background: none;
      color: #333;
      top: 0;
      line-height: 18px;
      right: 0;
      font-size: 14px;
      margin-left: 3px;
    }
  }
}

//表格样式
.el-table__header thead tr th {
  background: rgba(247, 248, 250, 1);
  height: 46px;
  padding: 0 !important;
}

.el-table__header thead tr th .cell {
  font-size: 14px;
  font-weight: 500;
  color: rgba(51, 51, 51, 1);
}



.el-table tbody tr td .cell {
  font-size: 14px !important;
  font-weight: 400 !important;
  color: rgba(51, 51, 51, 1) !important;

}

.el-table--mini td {
  padding: 2px 0 !important;
}

// tab 切换  样式
.el-tabs__nav-wrap {
  height: 50px !important;
}

.el-tabs__nav-scroll {
  height: 100% !important;
}

.el-tabs__nav {
  height: 100% !important;
  line-height: 50px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.el-tabs__nav-wrap::after {
  height: 1px;
  background-color: rgba(245, 245, 245, 1);
}

.el-tabs__item {
  height: 100% !important;
  line-height: 50px !important;
  font-size: 14px !important;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400 !important;
}

// 中英文切换tab
.el-radio-button__inner {
  border-color: $--color-primary;
}

.el-radio-button:first-child .el-radio-button__inner {
  border-left-color: $--color-primary;
}

// 对于 消息提示框的修改
.el-message {
  width: 266px !important;
  // height: 40px !important;
  background: rgba(255, 255, 255, 1) !important;
  box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12) !important;
  border-radius: 2px !important;
  border: none !important;
  padding-top: 9px;
  padding-bottom: 9px;
}

.el-message__content {
  font-size: 14px !important;
  font-family: PingFangSC-Regular, PingFang SC !important;
  font-weight: 400 !important;
  color: rgba(0, 0, 0, 0.65) !important;
  line-height: 22px;
}

// 超出两行省略号
.line2Ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
}

// 修改toolTip
.el-tooltip__popper {
  font-size: 12px;
  max-width: 240px !important;
  line-height: 18px;
  font-weight: 400;
}

// card 样式修改
.el-card__header {
  font-size: 16px !important;
  font-family: PingFangSC-Medium, PingFang SC !important;
  font-weight: 500 !important;
  color: #333333 !important;
  padding: 0 20px !important;
  height: 46px !important;
  line-height: 46px !important;
}

.switchStyle .el-switch__label {
  position: absolute !important;
  display: none !important;
  color: #fff !important;
}

.switchStyle .el-switch__label--left {
  z-index: 9 !important;
  left: 20px !important;
}

.switchStyle .el-switch__label--right {
  z-index: 9 !important;
  left: -5px !important;
}

.el-switch__label span {
  font-size: 12px !important;
}

.switchStyle .el-switch__label.is-active {
  display: block !important;
}

.switchStyle.el-switch .el-switch__core,
.el-switch .el-switch__label {
  width: 50px !important;
}

.width1200 {
  width: 1200px;
  margin: 0 auto;
}

// 修改底部分页颜色
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .btn-next,
.el-pagination.is-background .el-pager li {
  width: 32px;
  height: 32px;
  line-height: 32px;
  margin-right: 0;
  margin-left: 8px;
  background-color: #fff;
  border-radius: 2px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  color: rgba(0, 0, 0, 0.65);
  font-weight: normal;
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
  border: none;
}

.el-pagination__jump {
  margin-left: 8px;
  color: #666;
  line-height: 32px;
  height: 32px;

  .el-pagination__editor {
    margin: 0 8px;
    width: 60px;

    .el-input__inner {
      width: 60px;
      height: 32px;
    }
  }
}

/*! minireset.css v0.0.3 | MIT License | github.com/jgthms/minireset.css */
html,
body {
  margin: 0;
  padding: 0;
}

ul {
  list-style: none;
}

button,
input,
select,
textarea {
  margin: 0;
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

img,
embed,
iframe,
object,
audio,
video {
  height: auto;
  max-width: 100%;
}

iframe {
  border: 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
  text-align: left;
}

// 加粗样式
.bold-medium {
  font-family: PingFangSC-Medium, PingFang SC;
}

// 删除的message-box 提示
/** confirmButtonText: "取消",
    cancelButtonText: "确定",
    center: true,
    distinguishCancelAndClose: true,
    showClose: false,
    customClass: 'del-message-box'
*/
.del-message-box {
  width: 450px;

  .el-message-box__header {
    padding-top: 38px;
    min-height: 106px;

    .el-message-box__title {
      line-height: 22px;

      .icon-warning {
        color: #FAAD14;
        font-size: 22px !important;
        margin-right: 10px;
        margin-top: 2px;
      }
    }
  }

  .el-message-box__content {
    padding: 0;
  }

  .el-message-box__btns {
    .el-button {
      width: 86px;
      height: 36px;
      font-size: 14px;
      font-weight: 400;
      &.el-button--default {
        background-color: $--color-primary;
        border-color: $--color-primary;
        color: #fff;

        &:hover {
          background: $--color-primary;
          opacity: 1;
        }
      }

      &.el-button--primary {
        margin-left: 30px;
        background-color: #fff;
        color: #999;
        border-color: #ddd;

        &:hover {
          background: none;
          border-color: $--color-primary;
          color: $--color-primary;
        }
      }
    }
  }
}

.el-button {
  font-size: 14px;
  font-weight: 400;
  &.el-button--default:hover {
    background: none;
    border-color: $--color-primary;
    color: $--color-primary;
  }
  &.el-button--primary:hover {
    background: $--color-primary;
    opacity: 1;
    color:#fff;
  }
}

.liveListTop_top .el-input-group__append{
  width: 32px!important;
  padding: 0px;
}
// 点数管理统一按钮
.point_btn.el-button.el-button--primary{
	font-size: 14px;
}

.el-table--striped .el-table__body tr.el-table__row--striped td { 
  background: #F7F8FA !important;
}

.lookMobile{
  font-size: 12px!important;
  padding: 7px!important;
}

#WebrtcEverywherePluginId{
  display: none !important;
}
