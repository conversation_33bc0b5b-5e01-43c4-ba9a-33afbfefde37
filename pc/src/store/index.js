import Vue from "vue";
import Vuex from "vuex";
import $local from "store";
import common from "./modules/common";
Vue.use(Vuex);
import loginModule from "./modules/login";
import powerBtn from "./modules/powerBtn";
import permission from  './modules/permission';
import point from "./modules/point";
import commonLogin from '@enmore/enmore_common_validate_front/src/store/store';
const store = new Vuex.Store({
  state: {
    sideBar: {
      opened: !+$local.get("sideBarStatus"),
      withoutAnimation: false
    },
    //  进行修改公司信息时候的所产生的id
    companyId: "",
    linkmanId: "",
    changeOnoff: true,
    routerNames:'',
    routerQuery:'',
    iframeUrl:'',//iframe当前的地址
    topBar:{
      showChangePasswordDialog:false
    },
    isRefresh:false,
    // 是否已经提示404 token 过期
    hasWarnStatus:false,
    // 是否有呼叫中心权限
    callCenterPower:false,
     // 控制显示透明
     callCenterOpacity:true  
  },
  getters:{

  },
  mutations: {
    toggleSideBar(state) {
      if (state.sideBar.opened) {
        $local.set("sideBarStatus", 1);
      } else {
        $local.set("sideBarStatus", 0);
      }
      state.sideBar.opened = !state.sideBar.opened;
      state.sideBar.withoutAnimation = false;
    },
    closeSideBar(state, withoutAnimation) {
      $local.set("sideBarStatus", 1);
      state.sideBar.opened = false;
      state.sideBar.withoutAnimation = withoutAnimation;
    },
    clearId(state) {
      state.companyId = "";
      state.linkmanId = "";
    },
    changeEditOnoff(state, payload) {
      state.changeOnoff = payload;
    },
    changeRouterNames(state,payload){
      state.routerNames = payload;
      localStorage.setItem('currentRouter',payload);
    },
    changeRouterQuery(state,payload){
      state.routerQuery = payload;
      localStorage.setItem('currentRouterId',payload);
    },
    changeRefresh(state,payload){
      state.isRefresh = payload;
    },
	// 点数使用趋势图查询条件
    changePiontData(state,payload){
      state.piontData = payload
    },
    // 修改token过期状态
    changeHasWarnStatus(state,payload){
      state.hasWarnStatus = payload;
    },
    changeCallCenterPower(state,payload){
      state.callCenterPower = payload;
    },
    changeCallCenterOpacity(state,payload){
      state.callCenterOpacity = payload;
    }
  },
  actions: {},
  modules: {
    loginModule,
    common,
    powerBtn,
    permission,
    commonLogin,
    point
  }
});

export default store;