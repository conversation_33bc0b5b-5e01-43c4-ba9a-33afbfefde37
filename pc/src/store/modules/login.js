const $local = require('store')
import CONSTANT from '@/config/config_constant'


const loginModule = {
    state: {
        token: '',
        userInfo: {
            id: -1,
            userName: '',
            bizId: '',
            deptId: ''
        }
    },
    mutations: {
        login(state, token) {
            // 去掉引号
            let newToken = token.replace(/\"/g, "");
            $local.set(CONSTANT.token, newToken);
            state.token = newToken;
        },
        logout(state) {
            $local.remove(CONSTANT.token);
            state.token = null;
        },
        setUserInfo(state, payload) {
            state.userInfo.id = payload.id;
            state.userInfo.userName = payload.userName;
            state.userInfo.bizId = payload.bizId;
            state.userInfo.deptId = payload.deptId;
            localStorage.setItem('deptId', payload.deptId);
        },

    },
    actions: {

    }
}

export default loginModule;