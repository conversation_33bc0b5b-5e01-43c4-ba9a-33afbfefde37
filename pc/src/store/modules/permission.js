import {
  getMenu as fetchPermission
} from '@/api/nav';
import store from '@/store';

function recursionRouter(userRouter = []) {
  var recursion = [];
  if (userRouter && userRouter.length) {
    userRouter.forEach((item) => {
      if (item.children.length) {
        let arr = item.children.map((v) => {
          return v;
        });
        recursion.push(...arr);
      } else {
        recursion.push(item);
      }
    })
  }
  store.commit("CLEAR_ROUTER");
  store.commit("CHANGEALLROUTERCONFIG", recursion);
}

const state = {
  permissionList: null /** 所有路由 */ ,
  // 这里进行路由匹配的筛选操作
  allRouterConfig: {
    coursesUrl: [], // 讲师课程直播路由
    thinkTankUrl: [], // 关于其他内容的路由
    liveUrl: [],
    wechatUrl: [],
    financeUrl: [] // 财务系统管理
  }
}
const mutations = {
  SET_PERMISSION(state, routes) {
    state.permissionList = routes;
  },
  CLEAR_PERMISSION(state) {
    state.permissionList = null;
  },
  CLEAR_ROUTER(state) {
    state.allRouterConfig = {
      coursesUrl: [], // 讲师课程直播路由
      thinkTankUrl: [], // 关于其他内容的路由
      liveUrl: [],
      wechatUrl: [],
      financeUrl: [] // 财务系统管理
    }
  },
  // 进行更改路由配置的变换
  CHANGEALLROUTERCONFIG(state, payload) {
    if (payload && payload.length) {
      payload.forEach((val) => {
        // 这里进行配置相关的规则
        switch (val.moduleType) {
          case "courses":
            state.allRouterConfig.coursesUrl.push(val.menuComponent);
            break;
          case "thinkTank":
            state.allRouterConfig.thinkTankUrl.push(val.menuComponent);
            break;
          case 'live':
            state.allRouterConfig.liveUrl.push(val.menuComponent);
            break;
          case "wechat":
            state.allRouterConfig.wechatUrl.push(val.menuComponent);
            break;
          case "finance":
            state.allRouterConfig.financeUrl.push(val.menuComponent);
            break;
        }
      })
    }
  }
}
const actions = {
  async FETCH_PERMISSION({
    commit
  }) {
    /*  获取后台给的权限数组 */
    let permissionList = await fetchPermission(0, sessionStorage.getItem('platform'));

    //  /*  根据后台权限跟我们定义好的权限对比，筛选出对应的路由并加入到path=''的children */
    recursionRouter(permissionList.data);

    /* 路由 */
    commit('SET_PERMISSION', permissionList.data);
  }
}

export default {
  state,
  mutations,
  actions
};