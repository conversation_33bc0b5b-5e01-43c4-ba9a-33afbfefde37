const powerBtn = {
    state: {
        // 按钮控制
        btnPower: [],
        //  页面中所有的按钮进行权限的分配
        flag: {
            add: false,
            addBatch: false,
            deleteBatch: false,
            tabEdit: false,
            tabDelete: false,
            tabReset: false,
            save: false,
            publish: false,
            tabPublish: false,
            cancel: false,
            home_banner: false,
            hot_live: false,
            video_course: false,
            audio_course: false,
            conference: false,
            play_back: false,
            search_key: false,
            home_content: false,
            channel_content: false,
            addRank:false,
            addRoles:false
        },
        //新的按钮控制
        pcPositions: [],
        mobilePositions: []
    },
    mutations: {
        //  权限菜单的数据
        btnPowers(state, payload) {
            state.btnPower = payload;
            state.flag = {
                add: false,
                addBatch: false,
                deleteBatch: false,
                tabEdit: false,
                tabDelete: false,
                tabReset: false,
                save: false,
                publish: false,
                tabPublish: false,
                cancel: false,
                home_banner: false,
                hot_live: false,
                video_course: false,
                audio_course: false,
                conference: false,
                play_back: false,
                search_key: false,
                channel_content: false,
                home_content: false,
                addRank:false,
                addRoles:false
            }

            if(!state.btnPower || !state.btnPower.length){
                for(let attr in state.flag){
                    state.flag[attr] = true;
                }
            }else{
                let key =[];
                state.btnPower.forEach((item)=>{
                    key.push(item.btnId);
                    if(!item.initStatus){
                        state.flag[item.btnId]= false;
                    }else{
                        state.flag[item.btnId]= true; 
                    }
                });
                for(let attr in state.flag){
                    if(!key.includes(attr)){
                        state.flag[attr] = true;
                    }    
                }    
            }
            //新增的按钮平台发布控制
            state.pcPositions = []
            state.mobilePositions = []
            payload.map(item=>{
                //为区分旧的权限控制而增加的处理
                if(item.btnId>=0){
                    if(item.btnName.includes('PC')){
                        state.pcPositions.push({
                            btnName:item.btnName,
                            position:item.btnId
                        })
                    }else{
                        state.mobilePositions.push({
                            btnName:item.btnName,
                            position:item.btnId
                        })
                    }
                }
            })
        },
        //  新增按钮的禁止开启
        newBtnIsDisabled(state, payload) {
            state.newAddBtn = payload;
        }
    },
    actions: {

    }
}

export default powerBtn;