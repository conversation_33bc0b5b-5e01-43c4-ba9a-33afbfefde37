import Axios from '@/plugins/http'
const getMenu = (param,platform = 1) => {
     return Axios(1).get(`/bizMenu/info.json`, {param})
};

const checkMenuAccess = (params) => {
    return Axios(1).get(`/bizUser/checkMenuAccess/${params.menuName}.json`)
};

const resetSend = (params) => {
    return Axios(1).get(`/forget.json`, {params})
};

const validateToken = (params) => {
    return Axios(1).get(`/validateToken/${params.token}.json`)
};

const resetPwd = (params) => {
    return Axios(1).post(`/resetPwd.json`, params)
};

const changePwd = (params) => {
    return Axios(1).post(`/changePwd.json`, params)
};

const getToken = (params) => {
    return Axios().get(ssoUrl + `/token.json`, {params})
};

// 获取所能看到的系统
const getSystemInfo = (params) => {
    return Axios(1).get(`bizMenu/root`, params);
}


export {
    getMenu,
    checkMenuAccess,
    resetSend,
    validateToken,
    resetPwd,
    changePwd,
    getToken,
    getSystemInfo
}
