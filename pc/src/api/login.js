
/**
 * 注意事项
 * GET方法传参的时候需要用大括号包起来
 */
import Axios from '@/plugins/http'


const login=(params)=>{return Axios(1).post(`/common/login.json`,params)}

const getUserInfo = (params) => {return Axios(1).get(`/user/getInfo.json`,params)}

 // 登录密码验证邮箱
 const validateLogin = (params) => Axios(1).post(`user/accountValidateByLoginInfo.json`, params);

 //超管用户换取B端用户token
const getBizUserByOperId=(params)=>{return Axios(5).get(`/biz/authorize/apply/getBizUserByOperId`,{params})}

export {
    login,
    getUserInfo,
    validateLogin,
    getBizUserByOperId
}
