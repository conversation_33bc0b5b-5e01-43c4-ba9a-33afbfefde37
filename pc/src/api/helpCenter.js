import Axios from "@/plugins/http";
// 新增反馈
const addFeedback = (params) => {
    return Axios(1).post(`feedback`,params)
}
// 查询问题类型
const getFeedbackList = (params) => {
    return Axios(1).get(`feedback`,{params})
}
// 查询关联内容
const getContent = (params) => {
    return Axios(1).get(`helpcenter/content`,{params})
}
// 查询关联内容详情
const getContentDetail = (params) => {
    return Axios(1).get(`helpcenter/content/${params.id}`,{params})
}
// 查询树状菜单
const getMenu = (params) => {
    return Axios(1).get(`helpcenter/menu`,{params})
}
// 根据关键词查询关联内容
const getSearchList = (params) => {
    return Axios(1).get(`helpcenter/content/search`,{params})
}
// 根据顶部菜单（推送的）
const getPushMenu = (params) => {
    return Axios(1).get(`helpcenter/menu/push`,{params})
}

export {
    addFeedback,
    getFeedbackList,
    getContent,
	getContentDetail,
	getMenu,
	getSearchList,
	getPushMenu,
}
