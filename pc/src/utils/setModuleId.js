export default function  setModuleId(params){  // params = { moduleId, ModuleType, BusinessType, FormBusinessType }
    let enmoreFormParams = JSON.parse(sessionStorage.getItem('enmoreFormParams')) || {};
    Object.keys(params).forEach(key => {
        if (params[key]) enmoreFormParams[key] = params[key]
    })
    sessionStorage.setItem('enmoreFormParams',JSON.stringify(enmoreFormParams));
    let enmoreProductParams = JSON.parse(sessionStorage.getItem('enmoreProductParams')) || {};
    Object.keys(params).forEach(key => {
        if (params[key]) enmoreProductParams[key] = params[key]
    })
    sessionStorage.setItem('enmoreProductParams', JSON.stringify(enmoreProductParams));
    let moduleParams = JSON.parse(sessionStorage.getItem('moduleParams')) || {};
    Object.keys(params).forEach(key => {
        if (params[key]) moduleParams[key] = params[key]
    })
    sessionStorage.setItem('moduleParams',JSON.stringify(moduleParams));
    
}

