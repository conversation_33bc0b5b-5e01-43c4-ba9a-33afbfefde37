
import {
    COMMON_BASE_URL_TEST,
    COMMON_AUTH_URL_TEST,
    COMMON_PUBLIC_URL_TEST,
    COMMON_ENMORE_URL_TEST,
    COMMON_IEVENT_URL_TEST,
    COMMON_BASE_URL,
    COMMON_AUTH_URL,
    COMMON_ENMORE_URL,
    COMMON_IEVENT_URL
} from './host';

let baseUrl = '';
let authUrl = '';
let liveUrl = '';
let publicUrl = '';
let enmoreManageUrl = '';
// 主要内容
let coursesUrl = '';
// 其他内容
let thinkTankUrl = '';
let liveBizUrl = '';
let noticeApi = '';
let baseNoticeApi= '';
let uploadUrl='';
let wechatUrl = '';
let ieventUrl = "";
let serviceUrl = '';
let workflow = '';//待办查询地址
let systemApi = '';
switch (process.env.VUE_APP_CURRENTMODE) {
    case 'development':
        baseUrl = COMMON_BASE_URL_TEST + '/platform-biz-api';

        authUrl = COMMON_AUTH_URL_TEST + '/biz-api';
        serviceUrl = COMMON_AUTH_URL_TEST + '/platform-service-api';
        workflow = COMMON_AUTH_URL_TEST + '/workflow-api';


        publicUrl = COMMON_PUBLIC_URL_TEST + '/';
        liveBizUrl = COMMON_PUBLIC_URL_TEST + '/system';
        uploadUrl= COMMON_PUBLIC_URL_TEST + "/commonFile/upload/oss_ievent_public";

        enmoreManageUrl =  COMMON_ENMORE_URL_TEST + '/testapi';

        noticeApi = COMMON_IEVENT_URL_TEST + '/enmore-notice-api/';
        baseNoticeApi= COMMON_IEVENT_URL_TEST + "/notice-api/";
        ieventUrl= COMMON_IEVENT_URL_TEST + "/biz-api";
        systemApi = COMMON_AUTH_URL_TEST + '/system-api';

        coursesUrl = 'http://localhost:8082';
        thinkTankUrl = 'http://localhost:8082';
        liveUrl = 'http://localhost:8084';
        wechatUrl = 'http://localhost:8088';

        break;
    case 'test':
        baseUrl = COMMON_BASE_URL_TEST + '/platform-biz-api';

        authUrl = COMMON_AUTH_URL_TEST + '/biz-api';
        serviceUrl = COMMON_AUTH_URL_TEST + '/platform-service-api';
        workflow = COMMON_AUTH_URL_TEST + '/workflow-api';

        publicUrl = COMMON_PUBLIC_URL_TEST + '/';
        liveBizUrl = COMMON_PUBLIC_URL_TEST + '/system';
        uploadUrl= COMMON_PUBLIC_URL_TEST + "/commonFile/upload/oss_ievent_public";

        enmoreManageUrl =  COMMON_ENMORE_URL_TEST + '/testapi';

        noticeApi = COMMON_IEVENT_URL_TEST + '/enmore-notice-api/';
        baseNoticeApi= COMMON_IEVENT_URL_TEST + "/notice-api/";
        ieventUrl= COMMON_IEVENT_URL_TEST + "/biz-api";
        systemApi = COMMON_AUTH_URL_TEST + '/system-api';

        coursesUrl =  location.origin + '/course';
        thinkTankUrl =location.origin + '/bbs';
        wechatUrl =  location.origin + '/labelManager';
        liveUrl = location.origin + '/live';


        break;
    case 'production':
        baseUrl = COMMON_BASE_URL + '/platform-biz-api';
        publicUrl = COMMON_BASE_URL + '/';
        liveBizUrl = COMMON_BASE_URL + '/system';
        uploadUrl= COMMON_BASE_URL + "/commonFile/upload/oss_ievent_public";

        authUrl = COMMON_AUTH_URL + '/biz-api';
        serviceUrl = COMMON_AUTH_URL + '/platform-service-api';
        workflow = COMMON_AUTH_URL + '/workflow-api';
        
        enmoreManageUrl = COMMON_ENMORE_URL + '/api/';

        noticeApi = COMMON_IEVENT_URL + '/enmore-notice-api/';
        baseNoticeApi= COMMON_IEVENT_URL +"/notice-api/";
        systemApi = COMMON_AUTH_URL + '/system-api';
        
        coursesUrl =  location.origin + '/course';
        thinkTankUrl =location.origin + '/bbs';
        wechatUrl =  location.origin + '/labelManager';
        liveUrl = location.origin + '/liveBiz';

        break;
 
}


export {
    baseUrl,
    liveUrl,
    coursesUrl,
    thinkTankUrl,
    authUrl,
    publicUrl,
    enmoreManageUrl,
    liveBizUrl,
    noticeApi,
    baseNoticeApi,
    uploadUrl,
    wechatUrl,
    ieventUrl,
    serviceUrl,
    workflow,
    systemApi
}
