<template>
  <div id="app">
    <router-view v-if="show && update_view_status" />
    <!-- 换肤 -->
    <ele-theme :environment="environment" @callBackFun="callBackFun" :platformType="platformType" v-if="forcedFile"></ele-theme>
    <!-- iframe通信 -->
    <communication-iframe ref="communication" :query="query" @updateViewFun="updateViewFun" v-if="forcedFile"></communication-iframe>
    <!-- 加入全局的copy监听 -->
    <global-copy ref="globalCopy" sysCode="info" moduleType="live" businessType="liveSign"></global-copy>
  </div>
</template>

<script>
import { eleTheme, communicationIframe, globalCopy } from '@enmore/enmore-common-front'
export default {
  data() {
    return {
      show: false,
      update_view_status: false,
      environment: process.env.VUE_APP_CURRENTMODE,
      platformType: 3, // 自行的切换平台   3 是医疗平台  1是行家平台
      // 强制加载文件
      forcedFile: false,
      query: window.location.href.split('?')[1] //获取路径中的参数
    }
  },
  components: {
    eleTheme,
    communicationIframe,
    globalCopy,
  },
  computed: {},
  created() {
    // 缓存强制加载
    setTimeout(() => {
      this.show = false
      this.update_view_status = false
      this.forcedFile = true
    }, 100)
  },
  mounted() {
    // 设置copy信息
    this.$refs.globalCopy.setSystemData()
    this.$refs.globalCopy.globalCopy()
    // 监听来自各个系统的跳转
    this.$root.$on('messageLogin', (data) => {
      this.$store.commit('logout')
      this.removeContainerToken()
      setTimeout(() => {
        this.$router.replace('/login')
      }, 1000)
    })
  },
  provide() {
    return {
      removeContainerToken: this.removeContainerToken,
      setToken: this.setToken,
    }
  },
  methods: {
    callBackFun() {
      this.show = true
    },
    removeContainerToken() {
      this.$refs.communication.removeToken()
    },
    getToken() {
      this.$refs.communication.getToken()
    },
    setToken(token) {
      this.$refs.communication.setToken(token)
    },
    updateViewFun() {
      this.update_view_status = true
      this.$reslove()
    },
  },
}
</script>
<style lang="scss">
html,
body {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

#app {
  height: 100%;
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}
</style>
