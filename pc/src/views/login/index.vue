<template>
  <div class="login">
    <div id="login" class="flex-center flex-middle" v-if="show">
      <div class="flex-center flex-middle" v-if="platformInfoData == 1">
        <div class="logoTop"></div>
        <div class="loginContent flex-left">
          <div class="loginLeft unit"></div>
          <div class="loginRight unit flex-middle flex-center">
            <loginComponent @jumpIntoPage="jumpIntoPage" v-if="loginWayList.length"></loginComponent>
          </div>
        </div>
        <div class="bottomPlatform flex-center flex-middle">
          <img src="../../assets/img/logo_bottom.jpg" alt="" class="logo_bottom_hangjia" />
          <span>行家活动线上直播管理平台</span>
        </div>
      </div>
      <div class="MedicalWrap flex-vertical" v-else>
        <div class="MedicalContent flex-middle flex-center flex-vertical">
          <div class="formContent flex-center flex-middle">
            <loginComponent @jumpIntoPage="jumpIntoPage" v-if="loginWayList.length"></loginComponent>
          </div>
          <div class="bottomContent flex-middle">
            <img src="../../assets/img/logo_bottom.jpg" alt="" class="logo_bottom" />
            <div class="lineL"></div>
            <div class="bottomText flex-middle">
              <span>智会云提供技术支持</span>
            </div>
            <div class="lineR"></div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="login-loding">正在登录...</div>
  </div>
</template>

<script>
import { login, getUserInfo, validateLogin,getBizUserByOperId } from '@/api/login'
import { mapState, mapMutations } from 'vuex'
// import userPassword from './component/userPassword';
import { LoginComponent } from '@enmore/enmore_common_validate_front'
export default {
  name: 'login',
  mounted() {},
  inject: ['setToken','removeContainerToken'],
  components: {
    LoginComponent,
  },
  created() {
    //this.keyupSubmit();
    if (this.permissionList) {
      if (!localStorage.getItem('clientInfo')) {
        location.reload()
      }
      this.$store.commit('CLEAR_PERMISSION')
      // 重置路由为数据
      sessionStorage.setItem('fullPath', '/home')
    }
    // 重置呼叫中心状态
    this.$store.commit('changeCallCenterPower', false)
    this.$store.commit('changeCallCenterOpacity', true)
    this.getBizUserByOperId();
  },
  data() {
    return {
      form: {
        username: '',
        password: '',
        domain: location.host,
        url: location.href,
      },
      rules: {
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
      },
      show: true,
      query: this.$route.query
    }
  },
  computed: {
    permissionList() {
      return this.$store.state.permission.permissionList
    },
  },
  methods: {
    ...mapMutations({
      changeLogin: 'login',
      setUserInfo: 'setUserInfo',
    }),
    // 如果路由参数有userId，则换token后跳转到指定url
    getBizUserByOperId(){
      if(this.query.userId && this.query.targetUrl){ 
        this.show = false;
        let params = {
          bizId: this.query.bizId,
          sysCode: this.query.sysCode,
          operId: this.query.userId
        }
        getBizUserByOperId(params).then(res=>{
          if(res.code == this.$successCode){
            if(res.data){
                localStorage.setItem('bizId',this.query.bizId)
                sessionStorage.removeItem('breadCrumbData')
                this.changeLogin(res.data.token)
                this.setToken(res.data.token)
                window.location.href = decodeURIComponent(this.query.targetUrl);
            }else{
                this.$confirm('商家已撤销授权或管理员已取消分配,无法登录', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  showCancelButton: false,
                  showClose: false,
                  type: 'warning'
                }).then(() => {
                    window.location.href = decodeURIComponent(this.query.targetUrl);
                }).catch(() => {
                      
                });
            }
          }
        })
      }
    },

    keyupSubmit() {
      document.onkeydown = (e) => {
        let _key = window.event.keyCode
        if (_key === 13) {
          if (this.$route.name == 'login') {
            this.login()
          }
        }
      }
    },
    jumpIntoPage() {
      this.$router.push({ path: '/home' })
    },
    login() {
      if (!this.form.username) {
        this.$message.error('请输入用户名')
        return
      }
      if (!this.form.password) {
        this.$message.error('请输入密码')
        return
      }
      login(this.form).then((res) => {
        if (res.code == this.$successCode) {
          localStorage.setItem('bizId', res.data.bizId)
          // this.$message.success(res.info)
          sessionStorage.removeItem('breadCrumbData')
          this.changeLogin(res.data.token)
          this.$router.push({ path: '/home' })
          this.setToken(res.data.token)
          // 放开404的提示
          this.$store.commit('changeHasWarnStatus', false)
        } else if (res.code == '011') {
          // 打开邮箱验证
          this.$messageBox
            .confirm('密码长时间未更改,请前往邮箱进行验证', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })
            .then(() => {
              // 发送邮箱验证
              validateLogin(this.form).then((res) => {
                if (res.code != '001') {
                  this.$message.warning(res.info)
                }
              })
            })
            .catch(() => {})
        } else {
          //this.$message.error(res.info);
          return
        }
      })
    },
    getUserInfo() {
      getUserInfo().then((res) => {
        this.setUserInfo(res.data)
      })
    },
    forgetPassword() {
      let managerResetUrl = ''
      try {
        let sysInfo = JSON.parse(sessionStorage.getItem('sysInfo'))
        managerResetUrl = sysInfo.managerResetUrl
      } catch (e) {}
      if (managerResetUrl) {
        window.location.href = managerResetUrl + '?url=' + location.href
      }
    },
    submitLogin(data) {
      this.form = Object.assign(this.form, data)
      this.login()
    },
  },
}
</script>

<style scoped>
#login,.login {
  width: 100%;
  height: 100%;
  background: #ededed;
  -webkit-background-size: 100% 100%;
  background-size: 100% 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.logoTop {
  width: 247px;
  height: 55px;
  position: absolute;
  left: 42px;
  top: 38px;
  background: url('../../assets/img/1/logo.png') no-repeat;
}
.loginContent {
  width: 937px;
  height: 565px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 3px 27px 0px rgba(0, 0, 0, 0.17);
  border-radius: 13px;
}
.loginLeft {
  background: url('../../assets/img/1/leftBg.png') no-repeat;
  background-size: 100% 100%;
}
.loginRight {
  box-sizing: border-box;
}
.loginRightTop {
  width: 100%;
  height: 220px;
  font-size: 38px;
  font-family: PingFang SC;
  font-weight: 600;
  color: rgba(255, 98, 13, 1);
}
/* .form {
    height: 50px;
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 10px;
}
.form_name {
    font-size: 16px;
    height: 50px;
    color: rgba(255, 98, 13, 1);
    width: 80px;
}
.form_input {
    border: none;
    height: 48px;
    outline: none;
    font-size: 14px;
    padding-left: 10px;
}
.forgotPass {
    width: 100%;
    height: 40px;
    line-height: 40px;
    text-align: right;
    color: rgba(255, 98, 13, 1);
    font-size: 13px;
}
.btnlogin {
    width: 100%;
    height: 40px;
    background: rgba(255, 98, 13, 1);
    color: #fff;
    border-radius: 5px;
    font-size: 14px;
    margin-top: 10px;
    border: none;
    outline: none;
}
input::-webkit-input-placeholder {
    color: #be9e75;
}
input:-webkit-autofill {
    transition: background-color 5000s ease-in-out 0s;
} */

a:visited {
  font-size: 12px;
  color: #004ea1;
}
.bottomPlatform {
  width: 937px;
  height: 80px;
  font-size: 13px;
  font-family: PingFang SC;
  font-weight: 400;
  color: rgba(255, 98, 13, 1);
  line-height: 80px;
  position: absolute;
  left: 50%;
  bottom: 0;
  margin-left: -468px;
}
.MedicalWrap {
  width: 100%;
  height: 100%;
  background: url('../../assets/img/3/logo_bg.png') no-repeat;
  background-size: cover;
}
.MedicalContent {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  position: relative;
}
.formContent {
  width: 498px;
  height: 560px;
  background: #ffffff;
  border-radius: 6px;
}
.formContent_title {
  font-size: 36px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #065b98;
  margin-top: 109px;
  margin-bottom: 70px;
  text-align: center;
}
.formContent_content {
  padding: 0 70px;
}
.bottomContent {
  position: absolute;
  bottom: 30px;
  left: 50%;
  margin-left: -249px;
  width: 498px;
}
.lineL {
  width: 80px;
  height: 1px;
  background: linear-gradient(270deg, #c1c1c1 0%, rgba(193, 193, 193, 0) 100%);
}
.lineR {
  width: 80px;
  height: 1px;
  background: linear-gradient(270deg, rgba(193, 193, 193, 0) 0%, #c1c1c1 100%);
}
.bottomText {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #c1c1c1;
  margin: 0 6px;
}
.logo_bottom {
  width: 90px;
  margin-right: 40px;
}
.logo_bottom_hangjia {
  position: absolute;
  left: 0;
  width: 90px;
}
.login-loding{
  text-align: center;
  font-size: 18px;
}
</style>
