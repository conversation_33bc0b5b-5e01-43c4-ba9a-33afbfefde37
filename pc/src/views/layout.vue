<template>
  <div class="main-wrapper">
    <side-bar @updateIsRouter="updateIsRouter" ref="sildeBar"></side-bar>
    <div class="main-con unit flex-vertical">
      <top-bar>
        <breadCrumb @refreshRouterview="refreshRouterview"></breadCrumb>
      </top-bar>
      <div id="layoutContent" class="flex-vertical unit" style="overflow-x: hidden; padding: 20px">
        <transition class="unit">
          <router-view v-if="update" :key="$route.fullPath"></router-view>
        </transition>
      </div>
    </div>
    <!-- <pie-menu></pie-menu> -->
  </div>
</template>

<script>
import topBar from '@/components/common/topBar'
import sideBar from '@/components/common/sideBar'
import breadCrumb from '@/components/common/breadCrumb'
import getBtnId from '@/api/getBtnId'
import { pieMenuNew } from '@enmore/enmore-common-front/src/baseComponents/pieMenuNew.vue'
export default {
  name: 'layout',
  components: {
    topBar,
    sideBar,
    breadCrumb,
    pieMenuNew,
  },
  data() {
    return {
      update: false,
    }
  },
  watch: {
    $route: {
      handler(val) {
        if (document.getElementById('layoutContent')) {
          document.getElementById('layoutContent').style.padding = '20px'
        }
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    conPos() {
      let marginLeft = this.$store.state.sideBar.opened ? '180px' : '34px'
      return { 'margin-left': marginLeft }
    },
  },
  mounted() {},
  methods: {
    getId(id) {
      if (id) {
        getBtnId({ mid: id }).then((res) => {
          this.$store.commit('btnPowers', res.data)
          this.$store.commit('newBtnIsDisabled', true)
        })
      }
    },
    refreshRouterview() {
      this.update = false
      this.$nextTick(() => {
        this.update = true
      })
    },
    updateIsRouter() {
      this.$nextTick(() => {
        let el = document.querySelector('.el-menu-item.is-active')
        let id = ''
        let name = ''
        if (el) {
          id = document.querySelector('.el-menu-item.is-active').getAttribute('data-id')
          name = document.querySelector('.el-menu-item.is-active').getAttribute('data-name')
        }
        if (id) {
          localStorage.setItem('isInRouter', id);
          this.update = true;
          this.$refs.sildeBar.go(name, id, false);
        } else {
          this.update = true;
        }
      })
    },
  },
}
</script>

<style scoped>
.main-wrapper {
  width: 100vw;
  display: flex;
}
</style>
<style type="text/css">
/*导航收缩展开*/
.main-con {
  height: 100vh;
  overflow: auto;
  transition: margin-left 0.3s;
}
.el-menu-vertical-side {
  width: 100% !important;
  border-right: solid 0px #fff !important;
}
</style>
