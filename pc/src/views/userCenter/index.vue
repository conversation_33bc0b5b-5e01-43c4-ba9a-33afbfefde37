<template>
    <div id="userCenter">
        <div class="main-text" style="margin-bottom:20px;">
            信息设置
        </div>
        <el-card v-loading="acountMessage">
            <div class="perfix_line">账户信息</div>
            <div class="vertical-line"></div>
            <div class="card-content account-message">
                <div>
                    <div @click="headpicEditFlag = true" class="avator">
                        <img v-if="userInfo.userPhoto" :src="userInfo.userPhoto" alt="">
                        <img v-else src="https://os.ienmore.com/public/2020/08/26/e88d51c0f0cb4df0905f48405845dd62.jpeg" alt="">
                    </div>
                    <div class="account-text">登录账户：&nbsp{{userInfo.loginName}}</div>
                    <el-button style="font-size:14px;" @click="changePassword()" size="small" type="primary" v-if="loginWayList.includes('1')">修改密码</el-button>
                </div>
                <div>
                    <editableRow attributeName="userName" labelText="姓名" :value="userInfo.userName" @valueChanged="accountInfoChanged" />
                    <editableRow attributeName="userNameEn" labelText="英文名" :value="userInfo.userNameEn" @valueChanged="accountInfoChanged" />
                    <editableRow attributeName="infoName" labelText="公司" :value="userInfo.infoName" @valueChanged="accountInfoChanged" :editable='false' />
                    <editableRow attributeName="phone" labelText="手机" :value="userInfo.phone" @valueChanged="accountInfoChanged" />
                    <editableRow attributeName="email" labelText="邮箱" :value="userInfo.email" @valueChanged="accountInfoChanged" />
                </div>
            </div>
        </el-card>
        <el-card v-loading="componyMessage">
            <div  class="perfix_line">企业信息</div>
            <div class="vertical-line"></div>
            <div class="card-content compony-message">
                <editableRowImage attributeName="logo" :deleteAble="false" labelText="企业LOGO" :value="companyInfo.logo" :imageWidth='120' :imageHeight='49' :boxWidth='222' :boxHeight='91' placeholder="上传LOGO，建议尺寸120*49px" @valueChanged="companyInfoChanged" :editable="isAdmin" :required="true" />
                <div class="flex-row">
                    <div style="width:500px;">
                        <editableRow attributeName="bizName" labelText="企业名称" :value="companyInfo.bizName" @valueChanged="companyInfoChanged" :editable="isAdmin" :required="true" />
                        <editableRow attributeName="tradeText" labelText="行业" :value="companyInfo.tradeText" @valueChanged="companyInfoChanged" :editable='false' />
                        <editableRow attributeName="linkman.linkman" labelText="联系人姓名" :value="companyInfo.linkman.linkman" @valueChanged="companyInfoChanged" :editable="isAdmin" :required="true" />
                        <editableRow attributeName="linkman.title" labelText="联系人职位" :value="companyInfo.linkman.title" @valueChanged="companyInfoChanged" :editable="isAdmin" :required="true" />
                        <editableRow attributeName="linkman.phone" labelText="联系人电话" :value="companyInfo.linkman.phone" @valueChanged="companyInfoChanged" :editable="isAdmin" :required="true" />
                        <editableRow attributeName="linkman.email" labelText="联系人邮箱" :value="companyInfo.linkman.email" @valueChanged="companyInfoChanged" :editable="isAdmin" :required="true" />
                        <editableRowImage attributeName="linkman.wechatQrCode" labelText="微信二维码" :value="companyInfo.linkman.wechatQrCode" :imageWidth='140' :imageHeight='140' placeholder="上传微信二维码" @valueChanged="companyInfoChanged" :editable="isAdmin" />
                        <editableRow attributeName="linkman.wechatId" labelText="微信号" :value="companyInfo.linkman.wechatId" @valueChanged="companyInfoChanged" :editable="isAdmin" />
                    </div>
                    <div>
                        <editableRow :labelwidth="120" attributeName="bizNameEn" labelText="Company Name" :value="companyInfo.bizNameEn" @valueChanged="companyInfoChanged" :editable="isAdmin" />
                        <editableRow :labelwidth="120" attributeName="linkman.linkmanEn" labelText="Contact Name" :value="companyInfo.linkman.linkmanEn" @valueChanged="companyInfoChanged" :editable="isAdmin" />
                        <editableRow :labelwidth="120" attributeName="linkman.titleEn" labelText="Contact Title" :value="companyInfo.linkman.titleEn" @valueChanged="companyInfoChanged" :editable="isAdmin" />
                    </div>
                </div>
            </div>
        </el-card>
        <el-dialog v-if="headpicEditFlag" title="编辑图片" :visible.sync="headpicEditFlag" width="800px">
            <tailoredImage :imageUrl='userInfo.userPhoto' :imageWidth="80" :imageHeight="80" @success="getImage" @cancleTailor="headpicEditFlag=false" />
        </el-dialog>
    </div>
</template>
<script>
import editableRow from '@/components/common/editableRow.vue'
import editableRowImage from '@/components/common/editableRowImage.vue'

import tailoredImage from '@/components/common/tailoredImage'

import { getUserInfo } from '@/api/login.js'
import { editMember } from "@/api/department.js"
import { getBizInfo, editBizInfo } from "@/api/companyManagement.js"
export default {
    components: {
        editableRow,
        editableRowImage,
        tailoredImage
    },
    props: [],
    data () {
        return {
            acountMessage: false,
            componyMessage: false,
            headpicEditFlag: false,
            userInfo: {},
            companyInfo: {
                linkman: {}
            }
        };
    },
    computed: {
        isAdmin () {
            return this.userInfo.userType == 1
        }
    },
    mounted () {
        getUserInfo().then(res => {
            this.userInfo = res.data
        })
        getBizInfo().then(res => {
            this.companyInfo = res.data
        })
    },
    methods: {
        changePassword () {
            this.$store.state.topBar.showChangePasswordDialog = true
        },
        getImage (data) {
            this.userInfo.userPhoto = data
            editMember(this.userInfo).then(res => {
                console.log(res);
            })
            this.headpicEditFlag = false;
        },
        accountInfoChanged (data) {
            console.log(data);
            this.userInfo[data.attributeName] = data.inputValue
            editMember(this.userInfo).then(res => {
                console.log(res);
            })
        },
        companyInfoChanged (data) {
            console.log(data);
            let array = data.attributeName.split('.')
            if (array.length == 1) {
                this.companyInfo[array[0]] = data.inputValue || data.imageUrl
            } else {
                this.companyInfo[array[0]][array[1]] = data.inputValue || data.imageUrl
            }
            this.companyInfo
            editBizInfo(this.companyInfo).then(res => {
                console.log(res);
            })
        }
    }
}
</script>
<style lang='scss' scoped>
.card-content {
    margin-top: 44px;
}
.account-message {
    display: flex;
    flex-direction: row;
    > :nth-child(1) {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-left: 13px;
        min-width: 220px;
    }
    > :nth-child(2) {
        margin-left: 30px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }
    .avator {
        width: 80px;
        height: 80px;
        overflow: hidden;
        position: relative;
        border-radius: 50%;
        cursor: pointer;
        img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
        }
        &:hover {
            &:after {
                position: absolute;
                left: 0;
                bottom: 0;
                content: "修改";
                color: white;
                font-size: 12px;
                text-align: center;
                width: 80px;
                height: 20px;
                background: rgba(0, 0, 0, 0.5);
            }
        }
    }
    .account-text {
        margin-top: 22px;
        margin-bottom: 26px;
        @extend .normal-text;
    }
}
.compony-message {
}
</style>
<style lang='scss'>
#userCenter {
    .el-card {
        position: relative;
        margin-bottom: 20px;
    }
    .el-card__body {
        padding: 14px 20px;
    }
}
</style>