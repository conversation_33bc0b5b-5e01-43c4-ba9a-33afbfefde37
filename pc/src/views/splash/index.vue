<!--  -->
<template>
  <div class='splashWrap flex-center flex-middle'>
      <img :src="splashImg" alt="" >
  </div>
</template>

<script>
export default {
  name: '' ,
  data () {
    return {
      splashImg:''
    };
  },

  created(){},

  components: {},

  computed: {},

  mounted(){
    this.splashImg = require(`../../assets/img/${this.platformInfoData}/splash.png`);
  },

  methods: {}
}

</script>
<style  scoped>
.splashWrap{
    width: 100%;
    height: 100%;
}
.splashWrap img{
    width: 612px;
    height: 461px;
}
</style>