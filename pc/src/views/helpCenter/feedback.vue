<!-- 提交反馈意见 -->
<template>
    <div class="feedback">
        <helpBar :leftStyle="true"></helpBar>
        <div class="banner">
            <p class="width1200">提交意见反馈</p>
        </div>
        <div class="width1200 submit">
            <el-form :rules="rules" ref="form" :model="form" label-width="80px" label-position="top" size="medium">
              <el-form-item label="选择问题类型" class="type" prop="label">
                <el-checkbox-group v-model="form.label">
                  <el-checkbox :key="item.id" :label="item.id" v-for="item in typeList" size="small">{{item.name}}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="你的联系方式" prop="contract">
                 <el-input v-model="form.contract" style="width:449px;" placeholder="请输入邮箱或手机号"></el-input>
                 <p class="remarks">为了更快地响应和解决您的问题，请填写正在使用的联系方式哦</p>
              </el-form-item>
              <el-form-item label="问题内容" prop="content">
                <el-input type="textarea" v-model="form.content" style="width: 900px;" rows="11" placeholder="请填写问题内容"></el-input>
                <p class="remarks">请详细描述您遇到的问题，如能还原场景、提供解决方案，将便于我们更快优化您的使用体验。</p>
              </el-form-item>
              <el-form-item>
                <el-button size="medium" type="primary" @click="onSubmit('form')" calss="submitBtn">提交</el-button>
                <!-- <el-button>取消</el-button> -->
              </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script>
import helpBar from './component/helpBar';

import { addFeedback, getFeedbackList } from '@/api/helpCenter'
export default {
    name: 'feedback',
    components: {
        helpBar
    },
    data () {
        return {
            form: {
                label: [],
                contract: '',
                content: '',
            },
            rules: {
                label: [
                    { required: true, message: '请选择问题类型', trigger: 'change' }
                ],
                contract: [
                    { required: true, message: '请输入邮箱或手机号', trigger: 'blur' }
                ],
                content: [
                    { required: true, message: '请填写问题内容', trigger: 'blur' }
                ],
            },
            typeList: []
        };
    },

    computed: {
    },

    watch: {},

    mounted () {
        this.getFeedbackList()
    },

    methods: {
        onSubmit(formName) {
            this.$refs[formName].validate((valid) => {
              if (valid) {
                let labelList = []
                for (let item of this.form.label) {
                    for (let child of this.typeList) {
                        if (item == child.id){
                            labelList.push(child)
                        }
                    }
                }
                let params = {
                    content: this.form.content,
                    contract: this.form.contract,
                    bizId: localStorage.getItem('bizId'),
                    labelList: labelList,
                }
                // console.log(params)
                addFeedback(params).then(res => {
                    if (res.code == this.$successCode) {
                        this.$message.success('提交成功');
                        // this.$router.go(-1)
                        // 关闭当前页面
                        setTimeout(res=>{
                            window.opener=null;window.open('','_self');window.close()
                        },1000)
                    }
                })
              } else {
                console.log('error submit!!');
                return false;
              }
            });
        },
        getFeedbackList() {
            getFeedbackList({}).then(res => {
                if (res.code == this.$successCode) {
                    this.typeList = res.data
                }
            })
        },
    }
};
</script>
<style lang='scss' scoped>
.feedback{
    background-color: #fff;
    height: 100%;
    .banner{
        background: url("../../assets/img/helpBanner1.png") no-repeat 100% 100%;
        height: 85px;
        line-height: 85px;
        color: #FFFFFF;
    }
    .submit{
        font-size: 14px;
        .submitBtn{
            width: 86px;height:36px;font-size: 14px;
        }
        .remarks{
            color: #999999;
            font-size: 12px;
            margin-top: 4px;
            line-height: 17px;
        }
    }
}
</style>
<style lang='scss'>
.feedback{
    .type.el-form-item{
        margin-bottom: 13px;
    }
    .el-form-item__label, .el-checkbox__label{
        font-size: 14px;
    }
    .el-checkbox__label{
        color: #666666;
    }
    .el-form-item__label{
        color: #333333;
        line-height: 20px;
        margin: 18px 0 15px;
        padding: 0;
    }
    .el-checkbox{
        margin-top: 6px;
    }
    .el-input__inner, .el-textarea__inner{
        border: 1px solid #DDDDDD;
    }
    .el-checkbox__input.is-checked + .el-checkbox__label{
        color: #666666;
    }
}
</style>