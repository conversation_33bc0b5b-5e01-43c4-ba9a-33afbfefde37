<!-- 帮助中心 -->
<template>
    <div class="helpCenter">
        <helpBar></helpBar>
        <searchKeywords :sys-code="sysCodeStr" @getContent="getContent" @changeTagList="changeTagList" @searchList="searchList"></searchKeywords>
        <div class="helpMenu width1200">
            <div class="menu">
                <menuTitle :sys-code="sysCodeStr" @getContent="getContent" @changeTagList="changeTagList"></menuTitle>
            </div>
            <div class="menuCon">
                <messageContent ref="messageContent" :tagList="tagList" @getContent="getContent"></messageContent>
            </div>
        </div>
    </div>
</template>

<script>
import helpBar from './component/helpBar';
import searchKeywords from './component/searchKeywords';
import menuTitle from './component/menuTitle';
import messageContent from './component/messageContent';

export default {
    name: 'helpCenter',
    components: {
        helpBar,
        searchKeywords,
        menuTitle,
        messageContent,
    },
    data () {
        return {
            tagList: []
        };
    },

    computed: {
    },

    watch: {},

    mounted () {
        // 页面不允许复制
        document.getElementsByTagName('body')[0].setAttribute('topmargin', '0')
        document.getElementsByTagName('body')[0].setAttribute('oncontextmenu', 'return false')
        document.getElementsByTagName('body')[0].setAttribute('ondragstart', 'return false')
        document.getElementsByTagName('body')[0].setAttribute('onselectstart', 'return false')
        document.getElementsByTagName('body')[0].setAttribute('onselect', 'document.selection.empty()')
        document.getElementsByTagName('body')[0].setAttribute('oncopy', 'document.selection.empty()')
        document.getElementsByTagName('body')[0].setAttribute('onbeforecopy', 'return false')
        document.getElementsByTagName('body')[0].setAttribute('onmouseup', 'document.selection.empty()')
        // document.getElementsByTagName('body')[0].setAttribute('', '')
    },

    methods: {
        getContent(id) {
            this.$refs.messageContent.getContent(id, 1)
        },
        changeTagList(tagList) {
            this.tagList = tagList
        },
        searchList(keyword) {
            this.$refs.messageContent.searchList(keyword)
        }
    }
};
</script>
<style lang='scss' scoped>
.helpCenter{
    background-color: #fff;
    height: 100%;
    .helpMenu{
        margin-top: 20px;
        height: calc(100% - 334px);
        display: flex;
        justify-content: space-between;
        .menu{
            width: 236px;
            background-color: #FAFAFA;
            height: 100%;
            padding-left: 20px;
			overflow-y: auto;
			padding-bottom: 20px;
        }
        .menuCon{
            width: calc(100% - 266px);
        }        
    }
}
</style>
<style lang='scss'>
</style>