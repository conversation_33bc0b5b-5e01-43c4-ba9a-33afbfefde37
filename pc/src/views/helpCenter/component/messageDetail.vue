<template>
    <div v-loading="loading" style="height: calc(100% - 34px);overflow: auto;">
        <p class="title">{{fromDetail.title}}</p>
        <p class="time">更新时间：{{moment(fromDetail.updateDate)}}</p>
        <div class="content" v-html="fromDetail.content">
        </div>
    </div>
</template>

<script>
import moment from 'moment'
import { getContentDetail } from '@/api/helpCenter'
export default {
    name: 'messageDetail',
    components: {
    },
    data () {
        return {
            fromDetail: {},
            loading: false,
        };
    },

    computed: {
    },

    watch: {},

    mounted () {
    },

    methods: {
        getContentDetail(id){
            this.loading = true
            let params = {
              id: id,
            }
            getContentDetail(params).then(res => {
                if (res.code == this.$successCode) {
                    this.fromDetail = res.data
                    this.loading = false
                }
            })
        },
        moment(time) {
            return moment(time).format("YYYY-MM-DD")
        },
    }
};
</script>
<style lang='scss' scoped>
.title{
    font-size: 18px;
    color: #333333;
    margin-top: 10px;
    margin-bottom: 4px;
    font-weight: bold;
}
.time{
    font-size: 12px;
    color: #666666;
    margin-bottom: 20px;
}
</style>
<style lang='scss'>

</style>