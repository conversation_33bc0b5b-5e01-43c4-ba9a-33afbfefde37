<template>
    <div style="height: 100%">
    	<div class="breadcrumb">
	    	<el-breadcrumb separator-class="el-icon-arrow-right" v-if="tagShow">
			  <el-breadcrumb-item v-for="(tag,index) in tagList"><span @click="breadcrumbFun(tag.id, index)">{{tag.name}}</span></el-breadcrumb-item>
			</el-breadcrumb>
			<p v-else class="searchRes">搜索到 {{total}} 条<span> “{{keyword}}” </span>的相关结果：</p>
    	</div>
    	<div class="messageDetail" v-if="listShow" v-loading="loading">
    		<div class="messageTitle" v-for="item in messageList" @click="getDetail(item)" v-html="item.title"></div>
    		<div v-if="total>10" class="page">
	    		<el-pagination
		    		@current-change="handleCurrent"
		    		:total="total"
		    		background
		    		class="text-center"
					:page-size="10"
					layout="total, prev, pager, next,jumper"
					:current-page.sync="pageNum">
				</el-pagination>
    		</div>
    	</div>
        <messageDetail ref="messageDetail" v-if="!listShow"></messageDetail>
    </div>
</template>

<script>
import { getContent, getSearchList } from '@/api/helpCenter'
import messageDetail from './messageDetail';
export default {
    name: 'messageContent',
    props: {
        tagList: {
            type: Array
        },
    },
    components: {
    	messageDetail
    },
    data () {
        return {
        	messageList: [],
        	listShow: true,
        	loading: false,
        	menuId: '',
        	tagShow: true,
        	total: 1,
        	keyword: '',
        	pageNum: 1,
        };
    },

    computed: {
    },

    watch: {},

    mounted () {
    },

    methods: {
    	handleCurrent(val){
    		this.pageNum = val
    		if (this.tagShow) {
    			this.getContent(this.menuId)
    		} else {
    			this.searchList(this.keyword)
    		}
    	},
        breadcrumbFun(id, index) {
            //index
            this.tagList.splice(index+1, (this.tagList.length-index-1))
            this.getContent(id)
        },
    	// 点击菜单获取数据
    	getContent(id, index=2){
            if (index == 1) {
                // 点击 菜单
                this.pageNum = 1
            }
    		this.menuId = id
            this.listShow = true
            this.loading = true
            this.tagShow = true
            let params = {
              menuId:  id,
              pageNum:  this.pageNum,
              pageSize:  10,
            }
            getContent(params).then(res => {
                if (res.code == this.$successCode) {
                    // console.log(res.data)
                    this.messageList = res.data.list
                    this.loading = false
            		this.total = res.data.total
                }
            })
    	},
    	searchList(keyword) {
            this.listShow = true
            this.loading = true
            this.keyword = keyword
            let params = {
              menuId:  this.menuId,
              keyword: keyword,
              pageNum:  this.pageNum,
              pageSize:  10,
            }
            getSearchList(params).then(res => {
                if (res.code == this.$successCode) {
                    console.log(res.data)
                    for (let item of res.data.list) {
                    	item.title = item.title.replace(keyword,'<span style="color: #FF6B00">'+keyword+'</span>')
                    }
                    this.messageList = res.data.list
                    this.loading = false
            		this.tagShow = false
            		this.total = res.data.total
                }
            })
    	},
    	// 显示列表
    	detailShowFun(index) {
    		if (index == (this.tagList-1)) {
    			this.listShow = true
    		}
    	},
    	// 获取详情
    	getDetail(item){
            if (!this.tagShow) {
                item.title = item.title.replace('<span style="color: #FF6B00">'+this.keyword+'</span>',this.keyword)
                this.tagList = [{
                    name: item.title,
                    id: '',
                }]
            } else {
                this.tagList.push({
                    name: item.title,
                    id: '',
                })
            }
            this.pageNum = 1
    		this.listShow = false
            this.tagShow = true
            setTimeout(res=>{
                this.$refs.messageDetail.getContentDetail(item.id)
            },200)
    	},
    }
};
</script>
<style lang='scss' scoped>
.breadcrumb{
	color: #999999;
	font-size: 14px;
	margin-bottom: 20px;
    span{
        cursor: pointer;
    }
	.el-icon-arrow-right{
		color: #999999;
	}
	.searchRes{
		font-size: 16px;
		font-weight: bold;
		color: #333333;
		span{
			color: #FF6B00;
		}
	}
}
.messageDetail{
	color: #333333;
	font-size: 14px;
    height: calc(100% - 41px);
    overflow: auto;
	.messageTitle{
		line-height: 20px;
		border-bottom: 1px solid #F0F0F0;
		cursor: pointer;
        padding: 20px 0;
	}
	.messageTitle:hover{
		color: #FF6B00;
	}
	.page{
        margin-top: 20px;
        margin-bottom: 20px;
	}
}
</style>
<style lang='scss'>
.breadcrumb{
	.el-breadcrumb__inner{
		color: #999999;

	}
	.el-breadcrumb__item:last-child .el-breadcrumb__inner{
		color: #FF6B00;
	}
}
</style>