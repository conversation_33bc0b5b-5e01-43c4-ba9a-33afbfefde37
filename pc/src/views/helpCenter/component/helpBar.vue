<!-- 帮助中心 bar-->
<template>
    <div class="width1200 helpBar" :style="leftStyle?'':'padding-left: 34px;'">
            <svg width="117px" height="28px" viewBox="0 0 117 28" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <g id="1.0首页" transform="translate(-31.000000, -16.000000)" fill="#FF6B00">
                        <g id="编组-3备份" transform="translate(31.000000, 16.000000)">
                            <g id="编组-2" transform="translate(32.000000, 0.000000)">
                                <path d="M6.94444444,23.5686831 C5.06252003,24.4608584 3.15360443,25.1971475 1.21769763,25.7775502 L-1.31581988e-14,23.3508063 C2.15774439,22.6652356 4.46861123,21.7383911 6.93260052,20.5702728 C8.98397429,19.5977655 10.9940951,18.5278974 12.962963,17.3606684 L12.962963,15.5407958 C10.4197737,17.0225188 8.4191035,18.1090175 6.96095236,18.8002921 C5.07122259,19.6961678 3.15680435,20.3719205 1.21769763,20.8275502 L-1.31581988e-14,18.4008063 C2.20495113,17.8191461 4.53813865,16.9448649 6.99956255,15.7779627 C8.18914205,15.2140115 9.36020598,14.6046906 10.5127543,13.95 L5.09259259,13.95 L9.25925926,11.25 L12.7407407,11.25 C14.397595,11.25 15.7407407,12.5931458 15.7407407,14.25 L15.7407407,20.2573926 C15.7407407,22.5852007 14.5061728,24.8327365 12.037037,27 L10.224373,24.7537238 C11.9933574,23.4396391 12.905339,21.9933182 12.9603177,20.4147613 C10.4034107,21.8282232 8.39811964,22.8795304 6.94444444,23.5686831 Z" id="Combined-Shape"></path>
                                <path d="M1.20508493,15.8695058 L0.00442928738,13.4590004 C2.19934338,12.6858838 3.92090277,12.0029546 5.16910744,11.4102128 C6.41731211,10.8174709 7.51059329,10.1637013 8.44895099,9.44890382 L4.65014217,9.44890382 L4.65014217,6.75476332 L20.8364976,6.75476332 L20.8364976,9.44890382 L14.7616494,9.44890382 C10.0805611,11.9351117 7.06360277,13.4985761 5.71077441,14.1392968 C4.35794604,14.7800176 2.85604955,15.3567539 1.20508493,15.8695058 Z" id="家-copy"></path>
                                <path d="M0,9.45 L0,5.8 C-2.705415e-16,3.590861 1.790861,1.8 4,1.8 L21,1.8 C23.209139,1.8 25,3.590861 25,5.8 L25,9.45 L22.2457257,9.45 L22.2457257,4.50338333 L2.75427435,4.50338333 L2.75427435,9.45 L0,9.45 Z" id="Combined-Shape"></path>
                                <rect id="Rectangle-7" x="10.6481481" y="0" width="3.7037037" height="1.8"></rect>
                                <path d="M23.6807383,11.25 L23.6807383,13.95 L20.0298124,13.95 L21.6972002,19.99584 L21.7032802,20.01924 L21.6972002,19.99584 C22.2536566,22.0144155 23.4420254,23.7043881 25,24.9185557 L23.2138937,26.9873346 C21.2337098,25.4289719 19.7239543,23.2697842 19.0140729,20.6946514 L17.4572086,15.0472986 C17.0168727,13.450029 17.9547529,11.7982234 19.5520225,11.3578875 C19.8117389,11.2862888 20.0799163,11.25 20.3493212,11.25 L23.6807383,11.25 Z" id="路径"></path>
                            </g>
                            <g id="编组">
                                <polygon id="Combined-Shape" points="10.8308315 1.37268908 27.3554217 1.37268908 27.3554217 4.11806723 10.8308315 4.11806723"></polygon>
                                <polygon id="Path-Copy-2" points="9.92419248 12.3484707 9.92419248 9.61567996 27.8104608 9.61567996 27.8104608 12.3484707"></polygon>
                                <polygon id="Combined-Shape" points="6.24066761 11.8966387 6.24066761 26.5386555 3.48656925 26.5386555 3.48656925 11.8966387"></polygon>
                                <polygon id="Rectangle" transform="translate(4.131148, 12.258902) rotate(-45.000000) translate(-4.131148, -12.258902) " points="0.00425110201 10.8087488 8.25346587 10.8218292 8.25804398 13.7090544 0.00882921208 13.6959741"></polygon>
                                <polygon id="Rectangle-Copy" transform="translate(4.131148, 4.022767) rotate(-45.000000) translate(-4.131148, -4.022767) " points="0.00425110201 2.57261438 8.25346587 2.5856947 8.25804398 5.47291995 0.00882921208 5.45983964"></polygon>
                                <path d="M20.8877158,10.9815126 L23.6444368,10.9815126 L23.6444368,20.1402899 C23.6444368,22.5072209 22.4203931,24.7925304 19.9723057,26.9962185 L18.1750938,24.7121897 C19.9289984,23.3760195 20.8332057,21.9043148 20.8877158,20.2992276 C20.8877158,18.7272662 20.8877158,15.6213612 20.8877158,10.9815126 Z" id="Combined-Shape"></path>
                            </g>
                            <g id="动" transform="translate(91.000000, 1.000000)">
                                <rect id="矩形" x="0.912143928" y="1.8" width="11.4017991" height="2.7"></rect>
                                <rect id="矩形备份-2" x="0" y="9.9" width="13.226087" height="2.7"></rect>
                                <rect id="矩形" x="15.9625187" y="0" width="2.73643178" height="4.05"></rect>
                                <path d="M25.9961019,20.1225404 C25.9961019,22.4969047 24.77991,24.7893912 22.3475262,27 L20.5618429,24.7087982 C22.3044967,23.3684318 23.2029039,21.8921053 23.2570643,20.2819772 L23.2569337,6.804 L13.226087,6.804 L13.226087,4.05 L23.9961019,4.05 C25.1006714,4.05 25.9961019,4.9454305 25.9961019,6.05 L25.9961019,20.1225404 Z" id="路径"></path>
                                <path d="M16.1703296,6.3 L18.6989505,6.3 L18.6989505,20.1075569 C18.6989505,22.487094 17.5761882,24.7845751 15.3306636,27 L13.6821589,24.7038065 C15.2909396,23.3605199 16.1203299,21.8809769 16.1703296,20.267341 C16.1703296,18.6870074 16.1703296,14.031227 16.1703296,6.3 Z" id="Combined-Shape"></path>
                                <path d="M7.79501497,18.9878468 L10.8383567,18.9878468 L12.5273847,22.616946 C12.8769407,23.3680132 12.5514516,24.2602447 11.8003843,24.6098008 C11.6021452,24.7020638 11.3861318,24.7498707 11.1674741,24.7498732 L1.35035078e-14,24.75 L1.35035078e-14,22.0905447 L4.83628598,11.7 L7.37619393,12.7816979 L3.04301141,22.0904919 L9.23931849,22.0904919 L7.79501497,18.9878468 Z" id="形状结合"></path>
                            </g>
                            <g id="活" transform="translate(59.000000, 1.000000)">
                                <rect id="矩形" x="16.3333333" y="3.17625" width="2.8" height="12.705"></rect>
                                <path d="M9.8,26.77125 L9.8,15.4275 L26.6,15.4275 L26.6,22.77125 C26.6,24.980389 24.809139,26.77125 22.6,26.77125 L9.8,26.77125 Z M23.8,18.15 L12.6,18.15 L12.6,24.04875 L22.8,24.04875 C23.3522847,24.04875 23.8,23.6010347 23.8,23.04875 L23.8,18.15 Z" id="形状"></path>
                                <rect id="矩形" x="8.86666667" y="9.52875" width="19.1333333" height="2.7225"></rect>
                                <rect id="矩形" x="9.8" y="1.36125" width="16.8" height="2.7225"></rect>
                                <polygon id="Rectangle-Copy" transform="translate(4.539724, 3.244313) rotate(-315.000000) translate(-4.539724, -3.244313) " points="1.56871363 1.83663542 7.55142197 1.75267708 7.51073446 4.65198967 1.52802613 4.735948"></polygon>
                                <polygon id="Rectangle-Copy备份" transform="translate(4.539724, 10.504313) rotate(-315.000000) translate(-4.539724, -10.504313) " points="1.56871363 9.09663542 7.55142197 9.01267708 7.51073446 11.9119897 1.52802613 11.995948"></polygon>
                                <polygon id="Rectangle-Copy备份-2" transform="translate(3.839749, 21.848020) rotate(-60.000000) translate(-3.839749, -21.848020) " points="-1.20468922 20.3266673 8.84895146 20.4497163 8.88418787 23.3693726 -1.16945281 23.2463236"></polygon>
                            </g>
                        </g>
                    </g>
                </g>
            </svg>
            <p>帮助中心</p>
    </div>
</template>

<script>
export default {
    name: "helpBar",
    components: {
    },
    props: {
        leftStyle:{
          type:Boolean,
          default: false
        },
    },
    data () {
        return {
        };
    },

    computed: {
    },

    watch: {},

    mounted () {
    },

    methods: {
    }
};
</script>
<style lang='scss' scoped>
.helpBar{
    height: 101px;
    overflow: hidden;
    display: flex;
    align-items: center;
    p{
        display: inline-block;
        color: #333333;
        font-size: 18px;
        margin-left: 22px;
        position: relative;
    }
    p:before{
        content: '';
        display: inline-block;
        width: 1px;
        height: 28px;
        background-color: #FF6B00;
        position: absolute;
        top: 0;
        left: -11px;
    }
}
</style>