<template>
    <div class="menuTitle">
      <div class="title">常见问题</div>
      <div class="menu">
          <el-menu
            :default-active="firstIndex"
            class="el-menu-vertical-demo"
            background-color="#FAFAFA"
            text-color="#666666"
            active-text-color="#FF6B00"
            :collapse-transition="false"
            :unique-opened="true"
            @open="handleOpen"
            @close="handleOpen"
            @select="handleOpen">
              <el-submenu :index="item.id+''" v-for="item in menuList" :class="(item.children && item.children.length!=0)?'':'none'">
                <template slot="title">
                  <span class="titlefirst">{{item.name}}</span>
                </template>
                <el-menu-item-group>
                  <el-menu-item :index="child.id+''" v-for="child in item.children">{{child.name}}</el-menu-item>
                </el-menu-item-group>
              </el-submenu>
          </el-menu>
      </div>
    </div>
</template>

<script>
import { getMenu } from '@/api/helpCenter'
export default {
    name: 'menuTitle',
    props: {
        //系统标识
        sysCode: {
            type: String,
            required: true
        },
    },
    components: {
    },
    data () {
        return {
          firstIndex: '',
          menuList: []
        };
    },

    computed: {
    },

    watch: {},

    mounted () {
      this.getMenu()
    },

    methods: {
        handleOpen(key, keyPath) {
          this.$emit('getContent', key)
          this.changeTagList(keyPath)
        },
        changeTagList(list) {
          let tagList = []
          for (let item of this.menuList) {
            if (item.id == list[0]) {
                tagList.push({
                        id: item.id,
                        name: item.name,
                      })
                if (list.length==2) {
                  for (let child of item.children) {
                    if (child.id == list[1]) {
                      tagList.push({
                        id: child.id,
                        name: child.name,
                      })
                    }
                  }
                }
            }
          }
          this.$emit('changeTagList', tagList)
        },
        // 获取菜单
        getMenu() {
            let params = {
              sysCode:  this.sysCode
            }
            getMenu(params).then(res => {
                if (res.code == this.$successCode) {
                    this.menuList = res.data
                    if (res.data[0].children && res.data[0].children[0].id) {
                      this.firstIndex =res.data[0].children[0].id+''
                      this.$emit('getContent', res.data[0].children[0].id)
                      this.$emit('changeTagList', [{
                          id: res.data[0].id,
                          name: res.data[0].name,
                        },{
                          id: res.data[0].children[0].id,
                          name: res.data[0].children[0].name,
                        }])
                    } else {
                      this.firstIndex = res.data[0].id+''
                      this.$emit('getContent', res.data[0].id)
                      this.$emit('changeTagList', [{
                          id: res.data[0].id,
                          name: res.data[0].name,
                        }])
                    }
                }
            })
        }
    }
};
</script>
<style lang='scss' scoped>
.menuTitle{
    color: #333333;
    .title{
        font-size: 16px;
        margin: 20px 0;
        font-weight: bold;
    }
    .menu{
      .titlefirst{
        color: #333333;
        font-weight: bold;

      }
      .el-menu-vertical-demo{
        font-size: 14px;
      }
    }
}
</style>
<style lang='scss'>
  
    .menu{
      .el-menu{
        border: none;
      }
      .el-submenu__title, .el-menu-item{
        font-size: 14px;
        background-color: #FAFAFA !important;
      }
      .el-menu-item-group__title{
        display: none;
      }
      .el-submenu__title{
        height: 40px;
        line-height: 40px;
        padding-left: 0px !important;
      }
      // 没有子菜单不显示箭头
      .none.el-submenu .el-submenu__title i{
        display: none
      }
      .el-menu-item-group{
        ul{
          border-left: 2px solid #E5E8EE;
        }
      }
      .el-menu-item{
        height: 24px;
        line-height: 24px;
        padding-left: 15px !important;
        border-left: 2px solid #E5E8EE;
        margin-bottom: 4px;
        margin-left: -2px;
      }
      .el-menu-item.is-active{
        border-left: 2px solid #FF6B00;
        
      }
    }
</style>