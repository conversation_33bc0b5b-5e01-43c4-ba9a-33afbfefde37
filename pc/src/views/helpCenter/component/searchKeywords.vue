<!-- 帮助中心 -->
<template>
    <div class="searchKeywords">
        <div class="search">
            <el-input v-model.trim="keyword" class="keyTitle" placeholder="输入问题关键字，找到答案" @keyup.enter.native="searchList()">
                <i slot="prefix" class="iconfont iconsousuo el-icon-search" @click="searchList"></i>
            </el-input>
            <ul class="ToppingTitle">
                <li v-for="item in menuList" @click="menuFun(item)">{{item.name}}</li>
            </ul>
            <p class="opinion" @click="feedback">提交意见反馈</p>
        </div>
    </div>
</template>

<script>
import { getPushMenu } from '@/api/helpCenter'
export default {
    name: 'searchKeywords',
    props: {
        //系统标识
        sysCode: {
            type: String,
            required: true
        },
    },
    components: {
    },
    data () {
        return {
            keyword: '',
            menuList: [],
        };
    },

    computed: {
    },

    watch: {},

    mounted () {
        this.getPushMenu()
    },

    methods: {
        // 意见反馈页面
        feedback() {
            let feedback = this.$router.resolve({
                name: "feedback",
                params: this.$route.params
            });
            window.open(feedback.href, '_blank');
        },
        // 推送菜单数据
        getPushMenu() {
            let params = {
              sysCode:  this.sysCode
            }
            getPushMenu(params).then(res => {
                if (res.code == this.$successCode) {
                    console.log(res.data)
                    this.menuList = res.data
                }
            })
        },
        menuFun(item) {
          this.$emit('getContent', item.id)
          this.$emit('changeTagList', [{
            id: item.id,
            name: item.name,
          }])
        },
        // 搜索
        searchList() {
            if (this.keyword == '') {
                return
            }
            this.$emit('searchList', this.keyword)
        }
    }
};
</script>
<style lang='scss' scoped>
.searchKeywords{
    background: url("../../../assets/img/helpBanner.png") no-repeat 100% 100%;
    height: 169px;
    .search{
        width: 800px;
        margin: 0 auto;
        height: 100%;
        .keyTitle{
            width: 800px;
            height: 50px;
            margin-top: 39px;
            .el-icon-search{
                color: #AAAAAA;
                font-size: 21px;
                font-weight: bold;
                margin: 15px 15px 15px 17px;
            }
        }
        .ToppingTitle{
            color: #FFFFFF;
            font-size: 14px;
            display: inline-block;
            margin: 20px 5px;
            li{
                display: inline-block;
                position: relative;
                margin-left: 40px;
                cursor: pointer;
            }
            li:before{
                content: '';
                display: inline-block;
                height: 4px;
                width: 4px;
                border-radius: 2px;
                position: absolute;
                top: 8px;
                left: -8px;
                background-color: #ffffff;
            }
        }
        .opinion{
            float: right;
            color: #FFFFFF;
            font-size: 14px;
            margin-top: 20px;
            cursor: pointer;
        }
    }
}
</style>
<style lang='scss'>
    .keyTitle .el-input__inner{
        height: 50px;
        border-radius: 25px;
        border-color: #fff;
        padding-left: 52px;
        font-size: 16px;
    }
    .keyTitle .el-input__inner::-webkit-input-placeholder{
        color: #999999
    }
    .keyTitle .el-input__suffix i{
        font-size: 18px;
    }
</style>