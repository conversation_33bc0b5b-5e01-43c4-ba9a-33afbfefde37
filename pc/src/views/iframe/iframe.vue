<!-- 实现分包路由进行跳转 -->
<template>
    <div class='iframeWrap' v-loading="loading">
        <iframe @load="postData" v-if="isShowIframe" id="frontIframe" :key="iframeUrl" :src="iframeUrl" frameborder="0" style="width:100%;height:100%;"></iframe>
    </div>
</template>

<script>
import { liveUrl, coursesUrl, thinkTankUrl, wechatUrl} from '../../config/env';
export default {
    name: '',
    data () {
        return {
            loading: true,
            iframeUrl: "",
            isShowIframe: false,
            urlOptions: {
                liveUrl,
                coursesUrl,
                thinkTankUrl,
                wechatUrl
            }
        };
    },

    created () {
        this.isShowIframe = false;
    },
    computed: {
        isRefresh () {
            return this.$store.state.isRefresh;
        },
        allRouterConfig () {
            return this.$store.state.permission.allRouterConfig;
        }
    },

    mounted () {
        this.changeFrameUrl();
        document.getElementById("layoutContent").style.padding = "0 0 0 20px"
    },
    methods: {
        changeFrameUrl () {
            let onoff = false;
            for (let attr in this.allRouterConfig) {
                if (this.allRouterConfig[attr].includes(this.routerNames) || this.allRouterConfig[attr].includes(localStorage.getItem('currentRouter'))) {
                    this.iframeUrl = this.urlOptions[attr];
                    onoff = true;
                }
            }
            if (!onoff) {
                this.$router.replace('/splash');
            }
            this.isShowIframe = true;
        },
        postData () {
            this.loading = false;
            let sendData;
            //console.log(this.isRefresh,sessionStorage.getItem('fullPath') ,localStorage.getItem('currentRouter'))
            if (this.isRefresh || !sessionStorage.getItem('fullPath')) {
                if (this.routerQuery || localStorage.getItem('currentRouterId')) {
                    sendData = {
                        name: this.routerNames || localStorage.getItem('currentRouter'),
                        id: this.routerQuery || Number(localStorage.getItem('currentRouterId'))
                    };
                } else {
                    sendData = {
                        name: this.routerNames || localStorage.getItem('currentRouter'),
                    };
                }
            } else {
                sendData = {
                    name: sessionStorage.getItem('fullPath'),
                    refreshPage: true
                };
            }
            this.$store.commit('changeRefresh', true); 
            this.$store.state.iframeUrl = this.iframeUrl;
            document.getElementById("frontIframe").contentWindow.postMessage(JSON.stringify(sendData), this.iframeUrl);
        }
    },
}

</script>
<style  scoped>
.iframeWrap {
    width: 100%;
    height: 100%;
    overflow: hidden;
}
</style>
