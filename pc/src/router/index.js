import Vue from 'vue'
import Router from 'vue-router'
import store from '@/store';
import {
    setTableThBold
} from "@/utils/validate";
import CONSTANT from '@/config/config_constant';
let $local = require('store');
import login from './login';
import helpCenter from './helpCenter';
import helpFeedback from './helpFeedback';
import splash from './splash';
import userCenter from './userCenter';
import iframe from './iframe';

Vue.use(Router)

const originalPush = Router.prototype.push;
const originalReplace = Router.prototype.replace;
Router.prototype.replace = function push(location, onResolve, onReject) {
    if (onResolve || onReject) return originalReplace.call(this, location, onResolve, onReject)
    return originalReplace.call(this, location).catch(err => err)
}

Router.prototype.push = function push(location, onResolve, onReject) {
    if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
    return originalPush.call(this, location).catch(err => err)
}


const router = new Router({
    routes: [
        ...login,
        ...helpCenter,
        ...helpFeedback,
        ...userCenter,
        ...splash,
        ...iframe,
        {
            path: '*',
            redirect: '/splash',
        }
    ]
})

router.beforeEach(async (to, from, next) => {
    await router.app.$onWait;
    // 这里需要接受新页面传递过来的token
    let UserToken = $local.get(CONSTANT.token);
    if (to.name == 'login') {
        next();
        return;
    }

    if (!UserToken) {
        next({
            name: 'login',
            query: to.query
        });
    } else {
        if (!store.state.permission.permissionList) {
            store.dispatch('FETCH_PERMISSION').then(() => {
                if (!store.state.permission.permissionList) {
                    next({
                        name: 'login'
                    });
                } else {
                    next({
                        path: to.path,
                        query: to.query
                    })
                }
            })
        } else {
            if (to.path !== '/login') {
                next()
            } else {
                next(from.fullPath)
            }
        }
    }
})
router.afterEach(to => {
    store.commit('changeRouterNames', to.name ? to.name : (to.params.name ? to.params.name : 'home'));
    // 如果有id的话要进行保存操作
    if (to.params.id) {
        store.commit('changeRouterQuery', to.params.id);
    }
    // 给table表头添加 bold-medium
    setTimeout(() => {
        setTableThBold()
    }, 50)
})
export default router;