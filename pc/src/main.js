import Vue from 'vue';
import App from './App.vue'
import router from './router'
import installDirective from './directives';
import plugins from './plugins';
import store from './store';
import mixins from "@/mixins"; //引入混合
import components from './components';
//设置.bold-medium
import '@/assets/js/setBoldMedium';
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition';
Vue.component(CollapseTransition.name, CollapseTransition);
Vue.mixin(mixins);//使用混合
//注册所有全局组件
Object.values(components).forEach(item=>{
  Vue.component(item.name,item);
})
//注册所有全局指令
installDirective(Vue);
//注册插件
Object.keys(plugins).forEach((key)=>{
    plugins[key].install(Vue);
})
Vue.prototype.$onWait=new Promise((reslove)=>{
  Vue.prototype.$reslove=reslove;
})

Vue.config.productionTip = false;
import './assets/css/element-variables.scss';
var vm = new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
window.$vm = vm;