import store from '@/store';
const installDirective = Vue => {
    Vue.directive('has', {
        update: function (el, binding) {
                if (!powerBtn(binding.value)) { 
                     if (el.parentNode) { 
                    el.parentNode.removeChild(el);
                }
            }

        }
    });
}
// 判断按钮权限控制的函数
function powerBtn(value) {
    if(store.state.powerBtn.btnPower.length){
      if(!store.state.powerBtn.flag[value]){
            return false;
      }
      return true; 
    }   
    return true;  
}

export default installDirective;
