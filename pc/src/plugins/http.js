import axios from 'axios'
import {
    authUrl,
    baseUrl,
    liveBizUrl,
    noticeApi,
    ieventUrl,
    serviceUrl,
    systemApi
} from '../config/env'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import CONSTANT from '@/config/config_constant'
import {
    Message
} from 'element-ui'
import router from '@/router'
import store from '@/store';
let $local = require('store')
const loginOut = (that, callback) => {
    if (store.state.callCenterPower) {
        // 判断当前状态
        that.$root.$emit('emitCallCenter401')
    } else {
        callback();
    }
}
// 引入事件总线进行路由的跳转
export default function axiosHttp(type = 0) {
    let url = '';
    switch (type) {
        case 0:
            url = baseUrl;
            break;
        case 1:
            url = authUrl;
            break;
        case 2:
            url = liveBizUrl;
            break;
        case 3:
            url = '';
            break;
        case 4:
            url = noticeApi;
        case 5:
            url = systemApi;
            break;
        case 'ieventUrl':
            url = ieventUrl;
            break;
        case 'serviceUrl':
            url = serviceUrl;
            break;
        default:
            url = baseUrl;
            break;
    }
    const Axios = axios.create({
        baseURL: url
    });

    Axios.interceptors.request.use(
        config => {
            // console.log(config);
            NProgress.start();
            //router.app.$children[0].getToken();
            let token = $local.get(CONSTANT.token);
            if (token && !config.url.includes('login')) {
                config.headers.common['Authorization'] = token
            }
            return config;
        },
        error => {
            return Promise.reject(error);
        }
    )

    Axios.interceptors.response.use(
        response => {
            NProgress.done();
            let data = response.data;
            if (response.headers && response.headers['authorization']) {
                $local.set(CONSTANT.token, response.headers['authorization']);
            }
            if (data.code != '001' && data.code != 302) {
                Message({
                    message: data.info,
                    type: 'error'
                });
            } else if (data.code == 302) {
                window.location.href = response.location;
            }
            return data;
        },
        error => {
            NProgress.done();
            Message.closeAll();
            let message = '';
            if (error.response) {
                switch (error.response.status) {
                    case 401:
                        message = '登录超时，请重新登录';
                        router.app.$children[0].removeContainerToken();
                        if ($local.get(CONSTANT.token) && !store.state.hasWarnStatus) {
                            store.commit('changeHasWarnStatus', true);
                            sessionStorage.removeItem('fullPath');
                            Message({
                                message: message,
                                type: 'error'
                            });
                        }
                        loginOut(router.app, () => {
                            setTimeout(() => {
                                router.push('login');
                            }, 1000)
                        })
                        break;
                    case 416:
                        message = '账号被冻结,请联系管理员';
                        router.app.$children[0].removeContainerToken();
                        Message({
                            message: message,
                            type: 'error'
                        });
                        loginOut(router.app, () => {
                            setTimeout(() => {
                                router.push('login');
                            }, 1000)
                        })
                        break;
                    case 500:
                        message = '服务器出错';
                        Message({
                            message: message,
                            type: 'error'
                        });
                        break;
                }
            }
            return Promise.reject(error.response);
        }
    );
    Axios.all = axios.all;
    return Axios;
}