import {
    Button,
    Select,
    Cascader,
    Card,
    Form,
    FormItem,
    Input,
    InputNumber,
    RadioGroup,
    Radio,
    Checkbox,
    CheckboxGroup,
    Row,
    Col,
    Menu,
    MenuItemGroup,
    MenuItem,
    Submenu,
    Tree,
    Dropdown,
    DropdownMenu,
    DropdownItem,
    Breadcrumb,
    BreadcrumbItem,
    Steps,
    Step,
    Dialog,
    Table,
    TableColumn,
    Pagination,
    Option,
    Collapse,
    CollapseItem,
    RadioButton,
    Tag,
    ColorPicker,
    ButtonGroup,
    Tabs,
    TabPane,
    DatePicker,
    Upload,
    Progress,
    Slider,
    Switch,
    TimePicker,
    Loading,
    Tooltip,
    Notification,
    Badge,
    Link,
	Popover,
} from 'element-ui';
export default {
    Button,
    Select,
    Cascader,
    Card,
    Form,
    FormItem,
    Input,
    InputNumber,
    RadioGroup,
    Radio,
    Checkbox,
    CheckboxGroup,
    Row,
    Col,
    Menu,
    MenuItemGroup,
    MenuItem,
    Submenu,
    Tree,
    Dropdown,
    DropdownMenu,
    DropdownItem,
    Breadcrumb,
    BreadcrumbItem,
    Steps,
    Step,
    Dialog,
    Table,
    TableColumn,
    Pagination,
    Option,
    Collapse,
    CollapseItem,
    RadioButton,
    Tag,
    ColorPicker,
    ButtonGroup,
    Tabs,
    TabPane,
    DatePicker,
    Upload,
    Progress,
    Slider,
    Switch,
    TimePicker,
    Tooltip,
    Notification,
    Badge,
    Link,
	Popover,
}

import Vue from 'vue'
import {
    Message,
    MessageBox
} from 'element-ui'
Vue.use(Loading.directive);
Vue.prototype.$loading = Loading.service;
Vue.prototype.$message = Message;
Vue.prototype.$messageBox = MessageBox;
Vue.prototype.$confirm = MessageBox.confirm;