<template>
    <div>
        <div flex="dir:left">
            <div class="box" :style="boxStyle">
                <img v-if="imageUrl" :src="imageUrl"/>
                <label v-else class="tip">建议尺寸：{{suggestSize.width}}*{{suggestSize.height}}</label>
            </div>
            <div>
                <el-upload ref="upload" class="avatar-uploader" :action="uploadUrl" name="file" :on-success="itemUpload" :before-upload="beforeUpload">
                    <el-button style="margin-left: 10px" size="small">上传</el-button>
                </el-upload>
                <el-button style="margin:10px;" class="dels" size="small" @click="removeImage">删除</el-button>
            </div>
        </div>
        <el-dialog :visible.sync="dialogFlag">
            <img width="100%" :src="imageUrl" alt="">
        </el-dialog>
    </div>
</template>

<script>
    import CONSTANT from '@/config/config_constant'

    export default {
        name: "uploadImage",
        props: {
            url: {
                type: String,
                default: ''
            },
            boxStyle: {
                type: Object,
                default: {
                    width: '100px',
                    height: '100px',
                }
            },
            suggestSize: {
                type: Object,
                default: {
                    width: 200,
                    height: 200
                }
            }
        },
        data() {
            return {
                uploadUrl: CONSTANT.UPLOAD_URL,
                dialogFlag: false,
                imageFormat: ['image/jpeg', 'image/jpg', 'image/png'],
                imageUrl: ''
            }
        },
        computed: {},
        watch: {
            url(val) {
                this.imageUrl = val;
            }
        },
        mounted() {
            this.imageUrl = this.url;
        },
        methods: {
            beforeUpload(file) {
                console.log(this.uploadUrl);
                const isLt3M = file.size / 1024 / 1024 < 3;
                const isFormat = this.imageFormat.includes(file.type);
                if (!isFormat) {
                    this.$message.error('上传图片只能是 JPG 格式 和 PNG 格式!');
                }
                if (!isLt3M) {
                    this.$message.error("文件 " + file.name + " 太大，不能超过 3M。");
                }
                return isFormat && isLt3M
            },
            itemUpload(res, file) {
                if (res.code == '002') {
                    this.$message({
                        message: res.info,
                        type: 'warning'
                    })
                } else {
                    this.imageUrl = res.data;
                    this.$emit('get', this.imageUrl);
                }
            },
            removeImage(file, fileList) { //移除图片
                console.log(file, fileList);
                this.imageUrl = '';
                this.$emit('remove', this.imageUrl);
            }
        }
    }
</script>
<style scoped>
    .tip {
        font-size: 12px;
        color: red;
    }

    .box {
        border: thin solid #ccc;
        text-align: center;
        float: left;
        /*padding-left: 10px;*/
    }

    .box img {
        width: 100%;
        height: 100%;
    }

    .avatar-uploader {
        margin-left: 10px;
    }
</style>
<style>
    .avatar-uploader .el-upload-list {
        display: none;
    }
</style>
