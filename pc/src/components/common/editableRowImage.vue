<template>
    <div class="editableRow">
        <div class="normal-text label" :style="`color:${labelColor};width:${labelwidth}px;`">
            <span v-if="required" class="required"></span>
            {{labelText}}</div>
        <div class="image-box" ref='imageBox' :style="`width:${boxWidth?boxWidth:imageWidth}px;height:${boxHeight?boxHeight:imageHeight}px;`" @click="openUpdateImageDialog">
            <div v-show="imageHover" class="image-cover" style="color:#fff">
                <div v-if="deleteAble" class="calcle-button" @click.stop="cancleImage">
                    <i class="el-icon-close" style="font-size:14px;"></i>
                </div>
                <i class="iconfont iconjiahao"></i>
                <div class="placeholder">重新上传</div>
            </div>
            <img v-if="imageUrl" class="image" :src="imageUrl" alt=""></img>
            <div v-else class="no-image">
                <i class="iconfont iconjiahao" style="color:#aaa"></i>
                <div class="placeholder">{{placeholder}}</div>
            </div>
        </div>
        <el-dialog v-if="dialogFlag" title="编辑图片" :visible.sync="dialogFlag" width="800px">
            <tailoredImage :imageUrl='imageUrl' :imageWidth="imageWidth" :imageHeight="imageHeight" @success="getImage" @cancleTailor="cancleTailor" />
        </el-dialog>
    </div>
</template>
<script>
/**
 * 可接收的参数参考props中的内容
 * @valueChanged 传递修改后的值，请在外部接收
 *  { attributeName: this.attributeName, imageUrl: this.imageUrl }
 * 
 * 随机图片
 * 'https://picsum.photos/80/80?random=' + (new Date()).valueOf()
 * 
 * 必须配合图片裁剪 tailoredImage 使用
 */

import tailoredImage from './tailoredImage'


export default {
    components: {
        tailoredImage
    },
    props: {
        labelColor: {
            type: String,
            default: '#999',
        },
        labelwidth: {
            type: Number,
            default: 100
        },
        attributeName: {
            type: String,
            default: 'name',
        },
        labelText: {
            type: String,
            default: '默认字段',
        },
        imageWidth: {
            type: Number,
            default: 80
        },
        imageHeight: {
            type: Number,
            default: 80
        },
        boxWidth: {
            type: Number,
            default: 0
        },
        boxHeight: {
            type: Number,
            default: 0
        },
        placeholder: {
            type: String,
            default: '提示语',
        },
        value: {
            type: String,
            default: "",
        },
        editable: {
            type: Boolean,
            default: true,
        },
        //是否允许删除图片
        deleteAble: {
            type: Boolean,
            default: true,
        },
        required: {
            type: Boolean,
            default: false,
        }
    },
    data () {
        return {
            imageUrl: '',
            imageHover: false,
            dialogFlag: false,
        };
    },
    watch: {
        value: {
            handler (val) {
                this.imageUrl = val
            },
            immediate: true
        }
    },
    computed: {},
    mounted () {
        this.$refs.imageBox.onmouseover = () => {
            // console.log(1);
            if (this.imageUrl && this.editable) {
                this.imageHover = true;
            }
        };
        this.$refs.imageBox.onmouseout = () => {
            // console.log(2);
            this.imageHover = false;
        };
    },
    methods: {
        cancleImage () {
            this.imageUrl = ''
            this.imageHover = false;
            this.$emit('valueChanged', { attributeName: this.attributeName, imageUrl: this.imageUrl })
        },
        openUpdateImageDialog () {
            // console.log('dialogOpened');
            if (this.editable) {
                this.dialogFlag = true
            }
        },
        getImage (data) {
            console.log(data);
            this.imageUrl = data
            this.$forceUpdate()
            this.dialogFlag = false
            this.$emit('valueChanged', { attributeName: this.attributeName, imageUrl: this.imageUrl })
        },
        cancleTailor () {
            this.dialogFlag = false
        },
    },

}
</script>
<style lang='scss' scoped>
.required {
    &:after {
        content: "*";
        color: red;
        position: relative;
    }
}
.editableRow {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    width: fit-content;
    align-items: flex-start;
    margin-bottom: 15px;
}
.label {
    text-align: right;
}
.image-box {
    overflow: hidden;
    margin-left: 34px;
    position: relative;
    cursor: pointer;
    .image {
        height: 100%;
        width: 100%;
    }
    .no-image {
        height: 100%;
        width: 100%;
        border: 1px dashed rgba(221, 221, 221, 1);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .iconjiahao {
            font-size: 20px;
        }
        .placeholder {
            @extend .sub-text;
            color: #aaa;
            margin-top: 15px;
        }
    }
    .image-cover {
        @extend .no-image;
        position: absolute;
        border: none;
        background: rgba(0, 0, 0, 0.3);
        .calcle-button {
            height: 20px;
            width: 20px;
            position: absolute;
            right: 10px;
            top: 10px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.6);

            display: flex;
            i {
                margin: auto;
                transform: scale(0.7);
            }
        }
    }
}
</style>