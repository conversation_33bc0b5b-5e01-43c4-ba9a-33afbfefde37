<template>
    <div :class="'hamburger mr10 '+(isActive?'is-active':'')">
        <svg @click="toggleClick" width="16px" height="13px" viewBox="0 0 16 13" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <g id="1.0首页" transform="translate(-201.000000, -23.000000)" fill="#666666">
                    <g id="编组-3备份-2" transform="translate(200.000000, 23.000000)">
                        <path d="M6.75320024,8.99616373 L2.17345768,8.92497603 C2.11944582,8.92413646 2.07586354,8.88055418 2.07502397,8.82654232 L2.00383627,4.24679976 C2.0029779,4.19157796 2.04704817,4.14611599 2.10226997,4.14525762 C2.12932736,4.14483704 2.15540009,4.15540009 2.17453487,4.17453487 L6.82546513,8.82546513 C6.86451756,8.86451756 6.86451756,8.92783406 6.82546513,8.96688649 C6.80633036,8.98602127 6.78025763,8.99658432 6.75320024,8.99616373 Z" id="路径" transform="translate(4.500000, 6.500000) rotate(45.000000) translate(-4.500000, -6.500000) "></path>
                        <rect id="矩形" x="1" y="0" width="16" height="1"></rect>
                        <rect id="矩形备份-35" x="6" y="4" width="11" height="1"></rect>
                        <rect id="矩形备份-37" x="1" y="12" width="16" height="1"></rect>
                        <rect id="矩形备份-36" x="6" y="8" width="11" height="1"></rect>
                    </g>
                </g>
            </g>
        </svg>
    </div>
</template>

<script>
export default {
    name: 'Hamburger',
    props: {
        isActive: {
            type: Boolean,
            default: false
        },
        toggleClick: {
            type: Function,
            default: null
        }
    },
    computed: {
        iconTheme () {
            return this.$store.state.common.theme;
        }
    }
}
</script>

<style lang='scss' scoped>
.hamburger {
    transform: scaleX(-1);
    transition: 0.2s;
}

.is-active {
    transform: scaleX(1);
}
</style>
