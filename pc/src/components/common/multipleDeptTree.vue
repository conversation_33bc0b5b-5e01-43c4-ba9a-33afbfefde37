<template>
    <!--选择部门-->
    <el-popover placement="top" width="300" popper-class="dept-box" trigger="click" @show="getDeptInfo">
        <i class="pull-right el-icon-close" @click="closePopper($event)" style="cursor: pointer;position: relative;z-index:1000;"></i>
        <div v-if="expandedStatus">
            <el-tree show-checkbox :default-expanded-keys="expanded" ref="tree" class="tree" accordion :props="propsDep" :data="deptData" node-key="id" @check="setSelected" :check-strictly="true">
            </el-tree>
        </div>
        <div slot="reference">
            <div class="container el-select ">
                <span v-for="(item,index) in optionName" :key="index" class="el-tag el-tag--info el-tag--mini">
                    <span class="el-select__tags-text">{{item}}</span>
                    <i class="el-tag__close el-icon-close" @click.stop="deleteDept(index)"></i>
                </span>
            </div>
            <!--<el-input readonly="readonly" :value="optionName" class="custom-select" size="mini"></el-input>-->
        </div>
    </el-popover>
</template>

<script>
import { getDeptInfo } from '@/api/department';
import { getOperDept } from '@/api/rank';
export default {
    name: "dept-tree",
    props: ['optionName', 'objectId', 'businessType', 'type', 'index'],
    mounted () {
        this.getOperDept();
        this.getDeptInfo();
    },
    watch: {
        objectId () {
            this.getDeptInfo();
        }
    },
    data () {
        return {
            firmId: 10,
            propsDep: {
                label: 'deptName',
                children: 'children',
                isLeaf: true
            },
            expanded: [],
            expandedStatus: false,
            deptData: [],
        }
    },
    computed: {
    },
    methods: {
        closePopper (e) {
            e.target.parentNode.style.display = 'none';
        },
        getOperDept () {
            getOperDept().then(res => {
                if (res.code == this.$successCode) {
                    this.deptData = res.data;
                }
            })
        },
        deleteDept (index) {
            this.optionName.splice(index, 1);
            this.objectId.splice(index, 1);
            this.$forceUpdate();
            this.getDeptInfo();
        },
        setSelected (item, node, self) {
            //保存选中的机构
            this.$emit('change', node.checkedKeys, this.type, this.index);
        },
        //根据id获取部门信息
        getDeptInfo () {
            this.expandedStatus = true;
            this.expanded = [];
            this.$nextTick(() => {
                if (this.expandedStatus) {
                    this.$refs.tree.setCheckedKeys(this.objectId);
                    this.expanded = this.objectId;
                }
            })
        },
    }
}
</script>

<style lang="scss" scoped>
.container {
    width: 100%;
    min-height: 28px;
    box-sizing: border-box;
    line-height: 28px;
    padding: 0 3px;
    color: #606266;
    font-size: 12px;
    border: 1px solid #dcdfe6;
}
</style>
<style>
.dept-box {
    max-height: 430px;
    overflow-y: auto;
}
.dept-box::-webkit-scrollbar {
    width: 5px;
    height: 8px;
    background-color: #f5f5f5;
    display: none;
}

.dept-box::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
    box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    background-color: #f5f5f5;
}

.dept-box::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(153, 153, 153, 0.3);
    box-shadow: inset 0 0 6px rgba(153, 153, 153, 0.3);
    background-color: #555;
}
</style>