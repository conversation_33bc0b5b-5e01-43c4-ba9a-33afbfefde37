
<template>
    <div v-loading="loading">
        <div class="tailoredImage">
            <div class="cropper-img-box">
                <!-- 裁剪区域 -->
                <div class="cropper-img" :style="`width:496px;height:360px`">
                    <img v-if="tailoredImageUrl" ref="img" id="cropperImg" :src="tailoredImageUrl" alt />
                    <template v-else>
                        <el-upload ref="upload" class="avatar-uploader" :action="uploadUrl" name="file" :on-success="uploadInitialImage" :before-upload="beforeUpload">
                            <el-button size="small" type="primary">选择图片</el-button>
                        </el-upload>
                        <div class="normal-text" :style="`color:${textColor2};margin-top:15px;`">{{`建议尺寸${imageWidth}*${imageHeight}px,大小不超过3M`}}</div>
                    </template>
                </div>
            </div>
            <!-- 预览 -->
            <div class="preview-box">
                <div id="preview-area"></div>
                <template v-if="tailoredImageUrl">
                    <p class="remark normal-text" :style="`color:${textColor2};margin-top:12px;`">当前裁剪尺寸：{{tailoredWidth}}*{{tailoredHeight}}px</p>
                    <p v-if="tailoredWidth<imageWidth" class="remark sub-text" style="color:#FF4D4F;margin-top:6px;">注：您上传的图片尺寸不符合要求，可能导致图片显示不清</p>
                    <el-upload ref="upload" class="avatar-uploader" :action="uploadUrl" name="file" :on-success="uploadInitialImage" :before-upload="beforeUpload">
                        <el-button class="re-upload" size="small" type="primary">重新上传</el-button>
                    </el-upload>
                </template>
                <div v-else class="normal-text" :style="`color:${textColor2};margin-top:55px;`">
                    预览
                </div>
            </div>
        </div>
        <div class="button-group">
            <el-button @click="cropperSure" size="small" type="primary">确定裁剪</el-button>
            <el-button @click="cancleTailor" size="small">取消</el-button>
        </div>
    </div>
</template>
<script>
import "cropperjs/dist/cropper.css";
import Cropper from "cropperjs";
import axios from "axios";
/**
 * 图片裁剪公共组件
 * 参考文档https://github.com/fengyuanchen/cropperjs
 * 需要结合element 使用
 * 
 * this.$emit("success", res.data);
 * this.$emit("cancleTailor");
 */
export default {
    props: {
        imageUrl: {
            type: String,
            default: ''
        }, //图片地址
        imageWidth: {
            type: Number,
            default: 500
        }, //裁剪尺寸
        imageHeight: {
            type: Number,
            default: 500
        },
        uploadUrl: {
            type: String,
            default: (process.env.VUE_APP_CURRENTMODE != "production" ? "http://testpro.ienmore.com:809" : "https://pro.ienmore.com" )+ "/commonFile/upload/oss_ievent_public"
        }
    },
    components: {},
    data () {
        return {
            cropper: null, //cropper实例
            type: "png",
            loading: true,
            tailoredWidth: 0,
            tailoredHeight: 0,
            Axios: {},
            tailoredImageUrl: '',
            imageFormat: ['image/jpeg', 'image/jpg', 'image/png'],
        };
    },
    computed: {},
    created () {
        if (this.imageUrl) {
            this.tailoredImageUrl = this.imageUrl
            this.init();
        } else {
            this.loading = false
        }
        this.Axios = axios.create({ baseURL: '', });
    },
    mounted () {
    },
    watch: {

    },
    methods: {
        //裁剪插件初始化
        init () {
            let _this = this
            if (!this.cropper) {
                this.$nextTick(() => {
                    //初始化cropper
                    this.cropper = new Cropper(this.$refs.img, {
                        viewMode: 0, //视图模式
                        aspectRatio: this.imageWidth / this.imageHeight, //裁剪框比例
                        preview: "#preview-area", //预览div,
                        autoCropArea: 1,
                        crop (event) {
                            _this.loading = false;
                            // console.log(event.detail);
                            _this.tailoredWidth = event.detail.width.toFixed(0)
                            _this.tailoredHeight = event.detail.height.toFixed(0)
                        },
                    });
                });
            }
        },
        //裁剪
        cropperSure () {
            if (!this.cropper) {
                this.$message.warning('请上传图片后,进行裁切操作');
                return;
            }
            this.loading = true;
            let croppedCanvas = this.cropper.getCroppedCanvas();
            if (this.type == "jpg") {
                this.type = "jpeg";
            }
            let dataurl = croppedCanvas.toDataURL("image/" + this.type);
            let filename_ = parseInt(Math.random() * 100) + "." + this.type;
            let file_ = this._dataURLtoFile(dataurl, filename_);
            this.uploadTailoredImg(file_);
        },
        //上传裁剪图片
        uploadTailoredImg (file) {
            let primary = new FormData();
            primary.append("file", file);
            this.Axios.post(this.uploadUrl, primary).then(res => {
                this.loading = false
                if (res.data.data) {
                    this.$emit("success", res.data.data);
                } else {
                    console.log('图片上传失败');
                }
            });
        },

        //dataURL转file
        _dataURLtoFile (dataurl, filename) {
            var arr = dataurl.split(","),
                mime = arr[0].match(/:(.*?);/)[1],
                bstr = atob(arr[1]),
                n = bstr.length,
                u8arr = new Uint8Array(n);
            while (n--) {
                u8arr[n] = bstr.charCodeAt(n);
            }
            return new File([u8arr], filename, { type: mime });
        },
        cancleTailor () {
            this.$emit("cancleTailor");
        },
        beforeUpload (file) {
            const isLt3M = file.size / 1024 / 1024 < 3;
            const isFormat = this.imageFormat.includes(file.type);
            if (!isFormat) {
                this.$message.error('上传图片只能是 JPG 格式 和 PNG 格式!');
            }
            if (!isLt3M) {
                this.$message.error("文件 " + file.name + " 太大，不能超过 3M。");
            }
            return isFormat && isLt3M && (this.loading = true)
        },
        uploadInitialImage (res, file) {
            if (res.code == '002') {
                this.$message({
                    message: res.info,
                    type: 'warning'
                })
            } else {
                this.tailoredImageUrl = res.data;
                if (this.cropper) {
                    this.cropper.replace(this.tailoredImageUrl);
                } else {
                    this.init();
                }
            }
        },
    }
};
</script>

<style scoped lang="scss">
.tailoredImage {
    display: flex;
    justify-content: space-between;
}
.cropper-img-box {
    overflow: hidden;
}
.cropper-img {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background: rgba(245, 245, 245, 1);
}
.preview-box {
    position: relative;
    width: 244px;
    height: 360px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(245, 245, 245, 1);
    #preview-area {
        // min-width: 100px;
        // min-height: 100px;
        // max-width: 200px;
        // max-height: 200px;
        margin-top: 50px;
        border: 2px solid rgba(255, 255, 255, 1);
        width: 204px;
        height: 150px;
        overflow: hidden;
    }
}
.remark {
    width: 204px;
}
.re-upload {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
}
.button-group {
    display: flex;
    margin-top: 30px;
    justify-content: center;
}
</style>

<style lang="scss">
.cropper-point {
    background-color: $enmore-color;
}
.cropper-line {
    background-color: $enmore-color;
}
.cropper-view-box {
    display: block;
    height: 100%;
    outline: 1px solid $enmore-color;
    outline-color: $enmore-color;
    overflow: hidden;
    width: 100%;
}
.tailoredImage {
    .avatar-uploader .el-upload-list {
        display: none;
    }
}
</style>