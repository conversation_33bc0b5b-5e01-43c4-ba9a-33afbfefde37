<template>
    <div class="breadcrumb" :key="$route.fullPath">
        <el-breadcrumb>
            <el-breadcrumb-item v-for="(item,index) in levelList" :key="index">
                <span @mouseover="mouseOver(index)" @mouseleave="mouseLeave(index)" :class="['breadcrumb-text','normal-text',{'special':index == levelList.length-1}]" @click="jump(item,index)" style=" cursor:pointer;">{{item.name}}</span>
            </el-breadcrumb-item>
        </el-breadcrumb>
    </div>
</template>

<script>
import iframeConfigUrl from '@/config/iframeUrl';
import { getMenu } from '@/api/nav'
export default {
    name: "bread-crumb",
    mounted () {
        // 接收从子级传递过来的数据
        this.$root.$on('iframeMessage', (data) => {
            let flag = false; // 默认是不进行包含的
            for (let i = 0; i < this.levelList.length; i++) {
                if (this.levelList[i].name == data.name && this.levelList[i].path.split('/')[1] == data.path.split('/')[1]) {
                    flag = true;
                }
            }
            if (flag) {
                let index = this.levelList.findIndex((item) => {
                    return item.name == data.name && item.path.split('/')[1] == data.path.split('/')[1];
                })
								// 删除者一个
                if (data.editType&&data.editType == 'removeOne') {
                  this.levelList.splice(index, 1);
                } else {
                  // 删除当前后面所有的
                  this.levelList.splice(index+1);
                }
            } else {
               if(data.path !='/'){
                    this.levelList.push(data);
               }
               
            }
            sessionStorage.setItem('breadCrumbData', JSON.stringify(this.levelList));
        })
        // 这个进行判断  判断是从缓存读取 还是通过路由的信息进行读取
        // if (sessionStorage.getItem('breadCrumbData')) {
        //     console.log('bbbbb',sessionStorage.getItem('breadCrumbData'))
        //     // 这里进行缓存的读取
        //     this.levelList = JSON.parse(sessionStorage.getItem('breadCrumbData'));
        // } else {
        //     this.getBreadcrumb();
        // }
            this.getBreadcrumb();
    },
    watch: {
        $route () {
            setTimeout(() => {
                this.getBreadcrumb();
            }, 300)
        }
    },
    data () {
        return {
            levelList: [],
            leftMenu: [],
        }
    },
    methods: {
       async getBreadcrumb () {
            // 这里要进行区分本地和iframe框架
             let {data} = await getMenu(0,sessionStorage.getItem('platform'));
             this.leftMenu = this.leftMenu.concat(data);
             // 路由名字规划
            this.levelList = [];
            if (iframeConfigUrl.localRouter.includes(this.$route.name)) {
                let matched = this.$route.matched.filter(item => item.meta.title);
                for (let item of matched) {
                    if(item.path !='/'){
                        this.levelList.push({ name: item.meta.title, path: item.path });
                    }
                    
                }
            }else{
                this.levelList[0] = { name: this.calcRouterName(this.$route.path,0), path: '' };
                if(this.$route.path!='/'){
                    this.levelList.push({ name: this.calcRouterName(this.$route.path,1), path: this.$route.path });
                }
            }
            sessionStorage.setItem('breadCrumbData', JSON.stringify(this.levelList));
        },
        calcRouterName(path,index){
            let routerName;
            for(let i = 0;i<this.leftMenu.length;i++){
                let item = this.leftMenu[i];
                if(item.children.length){
                    item.children.forEach((subItem)=>{
                        if(path.indexOf(subItem.menuComponent)!=-1){
                            if(index){
                                routerName = subItem.menuName;
                            }else{
                                routerName = item.menuName;
                            }
                            return;
                        }
                    })   
                }else{
                    if(path=='/exhibitor'){
                         routerName = item.menuName;
                         break;
                    }else if(path=='/finance'){
                        routerName = '财务认账';
                        break;
                    }else if(path=='/authManage'){
                        routerName = '授权管理';
                        break;
                    }
                }
            }
            return  routerName;
        },
        jump (item, index) {
            if (index > 0 && index < this.levelList.length - 1) {
                if (iframeConfigUrl.localRouter.includes(this.routerNames)) {
                    this.$router.push(item.path);
                } else {
                    document.getElementById("frontIframe").contentWindow.$childVue.$root.$emit('childMessageRoute', item);
                }
            } else {
                // 看看是否是本地的文件
                if (iframeConfigUrl.localRouter.includes(this.routerNames)) {
                    this.$emit('refreshRouterview');
                }
            }
        },
        mouseOver (index) {
            if (index > 0 && index < this.levelList.length - 1) {
                let el = document.getElementsByClassName('breadcrumb-text');
                // 进行颜色的变化
                for (let i = 1; i < this.levelList.length - 1; i++) {
                    el[i].classList.remove("active");
                }
                el[index].classList.add("active");
            }
        },

        mouseLeave (index) {
            if (index > 0 && index < this.levelList.length - 1) {
                let el = document.getElementsByClassName('breadcrumb-text');
                for (let i = 1; i < this.levelList.length - 1; i++) {
                    el[i].classList.remove("active");
                }
            }
        }
    }
}
</script>

<style lang='scss' scoped>
.breadcrumb-text {
    color: $text-color-1;
}
.special {
    color: $text-color-0;
    font-weight: 500;
}
.active {
    color: var(--color-primary);
}
</style>

<style lang='scss'>
.el-breadcrumb__separator {
    @extend .normal-text;
    margin: 0 5px;
    font-weight: normal;
    color: $text-color-0;
    font-size: 18px;
}
</style>