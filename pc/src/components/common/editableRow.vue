<template>
    <div class="editableRow">
        <div class="normal-text label" :style="`color:${labelColor};width:${labelwidth}px;`">
            <span v-if="required" class="required"></span>
            {{labelText}}</div>
        <div class="content-area" :style="`width:${inputwidth}px`">
            <el-input v-if="editflag" size="small" type="text" v-model="inputValue" :placeholder="placeholder"></el-input>
            <div v-else class="normal-text single-line" :style="`color:${textColor0};`">{{inputValue}}</div>
        </div>
        <div class="edit-area" v-if="editable">
            <el-link v-if="!editflag" @click="editflag=true">修改</el-link>
            <div v-else>
                <el-button size="small" type="primary" @click="saveButtonClick">保存</el-button>
                <el-button size="small" @click="editflag=false, inputValue = value">取消</el-button>
            </div>
        </div>
    </div>
</template>
<script>
/**
 * 可接收的参数参考props中的内容
 * @valueChanged 传递修改后的值，请在外部接收
 * { attributeName: this.attributeName, inputValue: this.inputValue }
 */
export default {
    components: {},
    props: {
        labelColor: {
            type: String,
            default: '#999',
        },
        labelwidth: {
            type: Number,
            default: 100
        },
        attributeName: {
            type: String,
            default: 'name',
        },
        labelText: {
            type: String,
            default: '默认字段',
        },
        inputwidth: {
            type: Number,
            default: 220
        },
        placeholder: {
            type: String,
            default: '请输入',
        },
        value: {
            type: String,
            default: '默认值',
        },
        editable: {
            type: Boolean,
            default: true,
        },
        required: {
            type: Boolean,
            default: false,
        }
    },
    data () {
        return {
            inputValue: '',
            editflag: false,
        };
    },
    watch: {
        value: {
            handler (val) {
                this.inputValue = val
            },
            immediate: true
        }
    },
    computed: {},
    mounted () {

    },
    methods: {
        saveButtonClick () {
            if (this.required == true && !this.inputValue) {
                this.$message.warning(`${this.labelText}不能为空`)
                return
            }
            this.editflag = false
            this.$emit('valueChanged', { attributeName: this.attributeName, inputValue: this.inputValue })
        }
    }
}
</script>
<style lang='scss' scoped>
.required {
    &:after {
        content: "*";
        color: red;
        position: relative;
    }
}
.editableRow {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    width: fit-content;
    align-items: center;
    margin-bottom: 15px;
    height: 32px;
}
.label {
    text-align: right;
}
.content-area {
    margin-left: 34px;
}
.edit-area {
    width: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>

<style lang='scss'>
.editableRow {
    .el-link--inner {
        font-size: 14px;
        color: #1890ff;
    }
    .el-link.el-link--default:after {
        border-color: #1890ff;
    }
}
</style>
