<!--  -->
<template>
    <div class='pieWrap flex-center flex-middle flex-vertical'>
        <div class="flex-left system-content" v-if="isShowSystem">
            <div v-for="item,index in systemData" class="flex-vertical flex-center system-item" @click="linkUrl(item)">
                <div class="system-logo flex-center flex-middle">
                    <img :src="item.menuIcon" alt="">
                </div>
                <div class="system-name">{{item.menuName}}</div>
            </div>
        </div>
        <div class="pieCircle flex-center flex-middle" @click="expandSystem">
            <i class="el-icon-arrow-up icon"></i>
        </div>
    </div>
</template>

<script>
import constant from '@/config/config_constant';
export default {
    name: '',
    props:['systemData'],
    data () {
        return {
            isShowSystem:false
        };
    },

    created () { },

    components: {},

    computed: {},

    mounted () { },

    methods: {
        expandSystem(){
            this.isShowSystem = !this.isShowSystem;
        },
        linkUrl (item) {
             if(item.url.indexOf('http')!=-1){
                 window.location.href = item.url + '?token=' + localStorage.getItem(constant.token);
            }else{
             
                window.location.href = window.location.href + item.url;
            }
        },
    }
}

</script>
<style  scoped>
.pieWrap {
    position: absolute;
    bottom: 20px;
    z-index: 100;
    width: 100%;
}
.icon {
    font-size: 16px;
    color: #fff;
}
.pieCircle {
    width: 96px;
    height: 30px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 0px 0px 7px 7px;
    cursor: pointer;
}
.system-item{
    cursor: pointer;
}
.system-content {
    height: 170px;
    background: #f1f1f1;
    border-radius: 0px 0px 10px 10px;
    padding-right: 26px;
}
.system-logo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: #fff;
    margin-left: 26px;
    margin-top: 20px;
}
.system-logo img {
    height: 20px;
}
.system-name {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    line-height: 20px;
    margin-top: 10px;
    margin-left: 26px;
}
</style>