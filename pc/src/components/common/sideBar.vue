<template>
  <div id="navBar" :style="navWidth" class="flex-vertical">
    <div v-show="!isCollapse" class="navBar-logo">
      <img :src="sideBarLogo.managerLogo" alt="" style="width: 140px" />
    </div>
    <div :class="['unit', { platformMenu: platformInfoData == 3 }]" style="overflow-y: scroll">
      <el-menu class="el-menu-vertical-side" :default-active="defaultActive" :default-openeds="openedSub" unique-opened :collapse="isCollapse" :background-color="subColor" text-color="#BBBBBB" active-text-color="#409EFF">
        <template v-for="item in leftMenu">
          <el-submenu v-if="item.children && item.children.length > 0" :index="item.id + ''" :key="item.id">
            <template slot="title">
              <i style="width: 18px" :class="'iconfont ' + item.menuIcon"></i>
              <span style="padding-left: 8px">{{ item.menuName }}</span>
            </template>
            <el-menu-item v-for="child in item.children" :index="child.menuComponent" @click="go(child.menuComponent, child.id, true)" :key="child.id" :data-id="child.id" :data-name="child.menuComponent">{{ child.menuName }}</el-menu-item>
          </el-submenu>
          <el-menu-item v-if="item.children && item.children.length == 0" :index="item.menuComponent" @click="go(item.menuComponent, item.id, true)" :key="item.id" :data-id="item.id" :data-name="item.menuComponent">
            <i style="width: 18px" :class="'iconfont ' + item.menuIcon"></i>
            <span style="padding-left: 8px" slot="title">{{ item.menuName }}</span>
          </el-menu-item>
        </template>
      </el-menu>
    </div>
  </div>
</template>

<script>
import { getMenu } from '@/api/nav'
import getBtnId from '@/api/getBtnId'
import iframeUrl from '../../config/iframeUrl'
export default {
  name: 'side-bar',
  mounted() {
    let vm = this
    //响应父组件的菜单切换
    window.addEventListener('message', (ev) => {
      try {
        let params = JSON.parse(ev.data)
        if (typeof params == 'object' && 'name' in params) {
          vm.changeRouter(params)
        }
      } catch (e) {}
    })
  },
  data() {
    return {
      openedSub: [],
      leftMenu: [],
      sideBarLogo: {},
    }
  },
  watch: {},
  created() {
    try {
      this.getMenuData()
      this.sideBarLogo = JSON.parse(sessionStorage.getItem('sysInfo')) || {}
    } catch (e) {}
  },
  computed: {
    isCollapse() {
      return !this.$store.state.sideBar.opened
    },
    navWidth() {
      let width = this.$store.state.sideBar.opened ? 180 : 60
      return { width: width + 'px !important' }
    },
    defaultActive() {
      if (this.$route.path.indexOf('platformManage') == -1) {
        sessionStorage.setItem('defaultActive', this.$route.path.replace(/\//g, ''))
        return this.$route.path.replace(/\//g, '')
      } else {
        return this.$route.path.replace(/\//, '')
      }
    },
  },
  methods: {
    handleMenu() {
      let currentName = this.$route.path.replace('/', '')
      let openedIndex = ''
      for (var i = 0; i < this.leftMenu.length; i++) {
        for (var j = 0; j < this.leftMenu[i].children.length; j++) {
          if (this.leftMenu[i].children[j].menuComponent == currentName) {
            openedIndex = this.leftMenu[i].children[j].parentId + ''
          }
        }
      }
      this.openedSub.push(openedIndex)
      this.$nextTick(() => {
        setTimeout(() => {
          this.$emit('updateIsRouter')
        }, 500)
      })
    },
    changeRouter(params) {
      let { name, id } = params
      this.$store.commit('changeRouterQuery', id)
      this.$router.push({ path: `/${name}`, query: { id } })
    },
    go(name, id, isClick) {
      if (id) {
        localStorage.setItem('isInRouter', id)
      }
      if (isClick) {
        this.$store.commit('changeRefresh', true)
      }
      if (this.routerNames == name && !iframeUrl.localRouter.includes(name)) {
        try {
          document.getElementById('frontIframe').contentWindow.postMessage({ refresh: true, name }, this.$store.state.iframeUrl)
        } catch (e) {}
      }
      this.getId(id)
      // 这里要做路由跳转判断
      if (iframeUrl.localRouter.includes(name)) {
        this.$router.push({ name })
      } else {
        this.$store.commit('changeRouterQuery', '')
        this.$router.push({ path: `/${name}` })
      }
    },
    getId(id) {
      if (id) {
        getBtnId({ mid: id }).then((res) => {
          this.$store.commit('btnPowers', res.data)
        })
      }
    },
    getMenuData() {
      getMenu(0, sessionStorage.getItem('platform')).then((res) => {
        if (res.data.length) {
          this.leftMenu = this.leftMenu.concat(res.data)
        }
        this.handleMenu()
      })
    },
  },
}
</script>

<style scoped lang="scss">
.is-opened {
  > :nth-child(1) {
    color: #fff !important;
    i {
      color: #fff !important;
    }
  }
}
#navBar {
  height: 100vh;
  flex-shrink: 0;
  transition: width 0.3s;
  z-index: 1001;
  overflow: hidden;
  background-color: $sub-color;
  .el-submenu .el-menu-item {
    min-width: 170px;
  }
  .el-icon-menu {
    font-size: 16px;
  }
  .el-menu--collapse {
    width: 60px;
  }
}
.navBar-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  margin-top: 18px;
  margin-bottom: 13px;
}
.platform-area {
  span {
    color: var(--color-primary);
  }
  padding-bottom: 8px;
  border-bottom: 1px solid black;
}
::-webkit-scrollbar {
  width: 0;
  height: 0;
}
</style>

<style lang="scss">
#navBar {
  .el-dropdown {
    span {
      font-size: 14px;
    }
  }
  .el-menu-item.is-active {
    background-color: black !important;
    border-right: solid 3px var(--color-primary) !important;
    color: $text-color-light !important;
  }

  .el-menu-vertical-side {
    span {
      font-size: 14px !important;
    }
    li {
      font-size: 14px !important;
    }
    i:nth-child(1) {
      height: 18px;
      width: 18px;
      font-size: 18px;
    }
  }

  .platformMenu .el-menu-item.is-active {
    background-color: black !important;
    border-right: solid 3px var(--color-primary) !important;
    color: $text-color-light !important;
  }
}
.el-dropdown-menu__item {
  padding: 0 10px !important;
}
</style>
