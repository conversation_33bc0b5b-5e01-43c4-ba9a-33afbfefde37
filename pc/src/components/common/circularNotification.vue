<template>
  <div>
    <div v-for="(item,index) in thinkTankList" class="boydSystem">
      <div class="topClass">
        <img src="../../assets/img/zhikuIcon.png" alt="">
        <div style="margin-left:6px;color:#2f7fb0;display:flex;align-items:center;">{{item.name[0]}}
          <img class="shujuIcon" src="../../assets/img/shujuIcon.png" alt="">{{item.name[1]}}
        </div>
      </div>
      <div @click="currentBtn(value)" v-for="(value,current) in item.value" @mouseout="itemBadgeOutBtn" @mouseover="itemBadgeBtn(index,current)" :style="itemBadgeIndex==index&&currentIndex==current ? 'background:#EEEEEE;border-radius: 6px;cursor: pointer;':'cursor: pointer;'" class="bodyModulClass">
        <span style="padding-left:7px;cursor: pointer;">{{value.name}}</span>
        <el-badge :value="value.num" class="itemBadge"></el-badge>
      </div>
    </div>
  </div>
</template>

<script>
import {getInfoByUserId} from '../../api/circularNotification'
export default {
  name:'circularNotification',
  props: {
    systemType:{
      type:String,
    },
    currentLoginId:{
      type:Number,
    },
    sysCode:{
      type:String,
    },
    manageBizTokenId:{
      type:String,
    }
  },
    data() {
      return{
        itemBadgeIndex:null,
        currentIndex:null,
        thinkTankList:[],
      }
    },
    mounted(){
      this.getInfoByUserId();
    },
    created () {},
    methods:{
      getInfoByUserId(){
        let params={
          userId:this.currentLoginId,
          sysCode:this.sysCode,
        }
        getInfoByUserId(params).then(rs=>{
          Object.keys(rs.data).forEach(key=>{
            this.thinkTankList.push({
              name:key,
              value:rs.data[key]
            });
          });
          this.thinkTankList.forEach(item=>{
            item.name=item.name.split('-');
            item.name.forEach((val,key)=>{
              switch (val) {
                case 'thinktank':
                  item.name[key]='智库';
                  break;
                case 'crm':
                  item.name[key]='CRM';
                  break;
                case 'finance':
                  item.name[key]='认账';
                  break;
              };
            })

          })
        });
      },
      currentBtn(val){
        window.open(val.url, "_blank");
      },
      //移入
      itemBadgeBtn(index,current){
        this.itemBadgeIndex=index;
        this.currentIndex=current;
      },
      //移出
      itemBadgeOutBtn(){
        this.itemBadgeIndex=null;
        this.currentIndex=null;
      },
    },
}
</script>

<style scoped>
.shujuIcon{
  height: 10px;
  margin-left: 3px;
  margin-right: 3px;
}
.boydSystem{
  background: #f8f9fa;
  padding: 10px;
  margin-top: 12px;
}
.itemBadge{
  margin-left: 25px;
  padding-right:7px;
  display: flex;
  align-items: center;
}
.bodyModulClass{
  min-height: 30px;
  /* background: red; */
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 5px;
}
.topClass{
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.breathing_lamp {
  position: fixed;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: orange;
  line-height: 40px;
  text-align: center;
  color: #fff;
  cursor: pointer;
}
.betweenClass{
  width: 23px;
  height: 23px;
  background-color: orange;
  border-radius: 50%;
  margin-left: 6px;
}
</style>