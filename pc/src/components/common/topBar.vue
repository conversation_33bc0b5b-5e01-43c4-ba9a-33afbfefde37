<template>
  <div id="topBar">
    <div class="header">
      <hamburger :toggle-click="toggleSideBar" :is-active="sideBar.opened"></hamburger>
      <div class="slot">
        <slot></slot>
      </div>
      <!-- 呼叫中心 -->
      <call-center :class="{ 'call-center-opacity': callCenterOpacity }" @callCenterRole="callCenterRole" v-if="callCenterPower" ref="callCenter" :userInfo="userInfo" phoneSysCode="live" :platformId="callCenterId"></call-center>
      <div class="pull-right flex-middle">
        <el-dropdown @command="handleCommand">
          <span class="el-dropdown-link">
            <span @mouseover="userElButtonBtn" class="normal-text mr10">
              <el-badge :hidden="workListNum == 0 ? true : false" :value="workListNum" class="itemBadgeClass">
                {{ userInfo.userName }}
              </el-badge>
            </span>
            <i class="el-icon-caret-bottom"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <p class="pointNum">
              可用点数： <span>{{ pointNum }}</span>
            </p>
            <div class="menu_title">
              <el-dropdown-item command="userCenter" v-if="loginWayList.includes('1')">修改密码</el-dropdown-item>
              <div v-if="circularNotificationType">
                <el-popover @show="circularNotificationShowBtn" @hide="circularNotificationHideBtn" popper-class="topBarPopoverClass" :visible-arrow="false" placement="left" width="450" trigger="hover">
                  <circularNotification :sysCode="sysCodeId" :currentLoginId="currentLoginId.id" v-if="circularNotificationShow" :systemType="systemType" class="content"></circularNotification>
                  <el-dropdown-item slot="reference">
                    <el-badge :value="workListNum" class="badgeItemClass"> 待办工作 </el-badge>
                    <i style="margin-left: 39px" class="el-icon-arrow-right"></i
                  ></el-dropdown-item>
                </el-popover>
              </div>
              <el-dropdown-item command="loginOut">退出</el-dropdown-item>
            </div>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="borderClass"></div>
      <pieMenuNew></pieMenuNew>
    </div>
    <el-dialog title="" :visible.sync="passwordDialog" width="500px" center :close-on-click-modal="false" custom-class="validatePhPaDialog">
      <validatePhPa :isModify="true" @quitLogin="cancel" v-if="passwordDialog"></validatePhPa>
    </el-dialog>
  </div>
</template>

<script>
import { getInfoByUserId } from '../../api/circularNotification'
import circularNotification from './circularNotification.vue'
import { getUserInfo } from '@/api/login'
import { mapState, mapMutations } from 'vuex'
import hamburger from '@/components/common/hamburger'
import { changePwd } from '@/api/nav'
import { getUnNeedPoint } from '@/api/point'
import { validatePhPa } from '@enmore/enmore_common_validate_front'
import pieMenuNew from '@enmore/enmore-common-front/src/baseComponents/pieMenuNew.vue'
import { callCenter } from '@enmore/enmore-common-front'
export default {
  name: 'top_bar',
  components: {
    hamburger,
    validatePhPa,
    circularNotification,
    pieMenuNew,
    callCenter,
  },
  mounted() {
    this.getUserInfo()
    this.getUnNeedPoint()
    // 监听呼叫中心的状态
    this.$root.$on('emitCallCenter401', () => {
      if (this.$refs.callCenter) {
        this.$refs.callCenter.getCallCenterExtensionState().then((code) => {
          if (code == 0 || code == -1) {
            this.$root.$emit('emitCallCenterLoginOut', () => {
              this.logout()
            })
          } else {
            this.logout()
          }
        })
      } else {
        this.logout()
      }
    })
  },
  data() {
    var validatePassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        if (this.modeForm.newPassword !== '') {
          this.$refs.modeForm.validateField('newPasswd')
        }
        callback()
      }
    }
    var validatePasswd = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.modeForm.newPassword) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      workListNum: null, //待办工作显示总数
      sysCodeId: '',
      currentLoginId: {}, //获取当前登录人信息
      systemType: '',
      circularNotificationShow: false, //待办工作组件状态
      circularNotificationType: false, //待办工作按钮状态
      pointNum: 0,
      color: '#409EFF',
      modeForm: {
        oldPassword: '',
        newPassword: '',
        newPasswd: '',
      },
      rules: {
        oldPassword: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
        newPassword: [{ required: true, validator: validatePassword, trigger: 'blur' }],
        newPasswd: [{ required: true, validator: validatePasswd, trigger: 'blur' }],
      },
      passwordDialog: false,
      callCenterId: sessionStorage.getItem('platform'),
    }
  },
  watch: {
    showChangePasswordDialog(val) {
      this.passwordDialog = val
    },
    passwordDialog(val) {
      this.$store.state.topBar.showChangePasswordDialog = val
    },
    updatePointIndex() {
      this.getUnNeedPoint()
    },
  },
  computed: mapState({
    userInfo: (state) => state.loginModule.userInfo,
    sideBar: (state) => state.sideBar,
    iconTheme: (state) => state.common.theme,
    showChangePasswordDialog: (state) => state.topBar.showChangePasswordDialog,
    updatePointIndex: (state) => state.point.updatePointIndex,
    callCenterPower: (state) => state.callCenterPower,
    callCenterOpacity: (state) => state.callCenterOpacity,
  }),
  inject: ['removeContainerToken'],
  methods: {
    ...mapMutations({
      changeLogout: 'logout',
      toggleSideBar: 'toggleSideBar',
      setUserInfo: 'setUserInfo',
      changePassword: 'changePassword',
    }),
    logout() {
      this.changeLogout()
      this.removeContainerToken()
      localStorage.removeItem('callToken')
      setTimeout(() => {
        this.$router.replace('/login')
      }, 1500)
    },
    getUserInfo() {
      getUserInfo().then((res) => {
        if (res.code == this.$successCode) {
          this.currentLoginId = res.data
          this.setUserInfo(res.data)
          this.userElButtonBtn()
          this.$store.commit('changeCallCenterPower', true)
        } else {
          if (res.info == '用户已被禁用') {
            this.$message.error('用户已被禁用')
            this.logout()
          }
        }
      })
    },
    //打开待办工作
    circularNotificationShowBtn() {
      this.circularNotificationShow = true
    },
    //待办隐藏
    circularNotificationHideBtn() {
      this.circularNotificationShow = false
    },
    //查询判断待办工作是否有数据没有则不显示待办工作按钮
    userElButtonBtn() {
      let params = {
        userId: this.currentLoginId.id,
      }
      getInfoByUserId(params).then((rs) => {
        if (rs.code == this.$successCode) {
          this.workListNum = 0
          Object.keys(rs.data).forEach((key) => {
            rs.data[key].forEach((item, index) => {
              this.workListNum = this.workListNum + item.num
            })
          })
          if (JSON.stringify(rs.data) !== '{}') {
            this.circularNotificationType = true
          } else {
            this.circularNotificationType = false
          }
        }
      })
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          changePwd(this.modeForm).then((res) => {
            if (res.code == this.$successCode) {
              this.$message.success('密码修改成功,请重新登录')
              this.$store.state.topBar.showChangePasswordDialog = false
              this.logout()
            }
          })
        } else {
          return false
        }
      })
    },
    cancel() {
      this.$store.state.topBar.showChangePasswordDialog = false
    },
    changePassword() {
      this.$store.state.topBar.showChangePasswordDialog = true
    },
    handleCommand(command) {
      console.log(command)
      switch (command) {
        case 'userCenter':
          this.passwordDialog = true
          break
        case 'loginOut':
          if (this.callCenterPower) {
            // 判断当前处于的呼叫中心的状态  如果是空闲 或者离线的状态就可以去调用 呼叫中心的退出
            this.$refs.callCenter.getCallCenterExtensionState().then((code) => {
              console.log('code', code)
              if (code == 0 || code == -1) {
                this.$root.$emit('emitCallCenterLoginOut', () => {
                  this.logout()
                })
              } else {
                this.logout()
              }
            })
          } else {
            this.logout()
          }
          break
      }
    },
    helpCenter() {
      let helpCenter = this.$router.resolve({
        name: 'helpCenter',
        params: this.$route.params,
      })
      window.open(helpCenter.href, '_blank')
    },
    // 当前登录人所在公司或者部门的可用点数
    getUnNeedPoint() {
      getUnNeedPoint({}).then((res) => {
        if (res.code == this.$successCode) {
          this.pointNum = res.data ? res.data : 0
        }
      })
    },
    callCenterRole(payload) {
      this.$store.commit('changeCallCenterPower', payload)
      this.$store.commit('changeCallCenterOpacity', !payload)
    },
  },
}
</script>

<style scoped>
.borderClass {
  border: 1px solid #dddddd;
  width: 1px;
  height: 24px;
  margin-left: 11px;
  margin-right: 15px;
}
.pointNum {
  font-size: 12px;
  padding: 0 10px;
  color: #666;
  line-height: 30px;
}
.pointNum span {
  color: var(--color-primary);
  font-weight: 500;
}
.header {
  height: 56px;
  background: #fff;
  padding: 15px 20px;
  border-bottom: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.helpCenter {
  font-size: 14px;
  color: #666666;
  margin-right: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
}
.helpCenter i {
  font-size: 17px;
  color: #999;
  margin-right: 6px;
}
.slot {
  flex: 1;
}
.call-center-opacity {
  opacity: 0;
}
</style>
<style lang="scss">
.itemBadgeClass {
  .el-badge__content {
    top: -2px !important;
    right: 5px !important;
  }
}
.badgeItemClass {
  .el-badge__content {
    top: 3px !important;
    right: 3px !important;
    display: flex;
    align-items: center;
  }
}
.topBarPopoverClass {
  top: 50px !important;
}
.el-menu--popup {
  min-width: 160px;
}
.menu_title {
  display: flex;
  flex-direction: column;
  .el-dropdown-menu__item {
  }
}
.validatePhPaDialog {
  .el-dialog__body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .el-dialog__header {
    border-bottom: 0px !important;
  }
  .el-form-item {
    margin-top: 40px;
  }
  .el-form-item__label {
    font-size: 14px;
    padding-right: 20px;
  }
  .el-form-item__content {
  }
  .dialog-title {
    font-size: 24px;
    color: rgba(51, 51, 51, 1);
    line-height: 33px;
  }
}
</style>
