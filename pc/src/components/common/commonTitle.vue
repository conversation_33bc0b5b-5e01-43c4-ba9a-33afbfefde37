<!-- 公共的头部标题样式 -->
<template>
  <div class='commonTitle flex-left flex-middle'>
      <div class="line"></div>
      <div class="title">{{title}}</div>
  </div>
</template>

<script>
export default {
  name: 'commonTitle' ,
  props:['title'],
  data () {
    return {
    };
  },

  created(){},

  components: {},

  computed: {},

  mounted(){},

  methods: {}
}

</script>
<style  scoped lang="scss">
.commonTitle{
    height: 46px;
    color:$text-color-0;
    font-size: 15px;
    font-weight: 500;
}
.line{
    width: 3px;
    background: $--color-primary;
    height: 20px;
}
.title{
    margin-left: 10px;
}
</style>