{"name": "enmore", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "state": "vue-cli-service serve --mode state", "build": "vue-cli-service build", "test": "vue-cli-service build --mode test"}, "dependencies": {"@enmore/enmore-common-front": "git+ssh://****************:dtid_a56564cecace225e/enmore-common-front.git#release_live", "@enmore/enmore_common_validate_front": "git+ssh://****************:dtid_a56564cecace225e/enmore_common_validate_front/enmore_common_validate_front.git#master", "axios": "^0.18.0", "ckeditor": "^4.12.1", "copperjs": "^1.0.1", "core-js": "2.6.5", "cropperjs": "^1.5.6", "echarts": "^4.2.0-rc.2", "element-ui": "^2.13.0", "jsdom": "^12.2.0", "location": "0.0.1", "moment": "^2.22.2", "navigator": "^1.0.1", "nprogress": "^0.2.0", "sass": "^1.13.0", "spark-md5": "^3.0.1", "store": "^2.0.12", "vue": "^2.5.17", "vue-router": "^3.0.1", "vuedraggable": "^2.23.2", "vuex": "^3.0.1", "wangeditor": "^3.1.1", "xmlhttprequest": "^1.8.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.0.0", "@vue/cli-service": "^3.0.0", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.12.2", "compression-webpack-plugin": "^3.0.1", "image-webpack-loader": "^6.0.0", "node-sass": "^4.9.0", "sass-loader": "^7.0.1", "vue-template-compiler": "^2.5.17", "webpack-aliyun-oss": "^0.3.13"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}