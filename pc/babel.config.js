var commonNameMap = {
  formSet: 'components/formNew/formSet/index.vue',
  cropperImg: 'baseComponents/cropperImg.vue',
  eleTheme: 'baseComponents/eleTheme.vue',
  pieMenu: 'baseComponents/pieMenu.vue',
  pieMenuNew: 'baseComponents/pieMenuNew.vue',
  communicationIframe: 'components/iframe',
  globalCopy: 'baseComponents/globalCopy.vue',
  callCenter:'components/callCenter/index.vue'
};

module.exports = {
  presets: [
    '@vue/app'
  ],
  "plugins": [
    [
      "component",
      {
        "libraryName": "element-ui",
        "styleLibraryName": 'theme-chalk'
      }
    ],
    ["import", {
      "libraryName": "@enmore/enmore-common-front",
      customName: (name) => {
        return `@enmore/enmore-common-front/src/${commonNameMap[name]}`
      },
      "camel2DashComponentName": false,

    }, 'enmore'],
  ]
}