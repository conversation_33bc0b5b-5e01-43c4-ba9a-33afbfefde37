
const path = require('path');
const getDay=function(){
    var date=new Date();
    return date.getFullYear()+''+(date.getMonth()+1)+''+date.getDate();
}
const fileName=path.basename(path.resolve(__dirname, '.')) || 'oss';
module.exports={
    from: ['./dist/**', '!./dist/**/*.html'],
    dist:`${fileName}/${getDay()}/`,
    region: 'oss-cn-shanghai',
    accessKeyId: 'LTAItiRWTzPIWKIo',
    accessKeySecret: 'RVnZGZdATOlNHNA4MGePwU3lylCpfv',
    bucket: 'ienmore',
    deletOrigin:true,
    deleteEmptyDir:true,
    setOssPath(filePath) {
        const seps=filePath.split(path.sep);
        if(seps.length>=2){
            const len=seps.length;
            const suffix=seps[len-1];
            const file=seps[len-2];
            return file+'/'+suffix;
        }
        return false;
    },
}