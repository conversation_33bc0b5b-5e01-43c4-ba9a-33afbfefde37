const CompressionWebpackPlugin = require('compression-webpack-plugin');
const SkeletonWebpackPlugin = require('vue-skeleton-webpack-plugin');
const path = require('path');
const WebpackAliyunOss = require('webpack-aliyun-oss');
const OSS = require('./AliOss');
const getDay = function () {
  var date = new Date();
  return date.getFullYear() + '' + (date.getMonth() + 1) + '' + date.getDate();
}
const fileName = path.basename(path.resolve(__dirname, '.')) || 'oss';

function resolve(dir) {
  return path.join(__dirname, dir)
}
module.exports = {
  publicPath: process.env.VUE_APP_CURRENTMODE !== 'production' ? './' : `//oss.ienmore.com/${fileName}/${getDay()}/`,
  // 将构建好的文件输出
  outputDir: process.env.outputDir,
  // 是否为生产环境构建生成 source map？
  productionSourceMap: false,
  // 添加babel转换
  transpileDependencies: [/[/\\]node_modules[/\\]_@enmore_common-data-action@0.1.0@@enmore[/\\]/,/[/\\]node_modules[/\\]enmore-common-mobile[/\\]/],
  chainWebpack: config => {
    config.resolve.alias
      .set('@', resolve('src')) // key,value自行定义，比如.set('@@', resolve('src/components'))
      .set('ievent', resolve('src/components'))
      .set('vue$', 'vue/dist/vue.esm.js')
      config.plugins.delete('preload');
      config.plugins.delete('prefetch');
      const imagesRule = config.module.rule('images');
      imagesRule.use('image-webpack-loader').loader('image-webpack-loader')
          .options({
              bypassOnDebug: true
          }).end()
  },
  configureWebpack: config => {
    config.externals = {
      'cube-ui': 'cube',
      'dplayer': 'DPlayer',
      'moment': 'moment',
      'vue': 'Vue',
      'vant': 'vant',
      'vconsole': 'VConsole',
      'vue-router': 'VueRouter'
    }
    if (process.env.VUE_APP_CURRENTMODE == 'production') {
      config.plugins.push(new WebpackAliyunOss(OSS));
    }

    config.optimization = {
      runtimeChunk: 'single',
      splitChunks: {
        chunks: 'all',
        maxInitialRequests: Infinity,
        minSize: 20000,
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name(module) {
              // get the name. E.g. node_modules/packageName/not/this/part.js 
              // or node_modules/packageName 
              const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)[1]
              // npm package names are URL-safe, but some servers don't like @ symbols 
              return `npm.${packageName.replace('@', '')}`
            }
          }
        }
      }
    }

    return {
      plugins: [new CompressionWebpackPlugin({
        test: /\.(js|css)(\?.*)?$/i, //需要压缩的文件正则
        threshold: 10240, //文件大小大于这个值时启用压缩
        deleteOriginalAssets: false //压缩后保留原文件
      }), new SkeletonWebpackPlugin({
        webpackConfig: {
          entry: {
            app: path.join(__dirname, './src/components/entry-skeleton.js'),
          },

        },
        minimize: true,
        quiet: true,
      })]
    };
  },
  // 配置 webpack-dev-server 行为。
  devServer: {
    port: 8888,
    https: false,
    hotOnly: false,
    open: true,
    disableHostCheck: true,
  },

  css: {
    loaderOptions: {
      stylus: {
        'resolve url': true,
        'import': [
          './src/theme'
        ]
      }
    }
  }
}