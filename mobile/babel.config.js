const commonNameMap ={
  loginPage:'views/loginPage/index.vue',
  liveForm:'views/liveForm/form.vue',
  loginStore:'store/loginStore.js',
  medicineIndustry:'views/loginPage/medicineIndustry',
  carIndustry:'views/loginPage/carIndustry',
  beforeRouterLogic:'router/router',
  globalMins:'mixins/index'
}

module.exports = {
  presets: [
    '@vue/app'
  ],
  plugins: [
    ['import', {
      libraryName: 'vant',
      libraryDirectory: 'es',
      style: true
    }, 'vant'],
    [
      "transform-modules", {
        "cube-ui": {
          "transform": "cube-ui/lib/${member}",
          "kebabCase": true,
        }
      }
    ],
    ["import", {
      "libraryName": "enmore_common_mobile",
      customName: (name) => {
        return `enmore_common_mobile/common/${commonNameMap[name]}`
      },
      "camel2DashComponentName": false,
    },'enmore'],
  ]
}