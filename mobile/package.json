{"name": "base", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test": "vue-cli-service build --mode test", "testPreview": "vue-cli-service build --mode testPreview", "buildPreview": "vue-cli-service build --mode productionPreview", "developmentPreview": "vue-cli-service serve --mode developmentPreview", "dev": "npm run serve"}, "dependencies": {"@enmore/common-data-action": "git+ssh://****************:dtid_a56564cecace225e/common-data-action/common-data-action.git#v1.0.1", "axios": "^0.18.0", "enmore_common_mobile": "git+ssh://****************:dtid_a56564cecace225e/enmore_common_mobile_front.git#release_live", "minireset.css": "^0.0.3", "qrcodejs2": "^0.0.2", "store": "^2.0.12", "vue-awesome-swiper": "^3.1.3", "vuex": "^3.0.1", "wowjs": "^1.1.3"}, "devDependencies": {"@vue/cli-plugin-babel": "3.11.0", "@vue/cli-service": "^3.0.4", "amfe-flexible": "^2.2.1", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.12.2", "babel-polyfill": "6.26.0", "compression-webpack-plugin": "^3.0.0", "core-js": "2.6.12", "node-sass": "^4.9.3", "nprogress": "^0.2.0", "postcss-pxtorem": "^4.0.1", "sass-loader": "^7.1.0", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "vue-cli-plugin-cube-ui": "^0.1.5", "vue-skeleton-webpack-plugin": "1.2.2", "vue-template-compiler": "^2.5.17", "webpack-aliyun-oss": "^0.3.12", "image-webpack-loader": "6.0.0"}, "transformModules": {"cube-ui": {"transform": "cube-ui/src/modules/${member}", "kebabCase": true}}, "volta": {"node": "14.18.0"}}