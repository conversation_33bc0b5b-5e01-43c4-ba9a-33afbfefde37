export default [
  {
    path: "/myActivity",
    meta: {
      title: "我的活动",
      id: 171,
      key: "myActivity",
      isLogin: true,
    },
    name: "myActivity",
    component: () => import("@/views/userCenter/myActivity.vue"),
  },
  {
    path: "/myCollect",
    meta: {
      title: "我的收藏",
      id: 172,
      key: "myCollect",
      isLogin: true,
    },
    name: "myCollect",
    component: () => import("@/views/userCenter/myCollect.vue"),
  },
  {
    path: "/myOrder",
    meta: {
      // title: '参会订单记录',
      title: "我的订单",
      id: 173,
      key: "myOrder",
      isLogin: true,
    },
    name: "myOrder",
    component: () => import("@/views/userCenter/myOrder.vue"),
  },
  {
    path: "/myOrderDetail",
    meta: {
      title: "我的订单详情",
      id: 173,
      key: "myOrderDetail",
      isLogin: true,
    },
    name: "myOrderDetail",
    component: () => import("@/views/userCenter/myOrderDetail.vue"),
  },
  {
    path: "/appilyBill",
    meta: {
      title: "申请开票",
      id: 173,
      key: "appilyBill",
      isLogin: true,
    },
    name: "appilyBill",
    component: () => import("@/views/userCenter/appilyBill.vue"),
  },
  {
    path: "/myInvoice",
    meta: {
      title: "我的开票信息",
      id: 173,
      key: "myInvoice",
      isLogin: true,
    },
    name: "myInvoice",
    component: () => import("@/views/userCenter/myInvoice.vue"),
  },
  {
    path: "/userInfoSet",
    meta: {
      title: "用户信息设置",
      id: 173,
      key: "userInfoSet",
      isLogin: true,
    },
    name: "userInfoSet",
    component: () => import("@/views/userCenter/userInfoSet.vue"),
  },
  {
    path: "/mySpeech",
    meta: {
      title: "用户信息设置",
      key: "mySpeech",
      isLogin: true,
    },
    name: "mySpeech",
    component: () => import("@/views/userCenter/mySpeech.vue"),
  },
  {
    path: "/myBook",
    meta: {
      title: "我的预约活动",
      key: "myBook",
      isLogin: true,
    },
    name: "myBook",
    component: () => import("@/views/userCenter/myBook.vue"),
  },
  {
    path: "/discount",
    meta: {
      title: "我的优惠码",
      key: "discount",
      isLogin: true,
    },
    name: "discount",
    component: () => import("@/views/userCenter/discount.vue"),
  },
  {
    path: "/userInfoList",
    meta: {
      title: "报名",
      key: "userInfoList",
      isLogin: true,
    },
    name: "userInfoList",
    component: () => import("@/views/userCenter/userInfoList.vue"),
  },
  {
    path: "/myInquiry",
    meta: {
      title: "我的询价单",
      key: "myInquiry",
      isLogin: true,
    },
    name: "myInquiry",
    component: () => import("@/views/userCenter/myInquiryForm.vue"),
  },
  {
    path: "/myInquiry/:id",
    meta: {
      title: "询价单详情",
      key: "myInquiry",
      isLogin: true,
    },
    name: "myInquiryDetail",
    component: () => import("@/views/userCenter/myInquiryFormDetail.vue"),
  },
  {
    path: "/missionCenter",
    meta: {
      title: "任务中心",
      key: "missionCenter",
      isLogin: true,
    },
    name: "missionCenter",
    component: () => import("@/views/userCenter/memberMissionCenter.vue"),
  },
  {
    path: "/live",
    meta: {
      title: "我的直播",
      key: "live",
      isLogin: true,
    },
    name: "live",
    component: () => import("@/views/userCenter/myLive.vue"),
  },
  {
    path: "/activity",
    meta: {
      title: "我的活动",
      key: "activity",
      isLogin: true,
    },
    name: "activity",
    component: () => import("@/views/userCenter/activity.vue"),
  },
  {
    path: "/inquiry",
    meta: {
      title: "我的采购",
      key: "inquiry",
      isLogin: true,
    },
    name: "inquiry",
    component: () => import("@/views/userCenter/inquiry.vue"),
  },
  {
    path: "/collect",
    meta: {
      title: "我的收藏",
      key: "collect",
      isLogin: true,
    },
    name: "collect",
    component: () => import("@/views/userCenter/collect.vue"),
  },
  {
    path: "/myCoin",
    meta: {
      title: "我的易币",
      key: "myCoin",
      isLogin: true,
    },
    name: "myCoin",
    component: () => import("@/views/userCenter/myCoin.vue"),
  },
  {
    path: "/myCoin/info",
    meta: {
      title: "易币明细",
      key: "myCoin",
      isLogin: true,
    },
    name: "myCoin",
    component: () => import("@/views/userCenter/coinInfo.vue"),
  },
  {
    path: "/mall",
    meta: {
      title: "易币商城",
      key: "mallBody",
      isLogin: process.env.VUE_APP_CURRENTMODE.indexOf('Preview') > -1 ? false:true,
      keepAlive: true,
    },
    name: "mallBody",
    component: () => import("@/views/userCenter/mall.vue"),
  },
  {
    path: "/mall/goods/:id",
    meta: {
      title: "易币商城",
      key: "mallInfo",
      isLogin: true,
    },
    name: "mallGoods",
    component: () => import("@/views/userCenter/mallInfo.vue"),
  },
  {
    path: "/mall/order/:id",
    meta: {
      title: "易币商城",
      key: "listDealts",
      isLogin: true,
    },
    name: "mallOrder",
    component: () => import("@/views/userCenter/listDealts.vue"),
  },
  {
    path: "/mall/exchange",
    meta: {
      title: "我的兑换历史",
      key: "mallHistry",
      isLogin: true,
    },
    name: "mallExchange",
    component: () => import("@/views/userCenter/mallHistry.vue"),
  },
  {
    path: "/mall/search",
    meta: {
      title: "易币商城",
      key: "mallSearch",
      isLogin: true,
    },
    name: "mallSearch",
    component: () => import("@/views/userCenter/components/mallSearch.vue"),
  },
  {
    path: "/mall/orderConfirm",
    meta: {
      title: "订单确认",
      key: "orderConfirm",
      isLogin: true,
    },
    name: "mallOrderConfirm",
    component: () => import("@/views/userCenter/components/orderConfirm.vue"),
  },
  {
    path: "/myCareer",
    meta: {
      title: "我的职业",
      key: "myCareer",
      isLogin: true,
    },
    name: "myCareer",
    component: () => import("@/views/userCenter/myCareer.vue"),
  },
  {
    path: "/myAppointment",
    meta: {
      title: "我的约见",
      key: "myAppointment",
      isLogin: true,
    },
    name: "myAppointment",
    component: () => import("@/views/userCenter/myAppointment.vue"),
  },
];
