export default [
	// {
	// 	path: '/medicine',
	// 	name: 'medicine',
	// 	component: () => import('@/views/medicine'),
	// 	children: [
	// 		{
	// 			path: '/homePage/:pageId/:typeId/:subType',
	// 			name: 'homePage',
	// 			component: () => import('@/views/medicine/homePage'),
	// 		},
	// 		{
	// 			path: '/medicinePersonal',
	// 			name: 'medicinePersonal',
	// 			component: () => import('@/views/medicine/personal'),
	// 		}
	// 	]
	// },
	{  // 关注领域
		path: '/medicineIndustry',
		name: 'medicineIndustry',
		meta:{
			isLogin:true
		},
		component: () => import('@/views/medicine/personal/industry'),
	},
	{  // 关注领域
		path: '/carndustry',
		name: 'carndustry',
		meta:{
			isLogin:true
		},
		component: () => import('@/views/medicine/personal/industry'),
	},
	{
		path: '/medicineActivity',
		name: 'medicineActivity',
		component: () => import('@/views/medicine/activity'),
	},
	{
		path: '/medicine100',
		name: 'medicine100',
		component: () => import('@/views/medicine/medicine100'),
	},
	{
		path: '/medicineActivityList',
		name: 'medicineActivityList',
		component: () => import('@/views/medicine/activity/activityList'),
	},
	{
		path: '/medicineSearch',
		name: 'medicineSearch',
		component: () => import('@/views/medicine/search'),
	},
	{
		path: '/classifySearch',
		name: 'classifySearch',
		component: () => import('@/views/medicine/classifySearch'),
	},
	{
		path: '/aboutMedicine',
		name: 'aboutMedicine',
		component: () => import('@/views/medicine/personal/aboutMedicine')
	},
	{
		path: '/aboutCar',
		name: 'aboutCar',
		component: () => import('@/views/medicine/personal/aboutCar')
	},
	{
		path: '/addAssociation',
		name: 'addAssociation',
		component: () => import('@/views/medicine/personal/addAssociation')
	},
	{
		path: '/liveRoom/:id',
		name: 'liveRoom',
		component: () => import('@/views/medicine/enterpriseLiveRoom')
	},
	{
		path: '/liveRoomList',
		name: 'liveRoomList',
		component: () => import('@/views/medicine/enterpriseLiveRoom/list')
	},
	{
		path: '/medicineNewsList/:activityId',
		name: 'medicineNewsList',
		component: () => import('@/views/medicine/news/list'),
	},
	{
		path: '/medicineNewsDetail/:newsId',
		name: 'medicineNewsDetail',
		component: () => import('@/views/medicine/news/detail'),
	},
	{
		path: '/questionnaire',
		name: 'questionnaire',
		component: () => import('@/views/medicine/questionnaire'),
	},
	{
		path: '/moduleList/:modelType',
		name: 'moduleList',
		component: () => import('@/views/medicine/moduleList'),
	},
	{
		path: '/moduleDetail/:modelType/:id',
		name: 'moduleDetail',
		component: () => import('@/views/medicine/moduleList/moduleDetail'),
	},
]