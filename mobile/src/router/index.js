import Vue from "vue";
import Router from "vue-router";
import addRouterInfo from "../utils/router";
import register from "./register";
import personal from "./personal";
import searchAll from "./searchAll";
import activityIframe from "./activityIframe";
import userCenter from "./userCenter";
import contectSponsor from "./contectSponsor";
import medicine from "./medicine";
import CONSTANT from "@/config/config_constant";
import store from "@/store";
import getCookie from "@/utils/getCookie";
import { beforeRouterLogic } from "enmore_common_mobile";
import { getActivityShareConfig } from "../api/activityIframe"; // 用于获取活动详情页面分享设置
import mixin from "@/mixin";
import { useRouterError } from "../utils/routerError";
addRouterInfo(Router);
Vue.use(Router);
const router = new Router({
  routes: [
    {
      path: "/home",
      name: "home",
      component: () => import("@/views/home"),
    },
    {
      path: "/home/<USER>/:typeId/:subType",
      name: "targetPage",
      component: () => import("@/views/home"),
    },
    {
      path: "/commonList/:mode",
      name: "commonList",
      component: () => import("@/components/newCommon/commonList"),
      // mode 为 common 或live
    },
    {
      path: "/urlError",
      name: "urlError",
      component: () => import("@/views/urlError/index.vue"),
    },
    ...register,
    ...personal,
    ...searchAll,
    ...activityIframe,
    ...userCenter,
    ...contectSponsor,
    ...medicine,
    {
      path: "*",
      redirect: {
        name: "home",
      },
    },
  ],
});

useRouterError(router);

router.beforeEach((to, from, next) => {
  // 处理登录状态
  const handleLoginStatus = () => {
    if (to.meta.isLogin) {
      if (!getCookie(CONSTANT.Authorization)) {
        // 未登录时清除用户信息
        store.commit("changeLoginShow", true);
        store.commit("clearnPersonPro");
        localStorage.removeItem(CONSTANT.token);
      }
    } else {
      store.commit("changeLoginShow", false);
    }
  }

  // 记录页面访问信息
  const recordPageVisit = () => {
    // 埋点统计
    store.commit("changePageTime");
    // 记录页面访问时间戳
    localStorage.setItem("routerBeforeTime", new Date().getTime());
  }

  // 处理路由导航
  const handleRouteNavigation = async () => {
    const isPersonalCenter = CONSTANT.personalCenterRoutes.includes(to.name);
    const noUserProfile = !store.state.personal.personProfile.id;

    // 个人中心页面且无用户信息时,先获取用户信息
    if (isPersonalCenter && noUserProfile) {
      await store.dispatch("getPerprofile");
    }

    // 处理弹窗
    if(from.fullPath !== '/'){
      store.commit("changeIsShowAd", false);
    }

    // 统一处理路由导航逻辑
    beforeRouterLogic(
      to,
      from,
      next,
      process.env.VUE_APP_CURRENTMODE,
      store,
      false,
      "info"
    );
  }

  // 执行路由守卫逻辑
  handleLoginStatus();
  recordPageVisit();
  handleRouteNavigation();
});
router.afterEach((to, from) => {
  // 处理登录状态
  const handleLoginState = () => {
    const isAuthorized = getCookie(CONSTANT.Authorization);
    store.commit("changeLoginStatus", isAuthorized);
  }

  // 处理活动分享配置
  const handleActivityShare = async (to) => {
    if (to.name !== "activityIframe") return;

    const shareParams = {
      itemId: to.query.activityId,
      itemType: "activity", 
      platformId: localStorage.getItem("platformId"),
      source: to.query.source,
      bizId: to.query.bizId
    };

    try {
      const res = await getActivityShareConfig(shareParams);
      if (res.code === "001") {
        mixin.methods.wxShareConfig(res.data);
      }
    } catch (err) {
      console.error("获取分享配置失败:", err);
    }
  }

  // 处理埋点数据
  const handleTrackingData = (route, type = "to") => {
    if (route.name !== "activityIframe") return;

    const bizId = type === "to" 
      ? route.query.bizId
      : localStorage.getItem("bizId") || localStorage.getItem("platformId");

    store.commit("changeSystemInfo", {
      ...store.state.dataActionStore,
      bizId
    });
  }

  // 执行路由后置处理
  handleLoginState();
  handleActivityShare(to);
  handleTrackingData(to, "to");
  handleTrackingData(from, "from");
  store.commit("changeUserPopShow", false); //关闭实名认证弹框
});

export default router;
