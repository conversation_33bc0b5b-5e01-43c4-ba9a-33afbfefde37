<template>
  <div :class="{ scrollbgc: is_show_mask,bgc: isShowVipBgc}">
    <!-- 加入我们  预览环境不显示-->
    <div>
      <div v-if="!isPreviewEnviroment && indexbtn == 1 && !isEmptystatus">
        <div class="tojoin">
          <div class="tojoinbtn" @click="tojoin()">
            <p>申请</p>
            <p>加入</p>
          </div>
        </div>
        <div class="bullettojoin" v-if="is_show_tojoin" @click.stop>
          <h1 class="clearfix" style=" margin: 15px 0 5px 20px; color: #333333; height: 22px; line-height: 22px; font-size: 16px; ">
            <span>申请加入</span>
            <img src="./images/close.png" alt="" class="r" style=" margin-right: 15px; vertical-align: middle; margin-top: 5px; " @click="close()" />
          </h1>
          <cube-form :model="userInfo" :schema="schema" :immediate-validate="false" :options="options" @validate="validateHandler" @submit="submitHandler" :disable = disable></cube-form>
        </div>
      </div>
    </div>
    <div>
      <!-- 标题部分11 -->
      <div class="medicine100-title">
        <button :class="indexbtn == 1 ? 'btn_active' : 'btn'" @click="showinto(1), (isShowVipBgc = false)" @click.stop > BIO100+简介</button>
        <button :class="indexbtn == 2 ? 'btn_active' : 'btn'" @click="showcom(2), (isShowVipBgc = true)" @click.stop v-if="isPreviewEnviroment||(vipCompanyModel && guests.length)"> {{ vipCompanyModel?vipCompanyModel.subMenuName : "会员单位" }} </button>
      </div>
      <div class="coupon-popup" v-show="is_show_mask" @click="close()"></div>
      <div class="medicine100" style="margin-top:90px;">
        <!-- bio100+简介 -->
        <img src="./images/empty.png" alt=""  class="nocontent" v-if="indexbtn==1 && isEmptystatus">
        <div v-else>
          <div v-show="is_show_biointro" style="position: relative;">
            <div v-for="layoutItem in layout" :key="layoutItem.id">
              <!-- 平台组织介绍部分 -->
              <template v-if="layoutItem.subMenu==17 && introductions.length">
                <div class="introduction">
                  <div class="shuxian"><span class="verticalline"></span><span class="shuxiancon">{{ layoutItem.subMenuName }}</span></div>
                  <div style="font-size: 14px; color: #666666; line-height: 24px">
                    <div v-for="(item, i) in introductions" :key="i">
                      <p v-if="item.type == 'text'" v-html="item.text" style="text-indent: 2em" ></p>
                      <img v-else-if="item.type == 'img' && item.url !== ''" :src="item.url" alt="" />
                      <!-- <img :src="item.url" alt="" style="display:none;" v-else> -->
                    </div>
                  </div>
                </div> 
              </template>
              <!-- 创始理事单位部分 -->
              <template v-if="layoutItem.subMenu==18 && companys.length">
                <Foundingone :companys="companys" :layout="layoutItem" v-if="layoutItem.paramObj.param == 0" @showMask="showMask"></Foundingone>
                <Foundingtwo :companys="companys" :layout="layoutItem" v-else-if="layoutItem.paramObj.param == 1" @showMask="showMask"></Foundingtwo>
              </template>
              <!-- 理事单位部分 -->
              <template v-if="layoutItem.subMenu==19 && companylists.length">
                <Directorone :companylists="companylists" :layout="layoutItem" v-if="layoutItem.paramObj.param == 1" @showMask="showMask"></Directorone>
                <Directortwo :companylists="companylists" :layout="layoutItem" v-if="layoutItem.paramObj.param == 0" @showMask="showMask"></Directortwo>
              </template>
              <!-- 年度主席团 -->
              <template v-if="layoutItem.subMenu==20 && guestlists.length">
                <div class="presidium">
                  <div class="shuxian" style="margin-bottom:10px;margin-top:25px;"><span class="verticalline"></span><span class="shuxiancon">{{ layoutItem.subMenuName }}</span></div>
                  <div class="clearfix">
                    <div class="presidium-guest l" v-for="(item, i) in guestlists" :key="i" @click="preidium(i)">
                      <img :src="item.logo" alt=" " v-if="item.logo !== ''" />
                      <img src="./images/honouredGuest.png" alt=" " v-else />
                      <h1 class="predium-title">
                        <div class="presidium-guestname text-ellipsis">{{ item.name }}</div>&nbsp;
                        <div class="presidium-guestname text-ellipsis" style="color: #666666">{{ item.position }}</div>
                      </h1>
                      <p class="presidium-guestintro text-ellipsis">{{ item.company }}</p>
                      <!-- 点击之后 -->
                      <div class="bullet founding-bullet presidium-guest-bullet" v-if="is_show_pre" @click.stop @click="close()">
                        <h1 class="founding-bullet-title">个人简介</h1>
                        <div class="clearfix" style="margin: 21px 0 0px;">
                          <img :src="guestlists[index].logo" alt="" v-if="guestlists[index].logo !== ''" class="l" />
                          <img src="./images/honouredGuest.png" alt=" " v-else class="l" />
                          <div class="l presidium-guest-bulletcon">
                            <p>{{ guestlists[index].name }}</p>
                            <p>{{ guestlists[index].position }}</p>
                            <div class="text-ellipsis">{{ guestlists[index].company }}</div>
                          </div>
                        </div>
                        <div class="founding-bullet-intro" v-if="guestlists[index].introduction" style="white-space:pre-line;height: calc(100vh - 250px);">
                          <p style="text-indent: 0;">
                            {{ guestlists[index].introduction }}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              <!-- 预览环境显示遮罩层，不能点击 -->
              <div class="PreviewEnviroment" v-if="isPreviewEnviroment"></div>
            </div>
          </div>    
          <!-- 会员单位 -->
          <div v-show="is_show_memberunits" style="position: relative">
            <template v-if="vipCompanyModel && guests.length">
              <Membercompanyone :guests="guests" :layout="vipCompanyModel" v-if="vipCompanyModel.paramObj.param == 0" @showMask="showMask"></Membercompanyone>
              <Membercompanytwo :guests="guests" :layout="vipCompanyModel" v-if="vipCompanyModel.paramObj.param == 1" @showMask="showMask"></Membercompanytwo>
            </template>
            <!-- 预览环境显示遮罩层，不能点击 -->
            <div class="PreviewEnviroment" v-if="isPreviewEnviroment"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
window.onscroll = function () {
  let scrollTop = document.documentElement.scrollTop || document.body.scrollTop
  if (scrollTop < 0) {
     document.querySelector('.medicine100-title').style.position = 'absolute'
     document.querySelector('.tojoin').style.position = 'absolute'
  } else {
    document.querySelector('.medicine100-title').style.position = 'fixed'
    document.querySelector('.tojoin').style.position = 'fixed'
  }
 }

import Membercompanyone from "./component/membercompanyone";
import Membercompanytwo from "./component/membercompanytwo";
import Directorone from "./component/directorone";
import Directortwo from "./component/directortwo";
import Foundingone from "./component/foundingone";
import Foundingtwo from "./component/foundingtwo";
import { getapplylist,} from "@/api/medicine/medicine100";
import { getOfficialMenuMedicine, getConferenceFront } from "@/api/medicine/homePage";
export default {
  name: "medicine100",
  components: { Foundingone,Foundingtwo, Directorone, Directortwo, Membercompanyone, Membercompanytwo,},
  data() {
    return {
      //控制会员背景色状态
      isShowVipBgc: false,
      is_show_mask: false,
      is_show_pre: false,
      is_show_biointro: true,
      is_show_memberunits: false,
      is_show_tojoin: false,
      is_show_membercom: false,
      disable:false,
      indexbtn: 1,
      layout: [],
      companylists: [],
      guests: [],
      companys: [],
      guestlists: [],
      introductions: [],
      vipCompanyModel: '',   // 会员单位 模块
      PreviewUrl: this.isPreviewEnviroment ? "" : "front/",
      validity: {},
      valid: undefined,
      userInfo: {
        name: "",
        position: "",
        companyName: "",
        contact: "",
        companyIntroduce: "",
      },
      schema: {
        groups: [
          {
            fields: [
              {
                type: "input",
                modelKey: "name",
                label: "姓名",
                props: {
                  placeholder: "请输入",
                },
                rules: {
                  valid: false,
                  required: {
                    valid: false,
                    invalid: true,
                    message: "此为必填项",
                  },
                },
                trigger: "blur",
              },
              {
                type: "input",
                modelKey: "position",
                label: "职位",
                props: {
                  placeholder: "请输入",
                },
                rules: {},
                trigger: "blur",
              },
              {
                type: "input",
                modelKey: "companyName",
                label: "公司",
                props: {
                  placeholder: "请输入",
                },
                rules: {},
                trigger: "blur",
              },
              {
                type: "input",
                modelKey: "contact",
                label: "联系方式",
                props: {
                  placeholder: "请输入",
                },
                events: {
									'blur': (...args) => {
										document.activeElement.scrollIntoViewIfNeeded(true)
									}
								},
                rules: {
                  // pattern:
                  //   /^((\+?86)|(\(\+86\)))?(13[0123456789][0-9]{8}|15[*********][0-9]{8}|18[*********][0-9]{8}|14[57][0-9]{8}|17[3678][0-9]{8})$/,
                  required: true,
                },
                trigger: "blur",
              },
              {
                type: "textarea",
                modelKey: "companyIntroduce",
                label: "公司简介",
                props: {
                  placeholder: "请输入",
                  maxlength: 500,
                },
                events: {
									'blur': (...args) => {
										document.activeElement.scrollIntoViewIfNeeded(true)
									}
								},
                rules: {                
                },            
              },
            ],
          },
          {
            fields: [
              {
                type: "button",
                label: "关闭",
                events: {
                  click: () => {
                    this.close();
                    console.log("file removed", args);
                  },
                },
              },
              {
                type: "submit",
                label: "提交",
              },
            ],
          },
        ],
      },
      options: {
        scrollToInvalidField: true,
        layout: "standard", // classic fresh
      },
    };
  },
  computed: {
    isEmptystatus() {
      if (this.introductions.length || this.companys.length || this.companylists.length || this.guestlists.length) {
        return false
      }
      return true;
    }
  },
  watch: {
    // 兼容超管100+设置
    indexbtn() {
      if (this.isPreviewEnviroment) {
        let sendData = {
          type: "bio100+",
          value: this.indexbtn,
        };
        this.sendMessage(sendData);
      }
    },
    
  },
  mounted() {
    this.PreviewUrl = this.isPreviewEnviroment ? "" : "front/";
    // 监听变化
    window.addEventListener("message", (ev) => {
      this.indexbtn = 1;
      this.showinto(1);
      this.isShowVipBgc = false;
      this.getOfficialMenuMedicine();
    });
    this.getOfficialMenuMedicine();
  },
  methods: {
    showMask(status) {
      console.log(status)
      this.is_show_mask = status;
    },
    submitHandler(e) {
      e.preventDefault();
      if (this.disable) {
        return;
      }
      this.disable = true;
      getapplylist(this.userInfo).then((res) => {        
        if (res.code == this.$successCode ) {
          this.$createToast({ txt: "提交成功", type: "correct" }).show();
          this.close();
          this.userInfo = { };
          this.disable = false;
        } else {
          alert("正在操作，请不要重复提交，谢谢！");
          
        }
      });
    },
    validateHandler(result) {
      this.validity = result.validity;
      this.valid = result.valid;
      console.log(
        "validity",
        result.validity,
        result.valid,
        result.dirty,
        result.firstInvalidFieldIndex
      );
    },
    // 获取布局
    getOfficialMenuMedicine() {
      getOfficialMenuMedicine({ menu: 5 }).then((res) => {
        this.introductions = [];
        this.companys = [];
        this.companylists = [];
        this.guestlists = [];
        this.guests = [];
        this.vipCompanyModel = '';
        res.data.forEach(model => {
          let params = this.isPreviewEnviroment ? model.previewApiUrl : model.apiUrl;
          model.paramObj = model.param?JSON.parse(model.param):{};
          getConferenceFront(params).then(modelRes => {
            if (modelRes.code == this.$successCode) {
              if (model.subMenu == 17) {  
                // 平台组织介绍
                modelRes.data.forEach(item => {
                  if (item.url || item.text) {
                    this.introductions.push(item)
                  }
                })
              } else if (model.subMenu == 18) {
                // 创始理事单位
                this.companys = modelRes.data.list;
                this.companys.map((item) => {
                  item.introduction = JSON.parse(item.introduction);
                });
              } else if (model.subMenu == 19) {
                // 理事单位
                this.companylists = modelRes.data.list;
                this.companylists.map((item) => {
                  item.introduction = JSON.parse(item.introduction);
                });
              } else if (model.subMenu == 20) {
                // 年度主席团
                this.guestlists = modelRes.data.list;
              } else if (model.subMenu == 21) {
                // 会员单位
                this.vipCompanyModel = model;
                this.guests = modelRes.data.list;
                this.guests.map((item) => {
                  item.introduction = JSON.parse(item.introduction);
                });
              }
            } else {
              this.$message.error(res.info);
            }
          })
        })
        this.layout = res.data;
      });
    },
    close() {
      this.is_show_mask = false;
      this.is_show_intro = false;
      this.is_show_com = false;
      this.is_show_pre = false;
      this.is_show_tojoin = false;
      this.is_show_membercom = false;
    },
    showinto(i) {
      this.is_show_biointro = true;
      this.is_show_memberunits = false;
      this.indexbtn = i;
    },
    showcom(i) {
      this.is_show_biointro = false;
      this.is_show_memberunits = true;
      this.indexbtn = i;
    },
    preidium(i) {
      this.is_show_mask = true;
      this.is_show_pre = true;
      this.index = i;
    },
    tojoin() {
      this.is_show_mask = true;
      this.is_show_tojoin = true;
    },
  },
};
</script>
<style scoped>
@import "./style/medicine100.css";
@import "./style/main.css";
.container {
  overflow: auto;
  height: 100vh;
}
.PreviewEnviroment {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #ff000000;
  top: 0px;
}
.scrollbgc {
  overflow: hidden;
}
</style>