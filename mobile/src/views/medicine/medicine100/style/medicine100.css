@charset "UTF-8";
body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
button,
textarea,
p,
blockquote,
th,
td {
	margin: 0;
	padding: 0;
}

fieldset,
img {
	border: 0
}

:focus {
	outline: 0
}

address,
caption,
cite,
code,
dfn,
em,
strong,
th,
var,
optgroup {
	font-style: normal;
	font-weight: 400
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-size: 100%;
	font-weight: 400
}

abbr,
acronym {
	border: 0;
	font-variant: normal
}

code,
kbd,
samp,
tt {
	font-size: 100%
}

input,
button,
textarea,
select {
	*font-size: 100%;
	border: 0
}

ol,
ul {
	list-style: none
}

table {
	border-collapse: collapse;
	border-spacing: 0
}

caption,
th {
	text-align: left
}

sup,
sub {
	font-size: 100%;
	vertical-align: baseline
}

:link,
:visited,
ins {
	text-decoration: none
}

blockquote,
q {
	quotes: none
}

blockquote:before,
blockquote:after,
q:before,
q:after {
	content: '';
	content: none
}

a:link,
a:visited {
	color: #5e5e5e
}

a:hover {
	color: #c9394a
}

a:active {
	color: #666
}

.clearBoth {
	clear: both;
}

.clearfix:after {
	content: '\0020';
	display: block;
	height: 0;
	clear: both;
	visibility: hidden
}

.clearfix {
	zoom: 1
}

.l {
	float: left
}

.r {
	float: right
}

.clear {
	height: 0;
	width: 100%;
	overflow: hidden;
	clear: both
}

.hide {
	display: none
}

.btn.hide {
	display: none
}

a.hidefocus {
	outline: 0
}

button.hidefocus::-moz-focus-inner {
	border: 0
}

a:focus {
	outline: 0;
	-moz-outline: 0
}

input,
textarea {
	outline: 0
}

h2 {
	font-size: 20px
}

h3 {
	font-size: 16px;
	line-height: 32px
}

h5 {
	font-size: 14px;
	line-height: 28px
}

.hide-text {
	text-indent: 100%;
	white-space: nowrap;
	overflow: hidden;
}

.pointer {
	cursor: pointer;
}

.shadow {
	-webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, .1);
	-moz-box-shadow: 0 2px 4px rgba(0, 0, 0, .1);
	box-shadow: 0 2px 4px rgba(0, 0, 0, .1)
}

.text-ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap
}

.text-ellipsistwo {
	display: -webkit-inline-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;	
    white-space:normal;
}

.boxShadow {
	border-radius: 5px;
	transition: box-shadow 0.3s;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.boxShadow:hover {
	border-radius: 5px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}

.boxWhiteShadow {
	border-radius: 5px;
	transition: box-shadow 0.3s;
	box-shadow: 0 2px 12px 0 rgba(255, 255, 255, 0.3);
}

.boxWhiteShadow:hover {
	border-radius: 5px;
	box-shadow: 0 2px 12px 0 rgba(255, 255, 255, 0.5);
}

.beautifulScroll::-webkit-scrollbar {
	width: 5px;
	height: 5px;
	background-color: #F5F5F5;
}

.beautifulScroll::-webkit-scrollbar-track {
	border-radius: 1px;
	background-color: #F5F5F5;
	box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

.beautifulScroll::-webkit-scrollbar-thumb {
	border-radius: 2px;
	box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
	background-color: #999;
}

.hiddenScroll::-webkit-scrollbar-thumb {
	display: none !important;
}

.hiddenScroll::-webkit-scrollbar {
	display: none !important;
}

.hiddenScroll::-webkit-scrollbar-track {
	display: none !important;
}
.scrollmainY {
	width: 100%;
	height: 100%;
	overflow-x: hidden;
	overflow-y: scroll;
	-webkit-overflow-scrolling: touch;
}