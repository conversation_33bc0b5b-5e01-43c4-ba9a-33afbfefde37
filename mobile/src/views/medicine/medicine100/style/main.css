.bgc {
  background: linear-gradient( 180deg, #ffffff 0%, #f2f1f6 30%, #f2f1f6 70%, #f2f1f6 100%);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
  height: 100%;
  left: 0;
}
.nocontent {
  position: fixed;
  top:50%;
  left: 50%;
  transform:translate(-50%,-50%);
}
.bullettojoin {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 320px;
  height: 402px;
  transform:translate(-50%,-50%);
  background: #FFFFFF;
  border-radius: 10px;
  z-index: 1002;
  overflow: hidden;
}
.tojoin {
  position: fixed;
  top: 70px;
  right: 10px;
  width: 54px;
  height: 54px;
  background: #FF9B2D;
  border-radius: 50%;
  text-align: center;
  padding-top: 12px;
	z-index: 1001;
  color: #ffffff;
}
.tojoin .tojoinbtn p {
  line-height: 16px;
  font-size: 14px;
}
.cube-form-group-legend {
  display: none;
}
.cube-form-group {
  padding: 0 0rem;
}
.cube-form_standard .cube-validator-msg-def {
  font-size: 12px;
}

.cube-form-group-content {
      display: flex;
    flex-flow: row wrap;
    justify-content: center;
    align-items: center;
}
.cube-form-item {
  height: 50px;
  width: 100%;
  padding: 0 0;
  margin: 0 15px;
}
.cube-btn{
  /* margin-top: 50px; */
  width: 100%;
  height: 32px;
  border-radius: 4px;
border: 1px solid #DDDDDD;
background: bottom;
color: #999999;
padding: 0 0;
margin-right:0;
  }
  .cube-form-group-content {
    overflow: auto;
  }
  .cube-form .cube-form-group:nth-child(2) .cube-form-item {
    width: 138px;
    margin: 0 0;
    margin-top: 10px;
  }
  .cube-form .cube-form-group:nth-child(2) .cube-form-item:nth-child(1) {
    margin-right: 14px;
  }
.cube-form-group-content .cube-form-item_btn:nth-child(2) .cube-btn{
  background: #1464A1;
  color: #FFFFFF;
line-height: 20px;
}
.cube-form .cube-form-group:nth-child(1) .cube-form-item:nth-child(2) .cube-form-label,
.cube-form .cube-form-group:nth-child(1) .cube-form-item:nth-child(3) .cube-form-label,
.cube-form .cube-form-group:nth-child(1) .cube-form-item:nth-child(5) .cube-form-label
 {
  padding-left: 10px;
}
.cube-form .cube-form-group:nth-child(1) .cube-form-item:nth-child(5) .cube-form-label {
  display: block;
  margin-top: -50px;
  }
.cube-form .cube-form-group:nth-child(1) .cube-form-item:nth-child(5) {
  height: 100px;
}
.cube-form_standard .cube-textarea-wrapper {
  padding:0 0;
  height: 44px;
}
.cube-textarea-indicator {
  top:70px;
  right: 0;
}
.cube-textarea {
  height: 100px;
  width: 88%;
}
.cube-textarea-indicator {
  display: block;
}

.cube-form-group-content .cube-form-item:nth-child(5)
{
  border-bottom: 1px solid #EEEEEE;
}
.cube-form_standard .cube-textarea-wrapper.cube-textarea_expanded {
  height: 100px;
}
  .medicine100 {
    padding: 15px 15px;
    padding-bottom: 30px;  
			padding-top: 0;
			margin-top: -8px;
  }
  .medicine100-title button {
    width: 167px;
    height: 40px;
    border-radius: 4px;
    font-size: 14px;
  }
  .medicine100-title button:nth-child(1) {
    margin-right: 10px;
  }
  .medicine100-title {
    width: 100%;
    padding:15px 15px;
    background-color: #fff;
    display: flex;
    flex-flow: row wrap;
    /* justify-content: center; */
    align-items: center;
    position: fixed;
    z-index: 1000;
		top: 0;
  }
  .shuxian {        
    margin: 15px 0;
    margin-top:20px;
    height: 20px;
    line-height: 20px;
  }
  .verticalline {
    vertical-align: middle;
    width: 3px;
    height: 16px;
    display: inline-block;    
    border: 1px solid #1464A1;
    margin-right: 6px;
    background: #1464A1;
    border-radius: 3px;
  }
  .shuxiancon {
    display: inline-block;
    vertical-align: middle;
    height: 18px;
    line-height: 20px;
  }
  .introduction {
    margin: 0 0 25px;
  }
  .founding-company {
    width: 345px;
    line-height: 64px;
    border-radius: 4px;
    background: rgba(20, 100, 161, 0.1);
    margin-bottom: 10px;
    color: #1464A1;
    box-sizing: border-box;
  }
  .founding-company img {
    vertical-align: middle;
  }
  .founding-company span {
    width: calc(100% - 138px);
    display: inline-block;
    vertical-align: middle;
    font-size: 13px;
    line-height: 18px;
    margin-left: 10px;
    
  }  
  .founding img {
    margin-left: 10px;
    vertical-align: middle;
    width: 108px;
    height: 44px;
    border-radius: 4px;
  }
  .director-cpompany {
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
    align-items: center;
    width: 168px;
    height: 73px;
    border-radius: 4px;
    background: rgba(20, 100, 161, 0.1);
    margin: 0 9px 10px 0;
    overflow: hidden;
    box-sizing: border-box;
  }
  .director-the:nth-of-type(2n+1) .director-cpompany {
    margin-right: 0;
  }
  .director-thetwo:nth-of-type(2n) .director-cpompany {
    margin-right: 0;
  }  
  .director-cpompany img {
    vertical-align: middle;
    width: 68px;
    height: 28px;
    margin-left: 4px;
    border-radius: 4px;
  }
  
  .director-cpompany div {
    vertical-align: middle;
    width: calc(100% - 93px);
    color: #1464a1;
    font-size: 13px;
    margin-left: 6px;   
    line-height: 18px;
  }
  .presidium {
    padding-right: 0;
  }
  .presidium-guest {
    text-align: center;
    width: 168px;
    height: 110px;
    border-radius: 4px;
    border: 1px solid rgba(20, 100, 161, 0.2);
    font-size: 12px;
    padding-bottom: 5px;
    margin: 5px 0;
    margin-right: 9px;
    box-sizing: border-box;
  }
  .presidium-guest:nth-child(2n) {
    margin-right: 0px;
  }
  .presidium-guest img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin: 8px 0;
  }
.predium-title {
  display: flex;
  flex-flow: row wrap;
  justify-content: center;
  align-items: center;
  margin: 0 0 4px 0;
}
.presidium-guestintro {
  color: #666666;
  width: 90%;
  margin: 0 auto;
}
  .coupon-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(000, 000, 000, 0.3);
    color: #ffffff;
    z-index: 1001;
  }
  .bullet {
    position: fixed;
    width: 325px;
    height: calc(100% - 70px);
    background: #ffffff;
    border-radius: 10px;
    z-index: 1002;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    box-sizing: border-box;
  }

  .btn {
    background: #f2f2f2;
    color: #333333;
  }
  .btn_active {
    background: #1464a1;
    color: #ffffff;
  }
  .membercompany {
    display: flex;
	  justify-content: center;
	  align-items: center;
    width: 345px;
    height: 88px;
    border-radius: 6px;
    padding: 0px 15px;
    margin-bottom: 15px;
    box-sizing: border-box;
    background-color: #fff;
  }
  .membercompany img {
    width: 122px;
    height: 49px;
    border-radius: 4px;
    margin-right: 10px;
  }
  .membercompany div {
    width: 188px;
    color: #333333;
    line-height: 20px;
  }
  .membercompany-bullet-title {
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
    align-items: center;
  }
.membercompany-bullet-title img{
  vertical-align: middle;
   width: 91px;
   height: 37px;
   margin-right: 10px;
}
.membercompany-bullet-title div {
  width: 190px;
  color: #333333; 
  line-height: 20px; 
  font-size: 14px;
}
  .founding-bullet {
    padding: 20px 15px;
  }
  .founding-bullet .founding-bullet-title {
    font-size: 18px;
color: #333333;
line-height: 25px; 
  }
  .founding-bullet .founding-bullet-con {
    width: 100%;
    height: 80px;
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #EEEEEE;
  }
  .founding-bullet .founding-bullet-con img {
    width: 91px;
    height: 37px;
    vertical-align: middle;
    border-radius: 0;
    margin: 0 10px 0 0;
    }
  .founding-bullet .founding-bullet-con div {
    font-size: 14px;  
    color: #333333;
    line-height: 20px;
    width: calc(100% - 102px);
  }
  .founding-bullet-intro {
    margin-top:15px;
    overflow: auto;
    height: calc(100vh - 220px);
    }
    .founding-bullet-intro p {
      text-indent: 2em;
      color: #666666;
      line-height: 24px;
      font-size: 14px;
    }
    .founding-bullet-intro img {
      width: 100%;
      height: auto;
      margin-left: 0;
      border-radius: 0;
    }
.presidium-guest-bullet {
  text-align: left;
}    
.presidium-guest-bulletcon {
  margin-left: 15px;
  color: #333333;
  line-height: 20px;
  font-size: 14px;
  margin-bottom:10px;
  margin-left: 20px;
  width: calc(100% - 68px);
}
.presidium-guest-bulletcon p {
  margin-bottom: 4px;
}

