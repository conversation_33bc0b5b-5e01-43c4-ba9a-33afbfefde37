<template>
    <!-- 会员单位两行 -->
<div>      
    <div class="coupon-popup" v-show="is_show_mask" @click="close()"></div> 
    <div class="founding clearfix" style="margin-top:80px;">  
    <div  v-for="(item, i) in guests" :key="i" @click="membercompany(i)" class="director-thetwo" style="margin-top:5px;">
      <div class="director-cpompany l" style="background-color: #FFFFFF;">
        <img :src="item.logo" alt=" " v-if="item.logo !== ''" />
        <img src="../images/companyLogo.png" alt=" " v-else />
        <div class="text-ellipsistwo">{{ item.name }}</div>
      </div>
      <!-- 点击之后 -->
      <div class="bullet founding-bullet" v-if="is_show_membercom" @click.stop @click="close()">
        <h1 class="founding-bullet-title">公司简介</h1>
        <div class="founding-bullet-con"  >
          <img :src="guests[index].logo" alt="" v-if="guests[index].logo !== ''"/>
          <img src="../images/companyLogo.png" alt=" " v-else />
          <div class="text-ellipsistwo">{{ guests[index].name }}</div>
        </div>
        <div class="founding-bullet-intro">
        <div v-for="(item, i) in guests[index].introduction" :key="i" style="white-space:pre-line;">
          <p v-if="item.type == 'text'" v-html="item.text"></p>
          <div v-else-if="item.type == 'img'">
            <img :src="item.url" alt="" />
          </div>
        </div>
        </div>
      </div>
    </div>
    </div>	
</div>
</template>
<script>
export default {
  name: "Membercompanytwo",
  props: {
    guests: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      is_show_membercom: false,
      is_show_mask: false,
    };
  },
  mounted() {},
  methods: {
    membercompany(i) {
      this.$emit('showMask', true)
      this.is_show_mask = true;
      this.is_show_membercom = true;
      this.index = i;
    },
    close() {
      this.$emit('showMask', false)
      this.is_show_mask = false;
      this.is_show_intro = false;
      this.is_show_com = false;
      this.is_show_pre = false;
      this.is_show_tojoin = false;
      this.is_show_membercom = false;
    },
  },
};
</script>


<style scoped>
@import "../style/medicine100.css";
@import "../style/main.css";
</style>