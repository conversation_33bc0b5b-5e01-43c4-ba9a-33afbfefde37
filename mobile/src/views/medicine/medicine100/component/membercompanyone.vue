<template>
    <!-- 会员单位单行 -->
    <div>        
    <div class="coupon-popup" v-show="is_show_mask" @click="close()"></div>
    <div>   
      <div  v-for="(item, i) in guests" :key="i" @click="membercompany(i)">
      <div class="membercompany clearfix">
        <img :src="item.logo" alt=" " v-if="item.logo !== ''" />
        <img src="../images/companyLogo.png" alt=" " v-else />
        <div class="text-ellipsistwo">{{ item.name }}</div>
      </div>
      <!-- 点击之后 -->
      <div class="bullet founding-bullet membercompany-bullet" v-if="is_show_membercom" @click.stop @click="close()">
        <h1 style="color: #333333; line-height: 25px; font-size: 18px">公司简介</h1>
        <div class="membercompany-bullet-title" style="padding: 23px 0; border-bottom: 1px solid #eeeeee;" >
          <img :src="guests[index].logo" alt="" v-if="guests[index].logo !== ''"/>
          <img src="../images/companyLogo.png" alt=" " v-else />
          <div class="text-ellipsistwo">{{ guests[index].name }}</div>
        </div>
        <div class="founding-bullet-intro">
        <div v-for="(item, i) in guests[index].introduction" :key="i" style="white-space:pre-line;">
          <p v-if="item.type == 'text'" v-html="item.text"> </p>
          <div v-else-if="item.type == 'img'">
            <img :src="item.url" alt="" />
          </div>
        </div>
        </div>
      </div>
    </div>
    </div>	
    </div>
</template>
<script>
export default {
  name: "Membercompanyone",
  props: {
    guests: {
	type: Array,
      default: () => {
        return []
      },
    },
    layout: {
			type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      is_show_membercom: false,
      is_show_mask: false,
    };
  },
  mounted() {},
  methods: {
    membercompany(i) {
      this.$emit('showMask', true)
      this.is_show_mask = true;
      this.is_show_membercom = true;
      this.index = i;
    },
    close() {
      this.$emit('showMask', false)
      this.is_show_mask = false;
      this.is_show_intro = false;
      this.is_show_com = false;
      this.is_show_pre = false;
      this.is_show_tojoin = false;
      this.is_show_membercom = false;
    },
  },
};
</script>


<style scoped>
@import "../style/medicine100.css";
@import "../style/main.css";
</style>