<template>
  <!-- 创始理事单位部分单行 -->
  <div>
    <div class="coupon-popup" v-show="is_show_mask" @click="close()"></div>
    <div class="founding">
      <div class="shuxian" v-if="companys.length !==0"><span class="verticalline"></span><span class="shuxiancon">{{ layout.subMenuName }}</span></div>
      <div v-for="(item, i) in companys" :key="i" @click="foundingcompany(i)">
        <div class="founding-company">
          <img :src="item.logo" alt=" " v-if="item.logo !== ''" />
          <img src="../images/companyLogo.png" v-else />
          <span class="text-ellipsis">{{ item.name }}</span>
        </div>
        <!-- 点击之后 -->
        <div
          class="bullet founding-bullet"
          v-if="is_show_intro"
          @click.stop
          @click="close()"
        >
          <h1 class="founding-bullet-title">公司简介</h1>
          <div class="founding-bullet-con">
            <img :src="companys[index].logo" alt="" v-if="companys[index].logo !== ''" />
            <img src="../images/companyLogo.png" alt="" v-else />
            <div class="text-ellipsistwo">{{companys[index].name}}</div>
          </div>
          <div class="founding-bullet-intro">
            <div v-for="(item, i) in companys[index].introduction" :key="i" style="white-space:pre-line;">
              <p v-if="item.type == 'text'" v-html="item.text"></p>
              <div v-else-if="item.type == 'img'">
                <img :src="item.url" alt=""  />
                
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Foundingone",
  props: {
    companys: {
			type: Array,
      default: () => {
        return []
      },
    },
    layout: {
			type: Object,
      default: () => {}
    },
  },

  data() {
    return {
      is_show_intro: false,
      is_show_mask: false,
    };
  },
  mounted() {},
  methods: {
    foundingcompany(i) {
      this.$emit('showMask', true)
      this.is_show_mask = true;
      this.is_show_intro = true;
      this.index = i;
    },
    close() {
      this.$emit('showMask', false)
      this.is_show_mask = false;
      this.is_show_intro = false;
      this.is_show_com = false;
      this.is_show_pre = false;
      this.is_show_tojoin = false;
      this.is_show_membercom = false;
    },
  },
};
</script>

<style scoped>
@import "../style/medicine100.css";
@import "../style/main.css";
</style>