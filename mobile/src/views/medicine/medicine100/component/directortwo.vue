<template>
  <!-- 理事单位部分单行 -->
  <div>    
    <div class="coupon-popup" v-show="is_show_mask" @click="close()"></div>
    <div class="founding" >
      <div class="shuxian" style="margin-top:15px;" v-if="companylists.length !==0"><span class="verticalline"></span><span class="shuxiancon">{{ layout.subMenuName }}</span></div>
      <div v-for="(item, i) in companylists" :key="i">
        <div class="founding-company" @click="directorcompany(i)">
          <img :src="item.logo" alt=" " v-if="item.logo !== ''" />
          <img src="../images/companyLogo.png" v-else />
          <span class="text-ellipsis">{{ item.name }}</span>
        </div>
        <!-- 点击之后 -->
        <div class="bullet founding-bullet" v-if="is_show_com" @click.stop @click="close()">
          <h1 class="founding-bullet-title">公司简介</h1>
          <div class="founding-bullet-con">
            <img :src="companylists[index].logo" alt="" v-if="companylists[index].logo !== ''" />
            <img src="../images/companyLogo.png" v-else />
            <div class="text-ellipsistwo">{{ companylists[index].name }}</div>
          </div>
          <div class="founding-bullet-intro">
            <div v-for="(item, i) in companylists[index].introduction" :key="i" style="white-space:pre-line;">
              <p v-if="item.type == 'text'" v-html="item.text"></p>
              <div v-else-if="item.type == 'img'">
                <img :src="item.url" alt=" "  />
                
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Directortwo",
  props: {
    companylists: {
			type: Array,
      default: () => {
        return []
      },
    },
    layout: {
			type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      is_show_com: false,
      is_show_mask: false,
    };
  },
  mounted() {
    
  },
  methods: {
    directorcompany(i) {
      this.$emit('showMask', true)
      this.is_show_mask = true;
      this.is_show_com = true;
      this.index = i;
    },
    close() {
      this.$emit('showMask', false)
      this.is_show_mask = false;
      this.is_show_intro = false;
      this.is_show_com = false;
      this.is_show_pre = false;
      this.is_show_tojoin = false;
      this.is_show_membercom = false;
    },
  },
};
</script>


<style scoped>
@import "../style/medicine100.css";
@import "../style/main.css";
</style>