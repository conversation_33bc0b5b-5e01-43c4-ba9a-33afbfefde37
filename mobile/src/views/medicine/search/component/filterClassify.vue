<!-- 筛选过滤数据 -->
<template>
  <div class="filterClassify flex-left">
    <div class="unit flex-center flex-middle" v-for="(item, index) in filterData" @click="openFilterDialog(item.type)">
      <span :class="isFilterActive(item.type) ? 'fliterName active' : 'fliterName'">{{ item.name }}</span>
      <svg class="triangle back" v-if="isFilterActive(item.type)">
        <use xlink:href="#icon-zhankai1" class="yl" fill="#1464a1"></use>
      </svg>
      <svg class="triangle" v-else>
        <use xlink:href="#icon-zhankai1"></use>
      </svg>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  props: ["filterData", "filterName", "activeFilterType"],
  data() {
    return {};
  },

  created() {},

  components: {},

  computed: {},

  mounted() {
    // 这里进行图标的fill去除
    this.$nextTick(() => {
      document.querySelector("#icon-zhankai1 path").removeAttribute("fill");
    });
  },

  methods: {
    openFilterDialog(type) {
      this.$emit("openDialog", type);
    },
    
    /**
     * 判断筛选项是否处于激活状态
     * @param {string} filterType 筛选类型
     * @returns {boolean} 是否激活
     */
    isFilterActive(filterType) {
      // 如果当前正在操作某个筛选项，优先显示操作状态
      if (this.filterName === filterType) {
        return true;
      }
      
      // 检查URL参数中是否有对应的筛选条件
      return this.activeFilterType === filterType;
    },
  },
};
</script>
<style scoped lang="scss">
.filterClassify {
  width: 100%;
  height: 40px;
  background: #fff;
  border-bottom: 1px solid #eeeeee;
  .fliterName {
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    color: #666666;
    &.active {
      font-weight: 600;
      color: #1464a1;
    }
  }
  .triangle {
    width: 10px;
    height: 5px;
    margin-left: 6px;
    margin-top: 3px;
  }
  .back {
    transform: rotate(180deg);
    margin-top: 0px;
  }
}
</style>
