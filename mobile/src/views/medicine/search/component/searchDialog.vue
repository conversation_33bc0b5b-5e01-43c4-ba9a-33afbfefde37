<!-- 弹窗 -->
<template>
  <div class='dialogWrap'>
      <slot></slot>
  </div>
</template>

<script>
export default {
  name: '' ,
  data () {
    return {
    };
  },

  created(){},

  components: {},

  computed: {},

  mounted(){},

  methods: {
      
  }
}

</script>
<style  scoped>
.dialogWrap{
    position: absolute;
    left:0;
    top:0;
    z-index: 999;
    bottom:0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);
}
</style>