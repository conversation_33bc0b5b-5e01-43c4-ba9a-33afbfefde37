<!-- 共 -->
<template>
  <div class="medicineSearch flex-vertical" :class="{ carSearch: $route.query.platformId == 5 }">
    <!-- 搜索 -->
    <div class="unit-0">
      <div class="searchHeadWrap flex-left unit-0">
        <div class="searchLeft unit flex-middle">
          <div class="center_search">
            <img src="@/assets/img/search.png" alt="" />
            <iframe id="rfFrame" name="rfFrame" src="about:blank" style="display: none"></iframe>
            <form action="about:blank" class="form_search" @submit.prevent="search" target="rfFrame">
              <div class="form-search-div">
                <input type="search" v-model="searchOptions.name" autofocus="autofocus" placeholder="请输入关键字" id="searchInput" />
                <div v-show="searchOptions.name" class="clear-button" @click="clearData">
                  <svg width="8px" height="8px" viewBox="0 0 15 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                      <g id="画板备份-7" transform="translate(-572.000000, -176.000000)" fill-rule="nonzero">
                        <g id="删除" transform="translate(570.000000, 174.000000)">
                          <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="20" height="19"></rect>
                          <polygon
                            id="路径"
                            fill="#999999"
                            points="17.8947368 15.9285714 11.1278196 9.5 17.8947368 3.07142858 16.7669173 2 10 8.42857142 3.23308272 2 2.10526316 3.07142858 8.87218044 9.5 2.10526316 15.9285714 3.23308272 17 10 10.5714286 16.7669173 17"
                          ></polygon>
                        </g>
                      </g>
                    </g>
                  </svg>
                </div>
              </div>
            </form>
          </div>
        </div>
        <div class="searchRight unit-0 flex-center flex-middle">
          <div class="list-sort flex-middle">
            <!-- 列表模式的切换 -->
            <div class="list-mode-switch">
              <img v-if="listMode === 'list'" @click="handleListMode('grid')" src="https://oss.ienmore.com/frontUpload/partUpload/2025-06-17/5eed2f492d81cfbd6c8ed98a9df14358.png" alt="" />
              <img v-else @click="handleListMode('list')" src="https://oss.ienmore.com/frontUpload/partUpload/2025-06-17/d7205d3e69e2e5c8969516c79a557104.png" alt="" />
            </div>
            <div class="list-sort-btn" @click="triggerDropMenu">
              筛选
              <i class="cubeic-select"></i>
            </div>
            <cube-tip ref="dropMenu" direction="top" offsetRight="17px" class="list-drop-menu">
              <div v-for="(item, index) in typeList" :key="index" @click="changeType(item.value)" :class="{ active: selectType == item.value }">{{ item.label }}</div>
            </cube-tip>
          </div>
        </div>
        <cube-popup type="my-popup" :mask="false" content="<i style='padding:20px;background:rgba(0,0,0,.8);color:#fff'>搜索内容不能为空</i>" ref="myPopup" />
      </div>
    </div>
    <!-- 过滤 -->
    <div class="unit-0" v-if="optionData.length">
      <filter-classify @openDialog="openDialog" :filterData="optionData" :activeFilterType="activeFilterType"></filter-classify>
    </div>
    <!-- 查询结果 -->
    <div class="unit content">
      <cube-scroll v-if="liveData && liveData.length" class="item-list" ref="classifyScroll" :data="liveData" :options="options" @pulling-down="onPullingDown" @pulling-up="onPullingUp">
        <!-- 网格模式渲染 -->
        <component :class="currentContentComponent.wrapperClass" v-if="currentContentComponent.renderMode === 'single'" :is="currentContentComponent.component" v-bind="getSingleComponentProps()" />
        <!-- 列表组件渲染模式 -->
        <component :class="currentContentComponent.wrapperClass" v-else :is="currentContentComponent.component" v-for="(item, index) in liveData" :key="index" v-bind="getItemProps(item, index)" />
      </cube-scroll>
      <div class="noData flex-middle flex-center" v-else>暂无更多数据</div>
    </div>
    <!-- 弹窗 -->
    <filter-result
      @reset="reset"
      @goHome="goHome"
      :searchName="searchOptions.name"
      :selectOption="$route.query"
      v-show="filterController"
      @closeDialog="closeDialog"
      :filterType="filterType"
      :filterData="optionData"
      @changeTypeNew="changeTypeNew"
      :selectTypeNew="selectType"
    ></filter-result>
  </div>
</template>

<script>
import FilterClassify from "./component/filterClassify";
import FilterResult from "./component/filterResult";
import TwoInRow from "../../home/<USER>/medicalCompontent/liveListStyle/twoInRow";
import NewLiveItem from "@/components/common/newLiveItem.vue";
import { getSearchList, selectMoreItemList, getSelectConfig } from "@/api/medicine/homePage";

// 常量配置
const COMPONENT_CONFIG = {
  live: {
    list: {
      wrapper: "div",
      wrapperClass: "component-live list-list",
      component: "new-live-item",
      props: (item, index) => ({ itemData: item, isSearch: true }),
    },
    grid: {
      wrapper: "div",
      wrapperClass: "component-live grid-list",
      component: "two-in-row",
      props: (item, index, allData) => ({ list: allData, isShowWatchBtn: false }),
      renderMode: "single", // 表示只渲染一次，不是每个item一次
    },
  },
};

export default {
  name: "",
  data() {
    return {
      liveData: [],
      options: {
        pullUpLoad: {
          threshold: 50,
          txt: {
            more: "",
            noMore: "没有更多信息啦！",
          },
        },
        pullDownRefresh: {
          threshold: 60,
          stopTime: 1000,
          txt: "更新成功",
        },
        scrollbar: false,
      },
      pageData: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      filterController: false,
      searchOptions: {
        name: "",
        channelList: "",
        classifyList: "",
        order: "",
        typeList: "",
        status: null,
      },
      optionData: [
        { name: "行业", type: "channel" },
        { name: "职能", type: "classify" },
        { name: "类型", type: "type" },
        { name: "排序", type: "order" },
      ],
      filterType: "",
      typeList: [
        { value: "all", label: "全部" },
        { value: 1, label: "仅看收费直播" },
        { value: 0, label: "仅看免费直播" },
      ],

      selectType: "all",
      dropMenuShow: false,
      listMode: "list",
      isDestroyed: false,
    };
  },

  created() {
    // 初始化组件
    this.initializeComponent();
  },

  components: {
    FilterClassify,
    FilterResult,
    TwoInRow,
    NewLiveItem,
  },

  computed: {
    // 获取当前内容类型
    currentContentType() {
      // 由于只有直播相关组件，直接返回 live
      return "live";
    },

    // 获取当前组件配置
    currentContentComponent() {
      const contentType = this.currentContentType;
      const config = COMPONENT_CONFIG[contentType];

      // 如果是live类型，根据listMode选择配置
      if (contentType === "live") {
        return config[this.listMode] || config.list;
      }

      return config;
    },

    // 检查是否有激活的筛选条件
    hasActiveFilters() {
      const query = this.$route.query;
      return !!(query.channel || query.channal || query.classify || query.type);
    },

    // 获取当前激活的筛选类型
    activeFilterType() {
      const query = this.$route.query;
      if (query.channel || query.channal) return 'channel';
      if (query.classify) return 'classify';
      if (query.type) return 'type';
      return '';
    },
  },

  mounted() {},

  beforeDestroy() {
    // 组件销毁前清理，避免异步操作完成后调用已销毁组件的方法
    this.isDestroyed = true;
  },

  methods: {
    /**
     * 初始化组件
     * 设置筛选选项、初始化搜索参数、获取初始数据
     */
    initializeComponent() {
      // 设置筛选选项配置
      this.setupFilterOptions();
      // 初始化搜索参数
      this.initializeSearchOptions();
      // 加载初始数据
      this.loadInitialData();
    },

    /**
     * 设置筛选选项配置
     * 根据不同平台和模块类型配置不同的筛选项
     */
    setupFilterOptions() {
      // 根据模块类型配置筛选项
      if (!this.$route.query.modelType) {
        this.optionData = [];
        return;
      }

      // 可选：获取动态筛选配置
      // this.getSelectConfig();

      // 针对特定平台的筛选项调整
      this.adjustFilterOptionsForPlatform();
    },

    /**
     * 根据平台调整筛选选项
     * 不同平台可能有不同的筛选需求
     */
    adjustFilterOptionsForPlatform() {
      const platformId = this.$route.query.platformId;
      
      // 汽车平台的特殊处理：移除职能和类型筛选
      if (platformId == 5) {
        this.removeFilterOptions(['classify', 'type']);
      }

      // 可以在这里添加其他平台的特殊处理
      // if (platformId == 1) {
      //   // 医疗平台的特殊处理
      // }
    },

    /**
     * 移除指定的筛选选项
     * @param {Array} typesToRemove 要移除的筛选类型数组
     */
    removeFilterOptions(typesToRemove) {
      typesToRemove.forEach(type => {
        const index = this.optionData.findIndex(option => option.type === type);
        if (index !== -1) {
          this.optionData.splice(index, 1);
        }
      });
    },

    /**
     * 初始化搜索选项
     * 从路由参数中提取搜索条件
     */
    initializeSearchOptions() {
      const queryParams = this.$route.query;
      
      // 映射路由参数到搜索选项 (支持 channel 和 channal 两种拼写)
      const paramMapping = {
        channel: 'channelList',
        channal: 'channelList', // 兼容拼写错误
        classify: 'classifyList', 
        type: 'typeList'
      };

      // 设置搜索参数
      Object.keys(paramMapping).forEach(queryKey => {
        if (queryParams.hasOwnProperty(queryKey)) {
          const searchKey = paramMapping[queryKey];
          this.searchOptions[searchKey] = queryParams[queryKey];
        }
      });

      // 特殊状态参数处理
      if (queryParams.status) {
        this.searchOptions.status = 0;
      }
    },

    /**
     * 加载初始数据
     */
    loadInitialData() {
      this.getSearchList();
    },
    // 获取滚动组件引用的安全方法
    getScrollRef() {
      return this.$refs.classifyScroll;
    },
    
    // 安全调用滚动组件方法
    safeCallScrollMethod(methodName) {
      if (this.isDestroyed) return;
      
      const scrollRef = this.getScrollRef();
      if (scrollRef && typeof scrollRef[methodName] === 'function') {
        scrollRef[methodName]();
      }
    },

    onPullingDown() {
      this.pageData.pageNum = 1;
      this.liveData = [];
      this.getSearchList();
      this.$nextTick(() => {
        this.safeCallScrollMethod('forceUpdate');
      });
    },
    onPullingUp() {
      setTimeout(() => {
        if (this.liveData.length < this.total) {
          this.pageData.pageNum++;
          this.getSearchList();
          this.$nextTick(() => {
            this.safeCallScrollMethod('refresh');
          });
        } else {
          this.safeCallScrollMethod('forceUpdate');
        }
      }, 1000);
    },
    openDialog(type) {
      this.filterController = true;
      this.filterType = type;
      if (this.$refs.dropMenu) {
        this.$refs.dropMenu.hide();
      }
    },
    /**
     * 关闭筛选弹窗并应用筛选条件
     * @param {Object} searchData 筛选数据
     */
    closeDialog(searchData) {
      this.hideFilterDialog();
      this.applyFilterData(searchData);
      this.refreshSearchResults();
    },

    /**
     * 隐藏筛选弹窗
     */
    hideFilterDialog() {
      this.filterController = false;
      this.filterType = "";
    },

    /**
     * 应用筛选数据到搜索选项
     * @param {Object} searchData 筛选数据
     */
    applyFilterData(searchData) {
      if (!searchData || typeof searchData !== 'object') {
        return;
      }

      Object.keys(searchData).forEach(key => {
        const value = searchData[key];
        this.searchOptions[key] = this.formatFilterValue(value);
      });
    },

    /**
     * 格式化筛选值
     * @param {*} value 原始值
     * @returns {string|*} 格式化后的值
     */
    formatFilterValue(value) {
      // 如果是数组，转换为逗号分隔的字符串
      if (Array.isArray(value)) {
        return value.join(",");
      }
      return value;
    },

    /**
     * 刷新搜索结果
     * 重置分页并重新获取数据
     */
    refreshSearchResults() {
      this.resetPagination();
      this.clearCurrentData();
      this.getSearchList();
    },

    /**
     * 重置分页参数
     */
    resetPagination() {
      this.pageData.pageNum = 1;
    },

    /**
     * 清空当前数据
     */
    clearCurrentData() {
      this.liveData = [];
    },
    /**
     * 获取搜索列表数据
     * 支持普通搜索和自定义模块搜索
     */
    async getSearchList() {
      // 安全检查：组件是否已销毁
      if (!this.checkComponentStatus()) return;
      
      try {
        // 构建请求参数
        const requestParams = this.buildRequestParams();
        
        // 执行搜索请求
        const response = await this.executeSearchRequest(requestParams);
        
        // 再次检查组件状态
        if (!this.checkComponentStatus()) return;
        
        // 处理响应数据
        this.handleSearchResponse(response);
        
      } catch (error) {
        this.handleSearchError(error);
      }
    },

    /**
     * 检查组件状态是否可继续执行
     * @returns {boolean} 组件是否可继续执行
     */
    checkComponentStatus() {
      return !this.isDestroyed;
    },

    /**
     * 构建搜索请求参数
     * @returns {Object} 请求参数对象
     */
    buildRequestParams() {
      const baseParams = {
        platformId: this.$route.query.platformId,
        ...this.pageData,
        ...this.searchOptions
      };

      // 添加付费类型筛选
      if (this.selectType !== "all") {
        baseParams.totalPaymentType = this.selectType;
      }

      return baseParams;
    },

    /**
     * 执行搜索请求
     * @param {Object} params 请求参数
     * @returns {Promise<Object>} 响应数据
     */
    async executeSearchRequest(params) {
      const apiFunction = this.getApiFunction();
      const { data } = await apiFunction(params);
      return data;
    },

    /**
     * 根据查询类型获取对应的API函数
     * @returns {Function} API函数
     */
    getApiFunction() {
      const isCustomModule = this.$route.query.type === "custom";
      return isCustomModule ? selectMoreItemList : getSearchList;
    },

    /**
     * 处理搜索响应数据
     * @param {Object} responseData 响应数据
     */
    handleSearchResponse(responseData) {
      if (!responseData) return;

      // 更新总数
      this.total = responseData.total || 0;

      // 处理列表数据
      if (responseData.list && responseData.list.length > 0) {
        const processedList = this.processListData(responseData.list);
        this.updateLiveData(processedList);
      }
    },

    /**
     * 处理列表数据，进行数据转换和标准化
     * @param {Array} list 原始列表数据
     * @returns {Array} 处理后的列表数据
     */
    processListData(list) {
      return list.map(item => this.transformListItem(item));
    },

    /**
     * 转换单个列表项数据
     * @param {Object} item 原始数据项
     * @returns {Object} 转换后的数据项
     */
    transformListItem(item) {
      return {
        ...item,
        coverImg: item.bannerImg || item.coverImg, // 统一封面图片字段
        isExpand: false // 初始化展开状态
      };
    },

    /**
     * 更新直播数据列表
     * @param {Array} newData 新的数据列表
     */
    updateLiveData(newData) {
      this.liveData = this.liveData.concat(newData);
      
      // 等待DOM更新后刷新滚动组件
      this.$nextTick(() => {
        this.safeCallScrollMethod('refresh');
      });
    },

    /**
     * 处理搜索错误
     * @param {Error} error 错误对象
     */
    handleSearchError(error) {
      console.error('获取搜索列表失败:', error);
      
      // 可以在这里添加用户友好的错误提示
      // 例如：this.$toast('获取数据失败，请稍后重试');
      
      // 可以根据错误类型进行不同处理
      if (error.code === 'NETWORK_ERROR') {
        // 网络错误处理
      } else if (error.code === 'AUTH_ERROR') {
        // 认证错误处理
      }
    },
    /**
     * 执行搜索
     * 重置分页并重新获取数据
     */
    search() {
      this.refreshSearchResults();
    },
    async getSelectConfig() {
      let { data } = await getSelectConfig({ type: "live" });
      if (!data.channel) {
        this.optionData.splice(0, 1);
      }
      if (!data.classify) {
        this.optionData.splice(1, 1);
      }
    },
    changeExpand(index) {
      this.$set(this.liveData[index], "isExpand", !this.liveData[index]["isExpand"]);
    },
    clearData() {
      this.searchOptions.name = "";
      this.search();
    },
    goHome() {
      let { bizId, domainType, platformId, modelType } = this.$route.query;
      if (modelType == "live") {
        this.$router.push({ path: `/home/<USER>/0/0?platformId=${platformId}&bizId=${bizId}&domainType=${domainType}` });
      } else {
        this.$router.push({ path: `/home/<USER>/0/0?platformId=${platformId}&bizId=${bizId}&domainType=${domainType}` });
      }
    },
    /**
     * 重置搜索条件
     * 清空所有筛选条件并重新搜索
     */
    reset() {
      this.resetSearchOptions();
      this.hideFilterDialog();
      this.search();
    },

    /**
     * 重置搜索选项到初始状态
     */
    resetSearchOptions() {
      this.searchOptions = {
        name: "",
        channelList: "",
        classifyList: "",
        order: "",
        typeList: "",
        status: null,
      };
    },
    changeType(val) {
      this.selectType = val;
      this.search();
      this.dropMenuShow = !this.dropMenuShow;
    },
    // 排序下拉菜单 显示 / 隐藏
    triggerDropMenu() {
      if (!this.$refs.dropMenu) return;
      
      if (this.dropMenuShow) {
        this.$refs.dropMenu.hide();
      } else {
        this.$refs.dropMenu.show();
      }
      this.dropMenuShow = !this.dropMenuShow;
    },
    changeTypeNew(val) {
      console.log("pppp");
      this.selectType = val;
      this.search();
    },
    handleListMode(mode) {
      this.listMode = mode;
    },

    /**
     * 获取组件的props
     * @param {Object} item - 数据项
     * @param {number} index - 索引
     */
    getItemProps(item, index) {
      const config = this.currentContentComponent;

      if (!config || !config.props) {
        return {};
      }

      // 如果是函数，调用函数获取props
      if (typeof config.props === "function") {
        return config.props(item, index, this.liveData);
      }

      // 如果是对象，直接返回
      return config.props;
    },

    /**
     * 获取单个组件的props（用于renderMode为single的情况）
     */
    getSingleComponentProps() {
      const config = this.currentContentComponent;

      if (!config || !config.props) {
        return {};
      }

      // 如果是函数，调用函数获取props，传入第一个item作为参考
      if (typeof config.props === "function") {
        return config.props(this.liveData[0] || {}, 0, this.liveData);
      }

      // 如果是对象，直接返回
      return config.props;
    },
  },
};
</script>
<style scoped lang="scss">
.medicineSearch {
  position: relative;

  .content {
    overflow-y: scroll;
    padding-top: 5px;
    .grid-list {
      padding: 10px 15px;
    }
    .list-list {
      padding-top: 10px;
    }
  }
  &.carSearch {
    :deep .filterClassify .fliterName.active,
    :deep .list-sort .list-drop-menu .cube-tip-content > div.active {
      color: #5a2e84;
    }
    :deep use.yl {
      fill: #5a2e84;
    }
    :deep .filter-result .fliter-content .bottomGroup .reset {
      color: #5a2e84;
      background: #e7dfee;
    }
    :deep .filter-result .fliter-content .bottomGroup .sure {
      background: #5a2e84;
    }
  }
}

.grid-list {
  padding: 0 15px;
}

$color: #004da1;
// .header_wrap {
//     padding: 9px 15px 0;
//     width: 100%;
//     display: flex;
//     justify-content: center;
//     align-items: center;
// }
// .header_input {
//     height: 36px;
//     background: #f3f3f3;
//     border-radius: 4px;
//     display: flex;
//     padding-left: 10px;
//     justify-content: left;
//     align-items: center;
//     flex: 8;
// }
// .iconSearch {
//     width: 16px;
//     height: 16px;
//     margin-right: 7px;
// }
// input {
//     line-height: 22px;
//     font-size: 16px;
//     color: #999;
//     font-weight: 400;
//     width: 300px;
//     background: none;
//     outline: none;
// }
.noData {
  font-size: 14px;
  color: #999;
  height: 100%;
}

.searchHeadWrap {
  margin-top: 10px;
  margin-bottom: 5px;
  width: 100%;
  padding: 0 15px;
  background-color: #fff;
}

.searchLeft {
}

.iconArrow {
  width: 9.5px;
  height: 17px;
  background: url("../../../assets/img/leftArrow.png") no-repeat;
  background-size: 100%;
}

.searchRight {
  box-sizing: border-box;
  .list-sort-btn {
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    margin-left: 10px;
  }
  i {
    font-size: 14px !important;
    color: #333333;
   
  }
  .list-mode-switch {
    margin-left: 9px;
    img {
      width: 16px;
      height: 16px;
    }
  }
}

.center_search {
  height: 36px;
  width: 100%;
  display: flex;
  align-items: center;
  background: #f1f2f3;
  border-radius: 4px;
}

.center_search img {
  width: 12px;
  height: 12px;
  margin-right: 9px;
  vertical-align: middle;
  margin-left: 13px;
}

#searchInput {
  outline: none;
  background: transparent;
  border: none;
  font-size: 14px;
  line-height: 22px;
  width: 90%;
  padding-left: 2px;
}

input::-webkit-search-cancel-button {
  display: none;
}

input[type="search"]::-ms-clear {
  display: none;
}

#searchInput::placeholder {
  font-size: 16px;
  line-height: 18px;
  color: #999999;
  transform: translateY(2px);
}

.form_search {
  width: 100%;
}

.form-search-div {
  width: 100%;
  display: flex;
  align-items: center;
  height: 22px;
}

.search-button {
  margin-left: 8px;
  height: 35px;
  width: 35px;
  line-height: 35px;
  text-align: right;
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}
</style>
<style lang="scss">
.list-sort {
  position: relative;
  .list-sort-btn {
    margin-left: 5px;
    color: rgba(153, 153, 153, 1);
    img {
      width: 24px;
      height: 24px;
    }
  }
  .list-drop-menu {
    width: 200px;
    position: absolute;
    top: 33px;
    right: 0;
    background: #fff;
    max-height: none;
    border-radius: 4px;
    padding: 0;
    box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
    .cube-tip-close {
      display: none;
    }
    .cube-tip-angle {
      top: 1px;
      right: 12px !important;
      &::before {
        border-width: 0;
        width: 9px;
        height: 9px;
        background: #fff;
        transform: rotate(45deg);
        box-shadow: 0 -8px 10px 0px rgba(0, 0, 0, 0.1);
      }
    }
    .cube-tip-content {
      line-height: 38px !important;
      color: #999;
      font-size: 14px;
      z-index: 1;
      white-space: nowrap;
      & > div {
        padding: 0 16px;
        & + div {
          border-top: 1px solid rgba(0, 0, 0, 0.06);
        }
        &.active {
          color: #00629f;
        }
      }
    }
  }
}
</style>
