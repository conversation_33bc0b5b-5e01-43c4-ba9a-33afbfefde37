<template>
  <!-- 关于易贸汽车   -->
<div>
  <div class="about-car-container">
    <component v-for="item in layout" :key="item.id" :is="modelList[item.subMenu]" :configuration='item'></component>
  </div>
    <!-- 预览环境显示遮罩层，不能点击 -->
    <div class="PreviewEnviroment" v-if="isPreviewEnviroment"></div>
</div>
</template>

<script>
import swiperImage0513 from '@/views/home/<USER>/medicalCompontent/swiperImage'
import customPic0513 from '@/views/home/<USER>/medicalCompontent/customPic'
import { getOfficialMenuMedicine } from '@/api/medicine/homePage'
export default {
  data() {
    return {
      layout: [],
      modelList: {
        1: 'swiperImage0513',
        1000: 'customPic0513'
      }
    }
  },
  mounted() {
    this.getOfficialMenuMedicine()
    // 监听变化
    window.addEventListener("message", (ev) => {
      this.getOfficialMenuMedicine();
    });
  },
  methods: {
    // 获取页面布局
    getOfficialMenuMedicine() {
      getOfficialMenuMedicine({menu: 657}).then(res => {
        if (res.code == this.$successCode) {
          this.layout = res.data;
        }
      })
    },
  },
  components: {
    swiperImage0513,
    customPic0513
  }
}
</script>

<style lang="scss" scoped>
.about-car-container {
  padding-top: 15px;
  height: 100%;
  overflow: auto;
}
.PreviewEnviroment {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #ff000000;
  top: 0;
}
</style>