<template>
  <div class="ActivityHome">
    <cube-scroll class="list-scroll" ref="scroll" :data="list" :options="infiniteLoadLast == 1 ? options : optionsFalse" @pulling-up="selectLoadingActivityList">
      <div class="activity-home" v-if="pageShow">
        <div class="activity-home-scroll">
          <searchTop0513></searchTop0513>
          <component
            :ref="'component' + index"
            v-for="(item, index) in layout"
            :key="index"
            :is="assemblyType[item.subMenu].type"
            :hasIcon="assemblyType[item.subMenu].hasIcon ? assemblyType[item.subMenu].hasIcon : null"
            :type="assemblyType[item.subMenu].systemType ? assemblyType[item.subMenu].systemType : null"
            :configuration="item"
            :dataType="assemblyType[item.subMenu].dataType ? assemblyType[item.subMenu].dataType : null"
            :lastLayoutId="lastLayoutId"
            :infiniteLoadLast="infiniteLoadLast"
            layoutIdField="id"
            @refreshScroll="refreshScroll"
          >
          </component>

          <!-- 超管预览模式 禁止点击的遮罩层 -->
          <div v-if="isPreviewEnviroment" class="medicine-activity-preview-mask"></div>
        </div>
      </div>
    </cube-scroll>
  </div>
</template>

<script>
import "./component";
import customModular from "../../home/<USER>/medicalCompontent/customModular";
import recommend from "../../home/<USER>/medicalCompontent/recommend";
import customPic from "../../home/<USER>/medicalCompontent/customPic";
import swiperImage from "../../home/<USER>/medicalCompontent/swiperImage";
import information from "../../home/<USER>/medicalCompontent/information";
import pics from "../../home/<USER>/medicalCompontent/pics";
import { getOfficialMenuMedicine, getInfiniteLoad, selectLoadingItemList, getHomePageActivityList, getCustomLiveActivityRel, selectLoadingItemActivityList } from "@/api/medicine/homePage";

import { getActivityHomeList, selectLoadingActivityList } from "@/api/medicine/activity";
export default {
  name: "activity",
  data() {
    return {
      layout: [],
      soonList: [], // 即将召开 列表
      hotList: [], // 热门活动  列表
      hotListShowAll: false,
      pageShow: true, // 用于刷新页面
      infiniteLoad: 1, //是否开启上拉加载
      lastLayoutId: 0,
      lastLayout: {},
      options: {
        // 上拉加载设置
        pullUpLoad: {
          threshold: 30,
          visible: false,
          txt: {
            more: "加载更多数据",
            noMore: "没有更多了！",
          },
        },
      },
      optionsFalse: {
        // 上拉加载设置
        pullUpLoad: false,
      },
      page: {
        pageNum: 1,
        pageSize: 10,
        total: 10,
      },
      customList: [],
      list: [],
      itemIdList: [],
      homePageItem: {}, // 最后一个模块的数据
      assemblyType: {
        1: {
          type: "swiperImage", //banner
        },
        3: {
          type: "swiperImage", //banner
        },
        5: {
          type: "information",
        },
        13: {
          type: this.$route.query.platformId == 5 ? "classifyCar0513" : "classify0513", //活动分类设置 组件名字
          url: "getFunctionalList",
          hasIcon: true,
          systemType: "channel",
        },
        14: {
          type: this.$route.query.platformId == 5 ? "classifyCar0513" : "classify0513", //行业分类设置
          url: "getIndustryList",
          systemType: "classify",
        },
        15: {
          type: "recommend", //即将召开
          dataType: "activity", // 类型，是直播还是活动
        },
        16: {
          type: "recommend", //热门活动
          dataType: "activity",
        },
        28: {
          type: "recommend", //往期回顾
          dataType: "activity",
        },
        1001: {
          type: "customModular", //自定义活动模块
          dataType: "activity",
        },
        1000: {
          type: "customPic",
        },
        1002: {
          type: "pics",
        },
      },
      activityModels: [
        // 活动 相关的模块
        15, // 即将召开
        16, // 热门会展
        24, // 线下会议
        28, // 往期回顾
      ],
      liveActivityModels: [
        // 直播+活动 相关的模块
        1001, // 自定义活动模块（直播+活动）
      ],
    };
  },
  inject: ["getAdvertisement"],
  computed: {
    infiniteLoadLast() {
      // index是热门活动  index-1是即将召开
      if (this.lastLayout.status == 1 && this.lastLayout.listStyle != "carousel") {
        return this.infiniteLoad;
      } else {
        return 0;
      }
    },
  },
  components: {
    customModular,
    recommend,
    customPic,
    swiperImage,
    information,
    pics
  },
  mounted() {
    let that = this;
    this.getOfficialMenu();
    this.getInfiniteLoad();
    // 监听变化
    window.addEventListener("message", (ev) => {
      this.pageShow = false;
      this.$nextTick(() => {
        this.pageShow = true;
        that.getOfficialMenu();
      });
    });
    if (!this.isPreviewEnviroment) {
      // 埋点
      this.enterExposure("activityHome", this.$route.query.platformId, "activity", [{ id: this.$route.query.platformId, type: "activity" }]);
    }
  },
  beforeRouteLeave(to, from, next) {
    if (!this.isPreviewEnviroment) {
      this.computTimeDiff("activityHome", this.$route.query.platformId, "activity", [{ id: this.$route.query.platformId, type: "activity" }]);
    }
    next();
  },
  methods: {
    // 获取页面布局
    getOfficialMenu() {
      getOfficialMenuMedicine({ menu: this.$route.query.platformId == 5 ? 503 : 3 }).then((res) => {
        if (res.code == this.$successCode) {
          this.layout = res.data.filter((item) => {
            return item.status == 1 && item.existData != 0;
          });
          // 自定义模块
          this.customList = this.layout.filter((i) => {
            return i.fixed == 0;
          });
		  this.getAdvertisement('activity')
          let lastIndex = this.layout.length - 1;
          if (this.activityModels.includes(this.layout[lastIndex].subMenu) || this.liveActivityModels.includes(this.layout[lastIndex].subMenu)) {
            this.lastLayoutId = this.layout[lastIndex].id;
            this.lastLayout = this.layout[lastIndex];
            // 最后一个模块  要获取补全的itemid和sort,上拉的时候用的
            this.getHomePageItemList();
          }
        }
      });
    },
    getInfiniteLoad() {
      getInfiniteLoad({ menu: this.$route.query.platformId == 5 ? 503 : 3 }).then((res) => {
        if (res.code == this.$successCode) {
          this.infiniteLoad = res.data.infiniteLoad;
        } else {
          this.$message.error(res.info);
        }
      });
    },

    // 获取itemIdList 用下拉加载
    getHomePageItemList() {
      let _fun = this.liveActivityModels.includes(this.lastLayoutId) ? getCustomLiveActivityRel : getHomePageActivityList;
      _fun({ layoutId: this.lastLayoutId }).then((res) => {
        if (res.code == this.$successCode) {
          this.itemIdList = res.data.map((item) => item.itemId);
          this.homePageItem = res.data;
        } else {
          this.$message.error(res.info);
        }
      });
    },
    // 上划加载
    selectLoadingActivityList() {
      let params = {
        pageNum: this.page.pageNum,
        pageSize: this.page.pageSize,
        layoutId: this.lastLayoutId,
        addedList: this.itemIdList.join(","),
        // sort: this.homePageItem.sort,
      };
      let _fun = this.liveActivityModels.includes(this.lastLayoutId) ? selectLoadingItemActivityList : selectLoadingActivityList;
      _fun(params).then((res) => {
        if (res.code == this.$successCode) {
          let index = this.layout.findIndex((res) => {
            return res.id == this.lastLayout.id;
          });
          this.$refs["component" + index][0].updataList(res.data.list);
          // 手机端
          this.page.pageNum++;
          if (!res.data.list.length) {
            this.$refs.scroll.forceUpdate();
          } else {
            this.list = this.list.concat([{}]);
          }
        } else {
          this.$message.error(res.info);
        }
      });
    },
    // 自定义图文模块 ，图片 视频 加载完成后 刷新scroll，防止上拉 拉不动
    refreshScroll() {
      this.$refs.scroll.refresh();
    },
  },
};
</script>

<style lang="scss">
.ActivityHome {
  height: 100%;
  background: linear-gradient(180deg, #ffffff 0%, #f2f1f6 100%);
}
.activity-home {
  overflow: auto;
  height: 100%;
  // background: linear-gradient(180deg, #FFFFFF 0%, #F2F1F6 100%);
  .activity-model-title {
    color: #333;
    font-size: 18px;
    line-height: 25px;
    font-weight: 500;
  }
  .activity-home-scroll {
    position: relative;
    padding-bottom: 20px;
    .swiperImageZly {
      margin: 10px 0;
    }
    .medicine-activity-preview-mask {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
    }
  }
}
</style>
