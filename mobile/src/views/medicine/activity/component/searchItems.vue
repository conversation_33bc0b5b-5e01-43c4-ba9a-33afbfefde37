<template>
  <div class="activity-list-search-items">
    <div class="search-item-title-line">
      <div v-for="(item, key) in itemTitle" :key="key" v-show="!item.hide" class="search-item-title" :class="{ active: key == activeItem }" @click="showOptions(key)">
        <div class="search-item-title-text">{{ item.title }}</div>
        <svg class="backActivity"><use xlink:href="#icon-zhankai1"></use></svg>
      </div>
    </div>
    <cube-popup position="top" :mask-closable="true" ref="popup" @mask-click="showOptions('none')">
      <div class="search-item-options">
        <ul v-if="activeItem != 'none'">
          <li v-for="item in itemTitle[activeItem].options" :key="item.codeValueId" :class="{ activeOption: itemTitle[activeItem].checked.includes(item.codeValueId) }" @click="checkItem(item.codeValueId)">{{ item.codeValueDesc }}</li>
        </ul>
      </div>
      <div class="search-item-footer">
        <div @click="reset">重置</div>
        <div @click="showOptions('none')">确定</div>
      </div>
    </cube-popup>
  </div>
</template>

<script>
import { getOfficialMenuMedicine, getClassify } from "@/api/medicine/homePage";
export default {
  data() {
    return {
      itemTitle: {
        classifyList: { id: 1, title: "行业", hide: false, checked: [], options: [] },
        channelList: { id: 2, title: "类型", hide: false, checked: [], options: [] },
        status: {
          id: 3,
          title: "状态",
          hide: false,
          checked: [],
          options: [
            { codeValueId: 0, codeValueDesc: "未开始" },
            { codeValueId: 1, codeValueDesc: "进行中" },
            { codeValueId: 2, codeValueDesc: "已结束" },
          ],
        },
        order: {
          id: 4,
          title: "排序",
          hide: false,
          checked: ["beginTime"],
          options: [
            { codeValueId: "beginTime", codeValueDesc: "按时间排序" },
            { codeValueId: "recentView", codeValueDesc: "按热度排序" },
          ],
        },
      },
      activeItem: "none",
      platformId: this.$route.query.platformId,
    };
  },
  mounted() {
    this.getOfficialMenu();
    this.getClassify(this.platformId == 5 ? "root_system_info_car_activity_classify_publish" : "root_system_info_activity_classify_publish", "classifyList");
  },
  methods: {
    // 获取页面布局
    getOfficialMenu() {
      getOfficialMenuMedicine({ menu: this.platformId == 5 ? 503 : 3 }).then((res) => {
        if (res.code == this.$successCode) {
          let data = res.data.find((item) => {
            return item.subMenu == 13;
          });
          if (data && data.status != 1) {
            this.itemTitle.channelList.hide = true;
          } else {
            this.getClassify(this.platformId == 5 ? "root_system_info_car_activity_channel_publish" : "root_system_info_activity_channel_publish", "channelList");
          }
        }
      });
    },
    // 点击 显示、隐藏  选项弹窗
    showOptions(item) {
      if (this.activeItem == item || item == "none") {
        this.$refs.popup.hide();
        this.$emit("search");
        this.activeItem = "none";
      } else {
        this.$refs.popup.show();
        this.activeItem = item;
      }
    },
    // 重置
    reset() {
      Object.keys(this.itemTitle).forEach((key) => {
        if (key == this.activeItem) {
          let thisData = this.itemTitle[key];
          if (key == "order") {
            thisData.checked = ["beginTime"];
          } else {
            thisData.checked = [];
          }
          this.$set(this.itemTitle, key, thisData);
          this.$emit("changeSearchVal", this.itemTitle[key].checked.join(","), key);
        }
      });
      this.showOptions("none");
    },
    // 获取 行业、类型 列表
    getClassify(system, list) {
      getClassify(system).then((res) => {
        if (res.code == this.$successCode) {
          this.itemTitle[list].options = res.data;
          if (list == "channelList" && !res.data.length) {
            this.itemTitle[list].hide = true;
          }
        }
      });
    },
    // 获取链接中 行业 类型 筛选项
    getQuery() {
      if (this.$route.query.classify) {
        this.itemTitle.classifyList.checked.push(this.$route.query.classify);
        this.$emit("changeSearchVal", this.itemTitle.classifyList.checked.join(","), "classifyList");
      } else if (this.$route.query.channel) {
        this.itemTitle.channelList.checked.push(this.$route.query.channel);
        this.$emit("changeSearchVal", this.itemTitle.channelList.checked.join(","), "channelList");
      }
    },
    // 选项 选中 取消
    checkItem(codeValueId) {
      if (this.activeItem == "classifyList" || this.activeItem == "channelList") {
        // 行业   类型
        if (this.itemTitle[this.activeItem].checked.includes(codeValueId)) {
          this.itemTitle[this.activeItem].checked.splice(
            this.itemTitle[this.activeItem].checked.findIndex((item) => item == codeValueId),
            1
          );
        } else {
          this.itemTitle[this.activeItem].checked.push(codeValueId);
        }
      } else {
        // 状态  排序
        this.itemTitle[this.activeItem].checked = [codeValueId];
      }
      this.$emit("changeSearchVal", this.itemTitle[this.activeItem].checked.join(","), this.activeItem);
    },
  },
};
</script>

<style lang="scss" scope>
.activity-list-search-items {
  .search-item-title-line {
    display: flex;
    justify-content: space-around;
    border-bottom: 1px solid #eeeeee;
    .search-item-title {
      color: #333;
      font-size: 14px;
      line-height: 56px;
      height: 56px;
      width: 100%;
      text-align: center;
      &.active {
        color: #222;
        font-weight: 500;
      }
      .search-item-title-text {
        display: inline-block;
        width: 28px;
        white-space: nowrap;
      }
    }
    .search-item-title {
      &.active {
        color: #1464a1;
        .backActivity {
          transform: rotate(180deg);
          margin-top: 0px;
          fill: #1464a1;
          // fill: red;
        }
      }
    }
    svg {
      width: 8px;
      height: 8px;
      margin-left: 7px;
    }
  }
}
.cube-popup {
  top: 102px;
  .search-item-options {
    background: #fff;
    padding: 6px 10px 20px 18px;
    ul {
      li {
        min-width: 105px;
        background: #f5f6fa;
        height: 32px;
        text-align: center;
        display: inline-block;
        // padding: 0 10px;
        line-height: 32px;
        // background-color: rgba(247, 247, 247, 0.5);
        margin-right: 10px;
        margin-top: 10px;
        font-size: 13px;
        color: #666;
        border-radius: 2px;
        &.activeOption {
          background-color: rgba(20, 100, 161, 0.1);
          color: #1464a1;
        }
      }
    }
  }
  .search-item-footer {
    display: flex;
    & > div {
      width: 100%;
      text-align: center;
      line-height: 40px;
      height: 40px;
      &:nth-of-type(1) {
        background: #e7eff5;
        color: #1464a1;
      }
      &:nth-of-type(2) {
        background: #1464a1;
        color: #fff;
      }
    }
  }
}
</style>
