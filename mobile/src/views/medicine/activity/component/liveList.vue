<template>
  <div class="activity-list"  v-if="configuration && configuration.status==1 && activityList.length">
    <div class="activity-model-title">{{configuration.subMenuName}}</div>
    <div v-for="(item, index) in activityList" :key="item.id" class="activity-item" v-show="index<=1 || (index>1 && (listShowAll ||(lastLayoutId==configuration.id&&infiniteLoadLast==1)))">
      <activityItem0513 :item="item"></activityItem0513>
    </div>
  	<!-- 不展示热门活动且 关闭无限加载   就展示加载更多 -->
		<!-- listShowAll   默认显示两条，点击加载更多就显示全部 -->
    <div class="activity-load-more" @click="listShowAll=true" v-if="activityList.length&&!listShowAll && !(lastLayoutId==configuration.id&&infiniteLoadLast==1)">
      加载更多<img :src="require('@/assets/img/down.png')">
    </div>
  </div>
</template>

<script>
import { getActivityHomeList } from '@/api/medicine/activity'
import {
	getHomePageActivityList,
} from '@/api/medicine/homePage'

export default {
	props: {
	  configuration: {
	    type: Object,
	    default: res=>{
				return {}
			}
	  },
		lastLayoutId: {
	    type: String/Number,
	    default: ''
	  },
		infiniteLoadLast: {
	    type: String/Number,
	    default: ''
	  },
	},
  data () {
    return {
			activityList: [],
      listShowAll: false,
    };
  },

  created () { },

	watch: {
			configuration: {
					handler (val) {
						this.getActivityHomeList()
	          // this.configurationParam = this.configuration.param?JSON.parse(this.configuration.param):'';
					},
					deep: true,
					immediate: true
			}
	},
  components: {},

  computed: {
  },

  mounted () { },

  methods: {
		// 获取活动列表
		getActivityHomeList() {
		  getHomePageActivityList({layoutId: this.configuration.id}).then(res => {
		    if (res.code == this.$successCode) {
		      this.activityList = res.data.list;
		    }
		  })
		},
		updataList(data){
			this.activityList = this.activityList.concat(data);
		},
  }
}

</script>
<style  scoped  lang='scss'>

  .activity-list {
    margin-top: 20px;
    padding: 0 15px 5px;
    .activity-item {
      width: 100%;
      background: #fff;
      border-radius: 4px;
      margin-top: 15px;
      padding: 10px;
    }
    .activity-load-more {
      color: #666;
      font-size: 12px;
      text-align: center;
      margin-top: 10px;
      img {
        width: 10px;
        vertical-align: middle;
        margin-left: 6px;
      }
    }
  }
</style>
