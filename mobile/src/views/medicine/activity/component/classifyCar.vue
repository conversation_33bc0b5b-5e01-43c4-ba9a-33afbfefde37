<template>
  <div class="activity-classify-list-container">
    <!-- <ul :class="[hasIcon?'hasIcon':'noIcon', model]">
      <li v-for="item in list" :key="item.id" @click="toList(item.codeValueId)">
        <div v-if="hasIcon" class="icon">
          <img :src="item.codeValueDescEn">
        </div>
        <div class="title">{{item.codeValueDesc}}</div>
      </li>
    </ul> -->

    <!-- 直播和活动整好是反着来的 -->
    <div v-if="hasIcon" class="setSlideCar">
      <cube-slide :auto-play="false" @change="changePage" :data="listHasIcon">
        <cube-slide-item v-for="(iconItem, iconIndex) in listHasIcon" :key="iconIndex">
          <ul :class="['hasIcon', model]">
            <li v-for="(item, index) in iconItem" :key="item.id" @click="toList(item.codeValueId)">
              <div v-if="hasIcon" class="icon">
                <img :src="item.codeValueDescEn" />
              </div>
              <div class="title">{{ item.codeValueDesc }}</div>
            </li>
          </ul>
        </cube-slide-item>
        <template slot="dots" slot-scope="props">
          <span class="my-dot" :class="{ active: props.current === index }" v-for="(item, index) in props.dots" :key="index"></span>
        </template>
      </cube-slide>
    </div>
    <div v-else>
      <ul :class="['noIcon', model]">
        <li v-if="showMore || (!showMore && index < 7)" v-for="(item, index) in list" :key="item.id" @click="toList(item.codeValueId)">
          <div v-if="hasIcon" class="icon">
            <img :src="item.codeValueDescEn" />
          </div>
          <div class="title">{{ item.codeValueDesc }}</div>
        </li>
        <li>
          <div v-if="hasIcon" class="icon">
            <img :src="item.codeValueDescEn" />
          </div>
          <div class="title" @click="showMore = !showMore">
            {{ showMore ? "收起" : "展开" }}
            <img :src="require(`../../../../assets/img/classify_arrow_${showMore ? 'up' : 'down'}.png`)" width="15" height="15" />
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { getClassify } from "@/api/medicine/homePage";
export default {
  props: {
    hasIcon: {
      //  有没有 图标
      type: Boolean,
      default: false,
    },
    model: {
      // live   activity
      type: String,
      default: "activity",
    },
    type: {
      //  channel  classify
      type: String,
      default: "channel",
    },
  },
  data() {
    return {
      list: [],
      listHasIcon: [],
      changeStyle: false,
      carSystem: {
        //汽车
        live: {
          channel: "root_system_info_car_live_channel",
          classify: "root_system_info_car_live_classify",
        },
        activity: {
          channel: "root_system_info_car_activity_channel",
          classify: "root_system_info_car_activity_classify",
        },
      },
      showMore: false, //  是否展开
    };
  },
  mounted() {
    this.getClassify();
  },
  methods: {
    // 获取分类列表
    getClassify() {
      // getClassify(this.system[this.model][this.type] + (this.isPreviewEnviroment?'':'_publish')).then(res => {
      getClassify(this.carSystem[this.model][this.type] + "_publish").then((res) => {
        if (res.code == this.$successCode) {
          this.list = res.data;
          this.listHasIcon = [];
          for (let i = 0; i < this.list.length; i += 5) {
            this.listHasIcon.push(this.list.slice(i, i + 5));
          }
        }
      });
    },
    toList(codeValueId) {
      let query = {};
      query[this.type] = codeValueId;
      if (this.model == "activity") {
        location.assign(`${window.location.origin}${window.location.pathname}#/medicineActivityList?${new URLSearchParams(query).toString()}&platformId=${this.$route.query.platformId}&domainType=1`);
      }
    },
    changePage(current) {
      console.log(current);
      this.changeStyle = current == 1 ? true : false;
    },
  },
};
</script>

<style lang="scss" scoped>
.activity-classify-list-container {
  margin-bottom: 16px;
  .hasIcon {
    &.activity {
      padding-top: 0;
      // padding-left: 15px;
      li {
        width: 68px;
        text-align: center;
        display: inline-block;
        & + li {
          // margin-left: 21px;
        }
      }
      .icon {
        display: inline-block;
        width: 38px;
        height: 38px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .title {
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333;
        font-size: 13px;
        margin-top: 8px;
        line-height: 18px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    &.live {
      li {
        text-align: center;
        display: inline-block;
        &:nth-of-type(1) {
          margin-left: 15px;
        }
        &:nth-of-type(2) {
          // margin-left: 21px;
        }
        &:nth-of-type(3) {
          // margin-left: 16px;
        }
        &:nth-of-type(4) {
          // margin-left: 24px;
        }
        &:nth-of-type(5) {
          // margin-left: 43px;
        }
      }
      .icon {
        display: inline-block;
        width: 24px;
        height: 24px;
        svg {
          width: 24px;
          height: 24px;
        }
      }
      .title {
        color: #000;
        font-size: 13px;
        margin-top: 14px;
        line-height: 18px;
      }
    }
  }
  .noIcon {
    padding: 1px 5px 0 15px;
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    li {
      display: inline-block;
      width: 80px;
      height: 36px;
      // min-width: 79px;
      // padding: 0 8px 0 7px;
      color: #333;
      font-size: 12px;
      text-align: center;
      line-height: 36px;
      margin-right: 8px;
      margin-bottom: 8px;
      background: url("../../../../assets/img/classify_bg.png") center;
      background-size: cover;
      .title {
        border-radius: 2px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        img {
          vertical-align: middle;
        }
        &:nth-of-type(11) {
          margin-top: 20px;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.activity-classify-list-container {
  .setSlideCar {
    margin: 12px 15px 0;
    background-color: #fff;
    padding: 12px 0 9px;
    .cube-slide-dots {
      position: unset !important;
      margin-top: 9px;
    }
    .cube-slide-dots > span.active {
      background: #5a2e84 !important;
      width: 11px;
      height: 3px;
    }
    .cube-slide-dots > span {
      width: 4px;
      height: 3px;
      background: rgba(90, 46, 132, 0.2);
      margin: 0 4px;
    }
  }
}
</style>
