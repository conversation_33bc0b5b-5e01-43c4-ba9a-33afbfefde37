<template>
    <div class="header_wrap">
        <div class="header_input" :class="{searchable: !readonly}" @click="toActivityList">
            <img :src="require('@/assets/img/search_icon.png')" alt="" class="iconSearch" @click="search">
            <input ref="input" v-model="keyword" type="search" :readonly="readonly" placeholder="请输入关键字" @keyup.enter="search">
            <!-- 清除按钮 -->
            <svg v-if="keyword" width="8px" height="8px" @click="clear" viewBox="0 0 15 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <g id="画板备份-7" transform="translate(-572.000000, -176.000000)" fill-rule="nonzero">
                        <g id="删除" transform="translate(570.000000, 174.000000)">
                            <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="20" height="19"></rect>
                            <polygon id="路径" fill="#999999" points="17.8947368 15.9285714 11.1278196 9.5 17.8947368 3.07142858 16.7669173 2 10 8.42857142 3.23308272 2 2.10526316 3.07142858 8.87218044 9.5 2.10526316 15.9285714 3.23308272 17 10 10.5714286 16.7669173 17"></polygon>
                        </g>
                    </g>
                </g>
            </svg>
        </div>
        
        <div class="activity-search-cancel" v-if="!readonly" @click="cancel">取消</div>
    </div>
</template>

<script>
export default {
    props: {
        readonly: {
            type: Boolean,
            default: true
        }
    },
    data () {
        return {
            keyword: '',
        }
    },
    mounted() {
        if (!this.readonly) {
            this.$nextTick(() => {
                this.$refs.input.focus()
            })
        }
    },
    methods: {
        // 活动首页   跳转 活动列表
        toActivityList() {
            if (this.readonly) {
                this.$router.push({ name: 'medicineActivityList' })
            }
        },
        // 活动列表  搜索
        search() {
            if (!this.readonly) {
                this.$emit('search')
            }
        },
        inputBlur() {
            // 有键盘弹起状态时，影响scroll 上拉结束状态显示，需先关闭键盘
            this.$refs.input.blur()
        },
        // 重置搜索内容
        clear() {
            this.keyword = '';
            this.$nextTick(() => {
                this.search();
            })
        },
        // 取消 => 返回
        cancel() {
            this.$router.go(-1)
        }
    },
    watch: {
        keyword(val) {
            this.$emit('changeSearchVal', val, 'activityName')
        }
    }
}
</script>

<style scoped lang="scss">
$color: #004da1;
.header_wrap {
    padding: 9px 15px 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
}
.header_input {
    height: 36px;
    background: #F3F3F3;
    border-radius: 4px;
    display: flex;
    padding-left: 15px;
    justify-content: left;
    align-items: center;
    width: 100%;
    position: relative;
    &.searchable {
        width: 302px;
        input {
            width: 240px;
        }
    }
    svg {
        position: absolute;
        right: 10px;
        top: 14px;
    }
}
.activity-search-cancel {
    color: #999;
    width: 30px;
    text-align: right;
}
.iconSearch {
    width: 16px;
    height: 16px;
    margin-right: 7px;
}
input {
    line-height: 22px;
    font-size: 16px;
    color: #999;
    font-weight:400;
    width: 300px;
    background: none;
    border: none;
    outline: none;
    &[type=search]::-webkit-search-cancel-button{
        -webkit-appearance: none;  //此处去掉默认的小×
    }
}
</style>