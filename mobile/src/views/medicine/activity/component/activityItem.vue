<template>
  <div class="activity-item-container" @click="toDetail">
    <div class="activity-bannerImg-nav"><img :src="item.bannerImg" class="activity-bannerImg"></div>
    <div class="activity-content">
      <div class="activity-name">{{item.activityName || item.name}}</div>
      <div v-if="item.organizerName" class="activity-zhubanfang">{{item.organizerName}}</div>
      <div class="activity-time-address">
        <div class="activity-time">{{moment(item.beginTime).format('yyyy/MM/DD')}}</div>
        <div class="activity-address" v-show="item.address">
          <svg><use xlink:href="#icon-dingwei" class='svg-address'></use></svg>
          {{item.address}}
        </div>
      </div>
			<template v-if="item.channelList || item.typeList || item.classifyList">
        <div class="activity-tags" v-if=" (item.channelList && item.channelList.length) || (item.typeList && item.typeList.length) || ( item.classifyList && item.classifyList.length)">
          <div v-for="tag in item.channelList" :key="tag" class="activity-tag-item channel">{{tag}}</div>
          <div v-for="tag in item.typeList" :key="tag" class="activity-tag-item type">{{tag}}</div>
          <div v-for="tag in item.classifyList" :key="tag" class="activity-tag-item classify">{{tag}}</div>
        </div>
			</template>
      <!-- 新闻 -->
      <liveItemNews :itemData="item" from="activitySearch"></liveItemNews>
    </div>
    <div style="clear: both;"></div>
  </div>
</template>

<script>
import moment from 'moment'
import liveItemNews from '@/views/home/<USER>/medicalCompontent/liveListStyle/liveItemNews'
export default {
  props: {
    item: {
      type: Object,
      default:()=> {}
    }
  },
  methods: {
    moment,
    toDetail() {
      let thisItem = {
        ...this.item,
        itemId: this.item.activityId
      }
      this.naviToDetails(localStorage.getItem('platformId'), this.item.itemType, thisItem)
    }
  },
  components: {
    liveItemNews
  }
}
</script>

<style lang="scss" scope>
.activity-item-container {
  font-size: 12px;
  color: #666;
  line-height: 17px;
  .activity-bannerImg-nav {
    width: 120px;
    height: 72px;
    border-radius: 2px;
    float: left;
    overflow: hidden;
  }
  .activity-bannerImg {
    width: 120px;
    height: 72px;
  }
  .activity-content {
    margin-left: 130px;
    min-height: 72px;
    .activity-name {
      line-height: 20px;
      color: #333;
      font-size: 14px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
    }
    .activity-zhubanfang {
      margin-top: 6px;
    }
    .activity-time-address {
      margin-top: 6px;
      display: flex;
    }
    .activity-time {
      width: 45%;
    }
    .activity-address {
      width: 55%;
      color: #999;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      svg {
        width: 11px;
        height: 11px;
        vertical-align: middle;
        margin-top: -2px;
        margin-right: 3px;
        use.svg-address{
           fill: var(--color-primary);
        }
      }
    }
    .activity-tags {
      margin-top: 6px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      .activity-tag-item {
        display: inline-block;
        font-size: 11px;
        line-height: 16px;
        height: 16px;
        padding: 0 6px;
        border-radius: 1px;
        margin-right: 6px;
        &.channel {
          background: rgba(20, 100, 161, 0.1);
          color: #1464A1;
        }
        &.classify {
          color: #EB7B21;
          background: rgba(235, 123, 33, 0.1);
        }
				&.type{
					background: rgba(0, 137, 134, 0.2);
					color:rgba(0, 137, 134, 1)
				}
      }
    }
  }
}
</style>