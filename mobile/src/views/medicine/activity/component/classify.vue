<template>
  <div class="activity-classify-list-container">
    <!-- <ul :class="[hasIcon?'hasIcon':'noIcon', model]">
      <li v-for="item in list" :key="item.id" @click="toList(item.codeValueId)">
        <div v-if="hasIcon" class="icon">
          <img :src="item.codeValueDescEn">
        </div>
        <div class="title">{{item.codeValueDesc}}</div>
      </li>
    </ul> -->

    <!-- 直播和活动整好是反着来的 -->
    <div v-if="!hasIcon" class="setSlide">
      <cube-slide :auto-play="false" @change="changePage" :data="listHasIcon">
        <cube-slide-item v-for="(iconItem, iconIndex) in listHasIcon" :key="iconIndex">
          <ul :class="['noIcon', model]">
            <li v-for="(item, index) in iconItem" :key="item.id" @click="toList(item.codeValueId)">
              <div class="title">{{ item.codeValueDesc }}</div>
            </li>
          </ul>
        </cube-slide-item>
        <template slot="dots" slot-scope="props">
          <span class="my-dot" :class="{ active: props.current === index }" v-for="(item, index) in props.dots" :key="index"></span>
        </template>
      </cube-slide>
    </div>
    <div v-else>
      <ul :class="['hasIcon', model]">
        <li v-for="item in list" :key="item.id" @click="toList(item.codeValueId)">
          <div v-if="hasIcon" class="icon">
            <img :src="item.codeValueDescEn" />
          </div>
          <div class="title">{{ item.codeValueDesc }}</div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { getClassify } from "@/api/medicine/homePage";
export default {
  props: {
    hasIcon: {
      //  有没有 图标
      type: Boolean,
      default: false,
    },
    model: {
      // live   activity
      type: String,
      default: "activity",
    },
    type: {
      //  channel  classify
      type: String,
      default: "channel",
    },
  },
  data() {
    return {
      list: [],
      listHasIcon: [],
      changeStyle: false,
      system: {
        live: {
          channel: "root_system_info_medical_live_channel",
          classify: "root_system_info_medical_live_classify",
        },
        activity: {
          channel: "root_system_info_activity_channel",
          classify: "root_system_info_activity_classify",
        },
      },
    };
  },
  mounted() {
    this.getClassify();
  },
  methods: {
    // 获取分类列表
    getClassify() {
      // getClassify(this.system[this.model][this.type] + (this.isPreviewEnviroment?'':'_publish')).then(res => {
      getClassify(this.system[this.model][this.type] + "_publish").then((res) => {
        if (res.code == this.$successCode) {
          this.list = res.data;
          this.listHasIcon = [];
          for (let i = 0; i < this.list.length; i += 12) {
            this.listHasIcon.push(this.list.slice(i, i + 12));
          }
        }
      });
    },
    toList(codeValueId) {
      let query = {};
      query[this.type] = codeValueId;
      if (this.model == "activity") {
        location.assign(`${window.location.origin}${window.location.pathname}#/medicineActivityList?${new URLSearchParams(query).toString()}&platformId=${this.$route.query.platformId}&domainType=1`);
      }
    },
    changePage(current) {
      console.log(current);
      this.changeStyle = current == 1 ? true : false;
    },
  },
};
</script>

<style lang="scss" scoped>
.activity-classify-list-container {
  margin-bottom: 20px;
  padding-top: 5px;
  .hasIcon {
    text-align: center;
    margin-bottom: 10px;
    margin-top: 10px;
    &.activity {
      padding-top: 0;
      // padding-left: 15px;
      li {
        width: 70px;
        text-align: center;
        display: inline-block;
        & + li {
          // margin-left: 21px;
        }
      }
      .icon {
        display: inline-block;
        width: 38px;
        height: 38px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .title {
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333;
        font-size: 13px;
        margin-top: 8px;
        line-height: 18px;
      }
    }
    &.live {
      li {
        text-align: center;
        display: inline-block;
        &:nth-of-type(1) {
          margin-left: 15px;
        }
        &:nth-of-type(2) {
          // margin-left: 21px;
        }
        &:nth-of-type(3) {
          // margin-left: 16px;
        }
        &:nth-of-type(4) {
          // margin-left: 24px;
        }
        &:nth-of-type(5) {
          // margin-left: 43px;
        }
      }
      .icon {
        display: inline-block;
        width: 24px;
        height: 24px;
        svg {
          width: 24px;
          height: 24px;
        }
      }
      .title {
        color: #000;
        font-size: 13px;
        margin-top: 14px;
        line-height: 18px;
      }
    }
  }
  .noIcon {
    padding: 1px 5px 0 15px;
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    li {
      display: inline-block;
      height: 24px;
      // min-width: 79px;
      // padding: 0 8px 0 7px;
      color: #333;
      font-size: 12px;
      text-align: center;
      line-height: 24px;
      margin-right: 9px;
      margin-bottom: 10px;
      width: 79px;
      .title {
        border-radius: 2px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        background: #d7dada48;
        &:nth-of-type(11) {
          margin-top: 20px;
        }
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.activity-classify-list-container {
  .setSlide {
    .cube-slide-dots {
      position: unset !important;
    }
    .cube-slide-dots > span.active {
      background: rgba(20, 100, 161, 1) !important;
      width: 11px;
      height: 3px;
    }
    .cube-slide-dots > span {
      width: 4px;
      height: 3px;
      background: rgba(20, 100, 161, 0.6);
      margin: 0 4px;
    }
  }
}
</style>
