<template>
  <div>
    <div class="activity-list-container" :class="{'activity-list-container-car': $route.query.platformId == 5}">
      <!-- search bar -->
      <searchTop0513 ref="searchTop0513" :readonly="false" @changeSearchVal="changeSearchVal" @search="keywordSearch"></searchTop0513>
      <searchItems0513 ref="searchItem" @changeSearchVal="changeSearchVal" @search="search"></searchItems0513>
      <!-- list -->
      <div class="activity-list-content">
        <div v-if="list.length==0" class="no-data-tips">
          <img src="@/assets/img/no_data_icon.png" alt="">
          <div>暂无活动</div>
        </div>
        <cube-scroll v-else ref="scroll" :data="list" :options="options" @pulling-up="getActivityList">
          <div v-for="item in list" :key="item.id" class="activity-list-item">
            <activityItem0513 :item="item"></activityItem0513>
          </div>
        </cube-scroll>
      </div>
    </div>
  </div>
</template>

<script>
import './component'
import { getActivityList, getActivityListByLayoutId } from '@/api/medicine/activity'
export default {
  data() {
    return {
      hideSearchBar: false,
      list: [],
      options: { // 上拉加载设置
        pullUpLoad: {
          threshold: 30,
          // visible: false,
          txt: {
            more: '上拉加载更多',
            noMore: '没有更多信息啦！'
          }
        }
      },
      searchData: {
        order: 'beginTime'
      },
      currentPage: 0,
      total: 0
    }
  },
  mounted() {
    if (this.$route.query.channel || this.$route.query.classify) {
      this.$refs.searchItem.getQuery();
    }
    this.search();
    // 埋点
    this.enterExposure("activityList", this.$route.query.platformId, "activity", [{ id: this.$route.query.platformId, type: "activity" }]);
  },
  beforeRouteLeave (to, from, next) {
    this.computTimeDiff("activityList", this.$route.query.platformId, 'activity', [{ id: this.$route.query.platformId, type: 'activity' }]);
    next()
  },
  methods: {
    // 搜索
    search() {
      this.currentPage = 0;
      this.list = [];
      this.getActivityList();
    },
    // keyword
    keywordSearch() {
      this.$refs.searchTop0513.inputBlur();
      this.$refs.searchItem.showOptions('none');
    },
    // 搜索关键词 修改
    changeSearchVal(val, name) {
      this.searchData[name] = val;
    },
    // 获取活动列表
    getActivityList() {
      let params = {
        ...this.searchData,
        pageNum: this.currentPage + 1,
        platformId: this.$route.query.platformId
      }
      let fn = getActivityList;
      if (this.$route.query.type == 'custom') {
        // 自定义活动模块的 更多列表
        fn = getActivityListByLayoutId;
        params.layoutId = this.$route.query.layoutId;
      }
      fn(params).then(res => {
        if (res.code == this.$successCode) {
          this.list = this.list.concat(res.data.list);
          this.total = res.data.total
          this.currentPage++
        }
        // 已加载完毕  没有更多信息啦！
        this.$nextTick(() => {
          if (this.total == this.list.length && this.total!=0) {
            this.$refs.scroll.forceUpdate(false);
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-list-container {
  overflow: auto;
  .activity-list-item {
    padding: 0 14px;
    margin-top: 15px;
    .activity-content {
      padding-bottom: 10px;
      border-bottom: 1px solid #EEEEEE;
    }
  }
  .no-data-tips {
    > img {
      width: 185px;
      height: 142px;
    }
    > div {
      margin-top: 20px;
    }
    margin-top: 170px;
    text-align: center;
    color: #999;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-wrap: wrap;
  }
  .activity-list-content {
    height: calc(100vh - 100px);
    .cube-scroll-wrapper {
      overflow: auto;
    }
  }

}  
.activity-list-container-car{
  :deep .activity-list-search-items .search-item-title-line .search-item-title.active .backActivity{
    fill: #5A2E84;
  }
  :deep .cube-popup .search-item-footer > div:nth-of-type(1){
    color: #5A2E84;
    background: #e7dfee;
  }
  :deep .cube-popup .search-item-footer > div:nth-of-type(2){
    background: #5A2E84;
  }
  :deep .cube-popup .search-item-options ul li.activeOption{
    color: #5A2E84;
  }
}
</style>