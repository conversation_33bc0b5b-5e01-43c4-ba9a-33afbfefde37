<template>
    <div class="questionnaire-wrap">
        <scroll ref="betterScroll" class="scroll-wraps" :data="formData">
            <div>
                <div class="questionnaire-title">
                    <div class="main-title">
                        {{isQuestionnaireStatus?'提交成功，感谢您的建议！':'合作问卷'}}
                    </div>
                    <div class="sub-title">Hi，易贸的小伙伴，终于等到你~</div>
                </div>
                <div class="questionnaire-content" v-if="!isQuestionnaireStatus">
                    <form-item @changeValue="changeValue" @clearCurrentFocus="clearCurrentFocus" v-for="item,index in formData" :key="index" :itemData="item" :currentIndex="index" @changeCurrentFocus="changeCurrentFocus"></form-item>
                </div>
                <div class="submit-content flex-center" v-if="!isQuestionnaireStatus">
                    <div class="submitBtn" @click="submit">提交</div>
                </div>
                <div class="submit-succes flex-vertical flex-center" v-if="isQuestionnaireStatus">
                    <div class="submit-content-code flex-center flex-middle">
                        <img :src="require('@/assets/img/questionCode.png')" alt="">
                    </div>
                    <div class="submit-text">
                        长按添加小易备注“填写问卷”领取多种超值福利！
                    </div>
                </div>
            </div>
        </scroll>
    </div>
</template>
<script>
import FormItem from './component/formItem';
import { postQuestionNaire } from '@/api/medicine/questionnaire';
import scroll from '@/components/common/scroll';
export default {
    data () {
        return {
            // 定义表单的数据
            formData: [
                { label: '您所在机构名称', value: '', key: 'company', verifyStatus: true, type: 'input', isFocus: false, errorTip: '请补全必填项' },
                { label: '您的职位', value: '', key: 'position', verifyStatus: true, type: 'input', isFocus: false, errorTip: '请补全必填项' },
                { label: '您的姓名', value: '', key: 'name', verifyStatus: true, type: 'input', isFocus: false, errorTip: '请补全必填项' },
                { label: '您的电话号码', value: '', key: 'phone', verifyStatus: true, type: 'input', isFocus: false, errorTip: '请补全必填项' },
                { label: '您是否之前和易贸有合作过？合作形式是', value: '', key: 'cooperation', verifyStatus: true, type: 'input', isFocus: false, errorTip: '请补全必填项' },
                { label: '期待和易贸有哪些形式的合作？', value: '', key: 'nextCooperation', verifyStatus: true, type: 'input', isFocus: false, errorTip: '请补全必填项' },
                { label: '您期待通过合作为机构收获什么？', value: '', key: 'harvest', verifyStatus: true, type: 'input', isFocus: false, errorTip: '请补全必填项' },
                { label: '是否需要小易联系您', value: '', key: 'contactFlag', verifyStatus: true, type: 'radio', isFocus: false, errorTip: '请补全必填项' },
            ],
            // 是否提交过问卷
            isQuestionnaireStatus: false
        }
    },
    components: {
        FormItem,
        scroll
    },
    mounted () {

    },
    computed: {

    },
    methods: {
        changeCurrentFocus (index) {
            this.formData.forEach((item) => {
                item.isFocus = false;
            })
            this.$set(this.formData[index], 'isFocus', true);
        },
        clearCurrentFocus (index) {
            this.$set(this.formData[index], 'value', '');
        },
        changeValue ({ index, value }) {
            this.$set(this.formData[index], 'value', value);
            this.$set(this.formData[index], 'verifyStatus', true);
        },
        async submit () {
            let flag = true;
            this.formData.forEach((item, index) => {
                if (item.value === '') {
                    this.$set(this.formData[index], 'verifyStatus', false);
                    flag = false;
                    return;
                }

                if (item.key == 'phone') {
                    let reg = /^1[3-9][0-9]{9}$/;
                    if (!reg.test(item.value)) {
                        this.$set(this.formData[index], 'verifyStatus', false);
                        this.$set(this.formData[index], 'errorTip', '请输入正确电话号码');
                        flag = false;
                        return;
                    }
                }
            })

            if (flag) {
                let params = {
                    itemId: 0,
                    source: "bio",
                    platformId: 3,
                    itemType: "activity",
                    company: '',
                    position: '',
                    name: '',
                    phone: '',
                    cooperation: '',
                    nextCooperation: '',
                    harvest: '',
                    contactFlag: '',
                };
                this.formData.forEach((item, index) => {
                    params[item.key] = item.value
                })
                // console.log(params);
                await postQuestionNaire(params);

                this.isQuestionnaireStatus = true;

            } else {
                this.$nextTick(() => {
                    let scrollEl = document.querySelector('.formItem-error');
                    let elH;
                    if(scrollEl.parentNode.previousElementSibling){
                        elH = scrollEl.parentNode.previousElementSibling;
                    }else{
                        elH = scrollEl.parentNode.parentNode.previousElementSibling;
                    }
                    this.$refs.betterScroll.scrollToElement(elH,100)
                
                })
            }

        },
    },
}
</script>
<style scoped lang="scss">
.questionnaire-wrap {
    width: 100%;
    height: 100%;
    .scroll-wraps {
        height: 100%;
        overflow: hidden;
        .questionnaire-title {
            margin-bottom: 24px;
            padding-top: 20px;
            .main-title {
                margin-bottom: 10px;
                width: 100%;
                text-align: center;
                height: 25px;
                font-size: 18px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #333333;
                line-height: 25px;
            }
            .sub-title {
                height: 20px;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #666666;
                line-height: 20px;
                text-align: center;
            }
        }
        .questionnaire-content {
            width: 100%;
            padding: 0 24px;
        }
        .submit-content {
            width: 100%;
            text-align: center;
            padding-bottom: 32px;
            .submitBtn {
                width: 120px;
                height: 40px;
                background: #097db4;
                border-radius: 2px;
                text-align: center;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 40px;
            }
        }
        .submit-succes {
            margin-top: 100px;
            .submit-content-code {
                width: 160px;
                height: 160px;
                background: url(../../../assets/img/questionBg.png) no-repeat;
                background-size: cover;
                img {
                    width: 145px;
                    height: 145px;
                }
            }
            .submit-text {
                width: 198px;
                height: 40px;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #666666;
                line-height: 20px;
                text-align: center;
                margin-top: 20px;
            }
        }
    }
}
</style>
