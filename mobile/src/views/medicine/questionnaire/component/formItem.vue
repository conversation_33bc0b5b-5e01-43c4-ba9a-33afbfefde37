<template>
    <div class="formItem-content flex-vertical">
        <div class="formItem-label flex-left flex-middle">
            <img :src="require('@/assets/img/mandatory.png')" alt="">
            <span class="formItem-name">{{formItemData.label}}</span>
        </div>
        <div :class="['formItem-input','flex-middle',{'focusBorder':formItemData.isFocus && formItemData.verifyStatus},{'errorBorder':!formItemData.verifyStatus}]"  v-if="formItemData.type == 'input'" @click="clickInput">
            <input @change="changeValue" type="text" class="unit" v-model="formItemData.value" >
            <img :src="require('@/assets/img/close.png')" alt="" class="unit-0" v-show="(formItemData.value && formItemData.isFocus)" @click="clearCurrentData">
        </div>
        <div class="formItem-radio-content" v-else>
            <cube-radio-group v-model="formItemData.value" :options="options" class="formItem-radio" @input="changeValue"/>
        </div>
        <div class="formItem-error" v-if="!formItemData.verifyStatus">
            {{formItemData.errorTip}}
        </div>
        <div class="formItem-space" v-if="(formItemData.verifyStatus && currentIndex!=7)"></div>
    </div>
</template>
<script>
export default {
    props: ['itemData','currentIndex'],
    data () {
        return {
            formItemData: {},
            options: [
                {
                    label: '是',
                    value: 1
                },
                {
                    label: '否',
                    value: 0
                }
            ]
        }
    },
    mounted () {
        // 数据经过拷贝下
        this.copyData();
    },
    computed: {

    },
    watch:{
        itemData:{
            deep:true,
            handler:function(){
               this.copyData();
            }
        } 
    },
    methods: {
        clickInput(){
            this.$emit('changeCurrentFocus',this.currentIndex);

        },
        clearCurrentData(){
            this.$emit('clearCurrentFocus',this.currentIndex);
        },
        copyData(){
            this.formItemData = JSON.parse(JSON.stringify(this.itemData));
        },
        changeValue(){
            this.$emit('changeValue',{index:this.currentIndex,value:this.formItemData.value});
        }
    },
}
</script>
<style scoped lang="scss">
.formItem-content {
    .formItem-label {
        margin-bottom: 10px;
        margin-left: -6px;
        img {
            width: 18px;
            height: 18px;
        }
        .formItem-name {
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #333;
            line-height: 16px;
        }
    }
    .formItem-input {
        width: 100%;
        height: 42px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #e3e3e3;
        padding: 0 16px;
        img {
            width: 18px;
            height: 18px;
            margin-left: 6px;
        }
        input {
            height: 18px;
            line-height: 18px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
        }
    }
    .formItem-radio-content {
        .formItem-radio {
            margin-left: -30px;
        }
        .border-bottom-1px::before{
            border: none!important;
        }
       
        .border-top-1px::after{
            border: none!important;
        }
       :deep(.cube-radio .border-bottom-1px::after){
            border: none!important;
        }
        :deep(.cube-radio-wrap){
            padding-bottom: 10px;
            padding-top: 0px;
        }
        :deep(.cube-radio_selected .cube-radio-ui){
            background-color: var(--color-primary);
        }
        :deep(.cube-radio-group){
            padding-bottom: 0;
        }
    }
    .formItem-error {
        height: 16px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ff4d4f;
        line-height: 16px;
        margin-top: 2px;
        margin-bottom: 14px;
    }
    .formItem-space {
        height: 32px;
    }
}
.errorBorder{
    border: 1px solid #FF4D4F !important;
}
.focusBorder{
    border: 1px solid var(--color-primary) !important;
}
</style>
