<template>
  <!-- 单个企业直播间 的 直播列表 -->
  <div class="enterprise-live-room" :class="{'enterprise-live-room-car': $route.query.platformId == 5}">
    <div class="enterprise-live-header">
      <div class="header-platform" @click="homePage">
        <!-- <img :src="platformInfo.platformIcon">{{platformInfo.platformName}} -->
        <img :src="platformInfo.platformIcon">{{title}}
      </div>
      <div class="header-right">
        <span class="header-collect" @click="collectLiveRoom">{{collectFlag==0?'收藏':'取消收藏'}}</span>
        <span @click="toUserCenter">个人中心</span>
      </div>
    </div>
    <div class="live-room-top">
      <img :src="liveRoomInfo.logo">
      <p :style="!liveRoomInfo.exhibitorId ? 'width: 140px;':'width: 100px;'">{{liveRoomInfo.name}}</p>
      <div class="live-room-introduce-btn" @click="showDesc" :style="!liveRoomInfo.exhibitorId ? 'width: 40px;':'width: 75px;'">{{ !liveRoomInfo.exhibitorId ? "介绍":"去店铺主页" }}</div>
    </div>
    <div class="live-room-list">
      <cube-scroll ref="scroll" :data=" liveList" :options="options" @pulling-up="getLiveList">
        <div class="top_live_class" v-if="liveRoomInfo.exhibitorId">
          <div class="live_class_item">
            <div class="live_img_class">
              <img :src="liveForm.logoUrl">
            </div>
            <div class="live_text_class">
              <div class="title_class hidden_text_class">{{ liveForm.shopName }}</div>
              <div class="hidden_text_class">公司名：{{liveForm.busName}}</div>
            </div>
          </div>
          <div class="border_class"></div>
          <div class="bot_live_class">
            <img src="@/assets/img/<EMAIL>" />
            <p class="hidden_text_class">
              所在地：{{liveForm.resLocation}}
            </p>
          </div>
        </div>
        <div class="live_root_class" v-if="liveRoomInfo.exhibitorId">
          <div class="live_title_class">
            <div class="live_title_left">推荐产品</div>
            <div @click="jumpBtn" class="more_title_text">
              <span>更多</span>
              <img src="@/assets/img/路径 2备份 <EMAIL>" />
            </div>
          </div>
          <div class="displ_class" :style="exhList.length ? 'justify-content: space-between;':'justify-content: center;'">
            <div v-if="exhList.length" v-for="(item,index) in exhList" class="live_one_class">
              <div class="live_img_class">
                <img :src="item.picUrl">
              </div>
              <div class="bot_title_class hidden_text_class" style="-webkit-line-clamp: 2;" @click="jumpTwoBtn(item)">
                <span class="tag_class">国产</span>
                <span class="tag_text_class">{{item.name}}</span>
              </div>
            </div>
            <img class="no_data_img" v-if="exhList.length == 0" src="@/assets/img/Group <EMAIL>" alt="">
          </div>
        </div>
        <div v-if=" liveList.length == 0" class="no-data-tips" :style="liveRoomInfo.exhibitorId ? 'margin-top: 15px;':'margin-top: 130px;background-color: #F5F5F5;'">
          <img src="@/assets/img/notNothingPng.png" alt="">
          <span style="margin-top: 10px;">
            {{ liveList.length == 0 && !liveRoomInfo.exhibitorId ? "暂无数据":"暂无直播" }}
          </span>
        </div>
        <liveItem v-else class="live_item_class" :liveList=" liveList"></liveItem>
      </cube-scroll>
    </div>
    <div class="footer">
      <div class="ch">
        <img :src="require(`@/assets/${$route.query.platformId}/Icon_logo.png`)">
        <p>提供技术支持</p>
      </div>
    </div>

    <!-- 介绍 -->
    <liveRoomIntroduction v-if="showDescDialog" :info="liveRoomInfo" @click.stop @click.native="showDescDialog=false"></liveRoomIntroduction>
  </div>
</template>

<script>
import { getLiveRoomInfo, getCollectFlag, collectLiveRoom, getLiveList, getLiveRoomShareConfig,getPlatformInfo, selExhibitorDetails, selExhibitorDetailsRePro } from '@/api/medicine/enterpriseLiveRoom'
import liveItem from './components/liveItem';
import liveRoomIntroduction from './components/liveRoomIntroduction';
import CONSTANT from '@/config/config_constant';
import {exhibitorClientUrl} from '@/config/env'
export default {
  data() {
    return {
      exhList: [],
      liveForm: {},
      liveRoomInfo: {},
      liveList: [],
      collectFlag: 0,  // 是否收藏
      showDescDialog: false,
      options: { // 上拉加载设置
        pullUpLoad: {
          threshold: 30,
          visible: false,
          txt: {
            more: '上拉加载更多',
            noMore: '没有更多信息啦！'
          }
        }
      },
      currentPage: 1,
      total: 0,
      platformInfo: JSON.parse(localStorage.getItem(CONSTANT.USERINFO)), // 公众号信息
      title:''
    }
  },
  created() {
    // 企业直播间 埋点  用自己的 moduleId 和 moduleType
    let paramsData = {
      ...this.$store.state.dataActionStore,
      moduleId: this.$route.params.id,
      moduleType: "liveRoom",
      businessId: this.$route.params.id,
      businessType: "liveRoom",
    }
    this.$store.commit("changeSystemInfo", paramsData);
  },
  mounted() {
    this.getPlatformInfo()
    this.getLiveRoomInfo();
    this.getCollectFlag();
    this.getLiveList();
    // 埋点
    this.enterExposure("liveRoom", this.$route.params.id, "liveRoom", [{ id: this.$route.params.id, type: "liveRoom" }]);
    this.getLiveRoomShareConfig()
  },
  beforeRouteLeave (to, from, next) {
    this.computTimeDiff("liveRoom", this.$route.params.id, 'liveRoom', [{ id: this.$route.params.id, type: 'liveRoom' }]);
    next()
  },
  methods: {
    // 跳转 产品详情
    jumpTwoBtn(item) {
      window.location.href = `${exhibitorClientUrl}/productStore?productId=${item.id}&exhibitorId=${this.liveRoomInfo.exhibitorId}`
    },
    // 跳转搜索产品
    jumpBtn() {
      window.location.href = `${exhibitorClientUrl}/productList?keyVal=${this.liveForm.shopName}`
    },
    // 获取推荐产品
    selExhibitorDetailsRePro() {
      selExhibitorDetailsRePro({ id: this.liveRoomInfo.exhibitorId, pageSize: 3, pageNum: 1 }).then(res => {
        this.exhList = res.data.list
        if (this.exhList.length > 3) {
          this.exhList.splice(3,this.exhList.length - 1)
        }
      })
    },
    // 获取商家详情
    selExhibitorDetails() {
      selExhibitorDetails({ id: this.liveRoomInfo.exhibitorId }).then(res => {
        this.liveForm = res.data.exhibitor
        this.selExhibitorDetailsRePro();
      })
    },
    // 获取直播间 详情
    getLiveRoomInfo() {
      let params = {
        relId: this.$route.params.id
      }
      getLiveRoomInfo(params).then(res => {
        if(res.code == this.$successCode){
          this.liveRoomInfo = res.data;
          this.selExhibitorDetails();
        }else{
          this.$createToast({ txt: res.info, type: 'error' }).show()
        }
      })
    },
    // 获取直播间 直播列表
    getLiveList() {
      let params = {
        relId: this.$route.params.id,
        pageNum: this.currentPage
      }
      getLiveList(params).then(res => {
        if (res.code == this.$successCode) {
          if (res.data) {
            this.liveList = this.liveList.concat(res.data.list);
            this.total = res.data.total
            this.currentPage++
          }
          // 已加载完毕  没有更多信息啦！
          if (this.total == this.liveList.length) {
            this.$nextTick(() => {
              if( this.$refs.scroll){
                this.$refs.scroll.forceUpdate();
              }
            })
          }
        }
      }) 
    },
    // 查询是否有收藏直播间
    getCollectFlag() {
      let params = {
        itemType: 'liveRoom',
        itemId: this.$route.params.id
      }
      getCollectFlag(params).then(res => {
        if (res.code == this.$successCode) {
          this.collectFlag = res.data?1:0;
        }
      })
    },
    // 收藏 || 取消收藏
    collectLiveRoom() {
      let params = {
        itemType: 'liveRoom',
        itemId: this.$route.params.id,
        status: this.collectFlag==0?1:0
      }
      collectLiveRoom(params).then(res => {
        if (res.code == this.$successCode) {
          this.$createToast({ txt: this.collectFlag==0?'收藏成功':'取消收藏成功', type: 'txt' }).show()
          this.collectFlag = this.collectFlag==0?1:0;
        } else {
          this.$createToast({ txt: res.info, type: 'error' }).show()
        }
      })
    },
    // 个人中心
    toUserCenter() {
      const url = this.$router.resolve({ name: 'home', query: { targetTab: (this.$route.query.platformId==3 || this.$route.query.platformId==5)?'我的':'个人中心', local: true } }).href;
      window.top.location.href = url;
    },
    // 跳转首页
    homePage(){
      const url = this.$router.resolve({ name: 'home', query: { targetTab: '首页', local: true } }).href;
      window.top.location.href = url;
    },
    // 显示介绍
    showDesc() {
      if(!this.liveRoomInfo.exhibitorId && !JSON.parse(this.liveRoomInfo.introduction).length){
        return this.$createToast({
            txt: '暂无公司介绍',
            type: 'warn',
            time: 2000
          }).show();
      }
      if(this.liveRoomInfo.exhibitorId){
        window.location.replace(`${exhibitorClientUrl}/sellerStore?id=${this.liveRoomInfo.exhibitorId}`)
        }else{
            this.showDescDialog = true;
        }
    },
    // 企业直播间  单独获取后台设置的 分享参数
    getLiveRoomShareConfig() {
      let params = {
        relId: this.$route.params.id,
        platformId: this.$route.query.platformId,
        bizId: this.$route.query.bizId,
        sysCode: 'info',
        moduleType: 'liveRoom',
        businessType: 'liveRoom',
        businessId: 0,
        moduleId: this.$route.params.id,
        type: 'liveRoom'
      }
      getLiveRoomShareConfig(params).then(res => {
        if (res.code == '001') {
          let that = this;
          this.wxShareConfig(res.data, 'pageDataAction', {
            that,
            clickEle: 'liveRoomShare',
            currentPage: 'liveRoom',
            targetPage: '',
            businessId: that.$route.params.id,
            queryParam: '',
            businessType: 'liveRoom',
            businessSeq: null
          });
        }
      })
    },
    getPlatformInfo(){
      getPlatformInfo().then(res=>{
        console.log(res,'resss');
        this.title=res.data.title
      })
    } 
  },
  components: {
    liveItem,
    liveRoomIntroduction
  }
}
</script>

<style lang="scss" scope>
.enterprise-live-room {
    background: #F5F5F5;
    height: auto;
    min-height: 100%;
  .enterprise-live-header {
    line-height: 45px;
    display: flex;
    justify-content: space-between;
    padding: 0 15px;
    background: #FFFFFF;
    .header-platform {
      font-size: 13px;
      color: #333;
      img {
        width: 27px;
        height: 27px;
        margin-right: 6px;
        vertical-align: middle;
        margin-top: -3px;
      }
    }
    .header-right {
      font-size: 12px;
      color: #666;
      span {
        display: inline-block;
      }
      .header-collect {
        margin-right: 9px;
        position: relative;
        &::after {
          content: '';
          position: absolute;
          right: -5px;
          top: 18px;
          width: 1px;
          height: 10px;
          background: #D8D8D8;
        }
      }
    }
  }
  .live-room-top {
    margin: 4px 15px 0;
    background: #2c303c;
    width: calc(100% - 30px);
    height: 75px;
    border-radius: 4px;
    color: #ffffff;
    position: relative;
    img {
      width: 2.8rem;
      height: 1.17rem;
      border-radius: 4px;
      margin: 13px 15px 13px 10px;
      float: left;
    }
    p {
      color: #ffffff;
      left: 145px;
      font-size: 15px;
      line-height: 22px;
      width: 130px;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    .live-room-introduce-btn {
      position: absolute;
      right: 10px;
      top: 27px;
      width: 40px;
      height: 0.63rem;
      line-height: 22px;
      background: #00629F;
      font-size: 10px;
      text-align: center;
      border-radius: 4px 4px 4px 4px;
      font-size: 11px;
    }
  }
  .live-room-list {
    padding: 0 14px;
    height: calc(100% - 150px);
    .cube-scroll-wrapper {
      width: 100%;
    }
    .no-data-tips {
      height: 4.54rem;
      background: #FFFFFF;
      > img {
          width: 2.98rem;
      }
      > div {
          margin-top: 20px;
      }
      margin-top: 30px;
      text-align: center;
      color: #999;
      display: flex;
      flex-direction: column;
      align-items: center;
      flex-wrap: wrap;
      justify-content: center;
    }
  }
  .footer{
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 5px 0;
    font-size: 12px;
    line-height: 17px;
    text-align: center;
    color: #B2B2B2;
    width: 100%;
    background-color: #ffffff;
    .ch{
      width: 150px;
      margin: 0 auto;
      height: 17px;
    }
    img{
      height: 13px;
      width: 55px;
      float: left;
      margin-top: 1px;
    }
    p{
      display: inline;
      margin-left: 4px;
      float: left;
    }
  }
  &.enterprise-live-room-car{
    .live-room-introduce-btn {
      background: #5A2E84;
    }
  }
}
.top_live_class {
  padding: 7px 8px;
  background: #FFFFFF;
  border-radius: 4px 4px 4px 4px;
  margin-top: 15px;
  .live_class_item {
    height: 1.53rem;
    display: flex;
    align-items: center;
    .live_img_class {
      width: 1.52rem;
      height: 1.52rem;
      background: #D9D9D9;
      border-radius: 5px 5px 5px 5px;
      // border: 1px solid #D9D9D9;
      margin-right: 10px;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    .live_text_class {
      width: calc(100% - 2.23rem);
      div {
        font-weight: 400;
        font-size: 11px;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .title_class {
        font-weight: 600;
        font-size: 15px;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 12px;
      }
    }
  }
  .bot_live_class {
    font-weight: 400;
    font-size: 11px;
    color: #999999;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-top: 0.23rem;
    display: flex;
    align-items: center;
    img {
      width: 14px;
      height: 14px;
      margin-right: 5px;
    }
  }
  .border_class {
    height: 0.5px;
    background-color: #E5E5E5;
    margin: 7.5px 0px;
  }
}
.live_root_class {
  // height: 4.93rem;
  background: #FFFFFF;
  border-radius: 4px 4px 4px 4px;
  padding: 10px 6px;
  margin-top: 10px;
  padding-bottom: 11px;
  .live_one_class {
    width: 32%;
    border: 1px solid #D9D9D9;
    padding-bottom: 6px;
    min-width: 30%;
    height: 3.66rem;
    .live_img_class {
      height: 2.55rem;
      background: #FFFFFF;
      border-radius: 6px 6px 0px 0px;
      width: 100%;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    .bot_title_class {
      font-weight: 400;
      margin-top: 8px;
      padding: 0px 2px 0px 2px;
      .tag_class {
        width: 0.75rem;
        height: 0.42rem;
        background: #5FB1F8;
        border-radius: 1px 1px 1px 1px;
        font-weight: 500;
        color: #FFFFFF;
        text-align: left;
        font-style: normal;
        text-transform: none;
        display: inline-block;
        vertical-align: middle;
        text-align: center;
        line-height: 18px;
        margin-right: 2px;
        font-size: 11px;
      }
      .tag_text_class {
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
        line-height: 12px;
        font-size: 11px;
      }
    }
  }
  .displ_class {
    display: flex;
    .no_data_img {
      width: 2.98rem;
      margin: 0.64rem 0;
    }
  }
  .live_title_class {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    .live_title_left {
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      text-align: justified;
      font-style: normal;
      text-transform: none;
    }
    .more_title_text {
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      text-align: left;
      font-style: normal;
      text-transform: none;
      img {
        width: 9px;
        height: 9px;
      }
    }
  }
}
.hidden_text_class {
  overflow: hidden; /* 隐藏超出的内容 */
  display: -webkit-box; /* 使用弹性盒子模型 */
  -webkit-line-clamp: 1; /* 限制显示的行数为2行 */
  -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
  text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
}
.live_item_class {
  background-color: #FFFFFF;
  padding: 1px 6px;
  padding-bottom: 10px;
  margin-top: 15px;
}
</style>
<style lang="scss">
.cube-toast-icon{
  width: 18px;
  height: 18px;
}
</style>