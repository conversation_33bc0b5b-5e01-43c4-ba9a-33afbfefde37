<template>
  <div class="live-room-introduction">
    <div class="live-room-mask"></div>
    <div class="live-room-dialog">
      <div class="dialog-title">企业直播间简介</div>
      <div class="logo-name">
        <img :src="info.logo"/>
        <div class="live-room-name">{{info.name}}</div>
      </div>
      <ul class="introduction-content">
        <li v-for="(item, i) in introduction" :key="i" style="white-space:pre-line;">
          <p v-if="item.type == 'text'" v-html="item.text"> </p>
          <div v-else-if="item.type == 'img'">
            <img :src="item.url" alt="" />
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    info: Object
  },
  data() {
    return {
      introduction: []
    }
  },
  watch: {
    info: {
      immediate: true,
      handler(val) {
        if (val.introduction) {
          this.introduction = JSON.parse(val.introduction)
        }
      }
    }
  }
}
</script>

<style lang="scss">
.live-room-introduction {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9;
  .live-room-mask {
    width: 100%;
    height: 100%;
    background: #000;
    opacity: 0.4;
  }
  .live-room-dialog {
    position: absolute;
    left: 25px;
    top: 12%;
    height: 76%;
    width: 325px;
    background: #fff;
    border-radius: 10px;
    padding: 19px 15px;
    color: #333;
    font-size: 14px;
    font-weight: 500;
    .dialog-title {
      font-size: 18px;
      line-height: 25px;
    }
    .logo-name {
      padding: 24px 0 21px;
      border-bottom: 1px solid #F5F5F5;
      display: flex;
      img {
        width: 91px;
        height: 38px;
      }
      .live-room-name {
        margin-left: 10px;
        max-height: 40px;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
    .introduction-content {
      height: calc(100% - 110px);
      overflow: auto;
      li {
        margin-top: 11px;
        line-height: 24px;
        color: #666;
        img {
          vertical-align: middle;
        }
      }
    }
  }
}
</style>