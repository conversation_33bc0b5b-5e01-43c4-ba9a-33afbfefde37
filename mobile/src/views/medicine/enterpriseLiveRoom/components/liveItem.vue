<template>
  <div class='liveItem'>
    <div class="Item" v-for="item in liveList" @click="goLiveDetail(item)">
      <div class="imgDiv">
        <div class="liveStatus">{{item.conferenceType}}</div>
        <img :src="item.coverImg">
      </div>
      <div class="right_con">
        <h5>{{item.name}}</h5>
			  <!-- status 1 更新中，0 结束  -->
        <p v-if="item.itemType == 'series'">
          <span style="color:#999999;">{{item.statusText}}</span>
          <i></i>{{item.updateNum + '期'}} / {{'共' + item.renewalPeriod + '期'}}
        </p>
		    <!-- 0 结束，1 进行中， -->
        <p v-else-if="item.status == 0"><span style="color:#999999;">已结束</span><i></i>{{item.duration}} 分钟</p>
        <p v-else><span>开播时间</span><i></i>{{moment(item.beginTime).format('yyyy-MM-DD HH:mm')}}</p>
      </div>
    </div>
  </div>
</template>

<script>

import moment from 'moment'
export default {
  name: 'product-item',
  props: {
    liveList: Array
  },
  methods: {
    moment,
    goLiveDetail(item) {
      localStorage.setItem('platformId', this.$route.query.platformId)
      if (item.itemType == 'live') {
        window.location.href = location.origin  +'/mobile/#/0/liveDetail?liveId='+ item.id +'&platformId='+ this.$route.query.platformId +'&bizId='+ localStorage.getItem("bizId") +'&domainType='+ this.$route.query.domainType
      } else if (item.itemType == 'series') {
      // 私域的系列直播
        window.location.href = location.origin  +'/mobile/#/0/seriesLive/'+ item.id +'?platformId='+ this.$route.query.platformId +'&bizId='+ localStorage.getItem("bizId") +'&domainType='+ this.$route.query.domainType
      } 
    }
  }
};

</script>
<style scoped lang="scss">
.liveItem{
  .Item{
    width: 100%;
    margin: 15px 0px 0;
    border-radius: 4px;
    position: relative;
	  display: flex;
    .imgDiv{
      position: relative;
      width: 120px;
      height: 72px;
      display: inline-block;
    }
    .liveStatus{
      position: absolute; 
      font-size: 10px;
      color: #ffffff;
      background: rgba(0, 0, 0, 0.4);
      border-radius: 1px;
      padding: 0 3px;
      line-height: 15px;
      text-align: center;
      bottom: 2px;
      right: 2px;
    }
    img{
      width: 120px;
      height: 72px;
      border-radius: 4px;
      float: left;
    }
    .right_con{
      float: right;
      color: #333333;
      width: calc(100% - 130px);
      border-bottom: 0.03rem solid #eeeeee66;
      margin-left: 10px;
      padding-bottom: 15px;
      h5{
        font-size: 14px;
        line-height: 20px;
        height: 40px;
        margin-bottom: 15px;
        height: 40px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      p{
        font-size: 12px;
        color: #666666;
        i{
          display: inline-block;
          width: 10px;
          color: #979797;
          text-align: center;
          font-style: normal;
        }
      }
    }
  }
}
</style>