<template>
  <div class="ent-live-room-list-container" :class="{'ent-live-room-list-container-car': $route.query.platformId == 5}">
    <div class="list-search-top">
      <div class="search-left">
        <img :src="require('@/assets/img/search_icon.png')" alt="" class="iconSearch" @click="search">
        <input ref="input" v-model="name" type="text" placeholder="请输入关键字" @keyup.enter="search">
        <span v-if="name" @click="clearBtn">×</span>
      </div>
      <div class="list-sort">
        <div class="list-sort-btn" @click="triggerDropMenu">
          <img src="@/assets/img/sort.png">
        </div>
        <cube-tip ref="dropMenu" direction="top" offsetRight="17px" class="list-drop-menu">
          <div v-for="(type, key) in typeList" :key="key" @click="changeType(key)" :class="{active: sortType==key}">{{type}}</div>
        </cube-tip>
      </div>
    </div>
    <div class="list-container">
      <div v-if=" liveRoomList.length == 0" class="no-data-tips">
        <img src="@/assets/img/no_data_icon.png" alt="">
        <div>暂无数据</div>
      </div>
      <cube-scroll v-else ref="scroll" :data="liveRoomList" :options="options" @pulling-up="getLiveRoomList">
        <div v-for="item in liveRoomList" :key="item.id" class="live-room-item" @click="toLiveRoom(item.id)">
          <img :src="item.logo">
          <div class="live-room-name"><div>{{item.name}}</div></div>
        </div>
      </cube-scroll>
    </div>
  </div>
</template>

<script>
import { getLiveRoomList } from '@/api/medicine/enterpriseLiveRoom'
export default {
  data() {
    return {
      name: '',
      typeList: {
        default: '默认排序',
        asc: '按企业直播间名称正序',
        desc: '按企业直播间名称倒序'
      },
      sortType: 'default',
      liveRoomList: [],
      total: 0,
      currentPage: 1,
      options: { // 上拉加载设置
        pullUpLoad: {
          threshold: 30,
          visible: false,
          txt: {
            more: '上拉加载更多',
            noMore: '没有更多信息啦！'
          }
        }
      },
      dropMenuShow: false
    }
  },
  mounted() {
    this.getLiveRoomList()
    // 埋点
    this.enterExposure("liveRoomList", this.$route.query.platformId, "liveRoom", [{ id: this.$route.query.platformId, type: "liveRoom" }]);
  },
  beforeRouteLeave (to, from, next) {
    this.computTimeDiff("liveRoomList", this.$route.query.platformId, 'liveRoom', [{ id: this.$route.query.platformId, type: 'liveRoom' }]);
    next()
  },
  methods: {
    // 情书搜索条件
    clearBtn() {
      this.name = ''
      this.search()
    },
    // 搜索
    search() {
      this.total = 0;
      this.currentPage = 1;
      this.liveRoomList = []
      this.getLiveRoomList('search')
    },
    // 排序下拉菜单 显示 / 隐藏
    triggerDropMenu() {
      if (this.dropMenuShow) {
        this.$refs.dropMenu.hide()
      } else {
        this.$refs.dropMenu.show()
      }
      this.dropMenuShow = !this.dropMenuShow;
    },
    // 获取企业直播间列表
    getLiveRoomList() {
      let params = {
        pageNum: this.currentPage,
        orderType: this.sortType == 'default' ? null : this.sortType,
        name: this.name,
        column: this.sortType == 'default'? null : 'name'
      }
      getLiveRoomList(params).then(res => {
        if (res.code == this.$successCode) {
          if (res.data) {
            this.liveRoomList = this.liveRoomList.concat(res.data.list);
            this.total = res.data.total
            this.currentPage++
          }
          // 已加载完毕  没有更多信息啦！
          if (this.total == this.liveRoomList.length) {
            this.$nextTick(() => {
              this.$refs.scroll.forceUpdate();
            })
          }
        }
      })
    },
    // 修改排序
    changeType(type) {
      this.sortType = type;
      this.search()
    },
    // 跳转 单个 企业直播间
    toLiveRoom(id) {
        this.$router.push({name: 'liveRoom', params: {id}})
    }
  },
}
</script>

<style lang="scss">
.ent-live-room-list-container {
  padding: 14px 15px;
  .list-search-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .search-left {
      width: 311px;
      height: 34px;
      line-height: 34px;
      background: #F5F5F5;
      border-radius: 4px;
      padding-left: 15px;
      flex-shrink: 0;
      img {
        width: 15px;
        height: 15px;
        margin-right: 10px;
        vertical-align: middle;
        margin-top: -3px;
      }
      input {
        line-height: 22px;
        font-size: 14px;
        color: #999;
        font-weight:400;
        width: 246px;
        background: none;
        border: none;
        outline: none;
        &[type=search]::-webkit-search-cancel-button{
            -webkit-appearance: none;  //此处去掉默认的小×
        }
      }
      span {
        margin-left: 6px;
        font-size: 18px;
        color: #999;
      }
    }
    .list-sort {
      position: relative;
      .list-sort-btn {
        img {
          width: 24px;
          height: 24px;
        }
      }
      .list-drop-menu {
        top: 33px;
        right: 0;
        background: #fff;
        max-height: none;
        border-radius: 4px;
        padding: 0;
        box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
        .cube-tip-close {
          display: none;
        }
        .cube-tip-angle {
          top: 1px;
          right: 12px !important;
          &::before {
            border-width: 0;
            width: 9px;
            height: 9px;
            background: #fff;
            transform: rotate(45deg);
            box-shadow: 0 -8px 10px 0px rgba(0, 0, 0, 0.1);
          }
        }
        .cube-tip-content {
          line-height: 38px;
          color: #999;
          font-size: 14px;
          z-index: 1;
          white-space: nowrap;
          & > div {
            padding: 0 16px;
            & + div {
              border-top: 1px solid rgba(0, 0, 0, 0.06);
            }
            &.active {
              color: #00629F;
            }
          }
        }
      }
    }
  }
  .list-container {
    height: calc(100% - 35px);
    margin-top: 15px;
    .cube-scroll-wrapper {
      width: 100%;
    }
    .no-data-tips {
      > img {
          width: 185px;
          height: 142px;
      }
      > div {
          margin-top: 20px;
      }
      margin-top: 130px;
      text-align: center;
      color: #999;
      display: flex;
      flex-direction: column;
      align-items: center;
      flex-wrap: wrap;
    }
    .live-room-item {
      display: flex;
      padding: 10px;
      background: rgba(9, 96, 159, 0.1);
      border-radius: 4px;
      margin-bottom: 10px;
      img {
        width: 108px;
        height: 44px;
        flex-shrink: 0;
      }
      .live-room-name {
        width: 100%;
        margin-left: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        color: #1464A1;
        div {
          line-height: 20px;
          max-height: 40px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
  &.ent-live-room-list-container{
    .list-container .live-room-item{
      background: rgba(90, 46, 132,0.1);
      .live-room-name{
        color: rgba(90, 46, 132, 1);
      }
    } 
  }
}
  
</style>