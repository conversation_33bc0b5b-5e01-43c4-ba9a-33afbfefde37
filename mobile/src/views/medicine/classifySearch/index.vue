<template>
  <div class="medicineSearch flex-vertical">
    <!-- 搜索 -->
    <div class="unit-0">
      <div class="searchHeadWrap flex-left unit-0">
        <div class="searchLeft unit flex-middle">
          <div class="center_search">
            <img src="@/assets/img/search.png" alt="" />
            <iframe id="rfFrame" name="rfFrame" src="about:blank" style="display: none"></iframe>
            <form action="about:blank" class="form_search" @submit.prevent="handleSearch" target="rfFrame">
              <div class="form-search-div">
                <input type="search" v-model="searchOptions.name" autofocus="autofocus" placeholder="请输入关键字" id="searchInput" />
              </div>
            </form>
          </div>
        </div>
        <div class="searchRight unit-0 flex-center flex-middle" v-if="!isNotShowLayout">
          <img v-if="listMode === 'list'" @click="handleListMode('grid')" src="https://oss.ienmore.com/frontUpload/partUpload/2025-06-17/5eed2f492d81cfbd6c8ed98a9df14358.png" alt="" />
          <img v-else @click="handleListMode('list')" src="https://oss.ienmore.com/frontUpload/partUpload/2025-06-17/d7205d3e69e2e5c8969516c79a557104.png" alt="" />
        </div>
      </div>
    </div>
    <!-- 过滤 -->
    <div class="unit-0" v-if="optionData.length">
      <filter-classify @openDialog="handleOpenDialog" :filterData="optionData"></filter-classify>
    </div>
    <!-- 添加二级标题 -->
    <div class="sub-title-content">
      <div class="tag-container" ref="tagContainer">
        <div v-for="(tag, index) in tagList" :key="index" :class="['tag-item', { active: tag.active }]" @click="handleSelectTag(index)" :ref="`tagItem${index}`">
          {{ tag.subMenuName }}
        </div>
        <div class="tag-indicator" ref="tagIndicator"></div>
      </div>
    </div>
    <!-- 查询结果 -->
    <div class="unit content">
      <cube-scroll
        v-if="shouldShowList"
        class="item-list flex-left flex-wrap"
        ref="classifyScroll"
        :data="liveData"
        :options="scrollOptions"
        @pulling-down="handlePullingDown"
        @pulling-up="handlePullingUp"
      >
        <component :is="currentContentComponent.wrapper" :class="currentContentComponent.wrapperClass" v-bind="currentContentComponent.wrapperProps">
          <!-- 单个组件渲染模式（如two-in-row） -->
          <component v-if="currentContentComponent.renderMode === 'single'" :is="currentContentComponent.component" v-bind="getSingleComponentProps()" />
          <!-- 列表组件渲染模式 -->
          <component v-else :is="currentContentComponent.component" v-for="(item, index) in liveData" :key="index" v-bind="getItemProps(item, index)" />
        </component>
      </cube-scroll>
      <div class="noData flex-middle flex-center" v-else>
        {{ loadingStateText }}
      </div>
    </div>
    <!-- 弹窗 -->
    <filter-result
      @reset="handleReset"
      @goHome="handleGoHome"
      :searchName="searchOptions.name"
      :selectOption="$route.query"
      v-show="filterController"
      @closeDialog="handleCloseDialog"
      :filterType="filterType"
      :filterData="optionData"
      @changeTypeNew="handleChangeTypeNew"
      :selectTypeNew="selectType"
      @handleTime="handleTime"
    ></filter-result>
    <!-- 日期组件 -->
    <van-calendar
      :default-date="defaultDate"
      v-model="showCalendar"
      type="range"
      @confirm="handleDateConfirm"
      color="#097DB4"
      :min-date="minDate"
      :max-date="maxDate"
      :value="selectedDates.length > 0 ? selectedDates : defaultDate"
    />
  </div>
</template>

<script>
import FilterClassify from "./component/filterClassify";
import FilterResult from "./component/filterResult";
import TwoInRow from "../../home/<USER>/medicalCompontent/liveListStyle/twoInRow";
import NewLiveItem from "@/components/common/newLiveItem.vue";
import { getFunctionClassify, getFunctionClassifyList } from "@/api/medicine/classifySearch";
import commonVideo from "@/components/common/commonVideo.vue";
import commonInformation from "@/components/common/commonInformation.vue";
import { getShortVideoList, getInformationList } from "@/api/shortVideo";
// 常量配置
const CONSTANTS = {
  PAGE_SIZE: 10,
  INITIAL_PAGE: 1,
  PULL_UP_DELAY: 1000,
  SCROLL_OPTIONS: {
    pullUpLoad: {
      threshold: 50,
      txt: {
        more: "",
        noMore: "没有更多信息啦！",
      },
    },
    pullDownRefresh: {
      threshold: 60,
      stopTime: 1000,
      txt: "更新成功",
    },
    scrollbar: false,
  },
  FILTER_OPTIONS: [
    { name: "默认排序", type: "order" },
    { name: "时间筛选", type: "time" },
  ],
  INDICATOR_WIDTH: 20,
  TAG_MARGIN_RIGHT: 20,
  TAG_GAP: 8,
  // 组件配置映射
  COMPONENT_CONFIG: {
    live: {
      list: {
        wrapper: "div",
        wrapperClass: "component-live list-list",
        component: "new-live-item",
        props: (item, index) => ({ itemData: item, isSearch: true }),
      },
      grid: {
        wrapper: "div",
        wrapperClass: "component-live grid-list",
        component: "two-in-row",
        props: (item, index, allData) => ({ list: allData, isShowWatchBtn: false }),
        renderMode: "single", // 表示只渲染一次，不是每个item一次
      },
    },
    shortVideo: {
      wrapper: "div",
      wrapperClass: "component-shortVideo flex-left flex-wrap",
      component: "common-video",
      props: (item, index) => ({ item }),
    },
    newsInformation: {
      wrapper: "div",
      wrapperClass: "component-newsInformation",
      component: "common-information",
      props: (item, index) => ({ item }),
    },
    // 扩展新类型示例：
    // newType: {
    //   wrapper: 'div',
    //   wrapperClass: 'component-new-type',
    //   component: 'new-component',
    //   props: (item, index) => ({ data: item, index }),
    //   renderMode: 'list' // 可选：'list' 或 'single'
    // }
  },
};

// 状态枚举
const LOADING_STATES = {
  LOADING: "数据加载中...",
  EMPTY: "暂无数据",
  NO_MORE: "暂无更多数据",
};
export default {
  name: "MedicineClassifySearch",
  components: {
    FilterClassify,
    FilterResult,
    TwoInRow,
    NewLiveItem,
    commonVideo,
    commonInformation,
  },

  data() {
    return {
      // 数据相关
      liveData: [],
      tagList: [],
      total: 0,

      // 分页相关
      pageData: {
        pageNum: CONSTANTS.INITIAL_PAGE,
        pageSize: CONSTANTS.PAGE_SIZE,
      },

      // 搜索和过滤相关
      searchOptions: {
        name: "",
        order: "",
      },
      optionData: [...CONSTANTS.FILTER_OPTIONS],
      filterType: "",
      selectType: "all",
      dateRange: [], // 时间的筛选

      // UI状态相关
      filterController: false,
      dropMenuShow: false,
      showCalendar: false,
      currentTabIndex: 0,

      // 加载状态相关
      isLoadAll: false,
      isLoading: false,

      // 日期相关
      defaultDate: [],
      selectedDates: [],
      minDate: new Date(2000, 1, 1),
      maxDate: new Date(2100, 12, 31),
      // 列表的模式
      listMode: "list",
    };
  },

  computed: {
    scrollOptions() {
      return CONSTANTS.SCROLL_OPTIONS;
    },

    activeTab() {
      return this.tagList.find((tag) => tag.active) || null;
    },

    hasLoadingState() {
      return this.isLoading && !this.isLoadAll;
    },

    showEmptyState() {
      return !this.isLoading && this.isLoadAll && (!this.liveData || this.liveData.length === 0);
    },

    shouldShowList() {
      return this.liveData && this.liveData.length > 0;
    },

    loadingStateText() {
      if (this.hasLoadingState) return LOADING_STATES.LOADING;
      if (this.showEmptyState) return LOADING_STATES.EMPTY;
      return LOADING_STATES.NO_MORE;
    },
    // 是否需要显示切换按钮  默认是不显示
    isNotShowLayout() {
      return this.$route.query.menuKey && (this.$route.query.menuKey.includes("shortVideo") || this.$route.query.menuKey.includes("newsInformation"));
    },

    // 获取当前内容类型
    currentContentType() {
      const menuKey = this.$route.query.menuKey || "";

      if (menuKey.includes("shortVideo")) {
        return "shortVideo";
      } else if (menuKey.includes("newsInformation")) {
        return "newsInformation";
      }
      // 扩展新类型示例：
      // else if (menuKey.includes('newType')) {
      //   return 'newType';
      // }
      else {
        return "live";
      }
    },

    // 获取当前组件配置
    currentContentComponent() {
      const contentType = this.currentContentType;
      const config = CONSTANTS.COMPONENT_CONFIG[contentType];

      // 如果是live类型，根据listMode选择配置
      if (contentType === "live") {
        return config[this.listMode] || config.list;
      }

      return config;
    },
  },

  async created() {
    await this.initializeComponent();
  },

  mounted() {
    // 组件挂载后的初始化工作
  },

  beforeDestroy() {
    // 组件销毁前清理，避免异步操作完成后调用已销毁组件的方法
    this.isLoading = false;
  },

  methods: {
    async initializeComponent() {
      try {
        this.setupDefaultDates();
        await this.loadFunctionClassify();
      } catch (error) {
        this.handleError("组件初始化失败", error);
      }
    },
    // 设置默认日期
    setupDefaultDates() {
      const dateRange = this.getDefaultDateRange();
      this.defaultDate = dateRange;
      this.selectedDates = dateRange;
    },
    // 获取职能分类
    async loadFunctionClassify() {
      try {
        const layoutId = this.$route.query.layoutId;
        const { data } = await getFunctionClassify({ layoutId });

        if (this.isValidData(data)) {
          this.initializeTagList(data);
          await this.initializeFirstTab();
        } else {
          console.warn("职能分类数据为空");
        }
      } catch (error) {
        this.handleError("获取职能分类失败", error);
      }
    },
    // 初始化标签列表
    initializeTagList(data) {
      this.tagList = data.map((item, index) => ({
        ...item,
        active: index === 0,
      }));
    },
    // 初始化第一个标签
    async initializeFirstTab() {
      await this.$nextTick();
      const activeIndex = this.getActiveTabIndex();
      if (activeIndex !== -1) {
        this.currentTabIndex = activeIndex;
        this.updateIndicatorPosition(activeIndex);
        await this.loadTabData(activeIndex);
      }
    },
    // 获取当前激活的标签索引
    getActiveTabIndex() {
      return this.tagList.findIndex((tag) => tag.active);
    },
    // 加载指定tab的数据
    async loadTabData(tabIndex) {
      const tab = this.tagList[tabIndex];
      if (tab.id) {
        await this.loadFunctionClassifyList(tab.id);
      }
    },
    // 加载职能分类列表
    async loadFunctionClassifyList(layoutId, isRefresh = false) {
      if (this.isLoading) return;

      try {
        this.setLoadingState(true);
        const targetLayoutId = layoutId || this.getCurrentLayoutId();

        const requestParams = {
          platformId: this.currentPlatformId,
          layoutId: targetLayoutId,
          ...this.pageData,
          ...this.searchOptions,
        };

        // 如果有日期范围选择，将dateRange数组转换为字符串参数
        if (this.dateRange && this.dateRange.length > 0) {
          requestParams.dateRange = this.dateRange.join(",");
        }

        let fn = getFunctionClassifyList; // 默认函数
        const menuKey = this.$route.query.menuKey;
        if (menuKey.includes("shortVideo")) {
            fn = getShortVideoList;
        } else if (menuKey.includes("newsInformation")) {
            fn = getInformationList;
        }

        const { data } = await fn(requestParams);

        this.processListData(data);
        //console.log("职能分类列表加载成功", data);
      } catch (error) {
        console.log("获取职能分类列表失败", error);
      } finally {
        this.setLoadingState(false);
        if (isRefresh) {
          this.finishRefresh();
        }
      }
    },
    // 处理列表数据
    processListData(data) {
      const list = data.list || [];
      this.liveData = this.liveData.concat(list);
      this.total = data.total || 0;
      this.isLoadAll = true;
    },
    // 设置加载状态
    setLoadingState(loading) {
      this.isLoading = loading;
      if (loading) {
        this.isLoadAll = false;
      }
    },
    // 完成刷新操作
    finishRefresh() {
      if (this.getScrollRef()) {
        this.getScrollRef().forceUpdate();
      }
    },
    // 获取滚动组件引用
    getScrollRef() {
      return this.$refs.classifyScroll;
    },
    // 重置数据和分页
    resetDataAndPagination() {
      this.pageData.pageNum = CONSTANTS.INITIAL_PAGE;
      this.liveData = [];
    },
    // 下拉刷新处理
    handlePullingDown() {
      this.resetDataAndPagination();
      this.loadFunctionClassifyList(this.getCurrentLayoutId(), true);
    },
    // 上拉加载处理
    handlePullingUp() {
      setTimeout(() => {
        if (this.hasMoreData()) {
          this.loadMoreData();
        } else {
          this.finishLoadMore();
        }
      }, CONSTANTS.PULL_UP_DELAY);
    },
    // 检查是否有更多数据
    hasMoreData() {
      return this.liveData.length < this.total;
    },

    /**
     * 加载更多数据
     */
    async loadMoreData() {
      this.pageData.pageNum++;
      await this.loadFunctionClassifyList(this.getCurrentLayoutId());

      await this.$nextTick();
      if (this.getScrollRef()) {
        this.getScrollRef().refresh();
      }
    },

    /**
     * 完成加载更多操作
     */
    finishLoadMore() {
      if (this.getScrollRef()) {
        this.getScrollRef().forceUpdate();
      }
    },

    handleOpenDialog(type) {
      if (type === "time") {
        this.showCalendar = true;
        return;
      }
      this.openFilterDialog(type);
    },

    openFilterDialog(type) {
      this.filterController = true;
      this.filterType = type;
      this.hideDropMenu();
    },

    /**
     * 隐藏下拉菜单
     */
    hideDropMenu() {
      if (this.$refs.dropMenu) {
        this.$refs.dropMenu.hide();
      }
    },

    /**
     * 处理对话框关闭
     * @param {Object} searchData - 搜索数据
     */
    handleCloseDialog(searchData) {
      this.updateSearchOptions(searchData);
      this.closeFilterDialog();
      this.refreshSearchResults();
    },

    /**
     * 更新搜索选项
     * @param {Object} searchData - 搜索数据
     */
    updateSearchOptions(searchData) {
      for (let attr in searchData) {
        const value = searchData[attr];
        this.searchOptions[attr] = Array.isArray(value) ? value.join(",") : value;
      }
    },

    /**
     * 关闭过滤对话框
     */
    closeFilterDialog() {
      this.filterController = false;
      this.filterType = "";
    },

    /**
     * 刷新搜索结果
     */
    refreshSearchResults() {
      this.resetDataAndPagination();
      this.loadFunctionClassifyList(this.getCurrentLayoutId());
    },

    /**
     * 处理搜索
     */
    handleSearch() {
      this.refreshSearchResults();
    },

    /**
     * 清空搜索数据
     */
    clearSearchData() {
      this.searchOptions.name = "";
      this.handleSearch();
    },

    /**
     * 处理返回首页
     */
    handleGoHome() {
      const { bizId, domainType, platformId, modelType } = this.$route.query;
      const pathPrefix = modelType === "live" ? "/home/<USER>/0/0" : "/home/<USER>/0/0";
      const query = `platformId=${platformId}&bizId=${bizId}&domainType=${domainType}`;

      this.$router.push({ path: `${pathPrefix}?${query}` });
    },

    /**
     * 处理重置
     */
    handleReset() {
      this.resetSearchOptions();
      this.closeFilterDialog();
      this.handleSearch();
    },

    /**
     * 重置搜索选项
     */
    resetSearchOptions() {
      this.searchOptions = {
        name: "",
        order: "",
      };
    },

    /**
     * 处理类型改变（旧版本兼容）
     * @param {string} val - 新值
     */
    changeType(val) {
      this.selectType = val;
      this.handleSearch();
      this.dropMenuShow = !this.dropMenuShow;
    },

    /**
     * 处理类型改变（新版本）
     * @param {string} val - 新值
     */
    handleChangeTypeNew(val) {
      this.selectType = val;
      this.handleSearch();
    },

    /**
     * 处理标签选择
     * @param {number} index - 标签索引
     */
    async handleSelectTag(index) {
      if (this.currentTabIndex === index) return;

      try {
        this.updateTabActiveState(index);
        this.currentTabIndex = index;
        this.resetDataAndPagination();

        await this.loadTabData(index);
        await this.$nextTick();

        this.scrollToTag(index);
        this.updateIndicatorPosition(index);
      } catch (error) {
        this.handleError("切换标签失败", error);
      }
    },

    /**
     * 更新标签激活状态
     * @param {number} activeIndex - 激活的索引
     */
    updateTabActiveState(activeIndex) {
      this.tagList.forEach((tag, index) => {
        tag.active = index === activeIndex;
      });
    },

    /**
     * 滚动到指定标签
     * @param {number} activeIndex - 激活的索引
     */
    scrollToTag(activeIndex) {
      const container = this.$refs.tagContainer;
      const activeTag = this.$refs[`tagItem${activeIndex}`][0];

      if (!container || !activeTag) return;

      const containerRect = container.getBoundingClientRect();
      const tagRect = activeTag.getBoundingClientRect();
      const containerCenter = containerRect.width / 2;
      const tagCenter = tagRect.left - containerRect.left + container.scrollLeft + tagRect.width / 2;
      const scrollLeft = tagCenter - containerCenter;

      container.scrollTo({
        left: Math.max(0, scrollLeft),
        behavior: "smooth",
      });
    },

    /**
     * 更新指示器位置
     * @param {number} activeIndex - 激活的索引
     */
    updateIndicatorPosition(activeIndex) {
      const indicator = this.$refs.tagIndicator;
      const activeTag = this.$refs[`tagItem${activeIndex}`][0];

      if (!indicator || !activeTag) return;

      const { offsetLeft, width } = this.calculateTagPosition(activeIndex);
      const translateX = offsetLeft + (width - CONSTANTS.INDICATOR_WIDTH) / 2;

      indicator.style.transform = `translateX(${translateX}px)`;
    },

    /**
     * 计算标签位置
     * @param {number} activeIndex - 激活的索引
     */
    calculateTagPosition(activeIndex) {
      let offsetLeft = 0;
      const activeTag = this.$refs[`tagItem${activeIndex}`][0];

      if (!activeTag) return { offsetLeft: 0, width: 0 };

      // 计算当前标签之前所有标签的宽度总和
      for (let i = 0; i < activeIndex; i++) {
        const prevTag = this.$refs[`tagItem${i}`][0];
        if (prevTag) {
          offsetLeft += prevTag.offsetWidth + CONSTANTS.TAG_MARGIN_RIGHT;
        }
      }

      // 加上之前的间距
      if (activeIndex > 0) {
        offsetLeft += activeIndex * CONSTANTS.TAG_GAP;
      }

      return {
        offsetLeft,
        width: activeTag.offsetWidth,
      };
    },

    /**
     * 获取默认日期范围
     */
    getDefaultDateRange() {
      const today = new Date();
      const tomorrow = new Date();
      tomorrow.setDate(today.getDate() + 1);
      return [today, tomorrow];
    },

    /**
     * 处理日期确认
     * @param {Array} date - 选中的日期
     */
    handleDateConfirm(date) {
      console.log("选中的日期:", date);
      this.selectedDates = date;
      // 使用混合中的moment方法将日期数组转换成 YYYY-MM-DD 格式
      this.dateRange = date.map((d) => this.moment(d, true));
      this.showCalendar = false;
      // 重新请求数据
      this.refreshSearchResults();
    },
    /**
     * 打开日历
     */
    openCalendar() {
      this.showCalendar = true;
    },

    /**
     * 数据验证方法
     * @param {*} data - 要验证的数据
     */
    isValidData(data) {
      return data && Array.isArray(data) && data.length > 0;
    },

    /**
     * 获取当前激活tab的layoutId
     */
    getCurrentLayoutId() {
      const activeTab = this.tagList.find((tag) => tag.active);
      return activeTab.id || this.$route.query.layoutId;
    },

    /**
     * 统一错误处理
     * @param {string} message - 错误消息
     * @param {Error} error - 错误对象
     */
    handleError(message, error) {
      console.error(`${message}:`, error);
      // 这里可以添加用户友好的错误提示
      // 例如：this.$toast(message);
    },
    handleTime() {
      this.showCalendar = true;
      this.filterController = false;
    },
    handleListMode(mode) {
      this.listMode = mode;
    },

    /**
     * 获取组件的props
     * @param {Object} item - 数据项
     * @param {number} index - 索引
     */
    getItemProps(item, index) {
      const config = this.currentContentComponent;

      if (!config || !config.props) {
        return {};
      }

      // 如果是函数，调用函数获取props
      if (typeof config.props === "function") {
        return config.props(item, index, this.liveData);
      }

      // 如果是对象，直接返回
      return config.props;
    },

    /**
     * 获取单个组件的props（用于renderMode为single的情况）
     */
    getSingleComponentProps() {
      const config = this.currentContentComponent;

      if (!config || !config.props) {
        return {};
      }

      // 如果是函数，调用函数获取props，传入第一个item作为参考
      if (typeof config.props === "function") {
        return config.props(this.liveData[0] || {}, 0, this.liveData);
      }

      // 如果是对象，直接返回
      return config.props;
    },
  },
};
</script>
<style scoped lang="scss">
.medicineSearch {
  position: relative;
  .content {
    overflow-y: scroll;
  }
  // 添加标签样式
  .sub-title-content {
    background: #fff;
    padding: 10px 0px 10px 15px;

    .tag-container {
      display: flex;
      flex-wrap: nowrap;
      gap: 8px;
      overflow-x: auto;
      overflow-y: hidden;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none;
      -ms-overflow-style: none;
      position: relative;

      &::-webkit-scrollbar {
        display: none;
      }
      .tag-item {
        font-weight: 400;
        font-size: 12px;
        line-height: 17px;
        transition: color 0.3s ease;
        flex-shrink: 0;
        margin-right: 20px;
        margin-bottom: 4px;
        &.active {
          color: #004da1;
        }
      }

      .tag-indicator {
        position: absolute;
        bottom: 0;
        height: 2px;
        width: 20px;
        background: #004da1;
        border-radius: 1px;
        transition: transform 0.3s ease;
        transform-origin: left top;
      }
    }
  }

  &.carSearch {
    :deep .filterClassify .fliterName.active,
    :deep .list-sort .list-drop-menu .cube-tip-content > div.active {
      color: #5a2e84;
    }
    :deep use.yl {
      fill: #5a2e84;
    }
    :deep .filter-result .fliter-content .bottomGroup .reset {
      color: #5a2e84;
      background: #e7dfee;
    }
    :deep .filter-result .fliter-content .bottomGroup .sure {
      background: #5a2e84;
    }
  }
}
.grid-list {
  padding: 0 15px;
}

.component-shortVideo {
  padding: 0 15px;
}

.component-newsInformation {
  width: 375px;
  padding: 0 15px;
  box-sizing: border-box;
}

$color: #004da1;
.noData {
  font-size: 14px;
  color: #999;
  height: 100%;
}

.searchHeadWrap {
  margin-top: 10px;
  margin-bottom: 5px;
  width: 100%;
  padding: 0 15px;
  background-color: #fff;
}

.searchLeft {
}

.iconArrow {
  width: 9.5px;
  height: 17px;
  background: url("../../../assets/img/leftArrow.png") no-repeat;
  background-size: 100%;
}

.searchRight {
  padding-left: 15px;
  i {
    font-size: 16px;
    color: #666;
  }
  img {
    width: 18px;
    height: 18px;
  }
}

.center_search {
  height: 36px;
  width: 100%;
  display: flex;
  align-items: center;
  background: #f1f2f3;
  border-radius: 4px;
}

.center_search img {
  width: 12px;
  height: 12px;
  margin-right: 9px;
  vertical-align: middle;
  margin-left: 13px;
}

#searchInput {
  outline: none;
  background: transparent;
  border: none;
  font-size: 14px;
  line-height: 22px;
  width: 90%;
  padding-left: 2px;
}

input::-webkit-search-cancel-button {
  display: none;
}

input[type="search"]::-ms-clear {
  display: none;
}

#searchInput::placeholder {
  font-size: 16px;
  line-height: 18px;
  color: #999999;
  transform: translateY(2px);
}

.form_search {
  width: 100%;
}

.form-search-div {
  width: 100%;
  display: flex;
  align-items: center;
  height: 22px;
}

.search-button {
  margin-left: 8px;
  height: 35px;
  width: 35px;
  line-height: 35px;
  text-align: right;
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}
</style>
<style lang="scss">
.list-sort {
  position: relative;
  .list-sort-btn {
    margin-left: 5px;
    color: rgba(153, 153, 153, 1);
    img {
      width: 24px;
      height: 24px;
    }
  }
  .list-drop-menu {
    width: 200px;
    position: absolute;
    top: 33px;
    right: 0;
    background: #fff;
    max-height: none;
    border-radius: 4px;
    padding: 0;
    box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
    .cube-tip-close {
      display: none;
    }
    .cube-tip-angle {
      top: 1px;
      right: 12px !important;
      &::before {
        border-width: 0;
        width: 9px;
        height: 9px;
        background: #fff;
        transform: rotate(45deg);
        box-shadow: 0 -8px 10px 0px rgba(0, 0, 0, 0.1);
      }
    }
    .cube-tip-content {
      line-height: 38px !important;
      color: #999;
      font-size: 14px;
      z-index: 1;
      white-space: nowrap;
      & > div {
        padding: 0 16px;
        & + div {
          border-top: 1px solid rgba(0, 0, 0, 0.06);
        }
        &.active {
          color: #00629f;
        }
      }
    }
  }
}
</style>
