<!-- 查询结果 -->
<template>
  <filter-dialog>
    <div class="filter-result flex-vertical">
      <div class="unit-0 fliter-content">
        <div class="searchHeadWrap flex-left unit-0">
          <div class="searchLeft unit flex-middle">
            <div class="center_search">
              <img src="@/assets/img/search.png" alt="" />
              <iframe id="rfFrame" name="rfFrame" src="about:blank" style="display: none"></iframe>
              <form action="about:blank" class="form_search" @submit.prevent="search" target="rfFrame">
                <div class="form-search-div">
                  <input type="search" v-model="contentList.name" autofocus="autofocus" placeholder="请输入关键字" id="searchInput" />
                </div>
              </form>
            </div>
          </div>
        </div>
        <filter-classify :filterName="filterName" :filterData="filterData" @openDialog="changeTagData"></filter-classify>
        <div class="fliter-tag flex-left flex-wrap">
          <div class="tag-list">
            <div @click="clickSelect(index, item)" class="tag-item flex-middle" :class="{ active: item.isSelect }" v-for="(item, index) in tagData" :key="index">
              <span>{{ item.codeValueDesc }}</span>
              <span class="unit"></span>
              <van-icon v-if="item.isSelect" name="success" />
            </div>
          </div>
        </div>
        <div class="bottomGroup flex-left">
          <div class="reset unit flex-middle flex-center" @click="reset">重置</div>
          <div style="width: 13px"></div>
          <div class="sure unit flex-middle flex-center" @click="sure">确定</div>
        </div>
      </div>
      <div class="unit"></div> 
    </div>
  </filter-dialog>
</template>

<script>
import FilterDialog from "./searchDialog";
import FilterClassify from "./filterClassify";
export default {
  name: "",
  props: ["filterType", "filterData", "selectOption", "searchName", "selectTypeNew"],
  data() {
    return {
      tagData: [],
      filterName: "",
      contentList: {
        order: "",
        name: "",
      },
      orderList: [
        { codeValueId: "default", codeValueDesc: "默认排序", isSelect: true },
        { codeValueId: "beginTime", codeValueDesc: "按时间排序", isSelect: false },
        { codeValueId: "clickRate", codeValueDesc: "按热度排序", isSelect: false },
      ],
      dropMenuShow: false,
      selectType: "all",
    };
  },

  created() {
    
  },

  components: {
    FilterDialog,
    FilterClassify,
  },

  watch: {
    filterType() {
      this.filterName = this.filterType;
      this.changeTagData(this.filterName);
      this.contentList.name = this.searchName;
    },
  },

  computed: {},

  mounted() {},

  methods: {
    search() {
      this.$emit("closeDialog", this.contentList);
    },
    clearData() {
      this.contentList.name = "";
    },
    closeDialog() {
      this.$emit("closeDialog", this.contentList);
    },
    // 根据类型取值
    changeTagData(type) {
      if (type == "order") {
        this.tagData = this.orderList;
      }else if(type == "time"){
        this.$emit('handleTime');
      }
    },
    clickSelect(index, item) {
      this.tagData.forEach((item) => {
        item.isSelect = false;
      });
      item.isSelect = true;
      this.contentList.order = item.codeValueId;
    },
    reset() {
      this.$emit("reset");
      this.resetData();
    },
    resetData() {},
    sure() {
      this.closeDialog();
    },
    changeType(val) {
      this.selectType = val;
      this.$emit("changeTypeNew", val);
      this.dropMenuShow = !this.dropMenuShow;
    },
    triggerDropMenu() {
      if (this.dropMenuShow) {
        this.$refs.dropMenu.hide();
      } else {
        this.selectType = this.selectTypeNew;
        this.$refs.dropMenu.show();
      }
      this.dropMenuShow = !this.dropMenuShow;
    },
  },
};
</script>
<style scoped lang="scss">
.filter-result {
  height: 100%;
  .fliter-content {
    background: #fff;
    .fliter-tag {
      width: 100%;
      box-sizing: border-box;
      padding: 5px 15px 0px 15px;
      background: #fff;
      .tag-list {
        width: 100%;
        .tag-item {
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          line-height: 20px;
          padding: 11px 0;
          &.active {
            color: var(--color-primary);
          }
          i {
            font-weight: 600;
          }
        }
      }
    }
    .bottomGroup {
      width: 100%;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      padding: 10px 15px;
      box-sizing: border-box;
      line-height: 20px;
      .reset {
        background: #edf0f5;
        border-radius: 4px;
        padding: 10px 69px;
      }
      .sure {
        background: var(--color-primary);
        border-radius: 4px;
        padding: 10px 69px;
        color: #fff;
      }
    }
  }
}
.searchHeadWrap {
  margin-top: 10px;
  margin-bottom: 5px;
  width: 100%;
  padding: 0 15px;
  background-color: #fff;
}
.searchLeft {
}
.iconArrow {
  width: 9.5px;
  height: 17px;
  background: url("../../../../assets/img/leftArrow.png") no-repeat;
  background-size: 100%;
}
.searchRight {
  box-sizing: border-box;
}
.center_search {
  height: 36px;
  width: 100%;
  display: flex;
  align-items: center;
  background: #f1f2f3;
  border-radius: 4px;
}
.center_search img {
  width: 12px;
  height: 12px;
  margin-right: 9px;
  vertical-align: middle;
  margin-left: 13px;
}
#searchInput {
  outline: none;
  background: transparent;
  border: none;
  font-size: 14px;
  line-height: 22px;
  width: 90%;
  padding-left: 2px;
}
input::-webkit-search-cancel-button {
  display: none;
}
input[type="search"]::-ms-clear {
  display: none;
}

#searchInput::placeholder {
  font-size: 16px;
  line-height: 18px;
  color: #999999;
  transform: translateY(2px);
}
.form_search {
  width: 100%;
}
.form-search-div {
  width: 100%;
  display: flex;
  align-items: center;
  height: 22px;
}
.search-button {
  margin-left: 8px;
  height: 35px;
  width: 35px;
  line-height: 35px;
  text-align: right;
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}
</style>
