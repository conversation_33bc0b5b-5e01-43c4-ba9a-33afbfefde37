<!-- 筛选过滤数据 -->
<template>
  <div class="filterClassify flex-left">
    <div class="unit flex-center flex-middle" v-for="(item, index) in filterData" @click="openFilterDialog(item.type)">
      <span :class="item.type == filterName ? 'fliterName active' : 'fliterName'">{{ item.name }}</span>
      <svg class="triangle back" v-if="item.type == filterName">
        <use xlink:href="#icon-zhankai1" class="yl" fill="#1464a1"></use>
      </svg>
      <svg class="triangle" v-else>
        <use xlink:href="#icon-zhankai1"></use>
      </svg>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  props: ["filterData", "filterName"],
  data() {
    return {};
  },

  created() {},

  components: {},

  computed: {},

  mounted() {
    // 这里进行图标的fill去除
    this.$nextTick(() => {
      document.querySelector("#icon-zhankai1 path").removeAttribute("fill");
    });
  },

  methods: {
    openFilterDialog(type) {
      this.$emit("openDialog", type);
    },
  },
};
</script>
<style scoped lang="scss">
.filterClassify {
  width: 100%;
  height: 40px;
  background: #fff;
  border-bottom: 1px solid #eeeeee;
  .fliterName {
    font-size: 14px;
    color: #333;
    &.active {
      font-weight: 600;
      color: var(--color-primary);
    }
  }
  .triangle {
    width: 10px;
    height: 5px;
    margin-left: 6px;
    margin-top: 3px;
  }
  .back {
    transform: rotate(180deg);
    margin-top: 0px;
  }
}
</style>
