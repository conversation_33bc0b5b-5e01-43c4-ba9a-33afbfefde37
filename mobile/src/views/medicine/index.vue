<template>
	<div>
    <div class="app_wrap flex-vertical">
			<router-view   class="app_con"/>
			<bottomNavTab v-if="bottomNavTab" :configuration='bottomNavTab'></bottomNavTab>
	
    </div>
	</div>
</template>

<script>
import { getOfficialMenu } from '@/api/configurable/home'
import bottomNavTab from './component/bottomNavTab.vue'
export default {
		name: "homePage",
    props: {
    },
		data() {
				return {
					pageContent: [],
					routeName: '',
					bottomNavTab: '',
					allConfig:[],
				}
		},
    components: {
			bottomNavTab,
    },
    computed: {
    },
		mounted() {
			this.getCongfigration();
		},
		methods: {
			getCongfigration () {
					//获取首页配置
					getOfficialMenu({ pid: 0, platformId: localStorage.getItem('platformId') }).then(res => {
							if (res.code != this.$successCode) { return }
							//使用本地数据
							// res = JSON.parse(JSON.stringify(tempData))
							localStorage.setItem('officialMenu', JSON.stringify(res))
							this.allConfig = res.data
							this.setPageContentConfig(this.allConfig)
					})
			},

			setPageContentConfig (data) {
					//检测是否存在bottomNavTab,目前的架构都是基于存在bottomNavTab的情况下开发的
					if (data && data[0].menuKey == 'bottomNavTab') {
							this.bottomNavTab = data
							this.bottomNavTab.map(item => {
									if (item.id == this.$route.params.pageId) {
											if (item.components.length == 0) {
													// this.pageContent = item.children
											} else {
													// this.pageContent = item.components
											}
											this.$forceUpdate()
									}
							})
					}
			},
		}
}
</script>

<style scoped lang="scss">

.app_wrap {
    width: 100%;
    height: 100%;
    position: relative;
    box-sizing: border-box;
    justify-content: space-between;
    background: #f5f5f5;
}
.app_con{
	overflow: hidden scroll;height: inherit;
}
</style>
