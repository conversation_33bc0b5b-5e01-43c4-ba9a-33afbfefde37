<template>
  <div class="medicine-news-list-container">
    <cube-scroll ref="scroll" :data="newsList" :options="options" @pulling-up="getNewsList">
      <div v-for="item in newsList" :key="item.id" class="news-item" @click="toNewsDetail(item)">
        <div class="news-title">{{item.title}}</div>
        <div class="news-date">{{moment(item.publishTime).format('yyyy-MM-DD')}}</div>
      </div>
    </cube-scroll>
  </div>
</template>

<script>
import { getNewsList,putConferenceNews } from '@/api/medicine/news'
import moment from 'moment'
export default {
  data() {
    return {
      newsList: [],
      options: { // 上拉加载设置
        pullUpLoad: {
          threshold: 30,
          txt: {
            more: '上拉加载更多',
            noMore: '没有更多信息啦！'
          }
        }
      },
      currentPage: 0,
      total: 0
    }
  },
  mounted() {
    this.getNewsList()
  },
  methods: {
    moment,
    // 获取新闻列表
    getNewsList() {
      let params = {
        activityId: this.$route.params.activityId,
        pageSize: 10,
        pageNum: this.currentPage + 1,
      }
      getNewsList(params).then(res => {
        if (res.code == this.$successCode) {
          this.newsList =this.newsList.concat(res.data.list);
          this.total = res.data.total
          this.currentPage++
          // 已加载完毕  没有更多信息啦！
          this.$nextTick(() => {
            if (this.total == this.newsList.length && this.total!=0) {
              this.$refs.scroll.forceUpdate(false);
            }
          })
        }
      })
    },
    // 前往 新闻详情
    async toNewsDetail(item) {

      await putConferenceNews({newsId:item.id})

      if (item.type == 1) {
        // 跳外链 新闻
        window.location = item.content;
      } else {
        // 图文 新闻
        this.$router.push({name: 'medicineNewsDetail', params: {newsId: item.id}});
      }
    }
  }
}
</script>

<style lang="scss">
.medicine-news-list-container {
  padding: 0 15px;
  .news-item {
    padding: 14px 0 9px;
    border-bottom: 1px solid #E9E9E9;
    font-size: 14px;
    line-height: 20px;
    .news-title {
      color: #333;
      margin-bottom: 6px;
    }
    .news-date {
      color: #666;
    }
  }
}
</style>