<template>
  <div class="medicine-news-detail-container">
    <div class="news-top">
      <div class="news-title">{{newsInfo.title}}</div>
      <div class="news-date">{{moment(newsInfo.publishTime).format('yyyy-MM-DD')}}</div>
    </div>
    <ul class="news-content">
      <li v-for="item in detailList" :key="item.id">
        <p v-if="item.type == 'text' && item.text" v-html="item.text"></p>
        <video v-if="item.type == 'video' && item.url" :src="item.url" :poster="item.coverImg"  controls="controls" controlslist="nodownload"  x5-playsinline="true" webkit-playsinline="true" playsinline="true"  @click="clickVideo"  @canplay="imgLoad"></video>
        <img v-if="item.type == 'img' && item.url" :src="item.url" @load="imgLoad">
      </li>
    </ul>
  </div>
</template>

<script>
import { getNewsDetail } from '@/api/medicine/news'
import moment from 'moment'
export default {
  data() {
    return {
      newsInfo: {},
      detailList: []
    }
  },
  mounted() {
    this.getNewsDetail()
  },
  methods: {
    moment,
    // 获取新闻列表
    getNewsDetail() {
      getNewsDetail(this.$route.params.newsId).then(res => {
        if (res.code == this.$successCode) {
          this.newsInfo = res.data;
          this.detailList = JSON.parse(res.data.content);
        }
      })
    },
    clickVideo(e) {
      let isIOS = !!window.navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
      if (!isIOS && !e.target.paused) {
        // 安卓手机  如果视频正在播放  点击暂停
        e.target.pause();
      }
    },
    // 图片 视频 加载完成后 刷新scroll，防止上拉 拉不动
    imgLoad() {
      this.$emit('refreshScroll')
    }
  }
}
</script>

<style lang="scss">
.medicine-news-detail-container {
  padding: 0 15px;
  overflow: auto !important;
  .news-top {
    padding: 18px 0;
    border-bottom: 1px solid #F5F6FA;
    .news-title {
      font-size: 18px;
      color: #333;
      font-weight: 500;
      margin-bottom: 10px;
      line-height: 26px;
    }
    .news-date {
      color: #666;
    }
  }
  .news-content {
    padding-top: 8px;
    li {
      margin-bottom: 15px;
      color: #666;
      line-height: 20px;
      font-size: 14px;
      img, video {
        width: 100%;
        height: auto;
        object-fit: cover;
        vertical-align: top;
      }
    }
  }
}
</style>