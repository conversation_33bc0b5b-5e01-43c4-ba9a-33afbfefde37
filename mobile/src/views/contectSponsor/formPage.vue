<template>
    <div id="formPage" style="overflow-y: scroll;">
        <cube-scroll class="list-scroll" ref="scroll" :options="options">
            <div class="form-page">
                <div v-show="isEdit" id="form-cover" class="cover"></div>
                <!-- 升级版本-担心报错留坑
                <ievent-form ref="ieventForm" class="ieventForm" :customerFormType="3" :isHasTitle='false' :isEdit="isEdit" v-if="content.length" :uploadUrl="uploadUrl" :language="0" :formId="formId" :formData="formData" @submitFun="submitForm" :content="content" @showPopupFun="showPopupFun" /> -->
                <div v-show="!isEdit" class="submit-button" @click="clickButton">立即了解</div>
                <div class="form-info">
                    <span v-show="!isEdit">如需咨询本次会议详情，请填写提交您的信息，会务组会立即联系您。</span>
                    <div v-show="isEdit">您已提交信息,我们会马上联系您。</div>
                </div>
            </div>
        </cube-scroll>
    </div>
</template>
<script>
// import syxForm from './syxForm'

import { getContectSponsorFormPageFormId, getFormById, getMemberAdvice, setMemberAdvice, getFormFields } from '../../api/configurable/home'
import liveForm from 'enmore_common_mobile/common/views/liveForm/form.vue'

export default {
    components: {
        // syxForm
        liveForm
    },
    props: [],
    data () {
        return {
            isEdit: false,
            content: [''],
            uploadUrl: null,
            formId: null,
            formData: null,
            options: {
                scrollbar: false
            },
        };
    },
    computed: {},
    mounted () {
        this.getFormId()
    },
    created() {
        this.getMemberAdvice();
    },
    methods: {
        getFormId () {
            getContectSponsorFormPageFormId(this.$route.params).then(idRes => {
                this.formId = idRes.data
                getFormFields({ formId: this.formId }).then(formRes => {
                    this.formData = this.$refs.ieventForm.formatFormData(formRes.data);
                    setTimeout(() => {
                        this.$refs.ieventForm.setFormInfo(formRes.data);
                    }, 1000)
                })
            })
        },
        getMemberAdvice () {
            getMemberAdvice({
                activityId: this.$route.params.activityId,
                platformId: this.$route.params.platformId,
                source: this.$route.params.source,
                itemType: this.TYPE[0],
            }).then(res => {
                if (res.data.length > 0) {
                    this.content = res.data;
                    this.isEdit = true;
                    this.$nextTick(() => {
                        document.getElementById('form-cover').style.height = document.querySelector('.form-page').clientHeight + "px"
                    })
                }
            })
        },
        clickButton () {
            this.$refs.ieventForm.submit();
        },
        submitForm (val) {
            console.log(val);
            let params = {
                formList: val.data,
                itemId: this.$route.params.activityId,
                itemType: this.TYPE[0],
                platformId: this.$route.params.platformId,
                source: this.$route.params.source
            }
            setMemberAdvice(params).then(res => {
                location.reload()
            })
        },
        showPopupFun (info) {
            this.$toast(info);
        },
    }
}
</script>
<style lang='scss' scoped>
// @import "../../../node_modules/ievent_plugins/lib/ievent.css";

#formPage {
    background-color: #f5f5f5;
}

.submit-button {
    position: relative;
    background: #ff620d;
    color: white;
    width: 320px;
    height: 40px;
    border-radius: 5px;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    margin-top: 20px;
    line-height: 40px;
    left: 50%;
    transform: translate(-50%);
}

.form-page {
    position: relative;
    min-height: 100vh;
    background-color: #fff;
    margin: 10px;
    padding-bottom: 20px;
    border-radius: 4px;
}

.form-info {
    line-height: 18px;
    margin: 30px 50px;
    color: #999;
    font-size: 12px;
    text-align: center;
    div {
        text-align: center;
        color: #ff620d;
    }
}
.cover {
    z-index: 99;
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    min-height: 100vh;
    background: rgba(0, 0, 0, 0.07);
}
</style>