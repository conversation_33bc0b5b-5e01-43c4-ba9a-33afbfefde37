
<template>
    <div class='signNewFormWrap unit'>
        <div v-if="isHasTitle" class="signNewFormTitle">{{language=='0'?'报名信息':'Sign information'}}</div>
        <div class="formContent">
            <div v-for="item,index in formData" :key="index" v-if="!item.fieldLevel&&!item.hiddenFlag">
                <!-- input -->
                <div v-if="item.fieldType==1">
                    <div class="formItem ">
                        <div class="title-line">
                            <div class="left unit-0">
                                <img src="./require.png" alt="" class="bitian" v-if="item.requiredFlag">
                            </div>
                            <div class="Label unit-0">
                                {{item.fieldName}}
                            </div>
                        </div>
                        <div class="right unit flex-left flex-middle ">
                            <cube-input :readonly="customerFormType?false:true" v-if="item.fieldName=='手机'" v-model="item.value" :class="['unit']" :placeholder="language=='0'?placeholder +item.fieldName:placeholderEn + item.fieldName" @blur="changePhone"></cube-input>
                            <cube-input :readonly="customerFormType?true:false" v-else-if="item.fieldName=='邮箱'" v-model="item.value" :class="['unit']" :placeholder="language=='0'?placeholder +item.fieldName:placeholderEn + item.fieldName" @blur="changePhone"></cube-input>
                            <cube-input v-else v-model="item.value" :class="['unit']" :placeholder="language=='0'?placeholder +item.fieldName:placeholderEn + item.fieldName" @blur="changePhone"></cube-input>
                        </div>
                    </div>
                    <cube-validator style="line-height:30px;" class="validator" :id="validators[index]" :ref="validators[index]" v-model="result[index]" :model="item.value" :rules="item.rules"></cube-validator>
                </div>
                <!-- textarea-->
                <div v-if="item.fieldType==2">
                    <div class="formItemTextarea  ">
                        <div class="title-line">
                            <div class="left unit-0">
                                <img src="./require.png" alt="" class="bitian" v-if="item.requiredFlag">
                            </div>
                            <div class="Label unit-0">
                                {{item.fieldName}}
                            </div>
                        </div>
                        <div class="right unit flex-left flex-middle">
                            <textarea v-model="item.value" class="unit cube-textarea_expanded t1" :placeholder="language=='0'?placeholder+item.fieldName:placeholderEn+item.fieldName" @blur="changePhone"></textarea>
                            <!-- <cube-textarea id="textAreas"   :maxlength="textlength" :indicator="false" v-model="item.value" class="unit cube-textarea_expanded " :placeholder="language=='0'?placeholder:placeholderEn" @blur="changePhone" ></cube-textarea> -->
                        </div>
                    </div>
                    <cube-validator style="padding-left:0;line-height:30px;" class="validator" :id="validators[index]" :ref="validators[index]" v-model="result[index]" :model="item.value" :rules="item.rules"></cube-validator>
                </div>
                <!-- radio-->
                <div v-if="item.fieldType==3">
                    <div class="formItemRadio   ">
                        <div class="title-line">
                            <div class="left unit-0">
                                <img style="margin-top:18px;" src="./require.png" alt="" class="bitian" v-if="item.requiredFlag">
                            </div>
                            <div class="Label unit-0">
                                {{item.fieldName}}
                            </div>
                        </div>
                        <div class="right unit flex-left flex-middle">
                            <cube-radio-group @input="getFieldDependReFun(item)">
                                <div v-for="(element,secIndex) in item.details" :key="element.id">
                                    <cube-radio :option="element" @click.native="saveOldValue(item.value)" v-model="item.value">
                                        <div style="font-size:13px;white-space:nowrap;">{{element.theValue}}</div>
                                    </cube-radio>
                                    <div class="flex-left flex-middle" v-if="element.extendFlag && item.value == element.theKey ">
                                        <img class="bitian" src="./require.png" alt="" v-if="!element.emptyFlag">
                                        <cube-input v-model="element.extendText" style="border-bottom:1px solid red;margin-left:20px;" :placeholder="emptyMsg(element)" @blur="changePhone"></cube-input>
                                        <!-- <span v-if="!element.emptyFlag && !element.extendText" style="font-size:13px;white-space: nowrap;margin-left:10px;" :class="{'text-danger':!element.emptyFlag && !element.extendText}">{{}}</span> -->
                                    </div>
                                </div>
                            </cube-radio-group>
                        </div>
                    </div>
                    <cube-validator style="padding-left:0;line-height:30px;" class="validator" :id="validators[index]" :ref="validators[index]" v-model="result[index]" :model="item.value" :rules="item.rules"></cube-validator>
                </div>
                <!-- checkgroup -->
                <div class="formItemcheckbox " v-if="item.fieldType==4">
                    <div class="">
                        <div class="title-line">
                            <div class="left unit-0">
                                <img style="margin-top:18px;" src="./require.png" alt="" class="bitian" v-if="item.requiredFlag">
                            </div>
                            <div class="Label unit-0">
                                {{item.fieldName}}
                            </div>
                        </div>
                        <div class="right unit flex-left flex-middle ">
                            <cube-checkbox-group v-model="item.checkList" shape="square">
                                <div v-for="element,secIndex in item.details" :key="element.id">
                                    <cube-checkbox :option="element">
                                        <div style="font-size:13px;white-space:nowrap;">{{element.theValue}}</div>
                                    </cube-checkbox>
                                    <div class="flex-left flex-middle" v-if="element.extendFlag && item.checkList.includes(element.theKey)">
                                        <img class="bitian" src="./require.png" alt="" v-if="!element.emptyFlag">
                                        <cube-input v-model="element.extendText" style="border-bottom:1px solid red;margin-left:20px;" :placeholder="emptyMsg(element)" @blur="changePhone"></cube-input>
                                        <!-- <span v-if="!element.emptyFlag && !element.extendText" style="font-size:13px;white-space:nowrap;margin-left:10px;" :class="{'text-danger':!element.emptyFlag && !element.extendText}">{{emptyMsg(element)}}</span> -->
                                    </div>
                                </div>
                            </cube-checkbox-group>
                        </div>
                    </div>
                    <cube-validator class="validator common" :id="validators[index]" :ref="validators[index]" v-model="result[index]" :model="item.checkList" :rules="item.rules"></cube-validator>
                </div>
                <!-- 下拉菜单 -->
                <div v-if="item.fieldType==5">
                    <div class="formItem  ">
                        <div class="title-line">
                            <div class="left unit-0">
                                <img src="./require.png" alt="" class="bitian" v-if="item.requiredFlag">
                            </div>
                            <div class="Label unit-0">
                                {{item.fieldName}}
                            </div>
                        </div>
                        <div class="right unit flex-left flex-middle">
                            <cube-select class="unit" v-model="item.value" :options="item.details" @click.native="saveOldValue(item.value)" @change="getFieldDependReFun(item)"></cube-select>
                        </div>
                    </div>
                    <cube-validator style="line-height:20px;" class="validator" :id="validators[index]" :ref="validators[index]" v-model="result[index]" :model="item.value" :rules="item.rules"></cube-validator>
                </div>
                <!-- 日期时间 -->
                <div v-if="item.fieldType==6">
                    <div class="formItem ">
                        <div class="title-line">
                            <div class="left unit-0">
                                <img src="./require.png" alt="" class="bitian" v-if="item.requiredFlag">
                            </div>
                            <div class="Label unit-0">
                                {{item.fieldName}}
                            </div>
                        </div>
                        <div class="right unit flex-left flex-middle">
                            <!-- <vue-datetime-picker class="vue-picker1" name="picker1" :model.sync="item.value"></vue-datetime-picker> -->
                            <div style="height:40px;font-size:13px;line-height:40px;padding-left:10px;" :class="['unit',{'hasData':!item.value}]" @click="showDatepickData(index)">{{item.value||(language=='0'?"点击选择时间日期":'Click and select date')}}</div>
                            <!-- <cube-button @click="showDatepickData">TimePicker</cube-button> -->
                        </div>
                    </div>
                    <cube-validator style="line-height:20px;" class="validator" :id="validators[index]" :ref="validators[index]" v-model="result[index]" :model="item.value" :rules="item.rules"></cube-validator>
                </div>
                <!-- 日期选择 -->
                <div v-if="item.fieldType==7">
                    <div class="formItem  ">
                        <div class="title-line">
                            <div class="left unit-0">
                                <img src="./require.png" alt="" class="bitian" v-if="item.requiredFlag">
                            </div>
                            <div class="Label unit-0">
                                {{item.fieldName}}
                            </div>
                        </div>
                        <div class="right unit flex-left flex-middle ">
                            <div style="height:40px;font-size:13px;line-height:40px;padding-left:10px;" :class="['unit',{'hasData':!item.value}]" @click="showDatepickTime(index)">{{item.value||(language=='0'?"点击选择日期":'Click and select date')}}</div>
                        </div>
                    </div>
                    <cube-validator style="line-height:20px;" class="validator" :id="validators[index]" :ref="validators[index]" v-model="result[index]" :model="item.value" :rules="item.rules"></cube-validator>
                </div>
                <!-- 上传图片 -->
                <div v-if="item.fieldType==9">
                    <div class=" ">
                        <div class="title-line">
                            <div class="left unit-0">
                                <img src="./require.png" alt="" class="bitian" v-if="item.requiredFlag">
                            </div>
                            <div class="Label unit-0">
                                {{item.fieldName}}
                            </div>
                        </div>
                        <div class="right unit ">
                            <cube-upload v-if="!item.uploadUrlImg" ref="upload" class="uploadfile" :action="action" :simultaneous-uploads="1" :max='1' @file-success="successLoad($event,item)">
                            </cube-upload>
                            <div class="uploadImgContent" v-else>
                                <img :src="item.uploadUrlImg" alt="" class="uploadImgs">
                                <i class="cubeic-wrong" @click="quitUpload(item)"></i>
                            </div>
                        </div>
                    </div>
                    <cube-validator style="line-height:20px;" class="validator" :id="validators[index]" :ref="validators[index]" v-model="result[index]" :model="item.value" :rules="item.rules"></cube-validator>
                </div>
            </div>
        </div>
        <cube-popup type="my-popup" :mask="false" ref="myPopup" />
    </div>
</template>

<script>
// import moment from 'moment'
import Vue from 'vue';
import { Validator } from 'cube-ui';
Vue.use(Validator);
export default {
    name: 'ieventForm',
    props: {
        formId: {
            type: Number
        },
        isEdit: {
            type: Boolean
        },
        uploadUrl: {
            type: String
        },
        language: {
            type: [Number, String]
        },
        activityId: {
            type: String
        },
        memberId: {
            type: String
        },
        content: {
            type: Array
        },
        formData: {
            type: Array
        },
        isAreaCode: {
            type: Boolean,
            default: false
        },
        isChange: {
            type: Boolean,
            default: true
        },
        isHasTitle: {
            type: Boolean,
            default: true
        },
        customerFormType: {
            type: Number,
        }
    },
    data () {
        return {
            phoneArea: "+86",
            checkValue: '1',
            action: this.uploadUrl,
            imgUrl: '',
            // formData: [],
            dateTimeIndex: '', // filedType =6  索引
            dataTime: '',  //  filedType =6 
            yData: '',
            yIndex: '',
            customerType: 0,
            // content: [],
            validators: [],
            result: [],
            arrName: [],
            placeholder: '请输入您的',
            placeholderEn: 'Please enter your ',
            isChanges: this.isChange,
            textlength: 5000,
            autofocus: true
        };
    },
    created () {

    },

    components: {

    },

    computed: {

    },
    watch: {
        language: {
            handler () {
                if (this.language == '0') {
                    Validator.addMessage('required', '必填');
                    Validator.addMessage('custom', '格式错误');
                    Validator.addMessage('max', { string: '请勿超过 {{config}} 位字符' });
                } else {
                    Validator.addMessage('required', 'required');
                    Validator.addMessage('custom', 'invalid');
                    Validator.addMessage('max', { string: 'Do not exceed the specified character' });
                }
            },
            immediate: true
        }
    },
    mounted () {

    },

    methods: {
        // 数据格式处理
        formatFormData (data) {
            data.forEach((item, index) => {
                if (item.hiddenFlag) {
                    this.result.push(true);
                } else {
                    this.result.push(undefined);
                }
                this.validators.push("validator" + index);
                item.value = "";
                item.rules = this.patternForm(item);
                if (item.fieldType == 3 || item.fieldType == 4 || item.fieldType == 5) {
                    if (item.fieldType == 4) {
                        item.checkList = [];
                    }
                    if (item.fieldType != 5) {
                        item.details = item.details.map(el => {
                            return {
                                ...el,
                                label: el.theValue,
                                value: el.theKey
                            };
                        });
                    } else {
                        item.details = item.details.map(el => {
                            return {
                                ...el,
                                text: el.theValue,
                                value: el.theKey
                            };
                        });
                    }
                }
                if (item.fieldType == 9) {
                    item.uploadUrlImg = '';
                }
            });
            console.log(data)
            return data;
        },
        //  手机弹窗选择地区
        showPicker (index) {
            if (!this.picker) {
                this.picker = this.$createPicker({
                    title: '',
                    swipeTime: 100,
                    data: [
                        [
                            { id: '+86', name: '中国大陆+86' },
                            { id: '+852', name: '香港地区+852' },
                            { id: '+853', name: '澳门地区+853' },
                            { id: '+886', name: '台湾地区+886' },
                        ]
                    ],
                    onSelect: this.selectHandle,
                    alias: {
                        value: 'id',
                        text: 'name'
                    },
                })
            }
            this.picker.show();
        },
        selectHandle (selectedVal, selectedIndex, selectedText) {
            this.phoneArea = selectedVal[0];
            if (this.phoneArea == '+86') {
                this.customerType = 0;
            } else if (this.phoneArea == '+852') {
                this.customerType = 2;
            } else if (this.phoneArea == '+853') {
                this.customerType = 2;
            } else if (this.phoneArea == '+886') {
                this.customerType = 2;
            } else {
                this.customerType = 1;
            }
            let email = this.formData.filter((item) => {
                return item.fieldType == 1 && item.fieldName == '邮箱';
            })[0]
            let phone = this.formData.filter((item) => {
                return item.fieldType == 1 && item.fieldName == '手机';
            })[0]
            if (this.customerType != 0) {
                email.requiredFlag = true;
                email.rules.required = true;
                phone.requiredFlag = false;
                phone.rules.required = false;
                // 不进行验证手机的正则

                this.formData.forEach((item) => {
                    if (item.fieldType == 1 && item.fieldName == '手机') {
                        item.checkRules.length = 0;
                        item.rules = this.patternForm(item);
                    }
                })
            } else {
                email.requiredFlag = false;
                email.rules.required = false;
                phone.requiredFlag = true;
                phone.rules.required = true;
                // 进行验证手机的正则
                this.formData.forEach((item) => {
                    if (item.fieldType == 1 && item.fieldName == '手机') {
                        item.checkRules[0] = {
                            message: '手机格式错误',
                            pattern: '^1[3-9][0-9]{9}$'
                        }
                        item.rules = this.patternForm(item);
                    }
                })
            }
        },
        //日期时间
        showDatepickData (index) {
            this.dateTimeIndex = index;
            if (!this.dateTimePicker) {
                this.dateTimePicker = this.$createDatePicker({
                    title: '选择日期时间',
                    min: new Date(2008, 7, 8, 8, 0, 0),
                    max: new Date(2020, 9, 20, 20, 59, 59),
                    value: new Date(),
                    columnCount: 6,
                    onSelect: this.selectHandle1,
                    onCancel: this.cancelHandle
                })
            }
            this.dateTimePicker.show();
        },
        cancelHandle () {

        },
        // 处理日期时间  6
        selectHandle1 (date, selectedVal, selectedText) {
            for (let i = 0; i < selectedVal.length; i++) {
                if (selectedVal[i] < 10) {
                    selectedVal[i] = '0' + selectedVal[i]
                }
            }
            this.dataTime = date;
            this.formData[this.dateTimeIndex].value = selectedVal[0] + '-' + selectedVal[1] + '-' + selectedVal[2] + ' ' + selectedVal[3] + ':' + selectedVal[4] + ':' + selectedVal[5]
        },
        showDatepickTime (index) {
            this.yIndex = index;
            if (!this.datePicker) {
                this.datePicker = this.$createDatePicker({
                    title: '选择日期',
                    min: new Date(2000, 1, 1),
                    max: new Date(2020, 12, 31),
                    value: new Date(),
                    onSelect: this.selectHandle2,
                    onCancel: this.cancelHandle
                })
            }
            this.datePicker.show()
        },
        selectHandle2 (date, selectedVal, selectedText) {
            for (let i = 0; i < selectedVal.length; i++) {
                if (selectedVal[i] < 10) {
                    selectedVal[i] = '0' + selectedVal[i]
                }
            }
            this.yData = date;
            this.formData[this.yIndex].value = selectedVal[0] + '-' + selectedVal[1] + '-' + selectedVal[2];
        },
        // 上传成功
        successLoad (e, item) {
            this.$set(item, 'uploadUrlImg', e.response.data)
            this.isChanges = false;
        },
        // 表单验证的函数
        patternForm (item) {
            if (item.checkRules.length) {
                let message = '';
                return {
                    required: item.requiredFlag,
                    max: item.fieldLength,
                    custom: (val) => {
                        let flag;
                        item.checkRules.forEach((el, elIndex) => {
                            if (!(eval(`/${el.pattern}/`).test(val))) {
                                message = el.message;
                                flag = false;
                                return false;
                            }
                            flag = true;
                        })
                        return flag;
                    },
                    messages: {
                        custom: message
                    }
                }
            } else {
                return {
                    required: item.requiredFlag,
                    max: item.fieldLength,
                }
            }
        },

        // 取消上传图片
        quitUpload (item) {
            item.uploadUrlImg = '';
        },
        //修改时处理保单内容
        setFormInfo (formList) {
            for (let item of formList) {
                let theField = this.content.find(field => item.fieldId == field.formFieldId || item.fieldId == field.fieldId);
                if (theField) {
                    //保存id
                    item.fieldInfoId = theField.id;
                    item.value = theField.value;
                    //回显复选框的checkList
                    if (item.fieldType == 4 && item.value) {
                        item.checkList = item.value.split(',');
                    }
                    //回显单选和多选的扩展内容
                    if (item.fieldType == 3 || item.fieldType == 4) {
                        if (theField.extendText) {
                            this.reverseExtendText(item, theField);
                        }
                    }
                    //查询依赖问题
                    if (item.fieldType == 3 || item.fieldType == 5) {
                        if (item.value) {
                            //  this.getFieldDependReFun(item)
                            this.toggleDepend(item, 0);
                        }
                    }
                    //格式化时间
                    if (item.fieldType == 6 || item.fieldType == 7) {
                        if (item.value) {
                            if (item.fieldType == 6) {
                                this.dataTime = item.value;
                                item.value = this.moment(new Date(item.value), 0);
                            } else {
                                this.yData = item.value;
                                item.value = this.moment(new Date(item.value), 1);
                            }
                        }
                    }
                    // if (theField.fieldName == '手机') {
                    //     this.phoneArea = theField.extendText;
                    //     let email = formList.filter((items) => {
                    //         return items.fieldType == 1 && items.fieldName == '邮箱';
                    //     })[0]
                    //     let phone = formList.filter((items) => {
                    //         return items.fieldType == 1 && items.fieldName == '手机';
                    //     })[0]
                    //     if (this.phoneArea != '+86') {
                    //         email.requiredFlag = true;
                    //         email.rules.required = true;
                    //         phone.requiredFlag = false;
                    //         phone.rules.required = false;
                    //     } else {
                    //         email.requiredFlag = false;
                    //         email.rules.required = false;
                    //         phone.requiredFlag = true;
                    //         phone.rules.required = true;
                    //     }
                    // }
                    if (item.fieldType == 9) {
                        item.uploadUrlImg = item.value;
                    }
                }
            }
            console.log(formList)
        },
        //  moment (time,type) {
        //      if(type){
        //          return moment(time).format('YYYY-MM-DD');
        //      }else{
        //           return moment(time).format('YYYY-MM-DD HH:mm:ss');
        //      } 
        // },
        //反显扩展信息
        reverseExtendText (item, theField) {
            let extendTextObj = JSON.parse(theField.extendText);
            item.details.forEach(element => {
                if (item.fieldType == 3 && element.theKey == item.value ||
                    item.fieldType == 4 && item.checkList.includes(element.theKey)) {
                    element.extendText = extendTextObj[element.theKey]
                }
            });
            return item;
        },
        //扩展内容提示信息
        emptyMsg (element) {
            return element.emptyMsg;
        },
        Formvalidator () {
            this.arrName = [];
            let idArr = [];
            let validatorArr = [];
            let els = document.querySelectorAll('.validator');
            for (let i = 0; i < els.length; i++) {
                idArr.push(els[i].id);
                validatorArr.push(els[i].id)
            }
            idArr = idArr.map((item) => {
                return Number(item.replace(/[^0-9]/ig, ""));
            })
            validatorArr.forEach((item, index) => {
                this.arrName.push(this.$refs[item][0].validate());
            });
            this.result = this.result.map((items, index) => {
                if (idArr.indexOf(index) == -1) {
                    return true;
                } else {
                    return items;
                }
            })
        },
        extendTextFun () {
            let flag = true;
            let formlistData3 = this.formData.filter((element) => {
                return element.fieldType == 3
            });
            formlistData3.forEach((items) => {
                if (items.value) {
                    items.details.forEach((el) => {
                        if (el.theKey == items.value) {
                            if (el.extendFlag) {
                                if (!el.emptyFlag) {
                                    if (!el.extendText) {
                                        if (this.language == '0') {
                                            // this.showPopup('myPopup', '表单未验证通过');
                                            this.$emit('showPopupFun', '表单未验证通过');
                                            flag = false;
                                            return;
                                        } else {
                                            this.$emit('showPopupFun', 'The form failed validation');
                                            // this.showPopup('myPopup', 'The form failed validation')
                                            flag = false;
                                            return;
                                        }
                                    }
                                }
                            }
                        }
                    })
                }
            })
            let formlistData4 = this.formData.filter((element) => {
                return element.fieldType == 4
            });
            formlistData4.forEach((items) => {
                if (items.checkList.length) {
                    items.details.forEach((el) => {
                        if (items.checkList.indexOf(el.theKey) != -1) {
                            if (el.extendFlag) {
                                if (!el.emptyFlag) {
                                    if (!el.extendText) {
                                        if (this.language == '0') {
                                            // this.showPopup('myPopup', '表单未验证通过');
                                            this.$emit('showPopupFun', '表单未验证通过');
                                            flag = false;
                                            return;
                                        } else {
                                            // this.showPopup('myPopup', 'The form failed validation')
                                            this.$emit('showPopupFun', 'The form failed validation');
                                            flag = false;
                                            return;
                                        }
                                    }
                                }
                            }
                        }
                    })
                }
            })
            return flag;
        },
        submit () {
            this.Formvalidator();
            // 判断扩展字段是否验证通过
            if (!this.extendTextFun()) {
                return;
            }
            Promise.all(this.arrName).then(() => {
                if (this.result.every(item => item)) {
                    let newFiledArr = this.handleFormData(this.formData, this.isEdit);
                    console.log('组合新数据', newFiledArr);
                    this.$emit('submitFun', { type: this.isEdit ? 'edit' : 'add', data: newFiledArr });
                }
            })
        },
        handleFormData (formDate, onoff) {
            let arr = formDate.filter((filedItem) => {
                return (!filedItem.hiddenFlag && !filedItem.fieldLevel);
            })
            return arr.map((item) => {
                if (item.fieldType != 3 && item.fieldType != 4 && item.fieldType != 9 && item.fieldType != 5) {
                    if (item.fieldType == 6) {
                        let queryParams = {
                            fieldName: item.fieldName,
                            formFieldId: item.fieldId,
                            formId: this.formId,
                            text: this.dataTime,
                            value: this.dataTime,
                            model: item.model
                        }
                        return onoff ? Object.assign({ id: item.fieldInfoId }, queryParams) : queryParams;
                    } else if (item.fieldType == 7) {
                        let queryParams = {
                            fieldName: item.fieldName,
                            formFieldId: item.fieldId,
                            formId: this.formId,
                            text: this.yData,
                            value: this.yData,
                            model: item.model
                        }
                        return onoff ? Object.assign({ id: item.fieldInfoId }, queryParams) : queryParams;
                    } else {
                        let queryParams;
                        if (item.fieldName == '手机') {
                            queryParams = {
                                fieldName: item.fieldName,
                                formFieldId: item.fieldId,
                                formId: this.formId,
                                text: item.value,
                                value: item.value,
                                extendText: this.phoneArea,
                                model: item.model
                            }
                        } else {
                            queryParams = {
                                fieldName: item.fieldName,
                                formFieldId: item.fieldId,
                                formId: this.formId,
                                text: item.value,
                                value: item.value,
                                model: item.model
                            }
                        }
                        return onoff ? Object.assign({ id: item.fieldInfoId }, queryParams) : queryParams;
                    }
                } else if (item.fieldType == 3 || item.fieldType == 5) {
                    let extendText = null;
                    if (item.fieldType == 3) {
                        extendText = {};
                        item.details.forEach(element => {
                            if (element.theKey == item.value && element.extendText) {
                                let key = element.theKey;
                                extendText[key] = element.extendText;
                            }
                        });
                    }
                    let text = "";
                    let value = "";
                    if (item.value != null) {
                        let dto = item.details.find(d => d.theKey == item.value);
                        if (dto) {
                            text = dto.theValue;
                            value = dto.theKey;
                        }
                    } else {
                        text = "";
                        value = "";
                    }
                    let queryParams = {
                        fieldName: item.fieldName,
                        formFieldId: item.fieldId,
                        formId: this.formId,
                        text,
                        value,
                        extendText: JSON.stringify(extendText),
                        model: item.model
                    }
                    return onoff ? Object.assign({ id: item.fieldInfoId }, queryParams) : queryParams;
                } else if (item.fieldType == 4) {
                    let valueArr = [];
                    let textArr = [];
                    let extendText = {};
                    if (item.checkList.length) {
                        item.details.forEach((secItem, index) => {
                            item.checkList.forEach((sItem) => {
                                if (sItem == secItem.theKey) {
                                    valueArr.push(secItem.value);
                                    textArr.push(secItem.label);
                                    extendText[secItem.value] = secItem.extendText;
                                }
                            })
                        })
                    }
                    let queryParams = {
                        fieldName: item.fieldName,
                        formFieldId: item.fieldId,
                        formId: this.formId,
                        text: textArr.join(','),
                        value: valueArr.join(','),
                        extendText: JSON.stringify(extendText),
                        model: item.model
                    }
                    return onoff ? Object.assign({ id: item.fieldInfoId }, queryParams) : queryParams;
                } else if (item.fieldType == 9) {
                    let queryParams = {
                        fieldName: item.fieldName,
                        formFieldId: item.fieldId,
                        formId: this.formId,
                        text: item.uploadUrlImg,
                        value: item.uploadUrlImg,
                        model: item.model
                    }
                    return onoff ? Object.assign({ id: item.fieldInfoId }, queryParams) : queryParams;
                }
            })
        },

        //保存单选和下拉之前的值
        saveOldValue (value) {
            this.oldValue = value || '';
        },
        //单选或下拉选中时查询是否有依赖此项的问题
        getFieldDependReFun (item) {
            //隐藏之前选项的触发问题
            this.toggleDepend(item, 1);

            //显示触发问题
            this.toggleDepend(item, 0);
        },
        //显示或隐藏触发问题
        toggleDepend (item, type) { //type: 1 隐藏  0显示
            console.log(item.fieldName, item.value)
            let val = type == 0 ? item.value : this.oldValue;
            let dto = item.details.find(d => d.theKey == val);
            if (!dto) return;
            let triggerFields = dto.triggerFields;
            let triggerFieldsArr = triggerFields.map(item => item.triggerFieldId);
            this.formData.forEach(form => {
                if (triggerFieldsArr.includes(form.id)) {
                    form.fieldLevel = type;
                    //清空隐藏表单项的数据
                    if (type == 1) {
                        form.value = '';
                        if (form.fieldType == 4) {
                            form.checkList = [];
                        }
                    }
                }
            })
        },
        // 处理ios
        changePhone () {
            var u = navigator.userAgent, app = navigator.appVersion;
            var isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
            if (isIOS) {
                this.temporaryRepair();
            }
        },
        temporaryRepair () {
            var currentPosition, timers;
            var speed = 1;//页面滚动距离
            timers = setInterval(function () {
                currentPosition = document.documentElement.scrollTop || document.body.scrollTop;
                currentPosition -= speed;
                window.scrollTo(0, currentPosition);//页面向上滚动
                currentPosition += speed; //speed变量
                window.scrollTo(0, currentPosition);//页面向下滚动
                clearInterval(timers);
            }, 50)
        },
        // 弹窗提示
        showPopup (refId, contents) {
            const component = this.$refs[refId];
            component.content = `<i style='font-size:14px;padding:20px;background:rgba(0,0,0,.8);color:#fff'>${contents}</i>`
            component.show()
            setTimeout(() => {
                component.hide();
            }, 1000)
        },
        disAbleFun (item) {
            if (item.fieldName == '手机' || item.fieldName == '邮箱') {
                if (item.value && this.isEdit) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }
    }
}

</script>
<style>
@import "https://oss.ienmore.com/resources/public/css/cube-ui.css";

textarea {
    -webkit-appearance: none;
    appearance: none;
}

.t1 {
    outline: none;
}
.cube-radio_selected .cube-radio-ui {
    font-size: 15px !important;
}
.cube-radio-ui {
    font-size: 15px !important;
}

.cube-radio_selected .cube-checkbox-ui {
    font-size: 15px !important;
}

.cube-checkbox-ui {
    font-size: 15px !important;
}

.cube-radio-group {
    padding-bottom: 10px;
}

.cube-input::after {
    border: 0px !important;
}

.cube-input_active::after {
    border: 0px !important;
}

.cube-select::after {
    border: 0px !important;
}

.cube-select_active::after {
    border: 0px !important;
}

.cube-textarea-wrapper::after {
    border: 0px !important;
}

.cube-radio-wrap {
    border: 0px !important;
}

.text-danger {
    color: red;
}

.cube-upload-def .cube-upload-btn {
    margin: 10px 10px !important;
}
.cubeic-wrong {
    position: absolute;
    z-index: 2;
    top: -4px;
    right: -4px;
    color: rgba(0, 0, 0, 0.8);
    font-size: 20px;
    border-radius: 50%;
    background: #fff;
}
</style>

<style lang="scss">
.signNewFormWrap {
    width: 100%;
    background: #fff;
    border-radius: 8px;
    padding: 15px 18px;
    padding-top: 0px;
    position: relative;

    .signNewFormTitle {
        font-size: 18px;
        font-weight: 400;
        color: rgba(34, 34, 34, 1);
        line-height: 25px;
        padding-top: 9px;
        text-align: left;
    }
    .formContent {
        display: flex;
        flex-direction: column;
    }
    .formItem {
        > div {
            width: 100%;
            justify-content: left;
        }
        :nth-last-child(1) {
            :nth-last-child(1) {
                background-color: #f5f5f5;
                border-radius: 4px;
            }
        }
    }

    .formItemTextarea {
        > div {
            width: 100%;
            justify-content: left;
        }
        :nth-last-child(1) {
            :nth-last-child(1) {
                background-color: #f5f5f5;
                border-radius: 4px;
                padding: 5px 10px;
                font-size: 14px;
                color: #666;
            }
        }
        textarea {
            border-color: #f5f5f5;
            resize: none;
        }
    }

    .formItemcheckbox {
        > div {
            width: 100%;
            justify-content: left;
        }
        :nth-last-child(1) {
            :nth-last-child(1) {
                background-color: #f5f5f5;
                border-radius: 4px;
            }
        }
        textarea {
            border-color: #f5f5f5;
        }
    }

    .title-line {
        display: flex;
        align-items: center;
        margin: 20px 0 10px 0;
    }

    .bitian {
        width: 6px;
        height: 6px;
    }
    .left {
        margin-left: -8px;
        width: 8px;
        display: flex;
        justify-content: center;
    }

    .right {
        width: 100%;
        background-color: #f5f5f5;
        .cube-radio-group {
            background-color: #f5f5f5;
            border-radius: 4px;
        }
        border-radius: 4px;
    }
    .Label {
        width: 340px;
        line-height: 18px;
        font-weight: 400;
        color: #333;
        text-align: left;
        font-size: 15px;
    }

    .selectLeft {
        width: 60px;
        height: 100%;
        font-size: 15px;
        color: #444;
    }

    .validator {
        display: block;
    }

    .common {
        padding-left: 0;
        line-height: 30px;
    }

    .commonTop {
        margin-top: 15px;
    }

    .hasData {
        color: #ccc;
    }
    .uploadImgs {
        width: 80px;
        height: 80px;
    }
    .uploadImgContent {
        width: 80px;
        height: 80px;
        margin: 10px 0 10px 0px;
        position: relative;
    }
    .cube-validator-msg {
        text-align: left;
    }
}
</style>

<style>
::-webkit-input-placeholder,
.cube-select-placeholder {
    color: #ccc !important;
    font-size: 16px;
}
::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #ccc !important;
    font-size: 16px;
}
:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #ccc !important;
    font-size: 16px;
}
:-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: #ccc !important;
    font-size: 16px;
}
</style>