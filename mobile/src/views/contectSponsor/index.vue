<template>
    <div class="contectSponsor">
        <div class="button-item item1" @click="goForm">
            <div>
                <img src="../../assets/img/contectSponsor/mobilephone.png" alt="" srcset="">
                <div>
                    <p>让会务组联系我</p>
                    <p>请填写有效联系信息</p>
                </div>
            </div>
        </div>

        <div class="button-item item2" @click="call">
            <div>
                <img src="../../assets/img/contectSponsor/phone.png" alt="" srcset="">
                <div>
                    <p>拨打会务组电话</p>
                    <p>点击获取电话号码</p>
                </div>
            </div>
            <a id="contactPhone" :href="'tel:'+activityData.contactPhone"></a>
        </div>

        <div class="button-item item3" @click="showHidden = !showHidden">
            <div>
                <img src="../../assets/img/contectSponsor/wechat.png" alt="" srcset="">
                <div>
                    <p>添加会务组微信</p>
                    <p>点击添加微信</p>
                </div>
            </div>
        </div>

        <div v-show="showHidden" class="hidden-message">
            <div class="hidden-mask" @click="showHidden = !showHidden"></div>
            <div class="hidden-card">
                <div class="hidden-message-title">
                    <span>添加会务组微信</span>
                </div>
                <div>
                    <img class="info-image" :src="activityData.contactCodeUrl" alt="">
                </div>
                <div style="color:#333;margin-top:5px;">长按二维码或用微信扫一扫关注</div>
            </div>
            <div class="cancle-button" @click="showHidden = !showHidden">
                取消
            </div>
        </div>
    </div>
</template>
<script>
import { getContactMessage } from '@/api/configurable/home.js'
export default {
    name: 'contectSponsor',
    components: {},
    props: [],
    data () {
        return {
            activityData: {},
            showHidden: false,
        };
    },
    computed: {},
    mounted () {
        this.init()
    },
    methods: {
        init () {
            let params = {
                activityId: this.$route.params.activityId,
                source: this.$route.params.source,
                platformId: this.$route.query.platformId,
            }
            getContactMessage(params).then(res => {
                this.activityData = res.data
            })
        },
        goForm () {
            //填写动态表单
            let params = JSON.parse(JSON.stringify(this.$route.params))
            params.relId = this.activityData.relId
            this.$router.push({ name: 'contectSponsorFormPage', params: params })
        },
        call () {
            document.getElementById('contactPhone').click()
        },
        wechat () {

        }
    }
}
</script>
<style lang='scss' scoped>
.contectSponsor {
    background: #f5f5f5;
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.button-item {
    margin-top: 20.5px;
    > div {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 345px;
        height: 110px;
        border-radius: 6px;
        color: white;
        background-color: rgba($color: #000000, $alpha: 0.5);

        img {
            margin-left: 50px;
            height: 65px;
            width: 65px;
        }
        div {
            height: 100%;
            margin-left: 45px;
            padding-top: 32px;
            font-size: 18px;
            font-weight: 400;
            :nth-last-child(1) {
                font-size: 10px;
                font-weight: normal;
                margin-top: 10px;
            }
        }
    }
}
.item1 {
    background-image: url("../../assets/img/contectSponsor/image1.png");
    background-size: 100% 100%;
}
.item2 {
    background-image: url("../../assets/img/contectSponsor/image2.png");
    background-size: 100% 100%;
}
.item3 {
    background-image: url("../../assets/img/contectSponsor/image3.png");
    background-size: 100% 100%;
}
.hidden-message {
    z-index: 99;
    position: fixed;
    width: 100%;
    bottom: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .hidden-mask {
        flex: 1;
        background: rgba($color: #000000, $alpha: 0.2);
    }
    .hidden-card {
        display: flex;
        align-items: center;
        flex-direction: column;
        width: 100%;
        background: #f5f5f5;
        padding-bottom: 10px;
    }
    div {
        padding: 5px;
    }
}
.info-image {
    height: 97px;
    width: 97px;
    margin: 0px;
}
.hidden-message-title {
    display: flex;
    flex-direction: row;
    align-items: center;
    span {
        margin: 8px;
    }
}

.cancle-button {
    background-color: #fff;
    border-top: 1px solid #f5f5f5;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>