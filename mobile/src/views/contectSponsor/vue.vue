<template>
    <div>
    </div>
</template>
<script>
export default {
    components: {},
    props: [],
    data () {
        return {
        };
    },
    computed: {},
    mounted () { },
    methods: {
        getSignTitleForm (formId) {
            getSignTitleForm({ formId }).then(res => {
                if (res.code == "001") {
                    if (this.isEdit) {
                        //编辑时获取表单信息
                        getMemberSignInfoForEdit({ liveId: this.liveId }).then((response) => {
                            this.content = response.data.formContentsForEdit;
                            this.$nextTick(() => {
                                if (this.loginMethod) {
                                    // 海外的方式进行登录
                                    res.data.forEach((item) => {
                                        if (item.fieldName == '邮箱') {
                                            item.requiredFlag = true;
                                        } else if (item.fieldName == '手机') {
                                            item.requiredFlag = false;
                                        }
                                    })
                                }
                                this.formData = this.$refs.ieventForm.formatFormData(res.data);
                                setTimeout(() => {
                                    this.$refs.ieventForm.setFormInfo(res.data);
                                }, 300)
                            })
                        })
                    } else {
                        getFormEditInfo({ liveId: this.liveId }).then((response) => {
                            let { data } = response;
                            if (data) {
                                this.content = data.formContentsForEdit;
                                this.$nextTick(() => {
                                    if (this.loginMethod) {
                                        // 海外的方式进行登录
                                        res.data.forEach((item) => {
                                            if (item.fieldName == '邮箱') {
                                                item.requiredFlag = true;
                                            } else if (item.fieldName == '手机') {
                                                item.requiredFlag = false;
                                            }
                                        })
                                    }
                                    this.formData = this.$refs.ieventForm.formatFormData(res.data);
                                    setTimeout(() => {
                                        this.$refs.ieventForm.setFormInfo(res.data);
                                    }, 300)
                                })
                            } else {
                                this.formData = this.$refs.ieventForm.formatFormData(res.data);
                            }
                        })
                    }

                }
            });
        },
    }
}
</script>
<style lang='scss' scoped>
</style>