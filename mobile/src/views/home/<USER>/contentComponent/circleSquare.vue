<template>
    <div class="circle-square">
        <div class="circle-square-item" v-for="item in configuration.extraInfo.item" @click="itemClick(item)">
            <img class="item-image" :src="item.iconUrl" alt="">
            <div class="item-text">{{item.itemText}}</div>
        </div>
    </div>
</template>
<script>
export default {
    components: {},
    props: ['configuration'],
    data () {
        return {
        };
    },
    computed: {},
    mounted () { },
    methods: {
        itemClick (item) {
            if (item.targetUrl) {
                window.location.href = item.targetUrl
            } else {
                let params = {}
                item.menuParams.map((innerItem) => {
                    params[innerItem.menuParamKey] = innerItem.menuParamValue
                })
                this.naviToCommonList(params)
            }
        }
    }
}
</script>
<style lang='scss' scoped>
.circle-square {
    background: #fff;
    padding: 10px 12px;
    margin: 10px 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.circle-square-item {
    width: 18%;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.item-image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}
.item-text {
    font-size: 10px;
    margin-top: 5px;
    text-align: center;
}
</style>