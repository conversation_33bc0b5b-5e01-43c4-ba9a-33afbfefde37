<template>
  <div class="attend-activity-wrap" v-if='activityData.length'>
    <div class="attend-activity-content">
      <img src="@/assets/3/attendActivity.png" alt="" class="attend-img" />
      <div class="attend-swiper" @touchend="end" @touchstart="start" @touchmove="move">
        <div  @click="handlerPage(item)" :class="'attend-swiper-item-' + (index < 3 ? index + 1 : 'none')" class="attend-swiper-item flex-middle" v-for="(item, index) in activityData" :key="index">
          <div class="attend-swiper-item-left">
            <img :src="item.bannerImg" alt="" />
          </div>
          <div class="attend-swiper-item-right flex-vertical unit">
            <div class="attend-swiper-item-title">{{ item.name }}</div>
            <div class="unit"></div>
            <div class="attend-swiper-item-desc flex-middle">
              <span class="attend-swiper-item-time">{{ moment(item.beginTime, false, true) }}</span>
              <span class="unit"></span>
              <span class="attend-swiper-item-address">{{ omitWord(item)}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getConferenceFront } from "@/api/medicine/homePage";
export default {
  name: "attendActivity",
  props: {
    // 配置信息
    configuration: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // 会议信息数据
      activityData: [],
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
      // 当前的索引位置
      swiperIndex: 0,
    };
  },
  created() {
    // 获取配置信息数据
    this.getConferenceFront();
  },
  mounted() {},
  computed: {},
  methods: {
    // 获取配置信息数据
    getConferenceFront() {
      getConferenceFront(this.configuration.apiUrl).then((res) => {
        if (res.code == this.$successCode) {
          this.activityData = res.data || [];
          //console.log("this.activityData", this.activityData);
          // 删除第一个元素
          //this.activityData.push(this.activityData[3])
        } else {
          this.$message.error(res.info);
        }
      });
    },
    // 处理跳转
    handlerPage(item) {
      if(item.conferenceUrl){
        window.location.href = item.conferenceUrl;
      }
    },
    omitWord(item){
      let str = item.address;
      if(str && str.length > 4){
        return str.substring(0,4) + '...'
      }else{
        return str
      }
    },
    start(e) {
      if (this.activityData.length == 1) {
        return;
      }
      this.startX = event.changedTouches[0].clientX;
      this.startY = event.changedTouches[0].clientY;
    },
    move(e) {
      if (this.activityData.length == 1) {
        return;
      }
      // 移动过程中计算X轴的偏移量
      this.endX = event.changedTouches[0].clientX;
      this.endY = event.changedTouches[0].clientY;
      if (this.endX - this.startX < 0) {
        //console.log("向左运动", this.endX - this.startX);
        let X = (this.endX - this.startX) * 0.5 + "px";
        // 第一张图片先左开始运动
        document.getElementsByClassName("attend-swiper-item")[0].style.transform = `translateX(${X})`;
      }
    },
    end(e) {
      if (this.activityData.length == 1) {
        return;
      }
      this.endY = event.changedTouches[0].clientY;
      this.endX = event.changedTouches[0].clientX;
      if (this.endX - this.startX > 0) return;
      let X = Math.abs(this.endX - this.startX) * 0.5;
      let elWidth = document.querySelector(".attend-swiper-item-1").offsetWidth;
      // console.log("end", X, elWidth / 12, parseInt(X) > elWidth / 12);
      if (parseInt(X) > elWidth / 12) {
        // console.log("进行下一张");
        // 将第一张移动出界面
        // 第一张高度
        document.getElementsByClassName("attend-swiper-item")[0].style.transform = "translateX(-500px)";
        document.getElementsByClassName("attend-swiper-item")[0].style.top = (this.activityData.length == 3 ? 10 : 5) + "px";
        document.getElementsByClassName("attend-swiper-item")[0].style.height = this.activityData.length == 3 ? "1.71rem" : "1.97rem";
        document.getElementsByClassName("attend-swiper-item")[0].style.width = "7.2rem";
        document.getElementsByClassName("attend-swiper-item")[0].style.zIndex = 6;
        document.getElementsByClassName("attend-swiper-item")[0].style.background = this.activityData.length == 3 ? "linear-gradient(45deg, #a3c4fd 0%, #79bbfe 100%)" : "linear-gradient(45deg, #4ea4fe 0%, #84b1fd 100%)";

        document.getElementsByClassName("attend-swiper-item")[1].style.transform = "translateX(0px)";
        document.getElementsByClassName("attend-swiper-item")[1].style.top = 0;
        document.getElementsByClassName("attend-swiper-item")[1].style.height = "2.24rem";
        document.getElementsByClassName("attend-swiper-item")[1].style.width = "8.27rem";
        document.getElementsByClassName("attend-swiper-item")[1].style.zIndex = 10;
        document.getElementsByClassName("attend-swiper-item")[1].style.background = "linear-gradient(45deg, #108afe 0%, #6a9ffd 100%)";

        if (this.activityData.length > 2) {
          document.getElementsByClassName("attend-swiper-item")[2].style.transform = "translateX(1.28rem)";
          document.getElementsByClassName("attend-swiper-item")[2].classList.remove("attend-swiper-item-none");
          document.getElementsByClassName("attend-swiper-item")[2].style.top = 5 + "px";
          document.getElementsByClassName("attend-swiper-item")[2].style.height = "1.97rem";
          document.getElementsByClassName("attend-swiper-item")[2].style.width = "7.2rem";
          document.getElementsByClassName("attend-swiper-item")[2].style.zIndex = 8;
          document.getElementsByClassName("attend-swiper-item")[2].style.background = "linear-gradient(45deg, #4ea4fe 0%, #84b1fd 100%)";
        }

        // 如果有大于3个数的 需要再计算
        if (this.activityData.length > 3) {
          document.getElementsByClassName("attend-swiper-item")[3].style.transform = "translateX(1.49rem)";
          document.getElementsByClassName("attend-swiper-item")[3].style.position = "absolute";
          document.getElementsByClassName("attend-swiper-item")[3].classList.remove("attend-swiper-item-none");

          document.getElementsByClassName("attend-swiper-item")[3].style.top = 10 + "px";
          document.getElementsByClassName("attend-swiper-item")[3].style.height = "1.71rem";
          document.getElementsByClassName("attend-swiper-item")[3].style.width = "7.2rem";
          document.getElementsByClassName("attend-swiper-item")[3].style.zIndex = 6;
          document.getElementsByClassName("attend-swiper-item")[3].style.background = "linear-gradient(45deg, #a3c4fd 0%, #79bbfe 100%)";
        }

        // 假设你有一个包含至少两个元素的NodeList
        const nodeList = document.querySelectorAll(".attend-swiper-item");

        // 获取头部元素
        const firstElement = nodeList[0];

        // 获取尾部元素的父容器
        const parentElement = nodeList[nodeList.length - 1].parentNode;

        // 将头部元素移除
        firstElement.parentNode.removeChild(firstElement);

        // 将头部元素添加到末尾
        parentElement.appendChild(firstElement);

        if (this.activityData.length > 3) {
          firstElement.classList.add("attend-swiper-item-none");
        }
        firstElement.style.transform = this.activityData.length == 3 ? "translateX(1.49rem)" : "translateX(1.28rem)";
      } else {
        document.getElementsByClassName("attend-swiper-item")[0].style.transform = "translateX(0)";
      }
    },
  },
};
</script>
<style scoped lang="scss">
.attend-activity-wrap {
  width: 100%;
  padding: 0 15px;
  box-sizing: border-box;
  margin-top: 10px;
  margin-bottom: 15px;
  height: 134px;
  .attend-activity-content {
    width: 100%;
    height: 100%;
    background: linear-gradient(181deg, #f5fcff 0%, #ffffff 100%);
    border-radius: 4px;
    padding: 10px;
    .attend-img {
      height: 16px;
      margin-bottom: 10px;
    }
    .attend-swiper {
      width: 100%;
      height: 84px;
      position: relative;
      .attend-swiper-item {
        // 背景颜色渐变 从左到右 #6A9FFD  #108AFE 角度大概是45度
        background: linear-gradient(45deg, #108afe 0%, #6a9ffd 100%);
        padding: 15px 15px 15px 10px;
        border-radius: 4px;
        width: 310px;
        height: 84px;
        box-sizing: border-box;
        transition: all 0.3s;
        .attend-swiper-item-left {
          height: 54px;
          margin-right: 10px;
          max-width: 90px;
          img {
            height: 100%;
            border-radius: 4px;
          }
        }
        .attend-swiper-item-right {
          height: 54px;
          .attend-swiper-item-title {
            font-weight: 500;
            font-size: 14px;
            color: #ffffff;
            line-height: 18px;
            // 2行省略
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;

          }
          .attend-swiper-item-desc {
            font-weight: 400;
            font-size: 12px;
            color: #ffffff;
            line-height: 16px;
            .attend-swiper-item-time {
            }
            .attend-swiper-item-address {
            }
          }
        }
      }
      .attend-swiper-item-1 {
        position: absolute;
        top: 0;
        z-index: 10;
        transform: translateX(0);
      }
      .attend-swiper-item-2 {
        position: absolute;
        top: 5px;
        z-index: 8;
        width: 270px;
        height: 74px;
        background: linear-gradient(45deg, #4ea4fe 0%, #84b1fd 100%);
        transform: translateX(48px);
      }
      .attend-swiper-item-3 {
        position: absolute;
        top: 10px;
        z-index: 6;
        width: 270px;
        height: 64px;
        background: linear-gradient(45deg, #a3c4fd 0%, #79bbfe 100%);
        transform: translateX(56px);
      }
      .attend-swiper-item-none {
        display: none;
      }
      .attend-swiper-item-translate {
        transform: translateX(-500px) !important;
      }
    }
  }
}
</style>
