<template>
    <div class="userCenter-column-menu">
        <ul>
            <li v-for="(menu, index) in list" :key="menu.id" @click="toMenuPage(menu.menuKey, menu.children)">
                <img :src="menu.logo">
                {{menu.name}}
                <i class="cubeic-arrow"></i>
                <span v-if="menu.apiUrl">{{getRightNum(index, menu.apiUrl,menu.menuParams)}}{{rightNum[index]}}</span>
            </li>
        </ul>
    </div>
</template>

<script>
import Axios from '@/plugins/http'
export default {
    name: 'columnMenu',
    props: ['configuration'],
    data () {
        return {
            rightNum: {}, // 菜单右侧数据
        }
    },
    computed: {
        list () {
            return JSON.parse(JSON.stringify(this.configuration.children));
        }
    },
    methods: {
        // 跳转菜单 内页
        toMenuPage (name, children) {
            this.$emit('toMenuPage', name, children);
        },
        getRightNum(index, apiUrl, menuParams) {
            let params = {};
            menuParams.forEach( item => {
                params[item.menuParamKey] = item.menuParamValue
            })
            Axios().get(apiUrl, { params }).then( res => {
                if (res.code == this.$successCode) {
                    this.$set(this.rightNum, index, res.data)
                }
            })
        }
    }
}
</script>

<style lang="scss">
.userCenter-column-menu {
    margin-top: 10px;
    background-color: #fff;
    border-top: 10px solid #F7F7F7;
    ul {
        margin: 0;
        li {
            color: #333;
            font-size: 16px;
            line-height: 55px;
            position: relative;
            &::after {
                content: "";
                position: absolute;
                right: 0;
                bottom: 0;
                background-color: #eeeeee;
                height: 1px;
                width: calc(100% - 53px);
            }
            img {
                width: 22px;
                height: 22px;
                vertical-align: middle;
                margin: 0 16px;
                margin-top: -2px;
            }
            span {
                color: #999;
                font-size: 15px;
                float: right;
                margin-right: 10px;
            }
            .cubeic-arrow {
                float: right;
                color: #cccccc;
                margin-right: 10px;
            }
        }
    }
}
</style>