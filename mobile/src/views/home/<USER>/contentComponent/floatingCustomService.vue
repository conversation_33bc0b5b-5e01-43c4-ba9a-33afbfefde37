<template>
    <div class="floating">
        <div class="floating-button" @click="showMessage = !showMessage">
            <img src="@/assets/img/floating_phone.png" alt="">
        </div>
        <div class="hidden-message" v-show="showMessage">
            <span @click="showMessage = !showMessage" class="cancle-icon">×</span>
            <div class="main-title">
                <span>
                    <svg t="1584497472666" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15848" width="20" height="20">
                        <path d="M512 1024C228.693333 1024 0 795.306667 0 512S228.693333 0 512 0s512 228.693333 512 512-228.693333 512-512 512z m0-955.733333C266.24 68.266667 68.266667 266.24 68.266667 512s197.973333 443.733333 443.733333 443.733333 443.733333-197.973333 443.733333-443.733333S757.76 68.266667 512 68.266667z m139.946667 679.253333l-6.826667 3.413333h-10.24c-61.44 0-184.32-58.026667-245.76-119.466666C334.506667 570.026667 273.066667 450.56 273.066667 385.706667V375.466667l6.826666-6.826667C307.2 327.68 351.573333 273.066667 385.706667 273.066667c37.546667 0 95.573333 58.026667 112.64 98.986666 13.653333 34.133333 3.413333 54.613333-6.826667 64.853334-13.653333 13.653333-27.306667 20.48-34.133333 27.306666-3.413333 3.413333-10.24 6.826667-13.653334 10.24 0 13.653333 27.306667 40.96 47.786667 61.44 20.48 20.48 47.786667 47.786667 61.44 47.786667 3.413333 0 6.826667-6.826667 6.826667-10.24 6.826667-10.24 13.653333-20.48 27.306666-34.133333 10.24-10.24 20.48-13.653333 34.133334-13.653334 51.2 0 126.293333 78.506667 126.293333 119.466667 3.413333 30.72-51.2 75.093333-95.573333 102.4z" p-id="15849"></path>
                    </svg></span>
                <span>联系电话</span>
            </div>
            <div class="phomeNumber">{{configuration.extraInfo.phoneNumber}}</div>
            <div class="introduce">{{configuration.extraInfo.introduce}}</div>
            <img class="item-image" :src="qrCodeUrl" alt="">
            <!-- <div class="question" @click="questionClick">参会者常见问题</div> -->
        </div>
    </div>
</template>
<script>
export default {
    components: {},
    props: ['configuration'],
    data () {
        return {
            showMessage: false,
        };
    },
    computed: {},
    mounted () { },
    methods: {
        questionClick () {

        }
    }
}
</script>
<style lang='scss' scoped>
.floating {
    position: fixed;
    top: 65vh;
    right: 8px;
}
.floating-button {
    height: 60px;
    width: 60px;
}
.hidden-message {
    position: fixed;
    padding: 10px;
    border: 1px solid #eee;
    background: white;
    left: 50%;
    transform: translateX(-50%);
    top: 40vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 10px 8px 8px 0px rgba(0, 0, 0, 0.1),
        -10px 8px 8px 0px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    .cancle-icon {
        text-align: center;
        position: absolute;
        right: 10px;
        width: 20px;
        height: 20px;
    }
    .main-title {
        display: flex;
        flex-direction: row;
        align-items: center;
        font-size: 16px;
        font-weight: 400;
        span {
            margin: 2px;
        }
    }
    .phomeNumber {
        margin: 4px;
        font-size: 16px;
        font-weight: 400;
    }
    .introduce {
        font-size: 12px;
        margin-top: 5px;
        color: #999;
    }
    .item-image {
        height: 150px;
        width: 150px;
    }
    .question {
        color: rgb(8, 133, 182);
        font-size: 12px;
    }
}
</style>