<template>
  <div class="userCenter-row-menu">
    <ul>
      <li v-for="menu in list" :key="menu.id" @click="toMenuPage(menu.menuKey, menu.children)"><img :src="menu.logo"><br>{{menu.name}}</li>
    </ul>
  </div>
</template>

<script>
  export default {
    name: 'rowMenu',
    props: ['configuration'],
    data() {
      return {}
    },
    computed: {
      list() {
        return this.configuration.children;
      }
    },
    methods: {
      // 跳转菜单 内页
      toMenuPage(name, children) {
        this.$emit('toMenuPage', name, children);
      }
    }
  }
</script>

<style lang="scss">
.userCenter-row-menu {
  padding: 10px 10px 15px 10px;
  background-color: #fff;
  ul {
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: space-around;
    li {
      display: inline-block;
      text-align: center;
      color: #444;
      font-size: 14px;
      width: 100%;
      img {
        width: 22px;
        height: 22px;
        margin-bottom: 10px;
      }
      & + li {
        position: relative;
        &::before {
          position: absolute;
          content: '';
          left: 0;
          top: 11px;
          height: 24px;
          border-left: 1px solid #E5E5E5;
        }
      }
    }
  }
}
</style>