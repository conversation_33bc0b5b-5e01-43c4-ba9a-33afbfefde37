<template>
  <div class="member-kingkong-wrap">
    <div class="member-kingkong-content flex-left flex-wrap">
      <div @click="handlePage(item)" class="member-kingkong-item unit-1-4 flex-middle flex-center flex-vertical" v-for="(item, index) in kingkongData" :key="index">
        <img :src="item.logo" alt="" />
        <span class="member-kingkong-text">{{ item.name }}</span>
        <img class="member-kingkong-corner" src="@/assets/img/hot-icon.png" alt="" v-if="item.cornerMark == 'hot'"/>
        <img class="member-kingkong-corner" src="@/assets/img/new-icon.png" alt="" v-if="item.cornerMark == 'new'"/>
      </div>
    </div>
  </div>
</template>
<script>
import { getInfoByDynamicUrl } from "@/api/configurable/common";
export default {
  name: "memberKingkong",
  props: ["configuration"],
  data() {
    return {
      // 设置金刚区数据
      kingkongData: [],
    };
  },
  created() {
    // 获取配置信息数据
    this.getConferenceFront();
  },
  mounted() {},
  computed: {},
  methods: {
    // 获取配置信息数据
    async getConferenceFront() {
      let res = await getInfoByDynamicUrl(this.configuration.apiUrl);
      if (res.code == this.$successCode) {
        this.kingkongData = res.data;
      } else {
        this.$toast(res.info);
      }
    },
    handlePage(item) {
      if (item.status) {
        this.$emit("toMenuPage", item.router, []);
      }else {
        this.$toast("正在建设中...");
      }
      
    },
  },
};
</script>
<style scoped lang="scss">
.member-kingkong-wrap {
  margin-top: 13px;
  background: #f5f6fa;
  padding-left: 15px;
  padding-right: 15px;
  width: 100%;
  box-sizing: border-box;
  .member-kingkong-content {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 4px;
    padding-top: 9px;
    .member-kingkong-item {
      padding-top: 3px;
      padding-bottom: 15px;
      position: relative;
      img {
        width: 36px;
        margin-bottom: 6px;
      }
      .member-kingkong-text {
        font-weight: 400;
        font-size: 13px;
        color: #333333;
        line-height: 17px;
      }
      .member-kingkong-corner {
        width: 42px;
        height: 20px;
        position: absolute;
        top: -5px;
        right: -3px;
        margin-bottom: 0;
        animation: floatUpDown 2s ease-in-out infinite;
      }
      @keyframes floatUpDown {
        0%, 100% {
          transform: translateY(0);
        }
        50% {
          transform: translateY(-3px);
        }
      }
    }
  }
}
</style>
