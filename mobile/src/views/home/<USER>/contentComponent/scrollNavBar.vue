<template>
    <div class="scrollNavBar-page">
        <div class="top-row">
            <div class="scrollNavBar">
                <div style="width:-webkit-fill-available;">
                    <cube-scroll-nav-bar v-if="TypeLabels.length>0" :current="currentLabel" :labels="TypeLabels" @change="changeHandler" />
                </div>
            </div>
            <!-- <div class="filter-icon" @click="showPicker">
                <svg t="1583676606251" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2308" width="18" height="18">
                    <path d="M562.176 907.776c-13.312 0-25.6-3.072-37.376-9.216l-101.888-56.832c-25.088-13.312-40.448-39.424-40.448-68.096V450.048c0-4.608-2.56-9.728-6.144-12.288L140.8 248.832l-2.56-3.072c-22.016-22.016-27.136-54.784-14.848-83.456s39.424-46.08 70.656-46.08H829.44c30.72 0 58.368 17.92 71.168 46.08s7.168 60.416-13.824 83.456l-3.072 3.072L645.12 439.808c-3.584 3.072-5.632 7.68-5.632 12.288v377.856c0.512 43.52-34.304 77.824-77.312 77.824zM181.248 202.752l234.496 187.392c18.432 14.848 29.184 36.864 29.184 60.416v323.584c0 5.632 2.56 11.264 7.68 13.824l101.888 55.808c4.608 2.56 10.752 2.56 15.872 0 4.608-2.56 7.68-8.192 7.68-13.824V452.608c0-23.552 10.24-45.568 28.672-60.416l235.008-189.44c3.584-4.608 4.096-11.264 1.536-16.384s-7.68-8.704-13.824-8.704H194.048c-6.144 0-11.264 3.072-13.824 8.704-3.072 5.632-2.048 11.776 1.024 16.384z" p-id="2309"></path>
                    <path d="M876.032 524.288H701.44c-16.896 0-30.72-13.824-30.72-30.72s13.824-30.72 30.72-30.72h174.592c16.896 0 30.72 13.824 30.72 30.72 0.512 16.896-13.312 30.72-30.72 30.72z m0 115.712H701.44c-16.896 0-30.72-13.824-30.72-30.72s13.824-30.72 30.72-30.72h174.592c16.896 0 30.72 13.824 30.72 30.72 0.512 16.896-13.312 30.72-30.72 30.72z m0 115.2H701.44c-16.896 0-30.72-13.824-30.72-30.72s13.824-30.72 30.72-30.72h174.592c16.896 0 30.72 13.824 30.72 30.72s-13.312 30.72-30.72 30.72z" p-id="2310"></path>
                </svg>
            </div> -->
            <filterArea class="filterArea" :ifshow='ifShowfilterArea' :options='this.options' :selectPickerValue='selectPickerValue' @cancle="cancelPicker" @submit="selectPicker"></filterArea>
        </div>
        <!-- 分类标签 -->
        <div class="sub-radio-area" v-if="subType && subType.length">
            <div :class="ifSpread?'marker-group-radio':'marker-group-radio marker-group-radio-spread'">
                <div @click="subTypeClick(item)" :style="item.style" v-for="item in subType">{{item.name}}</div>
            </div>
            <img v-if="showSpread" :class="ifSpread?'spread-button':'spread-button spread-button-spread'" @click="spreadClick()" src="@/assets/img/spread_button.png" alt="">
        </div>
    </div>
</template>
<script>
import filterArea from '@/components/newCommon/filterArea.vue'
export default {
    props: ['configuration'],
    components: {
        filterArea
    },
    data () {
        return {
            currentLabel: '全部',
            TypeLabels: [
                // '全部'
            ],
            subType: [],
            picker: null,
            selectPickerValue: -1,//只读属性
            options: [
                {
                    text: '不限',
                    value: -1,
                },
                {
                    text: '只看线上直播',
                    value: 6,
                },
                {
                    text: '只看线下会议',
                    value: 0,
                }
            ],
            showSpread: false, //显示展开按钮
            ifSpread: true,   //展开状态
            ifShowfilterArea: false,
        }
    },
    watch: {
        $route: {
            handler (val) {
                this.init()
            },
            deep: true
        }
    },
    mounted () {
        this.init()
    },
    methods: {
        async init () {
            this.TypeLabels = []
            this.configuration.extraInfo.map(item => {
                this.TypeLabels.push(item.name)
            })
            await this.routerInit()
            //计算是否需要展开按钮
            this.coumpteSpread()
        },
        //接收路由初始化
        routerInit () {
            if (this.$route.params.typeId) {
                this.configuration.extraInfo.map(item => {
                    if (item.id == this.$route.params.typeId) {
                        this.currentLabel = item.name
                        this.subType = item.children
                    }
                })
            }
            //是否有二级标签
            if (this.$route.params.subType && this.subType.length) {
                this.subType.map(item => {
                    item.style = ""
                    if (item.id == this.$route.params.subType) {
                        item.style = "background:#f7e7dc; color:#ff620d;"
                        //将二级分类筛选条件送至store
                        if (item.codeValueId) {
                            item.menuParams = [
                                {
                                    menuParamKey: "classify",
                                    menuParamValue: item.codeValueId
                                }
                            ]
                        }
                        this.$store.state.scrollNavBar.extraMenuParams = item.menuParams
                        // console.log(item.menuParams);

                    }
                })
            } else {
                this.$store.state.scrollNavBar.extraMenuParams = []
            }
            //筛选默认选中项
            if (this.$route.query.type !== undefined) {
                this.selectPickerValue = this.$route.query.type
            }
        },
        //一级分类
        changeHandler (cur) {
            this.configuration.extraInfo.map(item => {
                if (item.name == cur) {
                    this.subType = item.children
                    if (this.subType.length > 0) {
                        this.subType.map(item => {
                            item.style = ""
                        })
                        this.subType[0].style = "background:#f7e7dc; color:#ff620d;"
                    }
                    this.$router.replace({
                        name: 'targetPage',
                        params: {
                            pageId: this.$route.params.pageId,
                            typeId: item.id,
                            subType: item.children.length > 0 ? item.children[0].id : 0
                        },
                        query: {
                            platformId: localStorage.getItem('platformId'),
                            type: this.$route.query.type
                        }
                    }).catch(err => { err })
                }
            })
        },
        //二级分类选择
        subTypeClick (cur) {
            console.log(cur);
            this.subType.map(item => {
                item.style = ""
            })
            cur.style = "background:#f7e7dc; color:#ff620d;"
            //将二级分类筛选条件送至store

            this.$router.replace({
                name: 'targetPage',
                params: {
                    pageId: this.$route.params.pageId,
                    typeId: this.$route.params.typeId,
                    subType: cur.id
                },
                query: {
                    platformId: localStorage.getItem('platformId'),
                    type: this.$route.query.type
                }
            })
            this.$forceUpdate()
        },

        //筛选
        showPicker () {
            // if (!this.picker) {
            //     this.picker = this.$createPicker({
            //         title: '筛选内容',
            //         data: [this.options],
            //         swipeTime: 500,
            //         selectedIndex: [this.getOptionIndex(this.selectPickerValue)],
            //         onSelect: this.selectPicker,
            //         onCancel: this.cancelPicker
            //     })
            // }
            // this.picker.show()
            this.ifShowfilterArea = true
        },
        // getOptionIndex (value) {
        //     let idx = 0
        //     this.options.map((item, index) => {
        //         if (item.value == value) {
        //             idx = index
        //         }
        //     })
        //     return idx
        // },
        selectPicker (val) {
            this.$router.replace(
                {
                    name: this.$route.name,
                    params: this.$route.params,
                    query: {
                        platformId: localStorage.getItem('platformId'),
                        bizId: localStorage.getItem('bizId'),
                        type: this.options[val].value,
                    }
                })
            this.ifShowfilterArea = false
        },
        cancelPicker () {
            this.ifShowfilterArea = false
        },
        coumpteSpread () {
            this.ifSpread = true
            if (document.querySelector('.marker-group-radio')) {
                document.querySelector('.marker-group-radio').style.height = 'auto'
            }


            if (document.querySelector('.marker-group-radio') && document.querySelector('.marker-group-radio').scrollHeight > 25) {
                //超过一行
                this.showSpread = true
            } else {
                this.showSpread = false
            }
        },
        spreadClick () {
            this.ifSpread = !this.ifSpread
            if (this.ifSpread) {
                document.querySelector('.marker-group-radio').style.height = document.querySelector('.marker-group-radio').scrollHeight + 'px'
            } else {
                document.querySelector('.marker-group-radio').style.height = '23px'
            }
        }
    }
}
</script>
<style lang='scss' scoped>
.scrollNavBar-page {
    background-color: #fff;
}
.top-row {
    position: relative;
    display: flex;
    align-items: center;
}
.scrollNavBar {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 0 10px;
    min-height: 30px;
    width: 100vw;
}
.marker-group-radio {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    transition-duration: 100ms;
    animation-duration: 100ms;
    div {
        font-size: 10px;
        color: #999;
        background: #eee;
        border-radius: 4px;
        height: 18px;
        line-height: 18px;
        text-align: center;
        padding: 0px 10px;
        margin: 0 5px 5px 5px;
    }
}
.marker-group-radio-spread {
    overflow: hidden;
}

.spread-button {
    height: 16px;
    transform: rotateZ(180deg);
    transition-duration: 300ms;
}
.spread-button-spread {
    transform: rotateZ(0deg);
}
.sub-radio-area {
    margin: 5px 12px;
    display: flex;
    position: relative;
}
.filter-icon {
    padding: 3px;
}
.filterArea {
    position: absolute;
    top: 0;
    left: 0;
}
</style>
<style  lang='scss'>
.scrollNavBar {
    .cube-scroll-nav-bar-item {
        // position: relative;
        padding: 10px;
    }
    .cube-scroll-nav-bar-item_active > span {
        font-weight: bolder !important;
    }
    //下划线
    // .cube-scroll-nav-bar-item_active ::after {
    //     width: 40%;
    //     position: absolute;
    //     left: 30%;
    //     bottom: 5px;
    //     height: 2px;
    //     content: "";
    //     background-color: #ff620d;
    // }
    .cube-scroll-nav-bar_horizontal .cube-scroll-wrapper {
        text-align: left;
    }
}
</style> 