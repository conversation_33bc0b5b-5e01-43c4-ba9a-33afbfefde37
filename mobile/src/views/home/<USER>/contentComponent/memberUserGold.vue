<template>
  <div class="member-user-gold-wrap" v-if="memberStatus" @click="handleClickCoin">
    <div class="member-user-gold-content flex-middle">
      <div class="member-user-gold-left unit flex-middle">
        <span class="member-user-gold-num">{{ pointInfos.balance || 0 }}</span>
        <span class="member-user-gold-text">可用易币</span>
        <!-- 过期易币 -->
        <span v-if="pointInfos.expiredBalance" class="member-user-gold-text expired-gold-text">有即将过期的易币</span>
      </div>
      <div v-if="signInfo.points" class="member-user-gold-right sign-in unit-0 flex-middle" :class="{ 'signed-in': signInfo.signInStatus }" @click.stop="changeSignIn">
        <img src="https://oss.ienmore.com/resources/public/img/health/new/integral_1.png" alt="" />
        <span class="member-user-gold-txt">{{ signInfo.signInStatus ? "已签到+" + signInfo.points : "签到+" + signInfo.points }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions } from "vuex";
import { userSignIn, getSignInfo } from "@/api/memberApi";
export default {
  name: "memberUserGold",
  data() {
    return {
      signInfo: {},
    };
  },
  created() {
    // 获取用户的签到信息
    this.getSignIn();
  },
  mounted() {
    this.$nextTick(() => {
      this.setMarginTop();
    });
  },
  computed: {
    pointInfos() {
      // 查询积分信息
      return this.$store.state.pointInfos;
    },
    memberInfo() {
      return this.$store.state.memberInfo;
    },
    userSignInInfo() {
      return this.$store.state.userSignInInfo;
    },
    // 是否开启会员管理
    memberStatus() {
      return this.$store.state.memberStatus;
    },
  },
  methods: {
    ...mapActions(["getMemberPoints"]),
    async changeSignIn() {
      if (!this.signInfo.signInStatus) {
        let params = {
          platformId: localStorage.getItem("platformId"),
        };
        let { data } = await userSignIn(params);
        this.$store.commit("changeUserSignInInfo", data);
        this.$store.commit("changeIsSignInStatus", true);
        this.getSignIn();
        this.getMemberPoints();
      }
    },
    async getSignIn() {
      let { data } = await getSignInfo();
      this.signInfo = data;
    },
    // 设置元素margin-top
    setMarginTop() {
      // 延迟处理
      const setMarginTopWithRetry = () => {
        const targetElement = document.querySelector(".member-user-gold-wrap");
        if (targetElement && targetElement.nextElementSibling) {
          targetElement.nextElementSibling.style.marginTop = "27px";
        } else {
          setTimeout(setMarginTopWithRetry, 300);
        }
      };
      setTimeout(() => {
        setMarginTopWithRetry();
      }, 500);
    },
    handleClickCoin() {
      this.$router.push({
        path: "/myCoin",
        query: {
          platformId: this.$route.query.platformId,
        },
      });
    },
  },
};
</script>
<style scoped lang="scss">
.member-user-gold-wrap {
  position: fixed;
  width: 100%;
  height: 42px;
  padding: 0 15px;
  z-index: 10;
  left: 0;
  top: 120px;
  z-index: 100;
  .member-user-gold-content {
    width: 100%;
    height: 100%;
    background: url("https://oss.ienmore.com/resources/public/img/health/new/gold_center_bg.png") no-repeat;
    background-size: 100% 100%;
    border-radius: 4px;
    padding-left: 15px;
    padding-right: 10px;
    box-sizing: border-box;
    .member-user-gold-left {
      .member-user-gold-num {
        font-weight: 600;
        font-size: 18px;
        color: #26527b;
      }
      .member-user-gold-text {
        font-weight: 400;
        font-size: 12px;
        color: #26527b;
        line-height: 18px;
        margin-left: 4px;
      }
      .expired-gold-text {
        color: #FF4D4F;
      }
    }
    .member-user-gold-right {
      height: 26px;
      border-radius: 13px;
      padding: 0 8px;
      img {
        width: 15px;
        margin-right: 3px;
      }
      .member-user-gold-txt {
        font-weight: 500;
        font-size: 11px;
        color: #ffffff;
      }
    }
    .sign-in {
      background: linear-gradient(90deg, #15b4db 0%, #0097df 100%);
      border: 1px solid #11adf7;
    }
    .signed-in {
      background: #84baed;
      border: 1px solid #84baed;
    }
  }
}
</style>
