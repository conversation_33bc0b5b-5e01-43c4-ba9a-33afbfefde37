<template>
  <div class="member-user-banner-wrap" v-if="bannerInfo.items && bannerInfo.items.length">
    <cube-slide :auto-play="bannerInfo.interval>0" :interval="bannerInfo.interval * 1000" :data="bannerInfo.items" allowVertical ref="slideImage">
      <cube-slide-item class="swiperItem-content" :key="index" @click.native="openUrl(item)" v-for="(item, index) in bannerInfo.items" >
        <img :src="item.logo" alt=""/>
      </cube-slide-item>
    </cube-slide>
  </div>
</template>
<script>
import { getInfoByDynamicUrl } from "@/api/configurable/common";
export default {
  name: "memberUserBanner",
  props: ["configuration"],
  data() {
    return {
      bannerInfo: {},
    };
  },
  created() {
    // 获取配置信息数据
    this.getConferenceFront();
  },
  mounted() {},
  computed: {},
  methods: {
    // 获取配置信息数据
    async getConferenceFront() {
      let res = await getInfoByDynamicUrl(this.configuration.apiUrl);
      if (res.code == this.$successCode) {
        this.bannerInfo = res.data || {};
      } else {
        this.$toast(res.info);
      }
    },
    openUrl(item) {
      window.open(item.url);
    },
  },
};
</script>
<style scoped lang="scss">
.member-user-banner-wrap {
  width: 100%;
  height: 85px;
  padding: 0 15px;
  box-sizing: border-box;
  margin-top: 13px;
  img {
    border-radius: 4px;
    width: 100%;
  }
  :deep(.cube-slide-dots) {
    .active {
      background: var(--color-primary);
    }
    span{
      height: 2px;
      margin-bottom: 5px;
      margin-right: 2px;
    }
  }
}
</style>
