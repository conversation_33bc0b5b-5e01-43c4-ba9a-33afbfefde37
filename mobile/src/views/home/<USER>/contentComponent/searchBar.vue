<template>
    <div class="header_wrap ">
        <div class="header_input" @click="showSearch">
            <img src="@/assets/img/search.png" alt="" class="iconSearch">
            <span class="text">{{configuration.extraInfo.placeholder}}</span>
        </div>
        <div class='head-right' v-show="configuration.extraInfo.rightButton" @click="showHidden = !showHidden">
            <img src="@/assets/img/wechat_icon.png" alt="">
        </div>
        <officialCode style="bottom:45px;" :visiable.sync="showHidden"></officialCode>
    </div>
</template>

<script>
import officialCode from '@/components/newCommon/officialCode';
export default {
    props: ['configuration'],
    data () {
        return {
            showHidden: false,
        }
    },
    methods: {
        showSearch () {
            //根据类型不同展示生鲜大学的搜索内页还是行家活动的内页,同时根据参数自定义内部元素
            this.$store.state.searchBar.configuration = this.configuration
            let query = {}
            this.configuration.children[0].menuParams.map(item => {
                query[item.menuParamKey] = item.menuParamValue
            })
            query.platformId = localStorage.getItem('platformId')
            this.$router.push({ name: 'searchAll', query: query });
        },
    },
    components: { officialCode }
}
</script>

<style scoped lang="scss">
$color: #004da1;
.header_wrap {
    padding: 0 12px;
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
}
.header_input {
    height: 35px;
    background: #f5f5f5;
    border-radius: 4px;
    display: flex;
    padding-left: 10px;
    justify-content: left;
    align-items: center;
    flex: 8;
}
.iconSearch {
    width: 12px;
    height: 12px;
    margin-right: 7px;
}
.text {
    font-size: 13px;
    color: #999;
}
.head-right img {
    margin-left: 10px;
    width: 24px; 
}
</style>