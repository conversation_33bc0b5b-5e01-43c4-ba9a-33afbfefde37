<template>
  <div class="member-user-info-wrap" :class="{ 'no-login': !loginStatus }">
    <div class="member-user-info-container flex-left">
      <div class="top_img_body">
        <div class="position_img_top">
          <img @click="jumpBtn" :src="personProfile.headImg ? personProfile.headImg : imgPlatChange()" alt="" class="member-user-info-avater" />
          <div v-if="loginStatus" @click="jumpBtn" class="top_edit_icon">
            <img src="@/assets/img/editTopIcon.png" alt="">
            <span>编辑</span>
          </div>
        </div>
      </div>
      <div class="member-user-info-content" v-if="loginStatus">
        <div class="member-user-info-name flex-middle">
          <div class="lineElipsis info-name" :style="memberStatus ? 'max-width: 100px;' : ''">{{ personProfile.memberName }}</div>
          <img v-if="memberStatus" :src="memberInfo.logoUrl" alt="" class="info-vip-icon" />
          <div v-if="memberStatus" class="info-vip-name">{{ memberInfo.name }}</div>
        </div>
        <div class="member-user-info-mark flex-middle flex-nowrap">
          <span class="info-real-name flex-middle" @click="popBtn" :class="getBerPointInfo.auditFlag == 0 || getBerPointInfo.auditFlag == 2 || getBerPointInfo.auditFlag == 3 ? 'red_color' : 'black_color'">
            <svg class="svg-icon" aria-hidden="true">
              <use v-if="memberInfo.auditFlag == 1" v-bind:xlink:href="'#icon-info_1'"></use>
              <use v-else v-bind:xlink:href="'#icon-leftbar_info1'"></use>
            </svg>
            <span class="info-real-name-text">{{ memMethoed(getBerPointInfo.auditFlag) }}</span>
            <svg v-if="memberInfo.auditFlag == 1" class="svg-icon-right" aria-hidden="true">
              <use v-bind:xlink:href="'#icon-Right'"></use>
            </svg>
            <img v-else class="svg-icon-right" src="@/assets/img/redTopIcon.png" alt="">
          </span>
          <span class="tag_body" ref="tagContainer" v-if="memberInfo.levelTitles && memberInfo.levelTitles.length && memberStatus">
            <span class="info-mark-item" v-for="(item, index) in memberInfo.levelTitles" :ref="`tag${index}`" v-if="tagList[index]">{{ item }}</span>
            <span class="info-mark-item eml_class" v-if="moreShow">...</span>
          </span>
        </div>
        <div class="member-user-info-position lineElipsis">
          <span>{{ personProfile.position }}</span>
        </div>
        <div class="member-user-info-company lineElipsis">
          <span>{{ personProfile.company }}</span>
        </div>
      </div>
      <div v-else class="member-user-info-login unit flex-middle" @click.stop="pageLogin">
        <span>点击登录/注册</span>
        <span class="unit"></span>
        <van-icon name="arrow" />
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions } from "vuex";
import { getMemberLevelSwitchStatus, getBioChinaInfo } from "@/api/memberApi";
export default {
  name: " memberUserInfo",
  props: ["configuration"],
  data() {
    return {
      moreShow: false,
      tagList: [],
      creaWidth: 0,
      platformId: 3,
    };
  },
  created() {
    this.$store.dispatch("getPerprofile");
  },
  components: {},
  mounted() {
    this.platformId = localStorage.getItem("platformId");
    this.getMemberLevelInfo();
    this.getMemberLevelSwitchStatus();
    this.getMemberPoints();
  },
  watch: {
    personProfile: {
      handler(val) {
        this.getMemberAuthStatus();
      },
    },
    "memberInfo.levelTitles": {
      handler(val) {
        this.memberInfo.levelTitles.forEach((item, index) => {
          this.tagList.push(true);
        });
        this.$nextTick(() => {
          this.isHidden();
        });
      },
    },
  },
  computed: {
    getBerPointInfo() {
      // 获取个人认证信息
      return this.$store.state.getBerPointInfo;
    },
    memberStatus() {
      return (this.memberStatus = this.$store.state.memberStatus);
    },
    memberInfo() {
      // 查询会员信息
      return this.$store.state.memberInfo;
    },
    pointInfos() {
      // 查询积分信息
      return this.$store.state.pointInfos;
    },
    personProfile() {
      return this.$store.state.personal.personProfile;
    },
    // 登录状态
    loginStatus() {
      return this.$store.state.loginStatus;
    },
  },
  methods: {
    ...mapActions(["getMemberLevelInfo", "getMemberPoints", "getMemberAuthStatus"]),
    // 点击打开提交审核弹出
    popBtn() {
      getBioChinaInfo().then((res) => {
        if (!res.data) {
          //if (this.getBerPointInfo.auditFlag == 0) return this.$toast("实名认证审核中，如有问题请联系主办方");
          this.$store.commit("changeUserPopShow", true);
        } else {
          this.$store.commit("changeBioChinaShow", true);
          this.$store.commit("changeBioChinaImg", res.data);
        }
      });
    },
    // 校验判断标签是否超出最大宽度
    isHidden() {
      this.creaWidth = 0;
      this.$nextTick(() => {
        setTimeout(() => {
          this.memberInfo.levelTitles.forEach((item, index) => {
            const container = this.$refs.tagContainer;
            const tags = this.$refs[`tag${index}`];
            if (!tags || !container) {
              return this.isHidden();
            }
            if (!tags[0] || !container) {
              return this.isHidden();
            }
            this.creaWidth = this.creaWidth + tags[0].offsetWidth;
            if (this.creaWidth > container.offsetWidth) {
              this.moreShow = true;
              this.tagList[index] = false;
            }
          });
        }, 300);
      });
    },
    // 默认头像
    imgPlatChange() {
      if (this.platformId == 3) {
        return require("@/assets/img/new_head_img.png");
      } else {
        return require("@/assets/img/default.png");
      }
    },
    // 跳转实名认证
    jumpBtn() {
      if (this.platformId != 3) return;
      this.$router.push({ name: "userInfoSet" });
    },
    // 查询会员等级开关是否打开
    getMemberLevelSwitchStatus() {
      getMemberLevelSwitchStatus().then((res) => {
        if (res.code == this.$successCode) {
          this.$store.commit("getMemberLevelSwitchStatus", res.data);
        }
      });
    },
    // 认证状态校验
    memMethoed(val) {
      switch (0) {
        case 0:
          if (this.getBerPointInfo.auditFlag == 3) {
            return "去实名";
          }
          if (this.getBerPointInfo.auditFlag == 0) {
            return "待审核";
          }
          if (this.getBerPointInfo.auditFlag == 2) {
            return "待重新提交";
          }
          if (this.getBerPointInfo.auditFlag == 1) {
            return "已实名";
          }
          break;
        case 1:

        default:
          break;
      }
    },
    pageLogin() {
      this.$router.push({ name: "userInfoSet" });
    },
  },
};
</script>
<style scoped lang="scss">
.member-user-info-wrap {
  width: 100%;
  height: 150px;
  background: url("https://oss.ienmore.com/resources/public/img/health/new/user_bg.png") no-repeat;
  background-size: 100% 100%;
  position: relative;
  .member-user-info-container {
    width: 100%;
    padding: 15px;
    box-sizing: border-box;
    // background: #111137;
    position: relative;
    .member-user-info-avater {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      margin-right: 15px;
    }
    .member-user-info-content {
      min-width: 0;
      width: 100%;
      .lineElipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .member-user-info-name {
        font-weight: 500;
        font-size: 18px;
        color: #ffffff;
        line-height: 24px;
        .info-name {
          min-width: 0;
        }
        .info-vip-icon {
          // height: 17px;
          width: auto;
          margin-left: 10px;
        }
        .info-vip-name {
          font-weight: 400;
          font-size: 12px;
          color: #dce6fc;
          margin-left: 4px;
        }
        img {
          height: 17px;
        }
      }
      .member-user-info-mark {
        margin-top: 4px;
        .info-real-name {
          height: 16px;
          // width: 66px;
          background: #dce6fc;
          border-radius: 2px;
          padding: 2px 4px;
          margin-right: 5px;
          .svg-icon {
            width: 16px;
            height: 13px;
          }
          .info-real-name-text {
            width: inherit;
            font-size: 12px;
            color: #333333;
            line-height: 12px;
            font-weight: 400;
            transform: scale(0.75);
            margin-left: -3px;
            margin-top: 1px;
          }
          .svg-icon-right {
            width: 9px;
            height: 8px;
            margin-left: -4px;
            margin-top: 1px;
          }
        }
        .red_color {
          background: #ffefef;
          .info-real-name-text {
            color: #ff4d4f;
          }
          .svg-icon-right {
            color: #ff4d4f;
          }
        }
        .info-mark-item {
          background: #dce6fc;
          border-radius: 2px;
          padding: 3px 5px;
          font-weight: 400;
          font-size: 10px;
          color: #333333;
          line-height: 17px;
          margin-right: 5px;
        }
        .tag_body {
          width: 57%;
          white-space: nowrap; /* 不换行 */
          overflow: hidden; /* 超出隐藏 */
          text-overflow: ellipsis; /* 文本溢出用省略号表示 */
        }
      }
      .member-user-info-position {
        margin-top: 8px;
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        line-height: 20px;
        margin-bottom: 4px;
      }
      .member-user-info-company {
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        line-height: 20px;
      }
    }
    .member-user-info-login {
      font-weight: 400;
      font-size: 18px;
      color: #ffffff;
    }
  }
}
.no-login {
  padding-top: 20px;
}
.eml_class {
  height: 17px;
  position: absolute;
  display: inline-flex;
  align-items: center;
}
.userPopClass {
  ::v-deep .hidden-card-outside {
    padding-bottom: 23vh;
  }
}
.top_img_body {
  margin-right: 15px;
  .top_edit_icon {
    display: flex;
    align-items: center;
    width: 45px;
    height: 16px;
    background: #DCE6FC;
    border-radius: 1px 1px 1px 1px;
    justify-content: center;
    position: absolute;
    left: 8px;
    bottom: -11px;
    span {
      font-weight: 400;
      font-size: 10px;
      color: #008CFF;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-top: 1.5px;
      margin-left: 1px;
    }
    img {
      width: 10px;
      height: 10px;
      margin-right: 1px;
    }
  }
}
.position_img_top {
  position: relative;
}
</style>
