<template>
    <div class="swiperImageZql" v-show="swiperImageItems.length">
        <cube-slide v-if="swiperImageItems.length" :data="swiperImageItems" @change="changePage" class="swipers" ref="slideImage" allowVertical>
            <cube-slide-item class="swiperItem-content" :key="indexs" @click.native="clickImage(item, indexs)" v-for="(item, indexs) in swiperImageItems">
                <img class="swiperItem" :src="item.bannerImg">
            </cube-slide-item>
        </cube-slide>
    </div>
</template>
<script>
import { getInfoByDynamicUrl } from '@/api/configurable/common'

export default {
    components: {},
    props: ['configuration'],
    data () {
        return {
            swiperImageItems: []
        };
    },
    computed: {
    },
    watch: {
        $route: {
            handler (val) {
                setTimeout(() => {
                    this.getInfo()
                }, 100);
            },
            deep: true,
            immediate: true
        }
    },
    mounted () {
        // this.getInfo()




    },
    updated () {
        setTimeout(() => {
            this.$nextTick(() => {
                let elCircle = document.getElementsByClassName('cube-slide-dots')[0];
                if (elCircle) {
                    let elSpan = elCircle.querySelectorAll('span');
                    for (let i = 0; i < elSpan.length; i++) {
                        elSpan[i].style.width = '5px';
                        elSpan[i].style.height = '5px';
                    }
                }
            })
        }, 200)
    },
    methods: {
        getInfo () {
            let params = {}
            this.configuration.menuParams.map(item => {
                params[item.menuParamKey] = item.menuParamValue
            })

            //加入二级标签和过滤器的参数
            if (this.$store.state.scrollNavBar.extraMenuParams.length) {
                this.$store.state.scrollNavBar.extraMenuParams.map(item => {
                    params[item.menuParamKey] = item.menuParamValue
                })
            }
            if (this.$route.query.type != null) {
                if (params.type != this.$route.query.type) {
                    params.type = -1
                } else {
                    params.type = this.$route.query.type
                }
            }
            getInfoByDynamicUrl(this.configuration.apiUrl, params).then(res => {
                this.swiperImageItems = []
                setTimeout(() => {
                    this.swiperImageItems = res.data.list
                }, 20);
            })
        },
        clickImage (item, indexs) {
            if (item.type == 'diy' && !item.topUrl.length) return
            this.naviToDetails(this.$route.query.platformId, item.type, item)
        },
        changePage (index) {
        },
    }
}
</script>
<style lang='scss' scoped>
.swiperImageZql {
    padding: 0 12px;
    height: 220px;
    border-radius: 5px;
    background-color: #fff;
}
.swipers {
    height: 220px;
}
.swiperItem-content {
    height: 220px;
}
.swiperItem {
    height: 210px;
    border-radius: 5px;
    object-fit: cover;
    border: 2px solid white;
}
</style>
<style >
.swiperImageZql .cube-slide-dots {
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 351px;
}
.swiperImageZql .cube-slide-dots span {
    border-radius: 100%;
    height: 5px;
    width: 5px;
    background-image: url("../../../../assets/img/icon_dot_gray.png");
    background-size: 100% 100%;
    object-fit: cover;
    margin: 0px 4px;
}
.swiperImageZql .cube-slide-dots > span.active {
    background-image: url("../../../../assets/img/icon_dot_orange.png");
}
</style>