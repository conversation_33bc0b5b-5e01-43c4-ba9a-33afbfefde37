<template>
    <div class="square2Column" v-show="infoData.length>0">
        <div class="title-line">
            <div v-show="configuration.name" class="square2Column-title">
                <img :src="configuration.logo" alt="" srcset="">
                <span>{{configuration.name}}</span>
                <!-- <span style="color:#999;">{{configuration.subName || configuration.extraInfo.subtitle}}</span> -->
            </div>
            <div v-show="configuration.extraInfo.showMore" class="more-icon" @click="showMore">
                <span>更多</span>
                <img style='width:5px;height:10px;' src="@/assets/img/more_icon.png" alt="">
            </div>
        </div>
        <div class='square-2-column'>
            <div class="square-2-column-item" v-for="item in infoData" @click="clickItem(item)">
                <div>
                    <img class="info-image" :src="item.previewPic || item.coverImg || item.coursePic" alt="">
                    <div class="info-title">{{item.courseName || item.name || item.infoTitle}}</div>
                </div>
                <div class="info-detail">
                    <div>
                        <svg v-if="[TYPE[0],TYPE[1],TYPE[5],TYPE[6]].includes(item.type)" style="margin-right:4px;" width="12px" height="12px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <g id="画板备份-2" transform="translate(-33.000000, -1675.000000)" fill-rule="nonzero">
                                    <g id="编组-12" transform="translate(20.000000, 1534.000000)">
                                        <g id="时间备份-8" transform="translate(11.000000, 139.000000)">
                                            <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="24" height="24"></rect>
                                            <path d="M12,2 C6.45833334,2 2,6.45833334 2,12 C2,17.5416667 6.45833334,22 12,22 C17.5416667,22 22,17.5416667 22,12 C22,6.45833334 17.5416667,2 12,2 Z M16.875,13.6666667 L11.2083333,13.6666667 L11.1666667,13.6666667 C11.0416667,13.6666667 10.9166667,13.5833333 10.8333333,13.4583333 C10.7916667,13.375 10.75,13.3333333 10.75,13.25 L10.75,13.2083333 L10.75,7.45833334 C10.75,7.20833334 10.9166667,7 11.1666667,7 C11.4166667,7 11.5833333,7.20833334 11.5833333,7.45833334 L11.5833333,12.8333333 L16.875,12.8333333 C17.1666667,12.8333333 17.4166667,13 17.4166667,13.25 C17.4166667,13.5 17.1666667,13.6666667 16.875,13.6666667 L16.875,13.6666667 Z" id="形状" fill="#DADADA"></path>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </svg>
                        <span v-if="[TYPE[0],TYPE[1]].includes(item.type)">{{formatTime(item.beginTime || item.publishTime || item.createTime,true)}}</span>
                        <span v-if="[TYPE[5],TYPE[6]].includes(item.type)">{{formatTime( item.beginTime || item.publishTime || item.createTime,'dateTime')}} </span>
                        <span v-if="[TYPE[0],TYPE[1]].includes(item.type)&&item.endTime"> - {{formatTime(item.endTime,'noYear')}}</span>
                    </div>
                    <!-- <div v-if="item.sponsorName || item.sponsorLogo ">
                        <img v-if="item.sponsorLogo" :src="item.sponsorLogo" alt="">
                        <span>{{item.sponsorName}}</span>
                    </div> -->
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { getInfoByDynamicUrl } from '@/api/configurable/common'
export default {
    components: {},
    props: ['configuration'],
    data () {
        return {
            infoData: [],
            params: {}
        };
    },
    computed: {},
    watch: {
        $route: {
            handler (val) {
                setTimeout(() => {
                    this.getInfo()
                }, 100);
            },
            deep: true,
            immediate: true
        }
    },
    mounted () {
        //  this.getInfo()
    },
    methods: {
        getInfo () {
            let params = {}
            this.configuration.menuParams.map(item => {
                params[item.menuParamKey] = item.menuParamValue
            })
            if (this.configuration.extraInfo.pageFlag) {
                params.pageNum = 1
                params.pageSize = this.configuration.extraInfo.defaultPageSize
            }

            //加入二级标签和过滤器的参数
            if (this.$store.state.scrollNavBar.extraMenuParams.length) {
                this.$store.state.scrollNavBar.extraMenuParams.map(item => {
                    params[item.menuParamKey] = item.menuParamValue
                })
            }
            if (this.$route.query.type != null) {
                if (params.type != this.$route.query.type) {
                    params.type = -1
                } else {
                    params.type = this.$route.query.type
                }
            }

            this.params = params
            getInfoByDynamicUrl(this.configuration.apiUrl, params).then(res => {
                // console.log(res);
                this.infoData = res.data.list
            })
        },
        showMore () {
            this.naviToCommonList(this.params)
        },
        clickItem (item) {
            //platformId, type, id, targetUrl
            this.naviToDetails(this.$route.query.platformId, item.type, item)
        }
    }
}
</script>
<style lang='scss' scoped>
.square2Column {
    margin-top: 10px;
    background: #fff;
    padding: 10px 12px;
}
.title-line {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}
.square2Column-title {
    display: flex;
    align-items: center;
    margin: 4px 0;
    img {
        height: 24px;
        width: 24px;
    }
    span {
        font-weight: 400;
        font-size: 15px;
        margin-left: 6px;
    }
}
.more-icon {
    display: flex;
    align-items: center;
    span {
        float: right;
        color: #999;
        font-size: 12px;
    }
    img {
        margin-left: 5px;
        height: 16px;
    }
}
.square-2-column {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
}
.square-2-column-item {
    width: 49%;
    margin-top: 7.5px;
    border: 1px solid #f5f5f5;
    border-radius: 4px;
}
.info-image {
    width: -webkit-fill-available;
    border-radius: 5px;
    height: 100px;
}
.info-title {
    margin-top: 4px;
    margin-left: 4px;
    line-height: 18px;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    font-size: 14px;
    -webkit-box-orient: vertical;
    font-weight: 400;
}
.no-cover {
    background: #3151bb;
    color: white;
    padding: 5px;
    border-radius: 5px;
}

.info-detail {
    margin-left: 4px;
    margin-bottom: 3px;
    min-height: 18px;
    margin-top: 5px;
    display: flex;
    flex-direction: column;
    justify-content: left;
    > div {
        font-size: 12px;
        line-height: 16px;
        color: #999;
        margin-right: 10px;
        display: flex;
        align-items: center;
        span {
            margin-left: 2px;
        }
        margin-bottom: 5px;
    }
    img {
        height: 20px;
        width: 20px;
        border-radius: 50%;
    }
}
</style>