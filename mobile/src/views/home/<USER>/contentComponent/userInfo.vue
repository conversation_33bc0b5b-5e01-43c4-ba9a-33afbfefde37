<template>
    <div class="userInfo-container">
      <div class="userInfo-content clear-fix">
        <img :src="userInfo.headImg?userInfo.headImg:require('@/assets/img/default.png')" class="userInfo-avater">
        <div class="userInfo-info">
          <div class="userInfo-info-name lineElipsis">{{userInfo.memberName}}</div>
          <div class="userInfo-info-company lineElipsis">{{userInfo.company}}</div>
          <div class="userInfo-info-company lineElipsis">{{userInfo.position}}</div>
          <div class="userInfo-info-progress-nav">
            <!-- <div class="userInfo-info-progress-bg"><div class="userInfo-info-progress"></div></div>
            资料完善度58% -->
            <router-link :to="{name: 'userInfoSet'}"><i class="cubeic-arrow"></i></router-link>
          </div>
        </div>
      </div>
      <div class="userInfo-white-bottom"></div>
    </div>
</template>

<script>
import { getUserInfo } from '@/api/userCenter';
export default {
  name: 'userInfo',
  props: [],
  data() {
    return {
      userInfo: {}, // 用户信息
    }
  },
  mounted() {
    this.getUserInfo()
  },
  methods: {
    // 获取 个人信息
    getUserInfo() {
      getUserInfo().then( res => {
        if (res.code == this.$successCode) {
          this.userInfo = res.data?res.data:{};
        }
      })
    },
  }
}
</script>
<style lang="scss" scoped>
  .userInfo-container {
    width: 100%;
    background-color: #FF7857;
    padding-top: 10px;
    padding-bottom: 10px;
    margin-bottom: -10px;
    .lineElipsis{
      white-space: nowrap;
      overflow-x: hidden;
      text-overflow: ellipsis;
    }
    .userInfo-content {
      padding: 18px 15px;
      margin: 0 15px;
      border-radius: 4px;
      background-color: #fff;
      box-shadow:0px 1px 5px 0px rgba(0,0,0,0.1);
      position: relative;
      .userInfo-avater {
        width: 67px;
        height: 67px;
        border-radius: 50%;
        float: left;
      }
      .userInfo-info {
        margin-left: 82px;
        .userInfo-info-name {
          color: #222;
          font-size: 18px;
          line-height: 24px;
          font-weight: 400;
          margin-bottom: 3px;
        }
        .userInfo-info-company {
          color: #666;
          font-size: 14px;
          line-height: 22px;
        }
        .userInfo-info-progress-nav {
          line-height: 18px;
          font-size: 10px;
          color: #999;
          .userInfo-info-progress-bg {
            width: 120px;
            height: 6px;
            border-radius: 3px;
            background-color: #eee;
            display: inline-block;
            .userInfo-info-progress {
              width: 58%;
              height: 100%;
              background:linear-gradient(90deg,rgba(255,128,13,1) 0%,rgba(255,98,13,1) 100%);
              border-radius: 3px;
            }
          }
          .cubeic-arrow {
            float: right;
            color: #ccc;
          }
        }
      }
    }
    .userInfo-white-bottom {
      height: 60px;
      background-color: #fff;
      margin-top: -50px;
      border-radius: 5px 5px 0 0;
    }
  }
</style>