<template>
    <div class="innerIframe">
        <iframe id="innerIframe" v-if="configuration.extraInfo.webUrl" width="100%" style="height:calc(100vh - 50px)" :src="configuration.extraInfo.webUrl" frameborder="0"></iframe>
    </div>
</template>
<script>
export default {
    components: {},
    props: ['configuration'],
    data () {
        return {
        };
    },
    computed: {},
    mounted () {
        window.addEventListener('message', (ev) => {
            try {
                // 屏蔽了控制台的广播
                if (typeof ev.data == 'object') { return }
                
                let item = JSON.parse(ev.data);
                if (item.type !== undefined && item.id) {
                    this.naviToDetails(localStorage.getItem('platformId'), item.type, item)
                }
            } catch (e) {
                console.log(e)
            }
        })
    },
    methods: {}
}
</script>
<style lang='scss' scoped>
.innerIframe {
    height: 100%;
}
</style>