<template>
    <div class="swiperArea" v-show="infoData.length">
        <cube-slide v-if="infoData.length" :data="infoData" :stopPropagation="true" @change="changePage" class="swipers" ref="slide" allowVertical>
            <cube-slide-item :key="indexs" v-for="(item, indexs) in infoData">
                <div @click="itemClick(item)" class="swiper-block-item">
                    <div class="title-row">
                        <div class="swiper-item-title-box">
                            <div class="swiper-item-title">{{item.name}}</div>
                        </div>
                        <div class="marker-radio-orange">{{item.conferenceType}}</div>
                    </div>
                    <div class="flex-center" style="width:100%;">
                        <div class='swiper-left-image unit'>
                            <img :src="item.coverImg" alt="">
                        </div>
                        <div class="unit swiper-item-right">
                            <div class="red-radio-button">立即了解</div>
                            <div>
                                <!-- <span>icon</span> -->
                                <span>{{formatAddress(item)}}</span>
                            </div>
                            <div>
                                <!-- <span>icon</span> -->
                                <span>{{formatTime(item.beginTime || item.publishTime || item.createTime,false)}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </cube-slide-item>
        </cube-slide>
    </div>
</template>
<script>
import { getInfoByDynamicUrl } from '@/api/configurable/common'
export default {
    components: {},
    props: ['configuration'],
    data () {
        return {
            infoData: [],
        };
    },
    computed: {},
    watch: {
        $route: {
            handler (val) {
                setTimeout(() => {
                    this.getInfo()
                }, 100);
            },
            deep: true,
            immediate: true
        }
    },
    mounted () {
        //  this.getInfo()
    },
    methods: {
        getInfo () {
            let params = {}
            this.configuration.menuParams.map(item => {
                params[item.menuParamKey] = item.menuParamValue
            })
            getInfoByDynamicUrl(this.configuration.apiUrl, params).then(res => {
                this.infoData = []
                setTimeout(() => {
                    this.infoData = res.data.list
                }, 20);
            })
        },
        clickImage (item, indexs) {

        },
        changePage (index) {

        },
        itemClick (item) {
            this.naviToDetails(localStorage.getItem('platformId'), item.type, item)
        }
    }
}
</script>
<style lang='scss' scoped>
.swiperArea {
    margin: 10px 0;
    background: #fff;
    padding:10px 0;
}
.swiper-block-item {
    border: 1px solid #eee;
    height: 140px;
    border-radius: 5px;
    margin: 0 12px;
}
.swiper-item-title-box {
    height: 20px;
}
.swiper-item-title {
    margin-left: 10px;
    font-size: 16px;
    line-height: 20px;
    font-weight: 400;
    overflow: hidden;
    max-width: 250px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-word;
}
.swiper-left-image {
    margin-left: 10px;
    img {
        height: 90px;
        width: 150px;
        border-radius: 5px;
        margin-right: 10px;
        border: 1px solid #eee;
    }
}
.red-radio-button {
    background: #ff620d;
    border-radius: 15px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    display: inline-block;
    padding: 1px 10px;
    color: white;
}
.marker-radio-orange {
    border-radius: 8px;
    height: 16px;
    font-size: 12px;
    line-height: 14px;
    padding: 2px 5px;
    background-color: #ff620d;
    color: #fff;
}
.title-row {
    display: flex;
    justify-content: space-between;
    height: 30px;
    align-items: center;
    padding-right: 10px;
}
.swiper-item-right {
    display: flex;
    flex-direction: column;
    div {
        width: fit-content;
        margin-top: 6px;
        span {
            color: #999;
            font-size: 12px;
        }
    }
}
</style>
