<template>
  <div class="app_wrap flex-vertical">
    <pageContent :configuration="pageContent" :allConfig="allConfig" @changeQuitStatus="changeQuitStatus"></pageContent>
    <bottomNavTab v-if="bottomNavTab && !isPreviewEnviroment && !loginShow" :routerName="routeName" :configuration="bottomNavTab"></bottomNavTab>
    <!-- 退出提示语 -->
    <warn-dialog v-if="quitStatus" @close="quitStatus = false"></warn-dialog>
    
  </div>
</template>
<script>
import pageContent from "./component/pageContent.vue";
import bottomNavTab from "./component/bottomNavTab.vue";
import { getSliderStatus } from "@/api/medicine/homePage";
import WarnDialog from "@/components/common/warnDialog";
export default {
  name: "home",
  components: {
    pageContent,
    bottomNavTab,
    WarnDialog
  },
  inject: ["getAdvertisement"],
  data() {
    return {
      // 控制退出提示弹窗
      quitStatus: false,
    };
  },
  //  获取曝光时间
  created() {
    this.$store.commit("changeSystemInfo", {
      ...this.$store.state.dataActionStore,
      businessId: this.$route.query.platformId,
      platformId: this.$route.query.platformId,
      businessType: "home",
      businessSeq: [{ id: this.$route.query.platformId, type: "home" }],
    });
    if (!this.isPreviewEnviroment) {
      this.enterExposure("home", this.$route.query.platformId, "home", [{ id: this.$route.query.platformId, type: "home" }]);
    }
    // 获取侧边栏的状态
    this.getSliderStatus();
    setTimeout(() => {
      this.getAdvertisement(this.$route.params.pageId);
    }, 1000);
  },
  computed: {
    routeName() {
      return this.$route.name;
    },
    loginShow() {
      return this.$store.state.loginShow;
    },
    isShowAd() {
      return this.$store.state.isShowAd;
    },
    allConfig() {
      return this.$store.state.allConfig;
    },
    pageContent() {
      return this.$store.state.pageContent;
    },
    bottomNavTab() {
      return this.$store.state.bottomNavTab;
    },
  },
  mounted() {
 
  },
  watch: {
    $route: {
      handler(val) {
       
      },
      deep: true,
    },
  },
  methods: {
    async getSliderStatus() {
      let { data } = await getSliderStatus();
      if (data && data.length) {
        let infoStatus = data.reduce((acc, item) => {
          if (["addxiaoyi", "contact", "questionnaire", "addweixin"].includes(item.itemType)) {
            acc[item.itemType] = true;
          }
          if (item.itemType == "share") {
            this.$store.commit("changeShareInfo", item);
          }
          return acc;
        }, {});
        this.$store.commit("changeSliderInfoStatus", infoStatus);
      }
    },
    changeQuitStatus() {
      this.quitStatus = true;
    },
  },
  beforeRouteLeave(to, from, next) {
    if (!this.isPreviewEnviroment) {
      this.computTimeDiff("home", this.$route.query.platformId, "home", [{ id: this.$route.query.platformId, type: "home" }]);
    }
    next();
  },
};
</script>

<style scoped>
.app_wrap {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
  justify-content: space-between;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(242, 241, 246, 0.98) 100%, #f2f1f6 100%);
}
</style>
