{"code": "001", "info": "操作成功", "data": [{"officialId": 1, "components": [{"officialId": 1, "components": [], "menuKey": "searchBarZly", "description": "搜索栏", "menuParams": [], "pid": 250, "type": 0, "children": [{"officialId": 1, "components": [], "menuKey": "searchBarInnerPage", "description": "搜索栏内页", "menuParams": [{"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}, {"menuParamName": "0-活动；1-课程；2-讲师；3-音频课程；4-智库；5-资讯；6-直播；7-自定义", "id": 25, "menuParamValue": "activity,live,series", "menuParamKey": "typeStringList"}], "pid": 251, "type": 2, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "name": "搜索栏内页", "menuId": 24, "menuType": 4, "sortNum": 2, "id": 252, "extraInfo": {"defaultPageSize": 10, "pageFlag": false}}], "name": "搜索栏", "menuId": 61, "menuType": 4, "sortNum": 1, "id": 251, "extraInfo": {"textAlign": "center", "rightButtonQrcodeUrl": "", "placeholder": "搜索活动/课程 ", "iconUrl": "", "rightButton": true}}, {"officialId": 1, "components": [], "menuKey": "checkDominZly", "description": "检查行业", "menuParams": [], "pid": 250, "type": 0, "apiUrl": "", "children": [], "name": "检查行业", "menuId": 62, "menuType": 3, "sortNum": 3, "id": 253, "extraInfo": {}}, {"officialId": 1, "components": [], "menuKey": "swiperImageZly", "description": "图片轮播", "menuParams": [{"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}, {"menuParamName": "位置（0-首页置顶 1-首页热门 2-频道置顶 ）", "id": 16, "menuParamValue": "0", "menuParamKey": "position"}, {"menuParamName": "最新活动标识（1-最新;2-首页热门和频道置顶根据排序值排序）", "id": 32, "menuParamValue": "2", "menuParamKey": "newStatus"}], "pid": 250, "type": 1, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "name": "图片轮播", "menuId": 63, "menuType": 2, "sortNum": 4, "id": 254, "extraInfo": {"borderRadius": "5px", "width": "350px", "height": "120px"}}, {"officialId": 1, "components": [], "menuKey": "typeAreaZly", "description": "行业分类", "menuParams": [], "pid": 250, "type": 0, "apiUrl": "", "children": [], "name": "", "logo": "", "menuId": 64, "menuType": 2, "sortNum": 5, "id": 255, "extraInfo": {"menuData": [{"name": "最新直播", "icon": "https://oss.ienmore.com/resources/public/pro/zuixinzhibo.png", "menuParams": [{"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}, {"menuParamName": "0-活动；1-课程；2-讲师；3-音频课程；4-智库；5-资讯；6-直播；7-自定义", "id": 25, "menuParamValue": "live", "menuParamKey": "type"}, {"menuParamValue": "1", "menuParamKey": "liveStatus"}]}, {"name": "系列直播", "icon": "https://oss.ienmore.com/resources/public/pro/xiliezhibo.png", "menuParams": [{"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}, {"menuParamName": "0-活动；1-课程；2-讲师；3-音频课程；4-智库；5-资讯；6-直播；7-自定义", "id": 25, "menuParamValue": "series", "menuParamKey": "type"}]}, {"name": "音视频回看", "icon": "https://oss.ienmore.com/resources/public/pro/yinshipinhuikan.png", "menuParams": [{"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}, {"menuParamName": "0-活动；1-课程；2-讲师；3-音频课程；4-智库；5-资讯；6-直播；7-自定义", "id": 25, "menuParamValue": "live", "menuParamKey": "type"}, {"menuParamName": "", "id": 25, "menuParamValue": "0", "menuParamKey": "liveStatus"}]}, {"name": "线下会议", "icon": "https://oss.ienmore.com/resources/public/pro/xianxiahuiyi.png", "menuParams": [{"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}, {"menuParamName": "0-活动；1-课程；2-讲师；3-音频课程；4-智库；5-资讯；6-直播；7-自定义", "id": 25, "menuParamValue": "activity", "menuParamKey": "type"}]}], "typeData": [{"name": "生物医药", "icon": "https://oss.ienmore.com/resources/public/pro/yiyao.png", "targetUrl": "/home/<USER>/347/354?platformId=1&typeList=activity,live,series"}, {"name": "汽车创新", "icon": "https://oss.ienmore.com/resources/public/pro/qiche.png", "targetUrl": "/home/<USER>/348/355?platformId=1&typeList=activity,live,series"}, {"name": "新材料", "icon": "https://oss.ienmore.com/resources/public/pro/cailiao.png", "targetUrl": "/home/<USER>/349/356?platformId=1&typeList=activity,live,series"}, {"name": "化工油气", "icon": "https://oss.ienmore.com/resources/public/pro/qiyou.png", "targetUrl": "/home/<USER>/350/357?platformId=1&typeList=activity,live,series"}, {"name": "物流包装", "icon": "https://oss.ienmore.com/resources/public/pro/wuliu.png", "targetUrl": "/home/<USER>/351/358?platformId=1&typeList=activity,live,series"}, {"name": "金属加工", "icon": "https://oss.ienmore.com/resources/public/icon/metal.png", "targetUrl": "/home/<USER>/352/359?platformId=1&typeList=activity,live,series"}, {"name": "化妆品", "icon": "https://oss.ienmore.com/resources/public/pro/huazhuangpin.png", "targetUrl": "/home/<USER>/353/360?platformId=1&typeList=activity,live,series"}, {"name": "更多", "icon": "https://oss.ienmore.com/resources/public/pro/gengduo.png", "targetUrl": "/home/<USER>/0/0?platformId=1&typeList=activity,live,series"}]}}, {"officialId": 1, "components": [], "menuKey": "swiperAreaZly", "description": "区域轮播", "menuParams": [{"menuParamName": "类型 0-线下会议,6-线上直播", "id": 13, "menuParamValue": "activity,live,series", "menuParamKey": "typeStringList"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}, {"menuParamName": "最新活动标识（1-最新;2-首页热门和频道置顶根据排序值排序）", "id": 27, "menuParamValue": "1", "menuParamKey": "activityPosition"}, {"menuParamName": "数量", "id": 63, "menuParamValue": "5", "menuParamKey": "pageSize"}], "pid": 250, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront/listMyActivity", "children": [], "name": "我的活动", "logo": "https://oss.ienmore.com/resources/public/img/news_icon.png", "menuId": 65, "menuType": 2, "sortNum": 6, "id": 256, "extraInfo": {"defaultPageSize": 4, "showMoreUrl": "", "tagIcon": "", "itemHeight": "100px", "subtitle": "", "pageFlag": false, "showMore": true, "loginFlag": true, "showViewCount": false, "tagText": "", "detailUrl": "Api", "viewCountIcon": ""}}, {"officialId": 1, "components": [], "menuKey": "hotLiveZly", "description": "热门直播", "menuParams": [{"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}, {"menuParamName": "", "id": 60, "menuParamValue": "live", "menuParamKey": "typeStringList"}, {"menuParamName": "位置（0-首页置顶 1-首页热门 2-频道置顶 ）", "id": 17, "menuParamValue": "3", "menuParamKey": "position"}], "pid": 250, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "以近七日热度计算为准", "name": "本周热门直播", "logo": "", "menuId": 66, "menuType": 2, "sortNum": 7, "id": 257, "extraInfo": {"defaultPageSize": 3, "showMoreUrl": "", "tagIcon": "", "itemHeight": "100px", "subtitle": "", "pageFlag": true, "showMore": true, "itemStyle": "hot", "showViewCount": false, "tagText": "", "detailUrl": "Api", "viewCountIcon": ""}}, {"officialId": 1, "components": [], "menuKey": "heavyLiveZly", "description": "重磅回看", "menuParams": [{"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}, {"menuParamName": "", "id": 60, "menuParamValue": "live", "menuParamKey": "typeStringList"}, {"menuParamName": "", "id": 61, "menuParamValue": "5", "menuParamKey": "position"}], "pid": 250, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "name": "重磅回看", "logo": "https://oss.ienmore.com/resources/public/img/series_icon.png", "menuId": 67, "menuType": 2, "sortNum": 8, "id": 258, "extraInfo": {"defaultPageSize": 4, "showTag": false, "itemHeight": "100px", "pageFlag": true, "itemStyle": "series", "tagText": "", "showMoreUrl": "", "tagIcon": "", "subtitle": "", "showMore": false, "showViewCount": false, "detailUrl": "Api", "viewCountIcon": ""}}, {"officialId": 1, "components": [], "menuKey": "recommendZly", "description": "为你推荐", "menuParams": [{"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 250, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "name": "为你推荐", "logo": "https://oss.ienmore.com/resources/public/img/hot_icon.png", "menuId": 68, "menuType": 2, "sortNum": 9, "id": 259, "extraInfo": {"defaultPageSize": 3, "showTag": false, "itemHeight": "100px", "pageFlag": false, "itemStyle": "recommend", "tagText": "", "showMoreUrl": "", "tagIcon": "", "subtitle": "", "showMore": false, "itemTextPlace": "out", "showViewCount": false, "detailUrl": "Api", "viewCountIcon": ""}}], "menuKey": "bottomNavTab", "description": "首页", "menuParams": [], "pid": 0, "type": 0, "children": [], "name": "发现", "menuId": 60, "menuType": 0, "sortNum": 0, "id": 250, "extraInfo": {"selected_icon": "https://oss.ienmore.com/resources/public/pro/find_zly_selected.png", "icon": "https://oss.ienmore.com/resources/public/pro/find_zly.png", "style": "zly"}}, {"officialId": 1, "components": [], "menuKey": "bottomNavTab", "description": "分类", "menuParams": [], "pid": 0, "type": 0, "children": [{"officialId": 1, "components": [], "menuKey": "checkDominZly", "description": "检查行业", "menuParams": [], "pid": 260, "type": 1, "url": "", "apiUrl": "", "children": [], "name": "检查行业", "menuId": 76, "menuType": 0, "sortNum": 11, "id": 261, "extraInfo": {}}, {"officialId": 1, "components": [], "menuKey": "activityPage2Zly", "description": "活动页面", "menuParams": [], "pid": 260, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [{"officialId": 1, "components": [], "menuKey": "", "icon": "https://oss.ienmore.com/resources/public/icon/medicalMedicine.png", "description": "字典菜单", "menuParams": [], "pid": 262, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [{"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 347, "type": 1, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "name": "全部", "menuId": 22, "menuType": 1, "sortNum": 0, "id": 354}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 347, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "抗体药物", "menuId": 22, "menuType": 1, "sortNum": 1, "id": 361, "codeValueId": "root_system_info_industryActivity_channel_medicalMedicine_biologicalDrug"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 347, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "细胞免疫治疗", "menuId": 22, "menuType": 1, "sortNum": 2, "id": 362, "codeValueId": "root_system_info_industryActivity_channel_medicalMedicine_immunotherapy"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 347, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "体外诊断", "menuId": 22, "menuType": 1, "sortNum": 3, "id": 363, "codeValueId": "root_system_info_industryActivity_channel_medicalMedicine_diacrisis"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 347, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "精准医疗", "menuId": 22, "menuType": 1, "sortNum": 4, "id": 364, "codeValueId": "root_system_info_industryActivity_channel_medicalMedicine_precisionMedicine"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 347, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "疫苗", "menuId": 22, "menuType": 1, "sortNum": 5, "id": 365, "codeValueId": "root_system_info_industryActivity_channel_medicalMedicine_vaccine"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 347, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "企业管理", "menuId": 22, "menuType": 1, "sortNum": 6, "id": 366, "codeValueId": "root_system_info_industryActivity_channel_medicalMedicine_finance"}], "name": "生物医药", "logo": "https://pro-cloud.oss-cn-shanghai.aliyuncs.com/live_front_test/2020/06/03/c7c69fb6e6664ae684a6c97ed05c66ca.jpg", "menuId": 22, "menuType": 1, "sortNum": 1, "id": 347, "codeValueId": "root_system_info_industryActivity_channel_medicalMedicine"}, {"officialId": 1, "components": [], "menuKey": "", "icon": "https://oss.ienmore.com/resources/public/icon/car.png", "description": "字典菜单", "menuParams": [], "pid": 262, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [{"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 348, "type": 1, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "name": "全部", "menuId": 22, "menuType": 1, "sortNum": 0, "id": 355}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 348, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "车联网", "menuId": 22, "menuType": 1, "sortNum": 1, "id": 368, "codeValueId": "root_system_info_industryActivity_channel_car_01"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 348, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "热管理", "menuId": 22, "menuType": 1, "sortNum": 2, "id": 369, "codeValueId": "root_system_info_industryActivity_channel_car_02"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 348, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "内饰", "menuId": 22, "menuType": 1, "sortNum": 3, "id": 370, "codeValueId": "root_system_info_industryActivity_channel_car_03"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 348, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "NVH", "menuId": 22, "menuType": 1, "sortNum": 4, "id": 371, "codeValueId": "root_system_info_industryActivity_channel_car_04"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 348, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "安全", "menuId": 22, "menuType": 1, "sortNum": 5, "id": 372, "codeValueId": "root_system_info_industryActivity_channel_car_05"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 348, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "新能源", "menuId": 22, "menuType": 1, "sortNum": 6, "id": 373, "codeValueId": "root_system_info_industryActivity_channel_car_06"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 348, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "自动驾驶", "menuId": 22, "menuType": 1, "sortNum": 7, "id": 374, "codeValueId": "root_system_info_industryActivity_channel_car_07"}], "name": "汽车创新", "logo": "https://pro-cloud.oss-cn-shanghai.aliyuncs.com/live_front_test/2020/06/03/22fe10d0996340fb8f4f94a2889270dd.jpg", "menuId": 22, "menuType": 1, "sortNum": 2, "id": 348, "codeValueId": "root_system_info_industryActivity_channel_car"}, {"officialId": 1, "components": [], "menuKey": "", "icon": "https://oss.ienmore.com/resources/public/icon/materials.png", "description": "字典菜单", "menuParams": [], "pid": 262, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [{"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 349, "type": 1, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "name": "全部", "menuId": 22, "menuType": 1, "sortNum": 0, "id": 356}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 349, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "聚氨酯", "menuId": 22, "menuType": 1, "sortNum": 1, "id": 375, "codeValueId": "root_system_info_industryActivity_channel_newMaterial_polyurethane"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 349, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "医用材料", "menuId": 22, "menuType": 1, "sortNum": 2, "id": 376, "codeValueId": "root_system_info_industryActivity_channel_newMaterial_medical"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 349, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "交通材料", "menuId": 22, "menuType": 1, "sortNum": 3, "id": 377, "codeValueId": "root_system_info_industryActivity_channel_newMaterial_traffic"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 349, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "基建材料", "menuId": 22, "menuType": 1, "sortNum": 4, "id": 378, "codeValueId": "root_system_info_industryActivity_channel_newMaterial_construction"}], "name": "新材料", "logo": "https://pro-cloud.oss-cn-shanghai.aliyuncs.com/live_front_test/2020/06/03/c2b9391edb6945d88fe4ee640324772d.jpg", "menuId": 22, "menuType": 1, "sortNum": 3, "id": 349, "codeValueId": "root_system_info_industryActivity_channel_newMaterial"}, {"officialId": 1, "components": [], "menuKey": "", "icon": "https://oss.ienmore.com/resources/public/icon/oil.png", "description": "字典菜单", "menuParams": [], "pid": 262, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [{"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 350, "type": 1, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "name": "全部", "menuId": 22, "menuType": 1, "sortNum": 0, "id": 357}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 350, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "化工", "menuId": 22, "menuType": 1, "sortNum": 1, "id": 382, "codeValueId": "root_system_info_industryActivity_channel_chemicalOil_chemical"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 350, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "生物能源", "menuId": 22, "menuType": 1, "sortNum": 2, "id": 383, "codeValueId": "root_system_info_industryActivity_channel_chemicalOil_biologicalEnergy"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 350, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "油脂油料", "menuId": 22, "menuType": 1, "sortNum": 3, "id": 384, "codeValueId": "root_system_info_industryActivity_channel_chemicalOil_grease"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 350, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "特种油品", "menuId": 22, "menuType": 1, "sortNum": 4, "id": 385, "codeValueId": "root_system_info_industryActivity_channel_chemicalOil_specialOil"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 350, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "成品油", "menuId": 22, "menuType": 1, "sortNum": 5, "id": 386, "codeValueId": "root_system_info_industryActivity_channel_chemicalOil_refinedOil"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 350, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "天然气", "menuId": 22, "menuType": 1, "sortNum": 6, "id": 387, "codeValueId": "root_system_info_industryActivity_channel_chemicalOil_naturalGas"}], "name": "化工油气", "logo": "https://pro-cloud.oss-cn-shanghai.aliyuncs.com/live_front_test/2020/06/03/4342c3ce3c364b3b9fc967fe6a37260c.jpg", "menuId": 22, "menuType": 1, "sortNum": 4, "id": 350, "codeValueId": "root_system_info_industryActivity_channel_chemicalOil"}, {"officialId": 1, "components": [], "menuKey": "", "icon": "https://oss.ienmore.com/resources/public/icon/logistics.png", "description": "字典菜单", "menuParams": [], "pid": 262, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [{"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 351, "type": 1, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "name": "全部", "menuId": 22, "menuType": 1, "sortNum": 0, "id": 358}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 351, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "包装", "menuId": 22, "menuType": 1, "sortNum": 1, "id": 389, "codeValueId": "root_system_info_industryActivity_channel_logistics_01"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 351, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "航运", "menuId": 22, "menuType": 1, "sortNum": 2, "id": 390, "codeValueId": "root_system_info_industryActivity_channel_logistics_02"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 351, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "储运", "menuId": 22, "menuType": 1, "sortNum": 3, "id": 391, "codeValueId": "root_system_info_industryActivity_channel_logistics_03"}], "name": "物流包装", "logo": "https://pro-cloud.oss-cn-shanghai.aliyuncs.com/live_front_test/2020/06/03/9fe0cff70fe9458a9cb8a53857c9eefb.jpg", "menuId": 22, "menuType": 1, "sortNum": 5, "id": 351, "codeValueId": "root_system_info_industryActivity_channel_logistics"}, {"officialId": 1, "components": [], "menuKey": "", "icon": "https://oss.ienmore.com/resources/public/icon/metalprocess.png\r\n", "description": "字典菜单", "menuParams": [], "pid": 262, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [{"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 352, "type": 1, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "name": "全部", "menuId": 22, "menuType": 1, "sortNum": 0, "id": 359}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 352, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "铝加工", "menuId": 22, "menuType": 1, "sortNum": 1, "id": 392, "codeValueId": "root_system_info_industryActivity_channel_metalProcessing_aluminium"}, {"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 352, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "subName": "", "name": "金属加工液", "menuId": 22, "menuType": 1, "sortNum": 2, "id": 393, "codeValueId": "root_system_info_industryActivity_channel_metalProcessing_MetalworkingFluids"}], "name": "金属加工", "logo": "https://pro-cloud.oss-cn-shanghai.aliyuncs.com/live_front_test/2020/06/03/c9f91b21ac554c179530b7f90ac30067.jpg", "menuId": 22, "menuType": 1, "sortNum": 6, "id": 352, "codeValueId": "root_system_info_industryActivity_channel_metalProcessing"}, {"officialId": 1, "components": [], "menuKey": "", "icon": "https://oss.ienmore.com/resources/public/icon/cosmetics.png", "description": "字典菜单", "menuParams": [], "pid": 262, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [{"officialId": 1, "components": [], "menuKey": "", "description": "字典菜单", "menuParams": [], "pid": 353, "type": 1, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront.json", "children": [], "name": "全部", "menuId": 22, "menuType": 1, "sortNum": 0, "id": 360}], "name": "化妆品", "logo": "https://pro-cloud.oss-cn-shanghai.aliyuncs.com/live_front_test/2020/06/03/5a9d138444bb4b20bdc71617af1b6fa2.jpg", "menuId": 22, "menuType": 1, "sortNum": 7, "id": 353, "codeValueId": "root_system_info_industryActivity_channel_cosmetics"}], "name": "活动页面", "menuId": 70, "menuType": 0, "sortNum": 12, "id": 262}], "name": "活动", "menuId": 69, "menuType": 0, "sortNum": 10, "id": 260, "extraInfo": {"selected_icon": "https://oss.ienmore.com/resources/public/pro/activity_zly_selected.png", "icon": "https://oss.ienmore.com/resources/public/pro/activity_zly.png"}}, {"officialId": 1, "components": [{"officialId": 1, "components": [], "menuKey": "userInfoZly", "description": "个人资料", "menuParams": [], "pid": 321, "type": 0, "children": [], "name": "个人资料", "menuId": 72, "menuType": 2, "sortNum": 72, "id": 322}, {"officialId": 1, "components": [], "menuKey": "rowMenuZly", "description": "横向菜单", "menuParams": [], "pid": 321, "type": 0, "children": [{"officialId": 1, "components": [{"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "最新活动标识（1-最新;2-首页热门和频道置顶根据排序值排序）", "id": 27, "menuParamValue": "1", "menuParamKey": "activityPosition"}, {"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}, {"menuParamName": "6-线上直播 9-系列直播", "id": 64, "menuParamValue": "live,series", "menuParamKey": "typeStringList"}], "pid": 324, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront/listMyActivity", "children": [], "name": "即将开始", "menuId": 74, "menuType": 2, "sortNum": 76, "id": 327, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}, {"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "6-线上直播 9-系列直播", "id": 64, "menuParamValue": "live,series", "menuParamKey": "typeStringList"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}, {"id": 65, "menuParamValue": "0", "menuParamKey": "activityPosition"}], "pid": 324, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront/listMyActivity", "children": [], "name": "已结束", "menuId": 74, "menuType": 2, "sortNum": 77, "id": 328, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}, {"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "0-线下", "id": 66, "menuParamValue": "activity", "menuParamKey": "typeStringList"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 324, "updateTime": 1589682268000, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront/listMyActivity", "children": [], "createTime": 1589682264000, "subName": "", "name": "线下", "logo": "", "menuId": 74, "menuType": 2, "sortNum": 78, "id": 326, "codeValueId": "", "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}], "menuKey": "myActivity", "description": "我的活动", "menuParams": [], "pid": 323, "type": 0, "children": [{"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "最新活动标识（1-最新;2-首页热门和频道置顶根据排序值排序）", "id": 27, "menuParamValue": "1", "menuParamKey": "activityPosition"}, {"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}, {"menuParamName": "6-线上直播 9-系列直播", "id": 64, "menuParamValue": "live,series", "menuParamKey": "typeStringList"}], "pid": 324, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront/listMyActivity", "children": [], "name": "即将开始", "menuId": 74, "menuType": 2, "sortNum": 76, "id": 327, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}, {"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "6-线上直播 9-系列直播", "id": 64, "menuParamValue": "live,series", "menuParamKey": "typeStringList"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}, {"id": 65, "menuParamValue": "0", "menuParamKey": "activityPosition"}], "pid": 324, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront/listMyActivity", "children": [], "name": "已结束", "menuId": 74, "menuType": 2, "sortNum": 77, "id": 328, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}, {"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "0-线下", "id": 66, "menuParamValue": "activity", "menuParamKey": "typeStringList"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 324, "updateTime": 1589682268000, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront/listMyActivity", "children": [], "createTime": 1589682264000, "subName": "", "name": "线下", "logo": "", "menuId": 74, "menuType": 2, "sortNum": 78, "id": 326, "codeValueId": "", "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}], "name": "我的活动", "logo": "iconhuod<PERSON>", "menuId": 36, "menuType": 2, "sortNum": 74, "id": 324}, {"officialId": 1, "components": [{"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "类型", "id": 73, "menuParamValue": "product", "menuParamKey": "itemType"}, {"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 333, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/member/center/memberCollect", "children": [], "name": "产品与服务", "menuId": 40, "menuType": 2, "sortNum": 87, "id": 398, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}, {"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 333, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/member/center/memberCollect", "children": [{"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"id": 68, "menuParamValue": "1", "menuParamKey": "status"}, {"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 399, "updateTime": 1589682095000, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/member/center/memberCollect", "children": [], "createTime": 1589682095000, "subName": "", "name": "即将开始", "logo": "", "menuId": 40, "menuType": 2, "sortNum": 84, "id": 335, "codeValueId": "", "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}, {"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"id": 67, "menuParamValue": "0", "menuParamKey": "status"}, {"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 399, "updateTime": 1589682095000, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/member/center/memberCollect", "children": [], "createTime": 1589682095000, "subName": "", "name": "已结束", "logo": "", "menuId": 40, "menuType": 2, "sortNum": 85, "id": 336, "codeValueId": "", "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}, {"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "类型", "id": 39, "menuParamValue": "activity", "menuParamKey": "itemType"}, {"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 399, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/member/center/memberCollect", "children": [], "name": "线下", "menuId": 40, "menuType": 2, "sortNum": 86, "id": 334, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}], "name": "活动", "menuId": 40, "menuType": 2, "sortNum": 87, "id": 399, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}], "menuKey": "myCollect", "description": "我的收藏", "menuParams": [], "pid": 323, "type": 0, "apiUrl": "", "children": [{"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "类型", "id": 73, "menuParamValue": "product", "menuParamKey": "itemType"}, {"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 333, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/member/center/memberCollect", "children": [], "name": "产品与服务", "menuId": 40, "menuType": 2, "sortNum": 87, "id": 398, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}, {"officialId": 1, "components": [{"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"id": 68, "menuParamValue": "1", "menuParamKey": "status"}, {"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 399, "updateTime": 1589682095000, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/member/center/memberCollect", "children": [], "createTime": 1589682095000, "subName": "", "name": "即将开始", "logo": "", "menuId": 40, "menuType": 2, "sortNum": 84, "id": 335, "codeValueId": "", "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}, {"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"id": 67, "menuParamValue": "0", "menuParamKey": "status"}, {"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 399, "updateTime": 1589682095000, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/member/center/memberCollect", "children": [], "createTime": 1589682095000, "subName": "", "name": "已结束", "logo": "", "menuId": 40, "menuType": 2, "sortNum": 85, "id": 336, "codeValueId": "", "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}, {"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "类型", "id": 39, "menuParamValue": "activity", "menuParamKey": "itemType"}, {"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 399, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/member/center/memberCollect", "children": [], "name": "线下", "menuId": 40, "menuType": 2, "sortNum": 86, "id": 334, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}], "menuKey": "", "menuParams": [{"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 333, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/member/center/memberCollect", "children": [{"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"id": 68, "menuParamValue": "1", "menuParamKey": "status"}, {"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 399, "updateTime": 1589682095000, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/member/center/memberCollect", "children": [], "createTime": 1589682095000, "subName": "", "name": "即将开始", "logo": "", "menuId": 40, "menuType": 2, "sortNum": 84, "id": 335, "codeValueId": "", "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}, {"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"id": 67, "menuParamValue": "0", "menuParamKey": "status"}, {"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 399, "updateTime": 1589682095000, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/member/center/memberCollect", "children": [], "createTime": 1589682095000, "subName": "", "name": "已结束", "logo": "", "menuId": 40, "menuType": 2, "sortNum": 85, "id": 336, "codeValueId": "", "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}, {"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "类型", "id": 39, "menuParamValue": "activity", "menuParamKey": "itemType"}, {"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 399, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/member/center/memberCollect", "children": [], "name": "线下", "menuId": 40, "menuType": 2, "sortNum": 86, "id": 334, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}], "name": "活动", "menuId": 40, "menuType": 2, "sortNum": 87, "id": 399, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}], "extra": "", "name": "我的收藏", "logo": "icons<PERSON>can<PERSON>", "menuId": 39, "menuType": 2, "sortNum": 83, "id": 333}], "name": "横向菜单", "menuId": 73, "menuType": 2, "sortNum": 73, "id": 323}, {"officialId": 1, "components": [], "menuKey": "columnSingleLineMenuZly", "description": "纵向单行菜单", "menuParams": [], "pid": 321, "type": 0, "apiUrl": "", "children": [{"officialId": 1, "components": [{"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "语言", "id": 72, "menuParamValue": "0", "menuParamKey": "lang"}, {"id": 68, "menuParamValue": "1", "menuParamKey": "status"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 397, "type": 0, "apiUrl": "http://testpro.ienmore.com:809/client/inviteGuest/list", "children": [], "name": "即将开始", "menuId": 81, "menuType": 2, "sortNum": 3, "id": 394, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}, {"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "语言", "id": 72, "menuParamValue": "0", "menuParamKey": "lang"}, {"id": 67, "menuParamValue": "0", "menuParamKey": "status"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 397, "type": 0, "apiUrl": "http://testpro.ienmore.com:809/client/inviteGuest/list", "children": [], "name": "已结束", "menuId": 81, "menuType": 2, "sortNum": 4, "id": 395, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}], "menuKey": "mySpeech", "description": "我的发言", "menuParams": [], "pid": 396, "type": 0, "apiUrl": "", "children": [{"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "语言", "id": 72, "menuParamValue": "0", "menuParamKey": "lang"}, {"id": 68, "menuParamValue": "1", "menuParamKey": "status"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 397, "type": 0, "apiUrl": "http://testpro.ienmore.com:809/client/inviteGuest/list", "children": [], "name": "即将开始", "menuId": 81, "menuType": 2, "sortNum": 3, "id": 394, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}, {"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "语言", "id": 72, "menuParamValue": "0", "menuParamKey": "lang"}, {"id": 67, "menuParamValue": "0", "menuParamKey": "status"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 397, "type": 0, "apiUrl": "http://testpro.ienmore.com:809/client/inviteGuest/list", "children": [], "name": "已结束", "menuId": 81, "menuType": 2, "sortNum": 4, "id": 395, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}], "extra": "", "name": "我的发言", "logo": "<PERSON><PERSON><PERSON>", "menuId": 78, "menuType": 2, "sortNum": 2, "id": 397}], "extra": "", "name": "纵向单行菜单", "menuId": 77, "menuType": 2, "sortNum": 75, "id": 396}, {"officialId": 1, "components": [], "menuKey": "columnSingleLineMenuZly", "description": "纵向单行菜单", "menuParams": [], "pid": 321, "type": 0, "apiUrl": "", "children": [{"officialId": 1, "components": [], "menuKey": "mySpeech", "description": "我的发言", "menuParams": [], "pid": 400, "type": 2, "apiUrl": "", "children": [], "extra": "", "name": "我关注的领域", "menuId": 78, "menuType": 2, "sortNum": 2, "id": 401}], "extra": "", "name": "纵向单行菜单", "menuId": 77, "menuType": 2, "sortNum": 75, "id": 400}, {"officialId": 1, "components": [], "menuKey": "columnMenuZly", "description": "纵向菜单", "menuParams": [], "pid": 321, "type": 0, "children": [{"officialId": 1, "components": [{"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "订单状态", "id": 40, "menuParamValue": "1", "menuParamKey": "orderStatus"}, {"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}, {"menuParamName": "类型", "id": 42, "menuParamValue": "live", "menuParamKey": "itemType"}], "pid": 341, "type": 0, "apiUrl": "http://testpro.ienmore.com:809/client/sign/getMyOrders", "children": [], "name": "全部", "menuId": 43, "menuType": 2, "sortNum": 92, "id": 342, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}], "menuKey": "myOrder", "description": "纵向菜单内部", "menuParams": [{"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}], "pid": 340, "type": 0, "apiUrl": "http://testinfo.ienmore.com:809/platform-biz-api/conferenceFront/member/orderNum", "children": [{"officialId": 1, "components": [], "menuKey": "", "menuParams": [{"menuParamName": "订单状态", "id": 40, "menuParamValue": "1", "menuParamKey": "orderStatus"}, {"menuParamName": "来源", "id": 37, "menuParamValue": "enmore", "menuParamKey": "source"}, {"menuParamName": "平台id", "id": 24, "menuParamValue": "1", "menuParamKey": "platformId"}, {"menuParamName": "类型", "id": 42, "menuParamValue": "live", "menuParamKey": "itemType"}], "pid": 341, "type": 0, "apiUrl": "http://testpro.ienmore.com:809/client/sign/getMyOrders", "children": [], "name": "全部", "menuId": 43, "menuType": 2, "sortNum": 92, "id": 342, "extraInfo": {"defaultPageSize": 10, "pageFlag": true}}], "name": "我的订单", "logo": "https://oss.ienmore.com/resources/public/pro/dingdan_zly.png", "menuId": 41, "menuType": 2, "sortNum": 91, "id": 341}, {"officialId": 1, "components": [], "menuKey": "myInvoice", "description": "纵向菜单内部", "menuParams": [], "pid": 340, "type": 0, "children": [], "name": "我的开票信息", "logo": "https://oss.ienmore.com/resources/public/pro/kaipiao_zly.png", "menuId": 42, "menuType": 2, "sortNum": 96, "id": 346}], "name": "纵向菜单", "menuId": 75, "menuType": 2, "sortNum": 90, "id": 340}], "menuKey": "bottomNavTab", "description": "个人中心", "menuParams": [], "pid": 0, "type": 0, "children": [], "name": "我的", "menuId": 71, "menuType": 0, "sortNum": 71, "id": 321, "extraInfo": {"selected_icon": "https://oss.ienmore.com/resources/public/pro/mine_zly_selected.png", "icon": "https://oss.ienmore.com/resources/public/pro/mine_zly.png"}}]}