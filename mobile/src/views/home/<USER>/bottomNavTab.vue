<template>
  <div class="footTab">
    <cube-tab-bar @click="clickHandler"  :class="configuration && configuration[0].extraInfo.style ? '' : 'medicine'">
      <cube-tab v-for="(item, index) in tabs" :label="item.label" :key="index">
        <div class="footTab-item">
          <div v-if="item.icon">
            <img :src="tabValue == item.label ? item.selected_icon : item.icon" alt="" />
          </div>
          <div :class="computeClass()" :style="computeStyle(item.label)">
            {{ item.text }}
          </div>
        </div>
      </cube-tab>
    </cube-tab-bar>
  </div>
</template>

<script>
import { locale } from "moment";
export default {
  props: ["configuration", "routerName"],
  data() {
    return {
      currentPage: "",
      tabs: [],
      tabValue: 0,
    };
  },
  created() {},
  watch: {
    $route: {
      handler(val) {
        // 如果存在路由则根据路由显示
        if (this.$route.params.pageId > 0) {
          this.tabValue = parseInt(this.$route.params.pageId);
        } else {
          if (val.query.targetTab) {
            this.configuration.map((item, index) => {
              if (item.description == val.query.targetTab) {
                if (val.query.local) {
                  //本地跳转指定页面
                  this.$router.replace({ name: "targetPage", params: { pageId: item.id, subType: "0", typeId: "0" }, query: { local: true } });
                } else {
                  //直播等跳转指定页面
                  this.$router.replace({ name: "targetPage", params: { pageId: item.id, subType: "0", typeId: "0" } });
                }
              }
            });
          } else {
            setTimeout(() => {
              this.clickHandler(this.tabs[0].label, null);
            }, 200);
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    // 重置底部栏内容
    if (this.$route.params.pageId > 0) {
      this.tabValue = parseInt(this.$route.params.pageId);
      sessionStorage.setItem('currentPageId', this.$route.params.pageId); //设置当前页面id
    }
    this.tabs = [];
    this.configuration.map((item) => {
      this.tabs.push({
        label: item.id,
        icon: item.extraInfo ? item.extraInfo.icon : "",
        selected_icon: item.extraInfo ? item.extraInfo.selected_icon : "",
        text: item.name,
      });
    });

    var interVal = setInterval(() => {
      if (this.routerName == "home" && !this.$route.query.targetTab) {
        if (this.$route.query.local) {
          this.clickHandler(this.tabs[0].label, true);
        } else {
          this.clickHandler(this.tabs[0].label, false);
        }
        clearInterval(interVal);
      }
    }, 10);

    setTimeout(() => {
      clearInterval(interVal);
    }, 5000);
  },
  computed: {},
  methods: {
    clickHandler(tab, local) {
      this.configuration.map((item) => {
        if (item.id == tab) {
          if (item.description == "ebc") {
            window.location.href = item.url;
          } else {
           
            // // 从当前URL中提取pageId
            // const currentPageId = this.$route.params.pageId;
            
            // // 构建新的URL
            // const newUrl = `${window.location.origin}${window.location.pathname}#/home/<USER>/0/0?platformId=${localStorage.getItem("platformId")}&domainType=${this.$route.query.domainType}`;

            // // 如果当前页面ID与点击的tab不同,则进行页面跳转
            // if (currentPageId != tab) {
            //   window.location.assign(newUrl);
            // }
            sessionStorage.setItem("currentPageId", tab);
            this.$router.push({ name: "targetPage", params: { pageId: tab, subType: "0", typeId: "0" } });
          }
        }
      });
    },
    computeClass() {
      if (this.configuration[0].extraInfo && this.configuration[0].extraInfo.style) {
        return this.configuration[0].extraInfo.style;
      } else {
        return "";
      }
    },
    computeStyle(label) {
      if (this.configuration[0].extraInfo && this.configuration[0].extraInfo.style) {
        if (this.tabValue == label) {
          return "color:rgba(77,83,107,1)";
        }
      } else {
        return "";
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.footTab {
  position: fixed;
  z-index: 1;
  bottom: 0;
  width: 100%;
  height: 50px;
  background-color: #fff;
  border-top: 1px solid #e5e5e5;
  box-shadow: 0 0px 3px rgba(197, 197, 197, 0.5);
  // padding-bottom: env(safe-area-inset-bottom);
  // box-sizing: content-box;
  // z-index: 1;
}
.footTab > div {
  height: 100%;
}
.footTab-item {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  img {
    width: 20px;
    height: 20px;
  }
}
.footTab-item div {
  margin: 0;
  padding: 0;
}
.zly {
  margin-top: 2px !important;
  font-size: 10px;
  font-weight: 400;
  color: rgba(77, 83, 107, 1);
  line-height: 14px;
}
.cube-tab div {
  margin-top: 0;
}

.medicine {
  padding: 6px 0;
  .footTab-item img {
    margin-bottom: 3px;
  }
}
</style>
<style>
.cube-tab_active.cube-tab div {
  color: var(--color-primary);
}
.cube-tab div {
  font-size: 10px;
}
</style>
