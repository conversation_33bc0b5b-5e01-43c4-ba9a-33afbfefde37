<template>
    <div v-show="$store.state.dominPage.domainShow" class="domin">
        <div class="title">选择您关注的领域</div>
        <div class="sub-title">方便我们为您提供最准确的活动</div>
        <div class="domin-group">
            <div v-for="(item,index) in dominData" @click="dominDataClick(item)">
                <span>{{item.name}}</span>
                <svg v-if="selectedDominData.includes(item.codeValueId)" width="100%" height="100%" viewBox="0 0 150 90" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
                    <title>编组</title>
                    <desc>Created with Sketch.</desc>
                    <defs>
                        <linearGradient x1="50%" y1="34.9532353%" x2="100%" y2="100%" id="linearGradient-1">
                            <stop stop-color="#FFB986" offset="0%"></stop>
                            <stop stop-color="#FF6B00" offset="100%"></stop>
                        </linearGradient>
                    </defs>
                    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g id="1.4选择领域备份" transform="translate(-195.000000, -321.000000)">
                            <g id="编组-2备份" transform="translate(195.000000, 321.000000)">
                                <g id="编组">
                                    <rect id="矩形备份" fill="#000000" opacity="0.5" x="0" y="0" width="150" height="90" rx="4"></rect>
                                    <rect id="矩形" stroke="#FF6B00" stroke-width="2" x="1" y="1" width="148" height="88" rx="4"></rect>
                                    <path d="M150,64 L150,86 C150,88.209139 148.209139,90 146,90 L124,90 L124,90 L150,64 Z" id="矩形备份-2" fill="url(#linearGradient-1)"></path>
                                    <polyline id="路径-2" stroke="#FFFFFF" stroke-linecap="round" stroke-linejoin="round" points="136 82.0702789 140.253056 85.6997655 147.14094 77"></polyline>
                                </g>
                            </g>
                        </g>
                    </g>
                </svg>
                <!-- <svg v-else  width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <image x="0px" y="0px" width="100%" height="100%" xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAABaBAMAAACoBbIeAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAGFBMVEWChpf///+ChpeChpeChpeChpf///////+6eTvuAAAAB3RSTlMAAIIXGAR367S13wAAAAFiS0dEAf8CLd4AAAAHdElNRQfkBgsWEB/GIUHzAAAAhUlEQVRYw+3OSxGFMADF0CKBTQ2g5Wm5EmqfP3TfLB4ziYAzKcs0U5UfaJU/tTBJS0tLS0tLS0tLS0tLS0tLS+tjVgWtcFZtnBXOqo2zwlnbFmaFs/YtygpnHVuQFc46txgrnHVtIVY4694irHDWswVY4ax3a9wKZ3Vbw1Y4q98atfqttgJRqAMIljzfKgAAAABJRU5ErkJggg==" />
                </svg> -->
                <img :src="item.logo" alt="">
            </div>
        </div>
        <div v-if="firstComeinFlag" :class="selectedDominData.length>=1?'button active':'button'" style="" @click="goActivity">
            {{selectedDominData.length>=1?"开启行家":"开启行家"}}
        </div>
        <div v-else :class="selectedDominData.length>=1?'button active':'button'" style="" @click="goActivity">
            保存我关注的领域
        </div>
    </div>
</template>
<script>
import { getInfoByDynamicUrl } from '@/api/configurable/common'
import { putmemberAndDomains, saveAutoTag } from '@/api/domin'
import { platformBizApi } from '@/config/env';
import {  getUserIndustryEnmore } from '@/api/userCenter'

export default {
    name: 'domin',
    components: {},
    props: [],
    data () {
        return {
            dominData: [],
            selectedDominData: [],
            firstComeinFlag: false,
        };
    },
    computed: {
        dominShow () {
            return this.$store.state.dominPage.domainShow
        }
    },
    mounted () {
        this.init()
    },
    watch: {
        $route: {
            handler (to, from) {
                window.addEventListener("popstate", () => {
                    if (!this.firstComeinFlag && this.$store.state.dominPage.domainShow || this.$store.state.loginShow) {
                        this.$store.state.dominPage.domainShow = false
                    }
                }, false);
            },
            deep: true,
            immediate: true
        },
        dominShow: {
            handler (val) {
                if (val) {
                    this.init();
                    this.$router.push({
                        name: this.$route.name,
                        params: this.$route.params,
                        query: {
                            ...this.$route.query,
                            dominShow: true
                        },
                    })
                }
            }
        }
    },
    methods: {
        init () {
            // if (localStorage.getItem('selectedDominData')) {
            //     this.selectedDominData = localStorage.getItem('selectedDominData').split(',')
            // } else {
            //     this.firstComeinFlag = true;
            // }
            getUserIndustryEnmore().then(res=>{
                this.selectedDominData = res.data.map(item=>item.channel)
            })
            let params = {
                platformId: localStorage.getItem('platformId')
            }
            //获取所有的领域信息
            getInfoByDynamicUrl(platformBizApi + '/officialMenu/industry', params).then(res => {
                this.dominData = res.data
            })
        },
        dominDataClick (item) {
            if (this.selectedDominData.includes(item.codeValueId)) {
                this.selectedDominData.splice(this.selectedDominData.indexOf(item.codeValueId), 1)
            } else {
                this.selectedDominData.push(item.codeValueId)
            }
        },
        async goActivity () {
            if (this.selectedDominData.length < 1) {
                return
            }

            await putmemberAndDomains({ channels: this.selectedDominData, platformId: localStorage.getItem('platformId') })
            localStorage.setItem('selectedDominData', this.selectedDominData)

            await saveAutoTag({
                classifyType: 1,
                domainList: this.selectedDominData,
                domainType: this.$route.query.domainType || 1,
                platformId: this.$route.query.platformId || 1,
            })

            let officialMenu = JSON.parse(localStorage.getItem('officialMenu'));
            this.$store.state.dominPage.domainShow = false;
            location.reload();

            // officialMenu.data.map((item) => {
            //     if (item.description == '分类') {
            //         if (item.id == this.$route.params.pageId) {
            //             //在分类页面
            //             this.$store.state.dominPage.domainShow = false
            //             this.$router.push({ name: 'targetPage', params: { pageId: item.id, subType: "0", typeId: "0" }, query: { platformId: localStorage.getItem('platformId') } })
            //         } else {
            //             //不在分类页面
            //             this.$store.state.dominPage.domainShow = false
            //             location.reload()
            //         }
            //     }
            // })
        },
    },
}
</script>
<style lang='scss' scoped>
.domin {
    z-index: 101;
    top: 0;
    left: 0;
    position: absolute;
    height: 100vh;
    width: 100vw;
    overflow: scroll;
    background: #fff;
    padding: 30px;
    .title {
        font-size: 22px;
        font-weight: 500;
        color: rgba(51, 51, 51, 1);
        line-height: 30px;
    }
    .sub-title {
        margin-top: 6px;
        font-size: 16px;
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
        line-height: 22px;
    }
    .domin-group {
        margin-top: 40px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        > div {
            position: relative;
            width: 150px;
            height: 90px;
            border-radius: 5px;
            margin-bottom: 15px;
            background: rgba(0, 0, 0, 1);
            span {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translateX(-50%) translateY(-50%);
                z-index: 2;
                font-size: 18px;
                font-weight: 500;
                color: rgba(255, 255, 255, 1);
                line-height: 25px;
            }
            svg {
                z-index: 1;
                top: 0;
                left: 0;
                position: absolute;
            }
            > img {
                border-radius: 5px;
                z-index: 0;
                top: 0;
                left: 0;
                position: absolute;
                opacity: 0.8;
            }
        }
    }
    .button {
        margin-top: 28px;
        margin-left: 28px;
        width: 260px;
        height: 46px;
        background: rgba(238, 238, 238, 1);
        border-radius: 23px;
        color: rgba(170, 170, 170, 1);
        font-size: 16px;
        text-align: center;
        line-height: 45px;
    }
    .active {
        color: white;
        background: linear-gradient(
            318deg,
            rgba(255, 107, 0, 1) 0%,
            rgba(255, 176, 118, 1) 100%
        );
    }
}
</style>