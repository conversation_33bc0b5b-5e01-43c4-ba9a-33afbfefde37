<template>
    <div class="userCenter-column-menu-zly">
        <ul>
            <li v-for="(menu, index) in list" :key="menu.id" @click="toMenuPage(menu.menuKey, menu.children)">
                <img :src="menu.logo">
                <span>{{menu.name}}</span>
                <div>
                    <!-- <span v-if="menu.apiUrl">{{getRightNum(index, menu.apiUrl,menu.menuParams)}}{{rightNum[index]}}</span> -->
                    <svg width="8px" height="13px" viewBox="0 0 8 13" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
                        <title>路径 2</title>
                        <desc>Created with Sketch.</desc>
                        <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
                            <g id="1.3我的" transform="translate(-338.000000, -359.000000)" stroke="#666666">
                                <g id="路径-5" transform="translate(333.000000, 359.000000)">
                                    <g id="路径-2">
                                        <polyline id="路径" transform="translate(6.000000, 6.000000) rotate(-45.000000) translate(-6.000000, -6.000000) " points="10 2 10 10 2 10"></polyline>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </svg>
                </div>
            </li>
        </ul>
    </div>
</template>

<script>
import Axios from '@/plugins/http'
export default {
    props: ['configuration'],
    data () {
        return {
            rightNum: {}, // 菜单右侧数据
        }
    },
    computed: {
        list () {
            return JSON.parse(JSON.stringify(this.configuration.children));
        }
    },
    methods: {
        // 跳转菜单 内页
        toMenuPage (name, children) {
            this.$emit('toMenuPage', name, children);
        },
        getRightNum (index, apiUrl, menuParams) {
            let params = {};
            menuParams.forEach(item => {
                params[item.menuParamKey] = item.menuParamValue
            })
            Axios().get(apiUrl, { params }).then(res => {
                if (res.code == this.$successCode) {
                    this.$set(this.rightNum, index, res.data)
                }
            })
        }
    }
}
</script>

<style lang="scss">
.userCenter-column-menu-zly {
    margin: 16px 15px;
    background-color: #fff;
    border-radius: 4px;
    ul {
        margin: 0;
        li {
            height: 50px;
            color: #333;
            font-size: 16px;
            line-height: 55px;
            position: relative;
            display: flex;
            flex-direction: row;
            align-items: center;
            &::after {
                content: "";
                position: absolute;
                right: 0;
                bottom: 0;
                // background-color: #eeeeee;
                height: 1px;
                width: calc(100% - 53px);
            }
            img {
                width: 30px;
                height: 30px;
                vertical-align: middle;
                margin: 0 15px;
                margin-top: -2px;
            }
            span {
                font-size: 14px;
                font-weight: 400;
                color: rgba(51, 51, 51, 1);
            }
            div {
                flex: 1;
                display: flex;
                flex-direction: row-reverse;
                align-items: center;
            }
            svg {
                margin-right: 16px;
            }
        }
    }
}
</style>