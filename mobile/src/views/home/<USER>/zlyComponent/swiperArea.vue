<template>
    <div class="swiperAreaZly" v-show="!(infoData.length==0 && tokenFlag)">
        <div class="swiperAreaZly-title-row">
            <div v-show="configuration.name" class="swiperAreaZly-title">
                <span>{{configuration.name}}</span>
            </div>
            <div v-show="configuration.extraInfo.showMore" class="more-icon" @click="showMore">
                <span>全部</span>
                <img style='width:5px;height:10px;' src="@/assets/img/more_icon.png" alt="">
            </div>
        </div>
        <div class="login-content" v-if="!tokenFlag">
            <span>您目前暂未登录，点击“去登录”可查看我的活动</span>
            <div @click="showLoginPage"> 去登录</div>
        </div>
        <cube-slide allowVertical v-if="infoData.length && tokenFlag" :data="infoData" :stopPropagation="true" @change="changePage" class="swipers" ref="slide" :autoPlay='true'>
            <cube-slide-item :key="indexs" v-for="(item, indexs) in infoData">
                <div @click="itemClick(item)" class="swiper-block-item">
                    <div class="left-content">
                        <div class="left-content-time">
                            <svg width="4px" height="20px" viewBox="0 0 4 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
                                <title>矩形备份 29</title>
                                <desc>Created with Sketch.</desc>
                                <defs>
                                    <linearGradient x1="50%" y1="0%" x2="50%" y2="0%" :id="`swiperAreaZlyIcon-${indexs}`">
                                        <stop stop-color="#4D536B" offset="0%"></stop>
                                        <stop stop-color="#4D536B" offset="100%"></stop>
                                    </linearGradient>
                                </defs>
                                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <g id="1.1首页改版" transform="translate(-15.000000, -614.000000)" :fill="`url(#swiperAreaZlyIcon-${indexs})`">
                                        <g id="矩形备份-15" transform="translate(15.000000, 614.000000)">
                                            <g id="矩形备份-29">
                                                <path d="M0,0 C2.209139,-4.05812251e-16 4,1.790861 4,4 L4,16 C4,18.209139 2.209139,20 0,20 L0,20 L0,20 L0,0 Z" id="矩形备份-14"></path>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </svg>
                            {{formatTime(item.beginTime || item.publishTime || item.createTime,'activity')}}
                        </div>
                        <div class="left-content-title">{{item.name}}</div>
                        <!-- {{item.conferenceType}} -->
                    </div>
                    <img class="cover-image" :src="item.coverImg" alt="">
                </div>
            </cube-slide-item>
        </cube-slide>
        <div class="dots" v-if="infoData.length && tokenFlag">
            <span :class="currentIndex == index?'active':''" v-for="(item,index) in infoData"></span>
        </div>
    </div>
</template>
<script>
import CONSTANT from '@/config/config_constant';
import { getInfoByDynamicUrl } from '@/api/configurable/common'
import myActivityVue from '../../../userCenter/myActivity.vue';
export default {
    components: {},
    props: ['configuration'],
    data () {
        return {
            infoData: [],
            currentIndex: 0,
            params: {}
        };
    },
    computed: {
        tokenFlag () {
            return localStorage.getItem(CONSTANT.token) || false
        }
    },
    watch: {
        $route: {
            handler (val) {
                setTimeout(() => {
                    this.getInfo()
                }, 100);
            },
            deep: true,
            immediate: true
        }
    },
    mounted () {
        //  this.getInfo()
    },
    methods: {
        getInfo () {
            if (!this.tokenFlag) {
                return
            }
            let params = {}
            this.configuration.menuParams.map(item => {
                params[item.menuParamKey] = item.menuParamValue
            })
            this.params = params
            getInfoByDynamicUrl(this.configuration.apiUrl, params).then(res => {
                this.infoData = []
                setTimeout(() => {
                    this.infoData = res.data.list
                }, 20);
            })
        },
        clickImage (item, indexs) {

        },
        changePage (index) {
            this.currentIndex = index
        },
        itemClick (item) {
            if (item.type == null && item.itemType !== null) {
                item.id = item.itemId
                item.type = item.itemType
            }
            this.naviToDetails(localStorage.getItem('platformId'), item.type, item)
        },
        showMore () {
            if (this.configuration.name == '我的活动') {
                this.$router.push({ name: 'myActivity' })
            } else {
                this.naviToCommonList(this.params)
            }
        },
        showLoginPage () {
            this.loginShowFunction()
            this.$store.state.loginOption = 'touch'
        }
    }
}
</script>
<style lang='scss' scoped>
.swiperAreaZly {
    margin: 0 15px;
    padding: 15px 0;
    // height: 140px;
    // background: #fff;
}
.swiperAreaZly-title-row {
    display: flex;
    justify-content: space-between;
    height: 30px;
    align-items: center;
    margin-bottom: 15px;
}
.swiperAreaZly-title {
    font-size: 18px;
    color: rgba(51, 51, 51, 1);
    line-height: 25px;
    span {
        font-weight: 500;
    }
}
.login-content {
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
}
.swiper {
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
}
.swiper-block-item {
    height: 114px;
    border-radius: 4px;
    width: 345px;
    background-color: white;
    padding: 15px 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    img {
        width: 140px;
        height: 84px;
        margin-right: 10px;
    }
}
.left-content {
    width: 182px;
}
.left-content-time {
    font-size: 16px;
    font-weight: 500;
    color: rgba(51, 51, 51, 1);
    line-height: 22px;
    display: flex;
    align-items: center;
    svg {
        margin-right: 6px;
    }
}
.left-content-title {
    margin-left: 10px;
    margin-top: 15px;
    font-size: 12px;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    line-height: 17px;
    width: 172px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    white-space: pre-wrap;
}
.cover-image {
    width: 140px;
    height: 84px;
    border-radius: 4px;
}
.more-icon {
    font-size: 14px;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    line-height: 20px;
    img {
        margin-left: 9px;
    }
}
.login-content {
    width: 345px;
    height: 114px;
    background: url("./image/myActivity-background.png") 100% 100%;
    background-size: cover;
    color: #dadcff;
    display: flex;
    flex-direction: column;
    align-items: center;
    span {
        margin-top: 22px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(218, 220, 255, 1);
        line-height: 20px;
    }
    div {
        margin-top: 20px;
        width: 80px;
        height: 30px;
        border-radius: 15px;
        border: 1px solid rgba(218, 220, 255, 1);
        line-height: 28px;
        text-align: center;
    }
}
</style>


<style >
.swiperAreaZly .cube-slide-dots > span.active {
    display: none;
}
.swiperAreaZly .cube-slide-dots > span {
    display: none;
}
.dots {
    margin-top: 10px;
    display: flex;
    justify-content: center;
}
.dots .active {
    width: 11px;
    background: rgba(77, 83, 107, 1);
}
.dots > span {
    width: 4px;
    height: 3px;
    background: rgba(77, 83, 107, 0.6);
    margin: 0 4px;
}
</style>