<template>
    <div>
    </div>
</template>
<script>
import { getInfoByDynamicUrl } from '@/api/configurable/common'
import { platformBizApi } from '@/config/env';
import CONSTANT from '@/config/config_constant';
export default {
    components: {},
    props: [],
    data () {
        return {
        };
    },
    computed: {},
    mounted () {
        if (localStorage.getItem(CONSTANT.token)) {
            getInfoByDynamicUrl(platformBizApi + `/conferenceFront/getDomain`, { platformId: localStorage.getItem('platformId') }).then(res => {
                if (res.data.length) {
                    let tempData = []
                    res.data.map(item => {
                        tempData.push(item.channel)
                    })
                    localStorage.setItem('selectedDominData', tempData)
                } else {
                    this.$store.state.dominPage.domainShow = true;
                }
            }, () => {
                this.$store.state.dominPage.domainShow = true
            })
        }
    },
    methods: {}
}
</script>
<style lang='scss' scoped>
</style>
