<template>
    <div class="square1Column" v-show="infoData.length">
        <!-- 根据类型分为两种item -->
        <div>
            <div class="title-row">
                <div v-show="configuration.name" class="square1Column-title">
                    <img :src="configuration.logo" alt="" srcset="">
                    <span>{{configuration.name}}</span>
                </div>
                <div v-show="configuration.extraInfo.showMore" class="more-icon" @click="showMore">
                    <span>更多</span>
                    <img style='width:5px;height:10px;' src="@/assets/img/more_icon.png" alt="">
                </div>
            </div>
            <div v-for="(item,index) in infoData" @click="clickItem(item)">
                <infoItem v-if="configuration.extraInfo.itemStyle == 'left'" :info='item' :iflast='index == infoData.length-1' :showTag='configuration.extraInfo.showTag'></infoItem>
                <infoItemRight v-if="configuration.extraInfo.itemStyle == 'right'" :info='item' :iflast='index == infoData.length-1' :showTag='configuration.extraInfo.showTag'></infoItemRight>
                <infoSeries v-if="configuration.extraInfo.itemStyle == 'series'" :info='item' :iflast='index == infoData.length-1' :showTag='configuration.extraInfo.showTag'></infoSeries>
            </div>
        </div>
    </div>
</template>
<script>
import { getInfoByDynamicUrl } from '@/api/configurable/common'
import infoItem from '@/components/newCommon/infoItem.vue'
import infoItemRight from '@/components/newCommon/infoItemRight.vue'
import infoSeries from '@/components/newCommon/infoSeries.vue'

export default {
    components: {
        infoItem,
        infoItemRight,
        infoSeries
    },
    props: ['configuration'],
    data () {
        return {
            infoData: [],
            tagList: [
                { tagName: '标签1' },
                { tagName: '标签1' },
                { tagName: '标签1' },
            ],
        };
    },
    computed: {
    },
    watch: {
        $route: {
            handler (val) {
                setTimeout(() => {
                    this.getInfo()
                }, 200);
            },
            deep: true,
            immediate: true,
            params: {}
        }
    },
    mounted () {
        // this.getInfo()
    },
    methods: {
        getInfo () {
            let params = {}
            this.configuration.menuParams.map(item => {
                params[item.menuParamKey] = item.menuParamValue
            })
            if (this.configuration.extraInfo.pageFlag && this.configuration.type == 0) {
                params.pageNum = 1
                params.pageSize = this.configuration.extraInfo.defaultPageSize
            }
            //加入二级标签和过滤器的参数
            if (this.$store.state.scrollNavBar.extraMenuParams.length) {
                this.$store.state.scrollNavBar.extraMenuParams.map(item => {
                    params[item.menuParamKey] = item.menuParamValue
                })
            }
            if (this.$route.query.type >= 0) {
                if (params.type >= 0 && params.type != this.$route.query.type) {
                    params.type = -1
                } else {
                    params.type = this.$route.query.type
                }
            }
            this.params = params
            // console.log(this.$store.state.scrollNavBar.extraMenuParams, params);
            getInfoByDynamicUrl(this.configuration.apiUrl, params).then(res => {
                // console.log(res);
                this.infoData = res.data.list
            })
        },
        showMore () {
            this.naviToCommonList(this.params)
        },
        clickItem (item) {
            this.naviToDetails(this.$route.query.platformId, item.type, item)
        },
    }
}
</script>
<style lang='scss' scoped>
.square1Column {
    background-color: #fff;
    margin-top: 10px;
    padding: 10px 0px;
}
.square1Column-title {
    display: flex;
    align-items: center;
    margin: 4px 0;
    img {
        height: 24px;
        width: 24px;
    }
    span {
        font-weight: 400;
        font-size: 16px;
        margin-left: 6px;
    }
}

.title-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
}
.more-icon {
    display: flex;
    align-items: center;
    span {
        float: right;
        color: #999;
       font-size:12px;
    }
    img {
        margin-left: 5px;
        height: 16px;
    }
}
.info-item {
    padding: 3px 0;
    display: flex;
    flex-direction: left;
    width: 100%;
    padding: 10px 0;
}
.info-right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 225px;
}

.info-coverImg {
    width: 113px;
    height: 80px;
    border-radius: 5px;
    margin-right: 10px;
    border: 1px solid #eee;
}

.marker-radio-orange {
    border-radius: 8px;
    height: 16px;
    font-size: 12px;
    line-height: 14px;
    padding: 0 5px;
    border: 1px solid #ff620d;
    color: #999;
}
.marker-radio-group-blue {
    display: flex;
    flex-wrap: wrap;
    span {
        font-size: 12px;
        border: 1px solid #4e6ba4;
        border-radius: 9px;
        color: #4e6ba4;
        padding: 3px 5px 1px 5px;
        margin-right: 5px;
    }
}
.info-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
        font-size: 12px;
        line-height: 16px;
        color: #999;
        margin-right: 10px;
    }
}
.marker-square-orange {
    font-size: 12px;
    // height: 16px;
    line-height: 16px;
    border-radius: 2px;
    background: #ff620d;
    color: white !important;
    padding: 2px 5px 0px 5px;
}
.marker-radio-group-red {
    display: flex;
    flex-wrap: wrap;
    span {
        font-size: 10px;
        height: 14px;
        line-height: 12px;
        border: 1px solid #af2e07;
        border-radius: 7px;
        color: #af2e07;
        padding: 0 5px;
        margin-right: 5px;
    }
}
.info-title-box {
    height: 32px;
}
.info-title {
    font-weight: 400;
    word-break: break-word;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    font-size: 14px;
    line-height: 18px;
}
</style>