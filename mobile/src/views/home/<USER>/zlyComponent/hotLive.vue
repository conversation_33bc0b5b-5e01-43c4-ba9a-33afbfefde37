<template>
    <div class="hotLive" v-show="infoData.length">
        <div>
            <div class="hotLive-title-row">
                <div class="left">
                    <span>{{configuration.name}}</span>
                    <span>{{configuration.subName}}</span>
                </div>
                <div class="right">Top {{configuration.extraInfo.defaultPageSize}}</div>
            </div>
            <div class="bottom" v-for="(item,index) in infoData" @click="clickItem(item)">
                <infoHot v-if="configuration.extraInfo.itemStyle == 'hot'" :info='item' :index='index' :iflast='index == infoData.length-1' :showTag='configuration.extraInfo.showTag'></infoHot>
            </div>
        </div>
    </div>
</template>
<script>
import { getInfoByDynamicUrl } from '@/api/configurable/common'
import infoItem from '@/components/newCommon/infoItem.vue'
import infoItemRight from '@/components/newCommon/infoItemRight.vue'
import infoSeries from '@/components/newCommon/infoSeries.vue'
import infoHot from '@/components/newCommon/infoHot.vue'

export default {
    components: {
        infoItem,
        infoItemRight,
        infoSeries,
        infoHot
    },
    props: ['configuration'],
    data () {
        return {
            infoData: [],
            tagList: [
                { tagName: '标签1' },
                { tagName: '标签1' },
                { tagName: '标签1' },
            ],
        };
    },
    computed: {
    },
    watch: {
        $route: {
            handler (val) {
                setTimeout(() => {
                    this.getInfo()
                }, 200);
            },
            deep: true,
            immediate: true,
            params: {}
        }
    },
    mounted () {
        // this.getInfo()
    },
    methods: {
        getInfo () {
            let params = {}
            this.configuration.menuParams.map(item => {
                params[item.menuParamKey] = item.menuParamValue
            })
            if (this.configuration.extraInfo.pageFlag && this.configuration.type == 0) {
                params.pageNum = 1
                params.pageSize = this.configuration.extraInfo.defaultPageSize
            }
            params.topSign = 1;
            this.params = params
            getInfoByDynamicUrl(this.configuration.apiUrl, params).then(res => {
                // console.log(res);
                this.infoData = res.data.list
            })
        },
        showMore () {
            this.naviToCommonList(this.params)
        },
        clickItem (item) {
            this.naviToDetails(this.$route.query.platformId, item.type, item)
        },
    }
}
</script>
<style lang='scss' scoped>
.hotLive {
    // background-color: #fff;
    margin: 0 15px;
    margin-top: 3px;
    padding: 0 0 0px 0;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
}
.hotLive-title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 15px 25px 15px;
    background: rgba(77, 83, 107, 1);
    border-radius: 4px 4px 0px 0px;
    .left {
        color: #fff;
        display: flex;
        flex-direction: column;
        :nth-child(1) {
            font-size: 18px;
            font-weight: 500;
            line-height: 25px;
        }
        :nth-child(2) {
            margin-top: 5px;
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
        }
    }
    .right {
        font-size: 18px;
        font-weight: 500;
        color: rgba(255, 199, 46, 1);
        line-height: 25px;
    }
}
.bottom {
    border-radius: 4px;
    background: #fff;
    position: relative;
    top: -3px;
}
</style>