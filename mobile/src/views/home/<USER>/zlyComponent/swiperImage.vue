<template>
    <div class="swiperImageZly" v-show="swiperImageItems.length">
        <cube-slide v-if="swiperImageItems.length" :data="swiperImageItems" @change="changePage" class="swipers" ref="slideImage" allowVertical>
            <cube-slide-item class="swiperItem-content" :key="indexs" @click.native="clickImage(item, indexs)" v-for="(item, indexs) in swiperImageItems">
                <img class="swiperItem" :src="item.bannerImg">
            </cube-slide-item>
        </cube-slide>
    </div>
</template>
<script>
import { getInfoByDynamicUrl } from '@/api/configurable/common'

export default {
    components: {},
    props: ['configuration'],
    data () {
        return {
            swiperImageItems: []
        };
    },
    computed: {
    },
    watch: {
        $route: {
            handler (val) {
                setTimeout(() => {
                    this.getInfo()
                }, 100);
            },
            deep: true,
            immediate: true
        }
    },
    mounted () {
        // this.getInfo()
    },
    updated () {
    },
    methods: {
        getInfo () {
            let params = {}
            this.configuration.menuParams.map(item => {
                params[item.menuParamKey] = item.menuParamValue
            })

            //加入二级标签和过滤器的参数
            if (this.$store.state.scrollNavBar.extraMenuParams.length) {
                this.$store.state.scrollNavBar.extraMenuParams.map(item => {
                    params[item.menuParamKey] = item.menuParamValue
                })
            }
            if (this.$route.query.type >= 0) {
                if (params.type >= 0 && params.type != this.$route.query.type) {
                    params.type = -1
                } else {
                    params.type = this.$route.query.type
                }
            }
            getInfoByDynamicUrl(this.configuration.apiUrl, params).then(res => {
                this.swiperImageItems = []
                setTimeout(() => {
                    this.swiperImageItems = res.data.list
                }, 20);
            })
        },
        clickImage (item, indexs) {
            if (item.type == 'diy') {
                if (item.topUrl) {
                    this.naviToDetails(this.$route.query.platformId, 'diy', item)
                }
            } else {
                if (item.type == null && item.itemType !== null) {
                    item.id = item.itemId
                    item.type = item.itemType
                }
                this.naviToDetails(this.$route.query.platformId, item.type, item)
            }
        },
        changePage (index) {
        },
    }
}
</script>
<style lang='scss' scoped>
.swiperImageZly {
    padding: 0 15px;
    height: 140px;
    border-radius: 4px;
}
.swipers {
    height: 140px;
}
.swiperItem-content {
    height: 140px;
}
.swiperItem {
    height: 100%;
    width: 100%;
    border-radius: 5px;
}
</style>

<style>
.swiperImageZly .cube-slide-dots {
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 351px;
    bottom: 5px !important;
}
.swiperImageZly .cube-slide-dots span.active {
    height: 3px;
    width: 11px;
    background-size: 100% 100%;
    margin: 0px 4px;
    background-color: #fff;
}
.swiperImageZly .cube-slide-dots span {
    height: 3px;
    width: 4px;
    background-size: 100% 100%;
    margin: 0px 4px;
    background: rgba(255, 255, 255, 0.6);
}
</style>