<template>
    <div class="activitypage-zly">
        <topBarZly :configuration='leftlistConfigList'></topBarZly>
        <div class="main-content">
            <div class="left-list">
                <div v-for="(item,index) in leftlistConfigList" @click="changeTypeIndex(item,index)">
                    <div :class="computeClass(index)">
                        <svg class="change-type-icon" v-if="index == currentTypeIndex" width="4px" height="20px" viewBox="0 0 4 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
                            <title>矩形 3</title>
                            <desc>Created with Sketch.</desc>
                            <defs>
                                <linearGradient x1="50%" y1="0%" x2="50%" y2="0%" id="linearGradient-1">
                                    <stop stop-color="#4D536B" offset="0%"></stop>
                                    <stop stop-color="#4D536B" offset="100%"></stop>
                                </linearGradient>
                            </defs>
                            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <g id="1.2活动分类" transform="translate(0.000000, -148.000000)" fill="url(#linearGradient-1)">
                                    <g id="矩形-2" transform="translate(0.000000, 148.000000)">
                                        <g id="矩形-3">
                                            <path d="M0,0 C2.209139,-4.05812251e-16 4,1.790861 4,4 L4,16 C4,18.209139 2.209139,20 0,20 L0,20 L0,20 L0,0 Z" id="矩形"></path>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </svg>
                        {{item.name}}
                    </div>
                </div>
            </div>
            <div class="right-content">
                <div class="scrollNavBar-zly" v-show="false">
                    <div class="cubeScroll" style="overflow: hidden;">
                        <cube-scroll-nav-bar v-if="cubeScrollRefreshFlag" :current="currentLabel" :labels="TypeLabels" @change="changeHandler" />
                    </div>
                    <div v-if="showRightButton" class="right-button" @click="rightButtonClick()">
                        <svg :class="rightButtonArrow?'right-button-icon':'right-button-icon reverse'" width="14px" height="8px" viewBox="0 0 14 8" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <desc>Created with Sketch.</desc>
                            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
                                <g id="1.2活动分类" transform="translate(-347.000000, -146.000000)" stroke="#666666">
                                    <g id="路径-2" transform="translate(347.000000, 141.000000)">
                                        <g id="路径-3" transform="translate(1.000000, 0.000000)">
                                            <polyline id="路径" transform="translate(6.000000, 6.000000) rotate(45.000000) translate(-6.000000, -6.000000) " points="10 2 10 10 2 10"></polyline>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </svg>
                    </div>
                </div>
                <div class="info-content">
                    <div :class="'typeLabels-group '+ computedTypeLabelGroupClass()">
                        <div :class="currentLabel == item?'typeLabels-item typeLabels-item-active':'typeLabels-item'" @click="currentLabel = item;showMoreFlag = false;" v-for="(item, index) in TypeLabels">{{item}}</div>
                        <div v-show="tpyeListButtonShow && showMoreFlag" class="close-more" @click="showMoreFlag = false">
                            <svg width="36px" height="13px" viewBox="0 0 36 13" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <g id="1.2活动分类备份-5" transform="translate(-272.000000, -231.000000)">
                                        <text id="收起" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" fill="#FF6B00">
                                            <tspan x="272" y="242">收起</tspan>
                                        </text>
                                        <g id="路径-2" transform="translate(303.000000, 239.500000) rotate(180.000000) translate(-303.000000, -239.500000) translate(298.000000, 235.000000)" stroke="#FF6B00" stroke-linecap="round" stroke-linejoin="round">
                                            <g id="路径-3" transform="translate(0.714286, 0.000000)">
                                                <polyline id="路径" transform="translate(4.285714, 4.770793) rotate(45.000000) translate(-4.285714, -4.770793) " points="7.03296703 1.80375973 7.25274725 7.73782567 1.31868132 7.51804545"></polyline>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </svg>
                        </div>
                        <div v-show="tpyeListButtonShow && !showMoreFlag" class="more" @click="showMoreFlag = true">
                            <svg width="36px" height="13px" viewBox="0 0 36 13" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <g id="1.2活动分类最终" transform="translate(-329.000000, -172.000000)">
                                        <text id="更多" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" fill="#FF6B00">
                                            <tspan x="329" y="183">更多</tspan>
                                        </text>
                                        <g id="路径-2" transform="translate(355.000000, 172.000000)" stroke="#FF6B00" stroke-linecap="round" stroke-linejoin="round">
                                            <g id="路径-3" transform="translate(0.714286, 0.000000)">
                                                <polyline id="路径" transform="translate(4.285714, 4.770793) rotate(45.000000) translate(-4.285714, -4.770793) " points="7.03296703 1.80375973 7.25274725 7.73782567 1.31868132 7.51804545"></polyline>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </svg>
                        </div>
                    </div>
                    <div v-show="!rightButtonArrow" class="cover-content">
                        <div class="cover-top">全部分类</div>
                        <div class="type-label-group">
                            <div :class="computeTypeLabelsClass(item)" @click="currentLabel = item; rightButtonArrow = true" v-for="(item,index) in TypeLabels">{{formatTag(item,5)}}</div>
                        </div>
                    </div>
                    <div class="v-line"></div>
                    <div class="info-type-group">
                        <span :class="computeInfoTypesClass(index)" @click="changeInfoTypes(item,index)" v-for="(item,index) in infoTypes">{{item.name}}</span>
                    </div>
                    <div class="v-line"></div>
                    <cube-slide allowVertical v-if="swiperImageItems.length" :data="swiperImageItems" @change="changeSwiperImageItems" class="swipers" ref="slideImage">
                        <cube-slide-item class="swiperItem-content" :key="indexs" @click.native="clickImage(item, indexs)" v-for="(item, indexs) in swiperImageItems">
                            <img class="swiperItem" :src="item.bannerImg">
                        </cube-slide-item>
                    </cube-slide>
                    <div>
                        <div v-for="(item,index) in infoItem" class="info-item">
                            <infoItemZly :info='item' :iflast='index == infoItem.length-1'></infoItemZly>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { getInfoByDynamicUrl } from '@/api/configurable/common'
import { platformBizApi } from '@/config/env';

export default {
    props: ['configuration'],
    components: {
    },
    data () {
        return {
            currentTypeIndex: 0,
            currentItem: {},
            TypeLabels: [],
            currentLabel: 0,
            showRightButton: false,
            rightButtonArrow: true,//true为向下箭头 false为向上
            currentInfoTypeIndex: 0,
            infoTypes: [],
            swiperImageItems: [],
            infoItem: [],
            cubeScrollRefreshFlag: true,
            leftlistConfigList: [],//改变排序后的Config
            showMoreFlag: false, // 展开状态标志
            tpyeListButtonShow: false
        };
    },
    watch: {
        showRightButton (val) {
            this.cubeScrollRefreshFlag = false
            setTimeout(() => {
                this.cubeScrollRefreshFlag = true
            }, 1);
        },
        TypeLabels (val) {

        },
        $route: {
            handler (val) {
                this.init()
                //计算是否需要展开收起
                setTimeout(() => {
                    if (document.querySelector('.typeLabels-group').scrollHeight < 70) {
                        this.tpyeListButtonShow = false
                    } else {
                        this.tpyeListButtonShow = true
                    }
                }, 10);
            },
            deep: true
        },
        // 监听展开收起状态
        showMoreFlag: {
            handler (val) {
                if (val) {

                }
            },
            deep: true,
            immediate: true
        }
    },
    computed: {
    },
    created () {
        this.infoTypes = [
            { name: '所有类型', typeList: `${this.TYPE[0]},${this.TYPE[6]},${this.TYPE[9]}` },
            { name: '直播', typeList: `${this.TYPE[6]},${this.TYPE[9]}`, liveStatus: '1' },
            { name: '回看', typeList: `${this.TYPE[6]},${this.TYPE[9]}`, liveStatus: '0' },
            { name: '线下', typeList: `${this.TYPE[0]}` },
        ]
    },
    mounted () {
        this.init()
    },
    methods: {
        async init () {
            // 没有分类的话获取分类
            if (!localStorage.getItem('selectedDominData')) {
                await getInfoByDynamicUrl(platformBizApi + `/conferenceFront/getDomain`, { platformId: localStorage.getItem('platformId') }).then(res => {
                    if (res.data.length) {
                        let tempData = []
                        res.data.map(item => {
                            tempData.push(item.channel)
                        })
                        localStorage.setItem('selectedDominData', tempData)
                    } else {
                        this.$store.state.dominPage.domainShow = true;
                    }
                }, () => {
                    this.$store.state.dominPage.domainShow = true
                })
            }
            this.computeRightButton()
            this.changeLeftBarOrder()
            this.routerInit()

        },
        //接收路由初始化
        routerInit () {
            if (this.$route.params.typeId != 0) {
                this.configuration.children.map(item => {
                    if (item.id == this.$route.params.typeId) {
                        this.currentItem = item
                    }
                })
            } else {
                this.currentItem = this.leftlistConfigList[0]
                this.$router.replace({
                    name: 'targetPage',
                    params: {
                        pageId: this.$route.params.pageId,
                        typeId: this.currentItem.id,
                        subType: this.$route.params.subType,
                    },
                    query: {
                        platformId: localStorage.getItem('platformId'),
                        type: this.$route.query.type
                    }
                })
            }

            this.TypeLabels = []
            this.currentItem.children.map(item => {
                this.TypeLabels.push(item.name)
            })

            //是否有二级标签
            if (this.$route.params.subType != 0) {
                this.currentItem.children.map(item => {
                    if (item.id == this.$route.params.subType) {
                        this.currentLabel = item.name
                    }
                })
            } else {
                this.currentLabel = this.currentItem.children[0].name
            }

            //筛选内容
            if (this.$route.query.name) {
                this.infoTypes.map((item, index) => {
                    if (item.name == this.$route.query.name) {
                        this.currentInfoTypeIndex = index
                    }
                })
            } else {
                this.currentInfoTypeIndex = 0
            }
            this.getData()
        },
        getData (data) {
            let params = {}
            if (data) {
                params = data
            }
            //根据页面上的id去计算需要的参数
            params.channel = this.currentItem.codeValueId
            params.pageSize = 999;
            this.currentItem.children.map(item => {
                if (item.id == this.$route.params.subType) {
                    params.classify = item.codeValueId
                }
            })
            params = {
                ...params,
                ...this.$route.query
            }
            if (!params.typeList) {
                params.typeList = this.infoTypes[0].typeList
            }
            // 获取内容
            getInfoByDynamicUrl(this.configuration.apiUrl, { ...params }).then(res => {
                setTimeout(() => {
                    this.infoItem = res.data.list
                }, 20);
            })
            // 获取置顶直播
            if (this.currentInfoTypeIndex == 0) {
                params.typeList += ("," + this.TYPE[8])
            }
            getInfoByDynamicUrl(this.configuration.apiUrl, { ...params, position: 2, newStatus: 2 }).then(res => {
                this.swiperImageItems = []
                setTimeout(() => {
                    this.swiperImageItems = res.data.list
                }, 20);
            })
        },
        changeLeftBarOrder () {
            this.leftlistConfigList = []
            // console.log('selectedDominData', localStorage.getItem('selectedDominData'));
            let selectedDominData = localStorage.getItem('selectedDominData').split(',')
            this.configuration.children.map(item => {
                if (selectedDominData.includes(item.codeValueId)) {
                    item.recommendFlag = true
                    this.leftlistConfigList.push(item)
                }
            })
            this.configuration.children.map(item => {
                if (!selectedDominData.includes(item.codeValueId)) {
                    this.leftlistConfigList.push(item)
                }
            })

            this.leftlistConfigList.map((item, index) => {
                if (this.$route.params.typeId == item.id) {
                    this.currentTypeIndex = index
                }
            })

        },
        changeTypeIndex (item, index) {
            this.currentTypeIndex = index
            this.currentItem = item
            this.$forceUpdate()
            this.showRightButton = false
            this.computeRightButton()
            this.$router.replace({
                name: 'targetPage',
                params: {
                    pageId: this.$route.params.pageId,
                    typeId: item.id,
                    subType: item.children.length > 0 ? item.children[0].id : 0
                },
                query: {
                    platformId: localStorage.getItem('platformId'),
                    // type: this.$route.query.type
                }
            }).catch(err => { err })
        },
        computeClass (index) {
            if (index == this.currentTypeIndex) {
                return 'active'
            } else if (index == this.currentTypeIndex + 1) {
                return 'active-next'
            } else if (index == this.currentTypeIndex - 1) {
                return 'active-last'
            } else {
                return ''
            }
        },
        computeInfoTypesClass (index) {
            if (index == this.currentInfoTypeIndex) {
                return 'active'
            } else {
                return ''
            }
        },
        computedTypeLabelGroupClass () {
            if (this.tpyeListButtonShow) {
                return this.showMoreFlag ? 'typeLabels-active' : 'typeLabels-dis-active'
            } else {
                return 'typeLabels-active'
            }
        },
        computeTypeLabelsClass (item) {
            // console.log('computeTypeLabelsClass', item, this.currentLabel);
            return item == this.currentLabel ? 'active' : ''
        },
        changeHandler (val) {
            console.log(val);
            this.currentLabel = val
            this.currentItem.children.map(item => {
                if (item.name == val) {
                    this.$router.replace({
                        name: 'targetPage',
                        params: {
                            pageId: this.$route.params.pageId,
                            typeId: this.$route.params.typeId,
                            subType: item.id
                        },
                        query: {
                            platformId: localStorage.getItem('platformId'),
                            type: this.$route.query.type
                        }
                    })
                }
            })
        },
        changeInfoTypes (item, index) {
            this.currentInfoTypeIndex = index;
            let temp = JSON.parse(JSON.stringify(item))
            // delete temp.name
            this.$router.replace({
                name: 'targetPage',
                params: {
                    pageId: this.$route.params.pageId,
                    typeId: this.$route.params.typeId,
                    subType: this.$route.params.subType,
                },
                query: {
                    platformId: localStorage.getItem('platformId'),
                    ...temp
                }
            })
        },
        changeSwiperImageItems () {

        },
        clickImage (item, indexs) {
            if (item.type == 'diy' && !item.topUrl.length) return
            this.naviToDetails(this.$route.query.platformId, item.type, item)
        },
        computeRightButton () {
            setTimeout(() => {
                let element = document.getElementsByClassName('cube-scroll-wrapper')[0]
                if (element && element.scrollWidth == element.clientWidth) {
                    this.showRightButton = false
                } else {
                    this.showRightButton = true
                }
            }, 10);
        },
        rightButtonClick () {
            this.rightButtonArrow = !this.rightButtonArrow
            this.$forceUpdate()
        },
    }
}
</script>
<style lang='scss' scoped>
.activitypage-zly {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #fff;
}
.main-content {
    flex: 1;
    display: flex;
    flex-direction: row;
    .left-list {
        height: calc(100vh - 90px);
        overflow: scroll;
        font-size: 13px;
        background: rgba(246, 247, 248, 1);
        font-weight: 400;
        width: 88px;
        > div {
            position: relative;
            background: #fff;
            > div {
                width: 88px;
                height: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: rgba(246, 247, 248, 1);
            }
            .active {
                font-weight: 500;
                background: rgba(0, 0, 0, 0);
            }
            .active-last {
                border-radius: 0px 0px 10px 0px;
            }
            .active-next {
                border-radius: 0px 10px 0px 0px;
            }
        }
        .change-type-icon {
            position: absolute;
            left: 0;
            background: rgba(51, 51, 51, 1);
            border-radius: 0 4px 4px 0;
        }
    }
    .right-content {
        width: calc(100vw - 88px);
        height: calc(100vh - 90px);
        overflow: scroll;
        .scrollNavBar-zly {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .cubeScroll {
                height: 50px;
            }
            .right-button {
                position: relative;
                flex-shrink: 0;
                width: 49px;
                height: 41px;
                background: url("./image/shadow.png");
                background-size: 100%;
                .right-button-icon {
                    transition: all 0.3s ease 0s;
                    position: absolute;
                    right: 14px;
                    top: 18px;
                }
                .reverse {
                    transform: rotateZ(180deg);
                }
            }
        }
    }
    .info-content {
        .cover-content {
            position: absolute;
            height: 100%;
            width: 100%;
            z-index: 1;
            background: rgba(0, 0, 0, 0.4);
            .cover-top {
                position: absolute;
                background: #fff;
                height: 50px;
                width: 250px;
                line-height: 50px;
                padding-left: 15px;
                top: -51px;
                font-size: 12px;
                font-weight: 400;
                color: rgba(102, 102, 102, 1);
            }
            .type-label-group {
                border-top: 0.5px solid #eee;
                background: #fff;
                padding: 15px;
                display: flex;
                flex-wrap: wrap;
                div {
                    margin: 7.5px 0;
                    width: 28%;
                    text-align: left;
                    font-size: 12px;
                    font-weight: 400;
                    color: rgba(102, 102, 102, 1);
                    line-height: 14px;
                }
                .active {
                    font-weight: 500;
                    color: rgba(51, 51, 51, 1);
                }
            }
        }
        .info-type-group {
            margin-left: 2px;
            span {
                display: inline-block;
                padding: 8px;
                font-size: 13px;
                font-weight: 400;
                color: rgba(153, 153, 153, 1);
                line-height: 17px;
            }
            .active {
                color: rgba(76, 88, 138, 1);
            }
        }
        .v-line {
            height: 1px;
            width: 258px;
            margin: 2px 10px;
            background: #ccc;
        }
        .swipers {
            margin: 15px;
            width: 257px;
            height: 104px;
            border-radius: 4px;
        }
    }
}
.typeLabels-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    margin: 5px 10px 10px 10px;
    overflow: hidden;
    align-items: center;
}
.typeLabels-dis-active {
    height: 60px;
}
.typeLabels-active {
    height: auto;
}
.typeLabels-item {
    position: relative;
    margin: 0 14px 0 0;
    font-size: 13px;
    line-height: 30px;
}
.typeLabels-item-active {
    font-weight: 500;
    &::after {
        content: "";
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        height: 3px;
        width: 24px;
        background: #4d536b;
        border-radius: 2px;
        border-radius: 2px;
    }
}
.more {
    position: absolute;
    z-index: 1;
    right: 0;
    bottom: 6px;
    line-height: 33px;
    padding: 2px 4px;
    color: #ff6b00;
    display: flex;
    background: #fff;
    box-shadow: -0.11rem 0 10px 3px #fff;
}
.close-more {
    position: relative;
    top: 2px;
    line-height: 30px;
    padding: 0 4px;
    font-size: 12px;
    color: #ff6b00;
}
</style>
<style lang="scss">
.scrollNavBar-zly {
    .cube-scroll-nav-bar-item {
        font-size: 12px;
        position: relative;
        // padding: 10px;
    }
    .cube-scroll-nav-bar-item_active > span {
        font-weight: bolder !important;
    }
    //下划线
    .cube-scroll-nav-bar-item_active ::after {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 13px;
        width: 24px;
        height: 3px;
        background: rgba(77, 83, 107, 1);
        border-radius: 2px;
        content: "";
    }
    .cube-scroll-nav-bar_horizontal .cube-scroll-wrapper {
        text-align: left;
    }
    .cube-scroll-nav-bar-item_active {
        font-weight: 500;
        color: rgba(51, 51, 51, 1);
    }
    .swiperItem-content {
        img {
            height: 100%;
            width: 100%;
        }
    }
    .swiperItem {
        height: 100%;
        width: 100%;
    }
}
.activitypage-zly {
    .cube-slide-item img {
        width: 100%;
        height: 100%;
    }
    .cube-slide-dots {
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        bottom: 5px !important;
    }
    .cube-slide-dots span.active {
        height: 3px;
        width: 11px;
        background-size: 100% 100%;
        margin: 0px 4px;
        background-color: #fff;
    }
    .cube-slide-dots span {
        height: 3px;
        width: 4px;
        background-size: 100% 100%;
        margin: 0px 4px;
        background: rgba(255, 255, 255, 0.6);
    }
}
</style>