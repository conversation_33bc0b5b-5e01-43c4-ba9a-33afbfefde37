<template>
    <div class="userCenter-row-menu-zly">
        <div v-for="menu in list" :key="menu.id" @click="toMenuPage(menu.menuKey, menu.children)">
            <i :class="`iconfont ${menu.logo}`"> </i>
            <div>{{menu.name}}</div>
        </div>
    </div>
</template>

<script>
export default {
    props: ['configuration'],
    data () {
        return {}
    },
    computed: {
        list () {
            return this.configuration.children;
        }
    },
    methods: {
        // 跳转菜单 内页
        toMenuPage (name, children) {
            this.$emit('toMenuPage', name, children);
        }
    }
}
</script>

<style lang="scss" scoped>
.userCenter-row-menu-zly {
    margin: 0 15px;
    position: relative;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    z-index: 1;
    justify-content: space-between;
    div {
        width: 165px;
        height: 90px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-around;
        padding: 0;
        background: rgba(255, 255, 255, 1);
        border-radius: 4px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(153, 153, 153, 1);
        line-height: 20px;
        i {
            margin-top: 25px;
            font-size: 32px;
            width: 32px;
            color: #4d536b;
            position: relative;
            &:before {
                top: 0px;
                position: absolute;
            }
        }
        div {
            margin-top: 20px;
            font-size: 12px;
        }
    }
}
</style>