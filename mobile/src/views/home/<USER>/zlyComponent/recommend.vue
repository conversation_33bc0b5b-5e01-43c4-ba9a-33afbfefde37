<template>
    <!-- 为你推荐 -->
    <div class="recommend" v-show="infoData.length">
        <!-- 根据类型分为两种item -->
        <div>
            <div class="title-row">
                <div v-show="configuration.name" class="recommend-title">
                    <!-- <img :src="configuration.logo" alt="" srcset=""> -->
                    <span>{{configuration.name}}</span>
                </div>
                <div v-show="infoData.length>=3" class="change-data" @click="changeData()">
                    <span>换一换</span>
                    <div id="changeDataIcon" class="">
                        <svg width="10px" height="10px" viewBox="0 0 10 10" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
                            <title>编组</title>
                            <desc>Created with Sketch.</desc>
                            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <g id="1.1首页改版-未登陆" transform="translate(-349.000000, -1702.000000)" fill="#666666">
                                    <g id="编组-36" transform="translate(349.000000, 1702.000000)">
                                        <g id="编组">
                                            <path d="M7.68347131,2.15306981 C9.24920408,3.68844033 9.2539516,6.18259976 7.69391586,7.72357724 C7.59991492,7.81702645 7.50116647,7.9048687 7.39767049,7.98897299 L6,6.62368003 L6,10 L9.43055942,10 L8.1981027,8.78796374 C10.3582252,6.9703766 10.6117429,3.77254462 8.76590633,1.64657509 C8.0471315,0.819549575 7.0776875,0.242967947 6,0 L6,1.15690122 C6.63711746,1.34379964 7.21631514,1.68582375 7.68347131,2.15306981" id="Fill-1"></path>
                                            <path d="M2.31669213,7.84766355 C0.751111377,6.31214953 0.746364316,3.81775701 2.3062486,2.27663551 C2.40024041,2.18317757 2.49897928,2.09439252 2.60246521,2.01121495 L4,3.37570093 L4,0 L0.569773643,0 L1.80211071,1.21214953 C-0.357802098,3.02990654 -0.612244573,6.2271028 1.2343622,8.35420561 C1.95306725,9.18037383 2.92241713,9.75794393 4,10 L4,8.83831776 C3.36389381,8.65140187 2.78475236,8.31121495 2.31669213,7.84766355" id="Fill-3"></path>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </svg>
                    </div>
                </div>
            </div>
            <div v-for="(item,index) in infoDataLimited" @click="clickItem(item)">
                <infoRecommend v-if="configuration.extraInfo.itemStyle == 'recommend'" :info='item' :iflast='index == infoDataLimited.length-1' :showTag='configuration.extraInfo.showTag'></infoRecommend>
            </div>
        </div>
    </div>
</template>
<script>
import { getInfoByDynamicUrl } from '@/api/configurable/common'
import infoItem from '@/components/newCommon/infoItem.vue'
import infoItemRight from '@/components/newCommon/infoItemRight.vue'
import infoSeries from '@/components/newCommon/infoSeries.vue'
import infoRecommend from '@/components/newCommon/infoRecommend.vue'

export default {
    components: {
        infoItem,
        infoItemRight,
        infoSeries,
        infoRecommend
    },
    props: ['configuration'],
    data () {
        return {
            infoData: [],
            infoDataLimited: [],
            tagList: [
                { tagName: '标签1' },
                { tagName: '标签1' },
                { tagName: '标签1' },
            ],
            changedDataIndex: 0,
        };
    },
    computed: {
    },
    watch: {
        $route: {
            handler (val) {
                setTimeout(() => {
                    this.getInfo()
                }, 200);
            },
            deep: true,
            immediate: true,
            params: {}
        }
    },
    mounted () {
        // this.getInfo()
    },
    methods: {
        getInfo () {
            let params = {}
            this.configuration.menuParams.map(item => {
                params[item.menuParamKey] = item.menuParamValue
            })
            if (this.configuration.extraInfo.pageFlag && this.configuration.type == 0) {
                params.pageNum = 1
                params.pageSize = this.configuration.extraInfo.defaultPageSize
            }

            params.topSign = 1;
            params.pageSize = 30;
            this.params = params
            // console.log(this.$store.state.scrollNavBar.extraMenuParams, params);
            getInfoByDynamicUrl(this.configuration.apiUrl, params).then(res => {
                // console.log(res);
                if (res.data.list > 3) {
                    this.infoData = res.data.list.concat(res.data.list, res.data.list)//三倍数组
                } else {
                    this.infoData = res.data.list
                }
                this.infoDataLimited = this.infoData.slice(this.changedDataIndex, this.changedDataIndex + 3)
            })
        },
        showMore () {
            this.naviToCommonList(this.params)
        },
        clickItem (item) {
            this.naviToDetails(this.$route.query.platformId, item.type, item)
        },
        changeData () {
            document.getElementById('changeDataIcon').classList.add('around')
            setTimeout(() => {
                document.getElementById('changeDataIcon').classList.remove('around')
                if (this.infoData.length > this.changedDataIndex + 3) {
                    this.changedDataIndex += 3
                } else {
                    this.changedDataIndex = 0
                }
                this.infoDataLimited = this.infoData.slice(this.changedDataIndex, this.changedDataIndex + 3)
            }, 1000);
        }
    }
}
</script>
<style lang='scss' scoped>
.around {
    animation: rotate 1s ease-in-out;
}
@keyframes rotate {
    from {
        transform: rotateZ(0deg);
    }
    to {
        transform: rotateZ(360deg);
    }
}
.recommend {
    // background-color: #fff;
    margin-top: 20px;
    padding: 10px 15px;
}
.recommend-title {
    display: flex;
    align-items: center;
    margin: 0px 0;
    img {
        height: 24px;
        width: 24px;
    }
    span {
        font-weight: 400;
        font-size: 16px;
        margin-left: 6px;
    }
}

.title-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-top: 10px;
    span {
        font-size: 18px;
        font-weight: 500;
        color: rgba(51, 51, 51, 1);
        line-height: 25px;
    }
}
.change-data {
    display: flex;
    align-items: center;
    span {
        font-size: 12px;
        font-weight: 400;
        color: rgba(102, 102, 102, 1);
        line-height: 17px;
        margin-right: 5px;
    }
    div {
        height: 16px;
        svg {
            height: 16px;
        }
    }
}
.info-item {
    padding: 3px 0;
    display: flex;
    flex-direction: left;
    width: 100%;
    padding: 10px 0;
}
.info-right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 225px;
}

.info-coverImg {
    width: 113px;
    height: 80px;
    border-radius: 5px;
    margin-right: 10px;
    border: 1px solid #eee;
}

.marker-radio-orange {
    border-radius: 8px;
    height: 16px;
    font-size: 12px;
    line-height: 14px;
    padding: 0 5px;
    border: 1px solid #ff620d;
    color: #999;
}
.marker-radio-group-blue {
    display: flex;
    flex-wrap: wrap;
    span {
        font-size: 12px;
        border: 1px solid #4e6ba4;
        border-radius: 9px;
        color: #4e6ba4;
        padding: 3px 5px 1px 5px;
        margin-right: 5px;
    }
}
.info-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
        font-size: 12px;
        line-height: 16px;
        color: #999;
        margin-right: 10px;
    }
}
.marker-square-orange {
    font-size: 12px;
    // height: 16px;
    line-height: 16px;
    border-radius: 2px;
    background: #ff620d;
    color: white !important;
    padding: 2px 5px 0px 5px;
}
.marker-radio-group-red {
    display: flex;
    flex-wrap: wrap;
    span {
        font-size: 10px;
        height: 14px;
        line-height: 12px;
        border: 1px solid #af2e07;
        border-radius: 7px;
        color: #af2e07;
        padding: 0 5px;
        margin-right: 5px;
    }
}
.info-title-box {
    min-height: 36px;
}
.info-title {
    font-weight: 400;
    word-break: break-word;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    font-size: 13px;
    line-height: 18px;
}
</style>