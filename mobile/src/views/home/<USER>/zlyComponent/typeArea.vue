<template>
    <div class="typeArea">
        <div class="type-group">
            <div @click="itemClick(item)" v-for="(item,index) in configuration.extraInfo.typeData">
                <div>
                    <img v-if="item.icon" :src="item.icon" alt="">
                </div>
                <span>{{item.name}}</span>
            </div>
        </div>
        <div class="menu-group">
            <div @click="itemClick(item)" v-for="(item,index) in configuration.extraInfo.menuData">
                <div>
                    <img v-if="item.icon" :src="item.icon" alt="">
                </div>
                <span>{{item.name}}</span>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    components: {},
    props: ['configuration'],
    data () {
        return {};
    },
    computed: {},
    mounted () { },
    methods: {
        itemClick (item) {
            if (item.targetUrl) {
                // window.location.href = item.targetUrl
                this.$router.push({ path: item.targetUrl })
            } else {
                let params = {}
                item.menuParams.map((innerItem) => {
                    params[innerItem.menuParamKey] = innerItem.menuParamValue
                })
                // params.channelStringList = localStorage.getItem('selectedDominData')
                this.naviToCommonList(params)
            }
        }
    }
}
</script>
<style lang='scss' scoped>
.typeArea {
    margin: 10px 0px;
}
.type-group {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    > div {
        margin-top: 7px;
        width: 25%;
        display: flex;
        flex-direction: column;
        align-items: center;
        > div {
            margin: 8px;
            border-radius: 15px;
            width: 44px;
            height: 44px;
        }
        span {
            font-size: 13px;
            color: #333;
        }
    }
}

.menu-group {
    margin-top: 6px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    > div {
        width: 25%;
        display: flex;
        flex-direction: column;
        align-items: center;
        > div {
            margin: 14px;
            border-radius: 50%;
            width: 30px;
            height: 30px;
        }
        span {
            font-size: 12px;
            color: #666;
        }
    }
}
</style>