<template>
    <div class="scrollArea" v-show="infoData.length">
        <!-- 根据类型分为两种item -->
        <div>
            <div class="title-row">
                <div v-show="configuration.name" class="scrollArea-title">
                    <img :src="configuration.logo" alt="" srcset="">
                    <span>{{configuration.name}}</span>
                </div>
                <div v-show="configuration.extraInfo.showMore" class="more-icon" @click="showMore">
                    <span>更多</span>
                    <img style='width:5px;height:10px;' src="@/assets/img/more_icon.png" alt="">
                </div>
            </div>
            <div class="scrollArea-row">
                <div v-for="(item,index) in infoData" @click="clickItem(item)">
                    <div class="info-item" :style="index != infoData.length-1?'border-bottom:1px solid #eee;':''">
                        <span class="marker-square-orange">{{item.conferenceType}}</span>
                        <img class="info-coverImg" :src="item.coverImg" alt="">
                        <div class="info-bottom">
                            <div class="info-title-box">
                                <div class="info-title">{{item.name}}</div>
                                <div class="info-detail">
                                    <img style="margin-right:4px;" src="@/assets/img/time_icon.png" alt="">
                                    <span>{{formatTime(item.beginTime || item.publishTime || item.createTime,true)}}</span>
                                    <span v-show="item.endTime">&nbsp-&nbsp{{formatTime(item.endTime,'noYear')}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { getInfoByDynamicUrl } from '@/api/configurable/common'

export default {
    components: {},
    props: ['configuration'],
    data () {
        return {
            infoData: [],
            tagList: [
                { tagName: '标签1' },
                { tagName: '标签1' },
                { tagName: '标签1' },
            ],
        };
    },
    computed: {
    },
    watch: {
        $route: {
            handler (val) {
                setTimeout(() => {
                    this.getInfo()
                }, 200);
            },
            deep: true,
            immediate: true,
            params: {}
        }
    },
    mounted () {
        // this.getInfo()
    },
    methods: {
        getInfo () {
            let params = {}
            this.configuration.menuParams.map(item => {
                params[item.menuParamKey] = item.menuParamValue
            })
            if (this.configuration.extraInfo.pageFlag && this.configuration.type == 0) {
                params.pageNum = 1
                params.pageSize = this.configuration.extraInfo.defaultPageSize
            }
            //加入二级标签和过滤器的参数
            if (this.$store.state.scrollNavBar.extraMenuParams.length) {
                this.$store.state.scrollNavBar.extraMenuParams.map(item => {
                    params[item.menuParamKey] = item.menuParamValue
                })
            }
            if (this.$route.query.type >= 0) {
                if (params.type >= 0 && params.type != this.$route.query.type) {
                    params.type = -1
                } else {
                    params.type = this.$route.query.type
                }
            }
            this.params = params
            // console.log(this.$store.state.scrollNavBar.extraMenuParams, params);
            getInfoByDynamicUrl(this.configuration.apiUrl, params).then(res => {
                // console.log(res);
                this.infoData = res.data.list
            })
        },
        showMore () {
            this.naviToCommonList(this.params)
        },
        clickItem (item) {
            this.naviToDetails(this.$route.query.platformId, item.type, item)
        },
    }
}
</script>
<style lang='scss' scoped>
.scrollArea-title {
    display: flex;
    align-items: center;
    margin: 4px 0;
    img {
        height: 24px;
        width: 24px;
    }
    span {
        font-weight: 400;
        font-size: 15px;
        margin-left: 6px;
    }
}
.scrollArea {
    background-color: #fff;
    margin-top: 10px;
}

.title-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    padding-top: 10px;
}

.more-icon {
    display: flex;
    align-items: center;
    span {
        float: right;
        color: #999;
        font-size: 12px;
    }
    img {
        margin-left: 5px;
        height: 16px;
    }
}

.info-item {
    display: flex;
    flex-direction: column;
    width: max-content;
    margin: 10px 5px 10px 0;
    position: relative;
    border: 1px solid #f6f6f6;
    border-radius: 4px;
}
.info-bottom {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.info-coverImg {
    width: 150px;
    height: 90px;
    border-radius: 4px;
}

.marker-radio-orange {
    border-radius: 8px;
    height: 16px;
    font-size: 12px;
    line-height: 14px;
    padding: 0 5px;
    border: 1px solid #ff620d;
    color: #999;
}
.info-detail {
    img {
        height: 12px;
        width: 12px;
    }
    display: flex;
    justify-content: left;
    align-items: center;
    margin: 4px 0;
    font-size: 10px;
    span {
        font-size: 12px;
        line-height: 16px;
        color: #999;
    }
}
.marker-square-orange {
    font-size: 12px;
    padding: 3px;
    position: absolute;
    opacity: 0.8;
    background: #ff6b00;
    border-radius: 4px 0 4px 0;
    color: white;
}
.marker-radio-group-red {
    display: flex;
    flex-wrap: wrap;
    span {
        font-size: 10px;
        height: 14px;
        line-height: 12px;
        border: 1px solid #af2e07;
        border-radius: 7px;
        color: #af2e07;
        padding: 0 5px;
        margin-right: 5px;
    }
}
.info-title-box {
    max-width: 150px;
    padding: 5px;
}
.info-title {
    font-weight: 400;
    word-break: break-word;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    font-size: 14px;
    line-height: 18px;
}

.scrollArea-row {
    display: flex;
    flex-direction: row;
    overflow: scroll;
    padding: 0 12px 0px 12px;
}
::-webkit-scrollbar {
    display: none;
}
</style>