<template>
    <div class="infoItem2-zly">
        <div @click="itemClick" class="item-body">
            <div class="item-left-container">
                <div class="item-image-container">
                    <img class="item-image" :src="(info.coverImg || info.bannerImg)" alt="">
                    <div v-show="true" class="item-tag">{{info.conferenceType}}</div>
                    <div class="hot-tag">
                        <img v-if="info.channelTagList && info.channelTagList[0]" :src="info.channelTagList[0].tagIcon" alt="">
                    </div>
                </div>
                <div class="item-title-box">
                    <div class="item-title">{{info.name}}</div>
                </div>
                <div class="info-detail" style="margin-top:8px">
                    <div style="width:100%;">
                        <div v-if="[TYPE[0]].includes(info.type)" style="display:flex;justify-content:space-between;width:100%;">
                            <div class="infoItem2-zly-activity-bottom">
                                <div class="infoItem2-zly-activity-time">{{formatTime(info.beginTime || info.publishTime || info.createTime,'YYYY-MM-DD HH:mm')}}</div>
                                <div class="infoItem2-zly-activity-address">
                                    <svg width="10px" height="12px" viewBox="0 0 24 26" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
                                        <title>编组 15</title>
                                        <desc>Created with Sketch.</desc>
                                        <defs>
                                            <polygon id="path-1" points="0.000189473684 0.09625 17.7841895 0.09625 17.7841895 23.0769231 0.000189473684 23.0769231"></polygon>
                                        </defs>
                                        <g id="行家活动0507/我的收藏修改" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <g id="行家活动-首页" transform="translate(-631.000000, -1456.000000)">
                                                <g id="编组" transform="translate(631.000000, 1456.000000)">
                                                    <g id="编组-15" transform="translate(3.000000, 0.865192)">
                                                        <g id="编组" transform="translate(0.000000, 0.000000)">
                                                            <mask id="mask-2" fill="white">
                                                                <use xlink:href="#path-1"></use>
                                                            </mask>
                                                            <g id="Clip-2"></g>
                                                            <path d="M8.89218947,0.09625 C3.98103158,0.09625 0.000189473684,4.13759615 0.000189473684,9.12125 C0.000189473684,11.3164423 0.776084211,13.3933654 2.16208421,15.0202885 L8.0964,22.6847115 C8.50187368,23.2077885 9.28250526,23.2077885 9.68797895,22.6847115 L15.6232421,15.0202885 C17.0082947,13.3933654 17.7841895,11.3164423 17.7841895,9.12125 C17.7841895,4.13759615 13.8033474,0.09625 8.89218947,0.09625" id="Fill-1" fill="#DADADA"></path>
                                                        </g>
                                                        <path d="M8.89218947,5.33451923 C6.83166316,5.33451923 5.16145263,7.02971154 5.16145263,9.12105769 C5.16145263,11.2133654 6.83166316,12.9085577 8.89218947,12.9085577 C10.9527158,12.9085577 12.6238737,11.2133654 12.6238737,9.12105769 C12.6238737,7.02971154 10.9527158,5.33451923 8.89218947,5.33451923" id="Fill-3" fill="#FFFFFF"></path>
                                                    </g>
                                                </g>
                                            </g>
                                        </g>
                                    </svg>
                                    {{info.city?info.city:'待定'}}
                                </div>
                            </div>
                        </div>
                        <div v-if="[TYPE[6]].includes(info.type) && info.status !=0" style="display:flex;justify-content: space-between;width:100%;">
                            <div> <span style="margin-right:10px;">开播时间</span> {{formatTime( info.beginTime || info.publishTime || info.createTime,'YYYY-MM-DD HH:mm')}}</div>
                        </div>
                        <div v-if="[TYPE[6]].includes(info.type) && info.status == 0" style="display:flex;justify-content: space-between;width:100%;">
                            <div><span style="margin-right:10px;">已结束</span>{{info.duration}}分钟</div>
                        </div>
                        <div v-if="[TYPE[9]].includes(info.type)" style="display:flex;justify-content: space-between;width:100%;">
                            <div><span style="margin-right:10px;">{{info.status == 0?'已结束':'更新中'}}</span>{{`${info.updateNum}期/共${info.renewalPeriod}期`}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    /**
     *  临时页面的信息流 item
     */
    components: {},
    props: ['info', 'iflast', 'showTag'],
    data () {
        return {
        };
    },
    computed: {

    },
    mounted () {

    },
    methods: {
        itemClick () {
            let info = JSON.parse(JSON.stringify(this.info))
            // info.id = info.itemId
            this.naviToDetails(localStorage.getItem('platformId'), this.info.type, info)
        },
    }
}
</script>
<style lang='scss' scoped>
.infoItem2-zly {
    .item-body {
        display: flex;
        justify-content: space-between;
        margin: 15px;
        margin-top: 0px;
        padding: 10px;
        overflow: hidden;
        background-color: #fff;
    }
    .item-image-container {
        flex-shrink: 0;
        width: 325px;
        height: 195px;
        position: relative;
    }
    .item-left-container {
        display: flex;
        flex-direction: column;
    }
    .item-image {
        border-radius: 4px;
        width: 325px;
        height: 195px;
    }
    .item-tag {
        position: absolute;
        right: 6px;
        bottom: 6px;
        color: white;
        font-size: 14px;
        background-color: rgba(0, 0, 0, 0.6);
        // opacity: 0.8;
        border-radius: 2px;
        padding: 6px 12px;
    }
    .hot-tag {
        width: 52px;
        height: 28px;
        position: absolute;
        right: -4px;
        top: 6px;
        color: white;
        font-size: 10px;
        border-radius: 1px;
    }
    .item-title-box {
    }
    .item-title {
        margin-top: 10px;
        font-weight: 400;
        color: #333;
        word-break: break-word;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        font-size: 16px;
        line-height: 22px;
    }

    .info-detail {
        min-height: 18px;
        display: flex;
        justify-content: space-between;
        > div {
            font-size: 12px;
            line-height: 16px;
            color: #999;
            display: flex;
            align-items: center;
        }
        .price-text {
            font-size: 12px;
            font-weight: 500;
            color: rgba(255, 107, 0, 1);
            line-height: 17px;
        }
        .infoItem2-zly-activity-bottom {
            white-space: nowrap;
            width: 100%;
        }
        .infoItem2-zly-activity-time {
            display: inline-block;
            width: 96px;
            margin-right: 10px;
            vertical-align: middle;
        }
        .infoItem2-zly-activity-address {
            display: inline-block;
            width: 218px;
            padding-left: 13px;
            vertical-align: middle;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
            svg {
                position: absolute;
                left: 0;
                top: 2px;
            }
        }
    }
    .info-tag {
        display: flex;
        align-items: center;
        background: #ffecde;
        border-radius: 2px;
        margin-right: 5px;
        color: #ff620d;
        padding: 3px 5px;
        font-size: 10px;
    }
    .v-line {
        margin-left: 15px;
        background-color: #eee;
        width: 258px;
        height: 1px;
    }
}
</style>