<template>
    <div class="topBar">
        <div class="shadow">
            <img src="./shadow.png" alt="" srcset="">
        </div>
        <!-- <span>您关注的行业：{{recommendDomins.join(',')}}</span> -->
        <svg @click="editClick()" width="18px" height="18px" viewBox="0 0 20 19" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
            <title>编组</title>
            <desc>Created with Sketch.</desc>
            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <g id="1.2活动分类" transform="translate(-340.000000, -99.000000)">
                    <g id="编组-2" transform="translate(340.000000, 99.000000)">
                        <g id="编组">
                            <path d="M16.0596,0.0044 C15.6076,0.0044 15.1926,0.1774 14.8856,0.4914 L4.6306,11.0864 C4.6206,11.0964 4.6236,11.1104 4.6156,11.1224 C4.6016,11.1394 4.5886,11.1584 4.5816,11.1824 L3.5286,15.1714 C3.4676,15.4034 3.5306,15.6554 3.6966,15.8314 C3.8216,15.9554 3.9856,16.0254 4.1606,16.0254 C4.2186,16.0254 4.2766,16.0184 4.3336,16.0024 L8.1686,14.9214 C8.1746,14.9214 8.1776,14.9284 8.1816,14.9284 C8.2266,14.9284 8.2686,14.9114 8.3026,14.8764 L18.5576,4.2854 C18.8626,3.9704 19.0296,3.5414 19.0296,3.0744 C19.0296,2.5464 18.8126,2.0164 18.4326,1.6244 L17.4636,0.6224 C17.0836,0.2294 16.5706,0.0044 16.0596,0.0044 M16.0596,1.0044 C16.2996,1.0044 16.5556,1.1214 16.7446,1.3174 L17.7146,2.3214 C17.9126,2.5244 18.0296,2.8054 18.0296,3.0744 C18.0296,3.2794 17.9626,3.4624 17.8396,3.5894 L7.7606,13.9984 L4.6416,14.8764 L5.4986,11.6264 L15.6016,1.1904 C15.7196,1.0684 15.8776,1.0044 16.0596,1.0044" id="Fill-1" fill="#FF6B00"></path>
                            <path d="M18.4463,6.3101 L18.4463,16.8031 C18.4463,17.7101 17.7103,18.4461 16.8033,18.4461 L2.1433,18.4461 C1.2363,18.4461 0.5003,17.7101 0.5003,16.8031 L0.5003,2.1431 C0.5003,1.2361 1.2363,0.5001 2.1433,0.5001 L12.4013,0.5001" id="Stroke-3" stroke="#FF6B00" stroke-linecap="round" stroke-linejoin="round"></path>
                            <line x1="5.187" y1="10.9019" x2="8.151" y2="13.6529" id="Stroke-5" stroke="#FF6B00"></line>
                            <line x1="13.3989" y1="3.3042" x2="16.2029" y2="6.1082" id="Stroke-7" stroke="#FF6B00"></line>
                        </g>
                    </g>
                </g>
            </g>
        </svg>
    </div>
</template>
<script>
import { getInfoByDynamicUrl } from '@/api/configurable/common'
import { platformBizApi } from '@/config/env';
export default {
    components: {},
    props: ['configuration'],
    data () {
        return {
            recommendDomins: []
        };
    },
    computed: {},
    watch: {
        configuration (val) {
            this.recommendDomins = []
            val.map(item => {
                if (item.recommendFlag) {
                    this.recommendDomins.push(item.name)
                }
            })
        }
    },
    mounted () {
    },
    methods: {
        editClick () {
            this.$store.state.dominPage.domainShow = true;
        }
    }
}
</script>
<style lang='scss' scoped>
.topBar {
    z-index: 1;
    height: 40px;
    width: 53px;
    display: flex;
    flex-direction: row;
    padding: 10px 15px 10px 5px;
    align-items: center;
    justify-content: space-between;
    position: relative;
    .shadow {
        position: absolute;
        left: -13px;
        img {
            height: 42px;
            width: 22px;
        }
    }
    svg{
        margin-top: 2px;
        z-index: 2;
    }
    span {
        font-size: 12px;
        font-weight: 400;
        color: rgba(255, 107, 0, 1);
        line-height: 17px;
    }
}
</style>