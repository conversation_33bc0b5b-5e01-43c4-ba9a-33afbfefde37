<template>
    <div class="activitypage-zly">
        <div class="main-content">
            <div class="left-list2">
                <div>
                    <cube-scroll-nav-bar v-if="leftlistConfigList && leftlistConfigList.length" :current="currenMainChannel" :labels="getArrayName(leftlistConfigList)" @change="changeTypeIndex" />
                </div>
                <topBar2Zly :configuration='leftlistConfigList'></topBar2Zly>
            </div>
            <cube-scroll  class="list-scroll right-content" ref="scroll" :data="infoItem" :options="options" :scrollEvents="['scroll']" @pulling-down="onPullingDown" @pulling-up="onPullingUp" @scroll='scrollEvent'>
                <cube-slide v-if="swiperImageItems.length" :data="swiperImageItems" @change="changeSwiperImageItems" class="swipers" ref="slideImage" allowVertical>
                    <cube-slide-item class="swiperItem-content" :key="indexs" @click.native="clickImage(item, indexs)" v-for="(item, indexs) in swiperImageItems">
                        <img class="swiperItem" :src="item.bannerImg">
                    </cube-slide-item>
                </cube-slide>
                <div style="padding-top:5px"></div>
                <div :class="rightButtonArrow?'scrollNavBar2-zly right-button-off':'scrollNavBar2-zly'">
                    <div :style="currentSubChannel == item.name?'font-weight:500;color:rgba(51,51,51,1);':''" v-for="(item,index) in currentItem.children" @click='subChannelClick(item.name)'>{{item.name}}</div>
                    <div v-if="showRightButton" class="right-button" @click="rightButtonClick()">
                        {{rightButtonArrow?'更多':'收起'}}
                        <svg :class="rightButtonArrow?'right-button-icon':'right-button-icon reverse'" width="10px" height="8px" viewBox="0 0 14 8" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
                            <title>路径 3</title>
                            <desc>Created with Sketch.</desc>
                            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
                                <g id="1.2活动分类" transform="translate(-347.000000, -146.000000)" stroke="#ff6b00">
                                    <g id="路径-2" transform="translate(347.000000, 141.000000)">
                                        <g id="路径-3" transform="translate(1.000000, 0.000000)">
                                            <polyline id="路径" transform="translate(6.000000, 6.000000) rotate(45.000000) translate(-6.000000, -6.000000) " points="10 2 10 10 2 10"></polyline>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </svg>
                    </div>
                </div>
                <div class="info-content">
                    <div style="height:1px;" class="v-line"></div>
                    <div class="info-type-group">
                        <span :class="computeInfoTypesClass(index)" @click="changeInfoTypes(item,index)" v-for="(item,index) in infoTypes">{{item.name}}</span>
                    </div>
                    <div v-if="infoItem.length>0" style="background:rgba(245,246,247,1);padding:15px 0;">
                        <div  v-for="(item,index) in infoItem" class="info-item">
                            <infoItem2Zly :info='item' :iflast='index == infoItem.length-1'></infoItem2Zly>
                        </div>
                    </div>
                    <div v-if="infoItem.length == 0" class="no-data-tips">
                        <img src="@/assets/img/no_data_icon.png" alt="">
                        <div>暂无数据</div>
                    </div>
                </div>
            </cube-scroll>

        </div>
    </div>
</template>
<script>
import { getInfoByDynamicUrl } from '@/api/configurable/common'
import { platformBizApi } from '@/config/env';


export default {
    props: ['configuration'],
    components: {
    },
    data () {
        return {
            currentItem: {},
            currentSubChannel: 0,
            currenMainChannel: 0,
            showRightButton: false,
            rightButtonArrow: true,//true为向下箭头 false为向上
            currentInfoTypeIndex: 0,
            infoTypes: [],
            swiperImageItems: [],
            infoItem: [],
            cubeScrollRefreshFlag: true,
            leftlistConfigList: [],//改变排序后的Config
            mainScrollTop: null,
            options: {
                pullUpLoad: {
                    threshold: 50,
                    stopTime: 1000,
                    txt: {
                        more: '',
                        noMore: '没有更多信息啦！'
                    }
                },
                pullDownRefresh: {
                    threshold: 60,
                    stopTime: 1000,
                    txt: '更新成功'
                },
                scrollbar: false
            },
            pageData: {
                pageNum: 1,
                pageSize: 10,
            },
        };
    },
    watch: {
        showRightButton (val) {
            this.cubeScrollRefreshFlag = false
            setTimeout(() => {
                this.cubeScrollRefreshFlag = true
            }, 1);
        },
        $route: {
            handler (val) {
                this.init()
            },
            deep: true
        },
    },
    computed: {
    },
    created () {
        this.infoTypes = [
            { name: '所有类型', typeList: `${this.TYPE[0]},${this.TYPE[6]},${this.TYPE[9]}` },
            { name: '直播', typeList: `${this.TYPE[6]},${this.TYPE[9]}`, liveStatus: '1' },
            { name: '回看', typeList: `${this.TYPE[6]},${this.TYPE[9]}`, liveStatus: '0' },
            { name: '线下', typeList: `${this.TYPE[0]}` },
        ]
    },
    mounted () {
        this.init()
    },
    methods: {
        scrollEvent (e) {
            if (e.y < 0) {
                document.querySelector(".left-list2").style.boxShadow = "0px 8px 10px 0px rgba(0, 0, 0, 0.1)"
            } else {
                document.querySelector(".left-list2").style.boxShadow = "unset"
            }
        },
        async init () {
            this.infoItem = []
            // 没有分类的话获取分类
            if (!localStorage.getItem('selectedDominData') ) {
                await getInfoByDynamicUrl(platformBizApi + `/conferenceFront/getDomain`, { platformId: localStorage.getItem('platformId') }).then(res => {
                    if (res.data.length) {
                        let tempData = []
                        res.data.map(item => {
                            tempData.push(item.channel)
                        })
                        localStorage.setItem('selectedDominData', tempData)
                    } else {
                        this.$store.state.dominPage.domainShow = true;
                    }
                }, () => {
                    this.$store.state.dominPage.domainShow = true
                })
            }
            this.computeRightButton()
            this.changeLeftBarOrder()
            this.routerInit()

        },
        //接收路由初始化
        routerInit () {
            if (this.$route.params.typeId != 0) {
                this.configuration.children.map(item => {
                    if (item.id == this.$route.params.typeId) {
                        this.currentItem = item
                    }
                })
            } else {
                this.currentItem = this.leftlistConfigList[0]
                this.$router.replace({
                    name: 'targetPage',
                    params: {
                        pageId: this.$route.params.pageId,
                        typeId: this.currentItem.id,
                        subType: this.$route.params.subType,
                    },
                    query: {
                        platformId: localStorage.getItem('platformId'),
                        ...this.infoTypes[this.currentInfoTypeIndex]
                    }
                })
            }


            //是否有二级标签
            if (this.$route.params.subType != 0) {
                this.currentItem.children.map(item => {
                    if (item.id == this.$route.params.subType) {
                        this.currentSubChannel = item.name
                    }
                })
            } else {
                this.currentSubChannel = this.currentItem.children[0].name
            }

            //筛选内容
            if (this.$route.query.name) {
                this.infoTypes.map((item, index) => {
                    if (item.name == this.$route.query.name) {
                        this.currentInfoTypeIndex = index
                    }
                })
            } else {
                this.currentInfoTypeIndex = 0
            }
            this.currenMainChannel = this.currentItem.name
            this.getData()
        },
        onPullingDown () {
            this.pageData.pageNum = 1
            this.getData()
        },
        onPullingUp () {
            this.pageData.pageNum++
            setTimeout(() => {
                this.getData('next')
            }, 300);
        },
        getData (mode) {
            let params = {}
            //根据页面上的id去计算需要的参数
            params.channel = this.currentItem.codeValueId
            params.pageSize = 10;
            this.currentItem.children.map(item => {
                if (item.id == this.$route.params.subType) {
                    params.classify = item.codeValueId
                }
            })
            params = {
                ...params,
                ...this.$route.query,
                ...this.pageData
            }
            if (!mode) {
                this.pageData.pageNum = 1
                params.pageNum = 1
                // 获取内容
                getInfoByDynamicUrl(this.configuration.apiUrl, params).then(res => {
                    setTimeout(() => {
                        this.infoItem = res.data.list
                    }, 20);
                })
                // 获取置顶直播
                getInfoByDynamicUrl(this.configuration.apiUrl, { ...params, pageSize: 999, position: 2 }).then(res => {
                    this.swiperImageItems = []
                    setTimeout(() => {
                        this.swiperImageItems = res.data.list
                    }, 20);
                })
            } else if (mode == 'next') {
                getInfoByDynamicUrl(this.configuration.apiUrl, params).then(res => {
                    setTimeout(() => {
                        this.infoItem = this.infoItem.concat(res.data.list)
                    }, 20);
                    if (res.data.list.length == 0) {
                        this.pageData.pageNum--
                        this.$refs.scroll.forceUpdate()
                    }
                })
            }

        },
        changeLeftBarOrder () {
            this.leftlistConfigList = []
            // console.log('selectedDominData', localStorage.getItem('selectedDominData'));
            let selectedDominData = [];
            if(localStorage.getItem('selectedDominData')){
                selectedDominData = localStorage.getItem('selectedDominData').split(',')
            }
            this.configuration.children.map(item => {
                if (selectedDominData.includes(item.codeValueId)) {
                    // item.recommendFlag = true
                    this.leftlistConfigList.push(item)
                }
            })
            this.configuration.children.map(item => {
                if (!selectedDominData.includes(item.codeValueId)) {
                    // item.recommendFlag = true
                    this.leftlistConfigList.push(item)
                }
            })
        },
        //切换一级分类
        changeTypeIndex (item) {
            this.currentInfoTypeIndex = 0
            this.leftlistConfigList.map(innerItem => {
                if (innerItem.name == item) {
                    this.currentItem = innerItem
                }
            })
            this.currenMainChannel = item
            this.$forceUpdate()
            this.showRightButton = false
            this.computeRightButton()
            this.$router.replace({
                name: 'targetPage',
                params: {
                    pageId: this.$route.params.pageId,
                    typeId: this.currentItem.id,
                    subType: this.currentItem.children.length > 0 ? this.currentItem.children[0].id : 0
                },
                query: {
                    platformId: localStorage.getItem('platformId'),
                    ...this.infoTypes[this.currentInfoTypeIndex]
                }
            }).catch(err => { err })
        },
        computeInfoTypesClass (index) {
            if (index == this.currentInfoTypeIndex) {
                return 'active'
            } else {
                return ''
            }
        },
        computeTypeLabelsClass (item) {
            // console.log('computeTypeLabelsClass', item, this.currentSubChannel);
            return item == this.currentSubChannel ? 'active' : ''
        },
        //切换二级分类
        subChannelClick (val) {
            this.currentInfoTypeIndex = 0
            // console.log(val);
            this.currentSubChannel = val
            this.currentItem.children.map(item => {
                if (item.name == val) {
                    this.$router.replace({
                        name: 'targetPage',
                        params: {
                            pageId: this.$route.params.pageId,
                            typeId: this.$route.params.typeId,
                            subType: item.id
                        },
                        query: {
                            platformId: localStorage.getItem('platformId'),
                            ...this.infoTypes[this.currentInfoTypeIndex]
                        }
                    })
                }
            })
        },
        changeInfoTypes (item, index) {
            this.currentInfoTypeIndex = index;
            let temp = JSON.parse(JSON.stringify(item))
            // delete temp.name

            this.$router.replace({
                name: 'targetPage',
                params: {
                    pageId: this.$route.params.pageId,
                    typeId: this.$route.params.typeId,
                    subType: this.$route.params.subType,
                },
                query: {
                    platformId: localStorage.getItem('platformId'),
                    ...temp
                }
            })
        },
        changeSwiperImageItems () {

        },
        clickImage (item, indexs) {
            if (item.type == 'diy' && !item.topUrl.length) return
            this.naviToDetails(this.$route.query.platformId, item.type, item)
        },
        computeRightButton () {
            setTimeout(() => {
                let element = document.querySelector('.scrollNavBar2-zly')
                if (element && element.scrollHeight == element.clientHeight && element.scrollHeight <= 60) {
                    this.showRightButton = false
                } else {
                    this.showRightButton = true
                }
            }, 10);
        },
        rightButtonClick () {
            this.rightButtonArrow = !this.rightButtonArrow
            this.$forceUpdate()
        },
        getArrayName (data) {
            let tempArray = []
            data.map(item => {
                tempArray.push(item.name)
            })
            return tempArray
        }
    }
}
</script>
<style lang='scss' scoped>
.activitypage-zly {
    display: flex;
    flex-direction: column;
}
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    .left-list2 {
        position: fixed;
        top: 0px;
        display: flex;
        flex-direction: row;
        background: #fff;
        align-items: center;
        justify-content: space-between;
        z-index: 2;
        // box-shadow: 0px 8px 10px 0px rgba(0, 0, 0, 0.1);
        > :nth-child(1) {
            width: 335px;
        }
    }
    .right-content {
        margin-top: 45px;
        height: calc(100vh - 45px - 50px);
        overflow: hidden;
        background: #fff;
        .swipers {
            margin: 7px 15px 20px 15px;
            width: 345px;
            height: 140px;
            border-radius: 4px;
        }
    }
    .info-content {
        .cover-content {
            position: absolute;
            height: 100%;
            width: 100%;
            z-index: 1;
            background: rgba(0, 0, 0, 0.4);
            .cover-top {
                position: absolute;
                background: #fff;
                height: 50px;
                width: 250px;
                line-height: 50px;
                padding-left: 15px;
                top: -51px;
                font-size: 12px;
                font-weight: 400;
                color: rgba(102, 102, 102, 1);
            }
            .type-label-group {
                border-top: 0.5px solid #eee;
                background: #fff;
                padding: 15px;
                display: flex;
                flex-wrap: wrap;
                div {
                    margin: 7.5px 0;
                    width: 28%;
                    text-align: left;
                    font-size: 12px;
                    font-weight: 400;
                    color: rgba(102, 102, 102, 1);
                    line-height: 14px;
                }
                .active {
                    font-weight: 500;
                    color: rgba(51, 51, 51, 1);
                }
            }
        }
        .info-type-group {
            margin-left: 7px;
            span {
                display: inline-block;
                padding: 8px;
                font-size: 12px;
                font-weight: 400;
                color: rgba(153, 153, 153, 1);
                line-height: 17px;
            }
            .active {
                color: rgba(76, 88, 138, 1);
                font-weight: 500;
            }
        }
        .v-line {
            height: 1px;
            width: 345px;
            margin: 0px 15px;
            background: #ccc;
        }
    }
}
.scrollNavBar2-zly {
    margin-left: 15px;
    margin-top: 0px;
    margin-bottom: 3px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    position: relative;
    > div {
        display: inline-block;
        font-size: 13px;
        margin-right: 15px;
        margin-bottom: 12px;
        line-height: 18px;
        color: #666;
    }
    .right-button {
        box-shadow: #fff 0 0 10px 10px;
        background: #ffffff;
        position: absolute;
        right: 0;
        bottom: 0px;
        font-size: 12px;
        line-height: 12px;
        color: #ff6b00;
    }
}
.reverse {
    transform: rotateZ(180deg);
}
.right-button-off {
    max-height: 60px;
    overflow: hidden;
}
.swiperItem {
    border-radius: 4px;
}
</style>
<style lang="scss">
.left-list2 {
    .cube-scroll-wrapper {
        text-align: left !important;
    }
    .cube-scroll-nav-bar-items {
        padding-left: 15px;
        padding-right: 15px;
    }
    .cube-scroll-nav-bar-item {
        padding: 0;
        margin: 15px 15px 10px 0px;
        font-size: 16px;
        position: relative;
        color: #666;
        span {
            line-height: 22px;
        }
    }
    //下划线
    .cube-scroll-nav-bar-item_active ::after {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: -5px;
        width: 24px;
        height: 3px;
        background: #4d536b;
        border-radius: 2px;
        content: "";
    }
    .cube-scroll-nav-bar-item_active span {
        font-weight: 500 !important;
        color: #333 !important;
    }
}
.activitypage-zly {
    .cube-slide-item img {
        width: 100%;
        height: 100%;
    }
    .cube-slide-dots {
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        bottom: 5px !important;
    }
    .cube-slide-dots span.active {
        height: 3px;
        width: 11px;
        background-size: 100% 100%;
        margin: 0px 4px;
        background-color: #fff;
    }
    .cube-slide-dots span {
        height: 3px;
        width: 4px;
        background-size: 100% 100%;
        margin: 0px 4px;
        background: rgba(255, 255, 255, 0.6);
    }

    .list-scroll {
        height: 100%;
        overflow: hidden;
        background-color: #fff;
    }
    .no-data-tips {
        > img {
            width: 185px;
            height: 142px;
        }
        > div {
            margin-top: 20px;
        }
        margin-top: 170px;
        text-align: center;
        color: #999;
        display: flex;
        flex-direction: column;
        align-items: center;
        flex-wrap: wrap;
    }
}
</style>