<template>
    <div class="userInfo-container-zly flex-middle" @click="naviToInfoSet">
        <img class="userInfo-avater" :src="personProfile.headImg?personProfile.headImg:require('@/assets/img/default.png')">
        <div style="margin-left:15px;" v-if="loginStatus">
            <div class="userInfo-info-name ">{{personProfile.memberName}}</div>
            <div class="userInfo-info-company ">{{personProfile.company}}</div>
            <div class="userInfo-info-position ">{{personProfile.position}}</div>
        </div>
        <div v-else class="userInfo-login unit" @click.stop="pageLogin">
            点击登录/注册
        </div>
        <div class="userInfo-info-progress-nav">
            <svg width="10px" height="18px" viewBox="0 0 10 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
                <title>路径备份 4</title>
                <desc>Created with Sketch.</desc>
                <g id="按计划提供页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
                    <g id="N60-1.4我的" transform="translate(-340.000000, -144.000000)" stroke="#FFFFFF" stroke-width="2">
                        <g id="路径备份-3" transform="translate(333.000000, 144.000000)">
                            <g id="路径备份-4" transform="translate(0.000000, 1.000000)">
                                <polyline id="路径备份-2" transform="translate(8.000000, 8.000000) rotate(-45.000000) translate(-8.000000, -8.000000) " points="13 3 13 13 3 13"></polyline>
                            </g>
                        </g>
                    </g>
                </g>
            </svg>
        </div>
    </div>
</template>

<script>
export default {
    props: [],
    data () {
        return {

        }
    },
    computed: {
        personProfile () {
            return this.$store.state.personal.personProfile;
        },
        loginStatus () {
            return this.$store.state.loginStatus;
        }
    },
    mounted () {
        // this.getUserInfo()
        this.$store.dispatch('getPerprofile');
    },
    methods: {
        // 获取 个人信息
        getUserInfo () {
            getUserInfo().then(res => {
                if (res.code == this.$successCode) {
                    this.userInfo = res.data ? res.data : {};
                }
            })
        },
        naviToInfoSet () {
            if (this.loginStatus) {
                this.$router.push({ name: 'userInfoSet' })
            }
        },
        pageLogin () {
           // this.$store.state.loginShow = true;
           this.$store.commit('changeLoginShow',true);
        }
    }
}
</script>
<style lang="scss" scoped>
.userInfo-container-zly {
    margin: 15px;
    position: relative;
    width: 345px;
    height: 120px;
    border-radius: 4px;
    background: rgba(77, 83, 107, 1);
    color: white;
    padding: 15px 10px 18px 10px;
    .userInfo-avater {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background: rgba(204, 204, 204, 1);
        border: 1px solid rgba(255, 255, 255, 1);
        margin-right: 15px;
    }
    .userInfo-info-name {
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(255, 255, 255, 1);
        line-height: 25px;
        margin-top: 6px;
    }
    .userInfo-info-company {
        font-size: 14px;
        font-weight: 400;
        color: rgba(247, 247, 247, 1);
        line-height: 20px;
        margin-top: 10px;
    }
    .userInfo-info-position {
        margin-top: 6px;
    }
    .userInfo-info-progress-nav {
        position: absolute;
        right: 14px;
        .cubeic-arrow {
            height: 18px;
            width: 10px;
        }
    }
    .userInfo-login {
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
    }
}
</style>