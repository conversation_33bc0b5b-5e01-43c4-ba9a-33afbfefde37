<template>
    <div class="heavyLive" v-show="infoData.length>0">
        <div class="heavyLive-title-row">
            <div v-show="configuration.name" class="heavyLive-title">
                <span>{{configuration.name}}</span>
            </div>
            <div v-show="configuration.extraInfo.showMore" class="more-icon" @click="showMore">
                <span>全部</span>
                <img style='width:5px;height:10px;' src="@/assets/img/more_icon.png" alt="">
            </div>
        </div>
        <div class='square-2-column'>
            <div class="square-2-column-item" v-for="item in infoData" @click="clickItem(item)">
                <div>
                    <div class="info-image" style="position:relative;">
                        <img class="info-image" :src="item.previewPic || item.coverImg || item.coursePic" alt="">
                        <div class="info-tag">回看</div>
                    </div>
                    <div class="info-title">{{item.courseName || item.name || item.infoTitle}}</div>
                </div>
                <div class="info-detail">
                    <div>
                        <span v-if="[TYPE[6]].includes(item.type) && item.duration">{{item.duration}}分钟 </span>
                    </div>
                    <div class="price-text">
                        <template v-if="item.livePrice >0">{{item.livePrice}}元</template>
                        <template v-if="item.livePrice == 0">免费</template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { getInfoByDynamicUrl } from '@/api/configurable/common'
export default {
    components: {},
    props: ['configuration'],
    data () {
        return {
            infoData: [],
            params: {}
        };
    },
    computed: {},
    watch: {
        $route: {
            handler (val) {
                setTimeout(() => {
                    this.getInfo()
                }, 100);
            },
            deep: true,
            immediate: true
        }
    },
    mounted () {
        //  this.getInfo()
    },
    methods: {
        getInfo () {
            let params = {}
            this.configuration.menuParams.map(item => {
                params[item.menuParamKey] = item.menuParamValue
            })
            if (this.configuration.extraInfo.pageFlag) {
                params.pageNum = 1
                params.pageSize = this.configuration.extraInfo.defaultPageSize
            }

            params.topSign = 1;

            this.params = params
            getInfoByDynamicUrl(this.configuration.apiUrl, params).then(res => {
                // console.log(res);
                this.infoData = res.data.list
            })
        },
        showMore () {
            this.naviToCommonList(this.params)
        },
        clickItem (item) {
            //platformId, type, id, targetUrl
            this.naviToDetails(this.$route.query.platformId, item.type, item)
        }
    }
}
</script>
<style lang='scss' scoped>
.heavyLive {
    margin-top: 20px;
    padding: 0px 15px;
}

.heavyLive-title-row {
    height: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: -5px;
}
.heavyLive-title {
    color: rgba(51, 51, 51, 1);
    line-height: 25px;
    span {
        font-size: 18px;
        font-weight: 500;
    }
}
.title-line {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}
.more-icon {
    display: flex;
    align-items: center;
    span {
        float: right;
        color: #999;
        font-size: 12px;
    }
    img {
        margin-left: 5px;
        height: 16px;
    }
}
.square-2-column {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
}
.square-2-column-item {
    width: 166px;
    margin-top: 15px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
}
.info-image {
    width: -webkit-fill-available;
    border-radius: 5px 5px 0 0;
    height: 100px;
}
.info-title {
    margin: 6px;
    line-height: 19px;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    font-size: 13px;
    -webkit-box-orient: vertical;
    font-weight: 500;
    min-height: 54px;
}
.no-cover {
    background: #3151bb;
    color: white;
    padding: 5px;
    border-radius: 5px;
}

.info-detail {
    margin: 6px;
    display: flex;
    flex-direction: row;
    width: 150px;
    justify-content: space-between;
    :nth-child(1) {
        font-size: 12px;
        line-height: 16px;
        color: #999;
        margin-right: 10px;
        display: flex;
        align-items: center;
        span {
            margin-left: 2px;
        }
        // margin-bottom: 5px;
    }
}
.price-text {
    font-size: 12px;
    font-weight: 500;
    color: rgba(255, 107, 0, 1);
    line-height: 17px;
    // margin-bottom: 5px;
}
.info-tag {
    position: absolute;
    right: 2px;
    bottom: 2px;
    color: white;
    font-size: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0.8;
    border-radius: 1px;
    padding: 3px;
}
</style>