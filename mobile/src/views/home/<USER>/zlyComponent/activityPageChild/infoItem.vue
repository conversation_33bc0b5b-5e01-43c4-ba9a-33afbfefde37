<template>
    <div class="infoItem-zly">
        <div @click="itemClick" class="item-body">
            <div class="item-left-container">
                <div class="item-title-box">
                    <div class="item-title">{{info.name}}</div>
                </div>

                <div class="info-detail" style="margin-top:8px">
                    <div style="width:100%;">
                        <span v-if="[TYPE[0]].includes(info.type)">{{formatTime(info.beginTime || info.publishTime || info.createTime,'YYYY-MM-DD HH:mm')}}</span>
                        <span v-if="[TYPE[6]].includes(info.type) && info.status !=0">{{formatTime( info.beginTime || info.publishTime || info.createTime,'YYYY-MM-DD HH:mm')}}</span>
                        <div v-if="[TYPE[6]].includes(info.type) && info.status == 0" style="display:flex;justify-content: space-between;width:100%;">
                            <div>
                                {{info.duration}}分钟
                            </div>
                            <div>
                                已结束
                            </div>
                        </div>
                        <div v-if="[TYPE[9]].includes(info.type)" style="display:flex;justify-content: space-between;width:100%;">
                            <div style="">
                                {{`${info.updateNum}期/共${info.renewalPeriod}期`}}
                            </div>
                            <div style="margin-left:10px;">
                                {{info.status == 0?'已结束':'更新中'}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="item-image-container">
                <img class="item-image" :src="info.coverImg" alt="">
                <div v-show="true" class="item-tag">{{info.conferenceType}}</div>
                <div class="hot-tag">
                    <img v-if="info.channelTagList && info.channelTagList[0]" :src="info.channelTagList[0].tagIcon" alt="">
                </div>
            </div>
        </div>
        <div v-show="!iflast" class="v-line"></div>
    </div>
</template>
<script>
export default {
    /**
     *  临时页面的信息流 item
     */
    components: {},
    props: ['info', 'iflast', 'showTag'],
    data () {
        return {
        };
    },
    computed: {

    },
    mounted () {

    },
    methods: {
        itemClick () {
            let info = JSON.parse(JSON.stringify(this.info))
            // info.id = info.itemId
            this.naviToDetails(localStorage.getItem('platformId'), this.info.type, info)
        },
    }
}
</script>
<style lang='scss' scoped>
.infoItem-zly {
    .item-body {
        display: flex;
        justify-content: space-between;
        padding: 15px 10px;
        overflow: hidden;
        flex-direction: row;
    }
    .item-image-container {
        flex-shrink: 0;
        width: 120px;
        height: 72px;
        position: relative;
    }
    .item-left-container {
        display: flex;
        flex-direction: column;
        width: 143px;
    }
    .item-image {
        border-radius: 4px;
        width: 120px;
        height: 72px;
    }
    .item-tag {
        position: absolute;
        right: 2px;
        bottom: 2px;
        color: white;
        font-size: 10px;
        background-color: rgba(0, 0, 0, 0.5);
        opacity: 0.8;
        border-radius: 1px;
        padding: 3px;
    }
    .hot-tag {
        width: 31px;
        height: 22px;
        position: absolute;
        right: -5px;
        top: -5px;
        color: white;
        font-size: 10px;
        border-radius: 1px;
        padding: 3px;
    }
    .item-title-box {
        height: 48px;
    }
    .item-title {
        font-weight: 500;
        word-break: break-word;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        font-size: 12px;
        line-height: 16px;
    }

    .info-detail {
        width: 126px;
        min-height: 18px;
        display: flex;
        justify-content: space-between;
        > div {
            font-size: 12px;
            line-height: 16px;
            color: #999;
            display: flex;
            align-items: center;
            span {
                margin-left: 2px;
            }
        }
        .price-text {
            font-size: 12px;
            font-weight: 500;
            color: rgba(255, 107, 0, 1);
            line-height: 17px;
        }
    }
    .info-tag {
        display: flex;
        align-items: center;
        background: #ffecde;
        border-radius: 2px;
        margin-right: 5px;
        color: #ff620d;
        padding: 3px 5px;
        font-size: 10px;
    }
    .v-line {
        margin-left: 15px;
        background-color: #eee;
        width: 258px;
        height: 1px;
    }
}
</style>