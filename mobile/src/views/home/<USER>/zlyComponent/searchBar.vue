<template>
    <div class="header_wrap ">
        <div class="header_input" @click="showSearch">
            <img src="@/assets/img/search.png" alt="" class="iconSearch">
            <span class="text">{{configuration.extraInfo.placeholder}}</span>
        </div>
        <!-- <div class='head-right' v-show="configuration.extraInfo.rightButton" @click="showHidden = !showHidden">
            <svg width="25px" height="20px" viewBox="0 0 25 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <title>编组</title>
                <desc>Created with Sketch.</desc>
                <defs>
                    <polygon id="path-1" points="0 0.9814 24.392 0.9814 24.392 20.9994 0 20.9994"></polygon>
                </defs>
                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <g id="1.1首页改版" transform="translate(-331.000000, -106.000000)">
                        <g id="编组-42" transform="translate(331.000000, 105.000000)">
                            <g id="编组">
                                <path d="M11.888,7.1088 C12.548,7.1088 12.978,6.6648 12.978,6.0188 C12.978,5.3738 12.533,4.9288 11.888,4.9288 C11.257,4.9288 10.583,5.3448 10.583,6.0188 C10.583,6.6788 11.257,7.1088 11.888,7.1088" id="Fill-1" fill="#666666"></path>
                                <path d="M7.1414,6.0048 C7.1414,5.3588 6.6964,4.9148 6.0514,4.9148 C5.4204,4.9148 4.7464,5.3308 4.7464,6.0048 C4.7464,6.6788 5.4204,7.0948 6.0514,7.0948 C6.7114,7.1088 7.1414,6.6648 7.1414,6.0048" id="Fill-3" fill="#666666"></path>
                                <g transform="translate(0.000000, 0.018700)">
                                    <mask id="mask-2" fill="white">
                                        <use xlink:href="#path-1"></use>
                                    </mask>
                                    <g id="Clip-6"></g>
                                    <path d="M4.3309,14.0594 C4.3879,13.8874 4.3309,13.7014 4.1729,13.5864 C2.1649,12.1814 1.1469,10.4744 1.1469,8.5104 C1.1469,4.9684 4.5599,2.0854 8.7619,2.0854 C12.4329,2.0854 15.7169,4.3224 16.4339,7.2904 C12.5189,7.3764 9.3499,10.1594 9.3499,13.5284 C9.3499,13.9734 9.4069,14.4324 9.5359,14.8914 L9.4789,14.8914 C9.2489,14.9054 9.0059,14.9194 8.7619,14.9194 C7.9439,14.9194 7.2419,14.7764 6.4819,14.6184 L6.0229,14.5324 C5.9369,14.5184 5.8359,14.5324 5.7499,14.5614 L3.8429,15.5224 L4.3309,14.0594 Z M20.5919,17.9454 C20.4489,18.0464 20.3919,18.2324 20.4489,18.3904 L20.7499,19.4084 L19.4589,18.7054 C19.4019,18.6774 19.3309,18.6484 19.2589,18.6484 C19.2299,18.6484 19.1869,18.6484 19.1589,18.6624 C18.4129,18.8494 17.6379,19.0494 16.8779,19.0494 C13.3939,19.0494 10.5539,16.6554 10.5539,13.7154 C10.5539,10.7754 13.3939,8.3804 16.8779,8.3804 C20.3059,8.3804 23.1879,10.8184 23.1879,13.7154 C23.2019,15.1784 22.2849,16.6844 20.5919,17.9454 L20.5919,17.9454 Z M24.3929,13.7014 C24.3929,12.0954 23.6759,10.5464 22.3849,9.3414 C21.1089,8.1514 19.4019,7.4204 17.5949,7.2624 L17.5949,7.1764 C16.8349,3.6484 13.0499,0.9814 8.7759,0.9814 C3.9289,0.9814 -0.0001,4.3514 -0.0001,8.4954 C-0.0001,10.7044 1.1189,12.7114 3.2549,14.2744 L2.5239,16.4824 C2.4669,16.6554 2.5239,16.8274 2.6529,16.9414 C2.7819,17.0564 2.9829,17.0704 3.1259,16.9994 L5.9079,15.6084 L6.2379,15.6804 L6.2519,15.6804 C7.1129,15.8514 7.8579,16.0094 8.7759,16.0094 C8.9479,16.0094 9.6079,15.9524 9.8229,15.8514 C10.8839,18.4334 13.7239,20.1684 16.9069,20.1684 C17.7099,20.1684 18.5269,19.9814 19.3449,19.7814 L21.4809,20.9424 C21.5389,20.9714 21.6109,20.9994 21.6819,20.9994 C21.7829,20.9994 21.8829,20.9714 21.9549,20.8994 C22.0979,20.7854 22.1409,20.6124 22.0979,20.4404 L21.5529,18.6484 C23.3739,17.2004 24.3929,15.4354 24.3929,13.7014 L24.3929,13.7014 Z" id="Fill-5" fill="#666666" mask="url(#mask-2)"></path>
                                </g>
                                <path d="M14.598,10.9663 C14.154,10.9663 13.709,11.4113 13.709,11.8553 C13.709,12.2993 14.154,12.7443 14.598,12.7443 C15.272,12.7443 15.688,12.2853 15.688,11.8553 C15.688,11.4253 15.272,10.9663 14.598,10.9663" id="Fill-7" fill="#666666"></path>
                                <path d="M19.1727,10.9663 C18.7277,10.9663 18.2977,11.4113 18.2977,11.8553 C18.2977,12.2993 18.7277,12.7443 19.1727,12.7443 C19.8467,12.7443 20.2627,12.2853 20.2627,11.8553 C20.2627,11.4253 19.8467,10.9663 19.1727,10.9663" id="Fill-9" fill="#666666"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </svg>
        </div> -->
        <!--  <officialCode style="bottom:45px;" :visiable.sync="showHidden"></officialCode> -->
    </div>
</template>

<script>
// import officialCode from '@/components/newCommon/officialCode';
export default {
    props: ['configuration'],
    data () {
        return {
            showHidden: false,
        }
    },
    methods: {
        showSearch () {
            //根据类型不同展示生鲜大学的搜索内页还是行家活动的内页,同时根据参数自定义内部元素
            this.$store.state.searchBar.configuration = this.configuration
            let query = {}
            this.configuration.children[0].menuParams.map(item => {
                query[item.menuParamKey] = item.menuParamValue
            })
            query.platformId = localStorage.getItem('platformId')
            this.$router.push({ name: 'searchAll', query: query });
        },
    },
    components: { /* officialCode */ }
}
</script>

<style scoped lang="scss">
$color: #004da1;
.header_wrap {
    padding: 0 15px;
    width: 100%;
    height: 56px;
    display: flex;
    justify-content: center;
    align-items: center;
    // background-color: #fff;
}
.header_input {
    height: 36px;
    background: #fff;
    border-radius: 18px;
    display: flex;
    padding-left: 10px;
    justify-content: left;
    align-items: center;
    flex: 8;
}
.iconSearch {
    width: 16px;
    height: 16px;
    margin-right: 7px;
}
.text {
    font-size: 16px;
    color: #999;
    font-weight:400;
}
.head-right svg {
    margin-left: 15px;
    width: 24px;
}
</style>