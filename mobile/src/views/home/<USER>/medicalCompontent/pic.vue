<template>
    <div class="pic-container" :class="{'fit': fit}" @click="jump(itemInfo.targetUrl)" :style="{marginBottom}">
        <img :src="itemInfo.url" alt=""/>
    </div>
    </template>
    <script>
    export default{
        name:'pic',
        props: {
            itemInfo: {
                type: Object,
                default: () => {},
            },
            fit:{ // 是否自适应
                type: Boolean
            },
            marginBottom:{ //底部外边距
                type: String
            }
        },
        data(){
            return {
    
            }
        },
        methods:{
             // 跳转链接
             jump(url) {
                if (url) {
                    window.location.href = url
                }
            },
        }
    }
    </script>
    <style lang="scss" scoped>
    .pic-container{
        width: 100%;
        height: 100%;
        background: #D8D8D8;
        border-radius: 4px;
        cursor: pointer;
        img{
            width: 100%;
            height: 100%;
            border-radius: 4px;
        }
        &.fit{
            img{
                object-fit: cover;
            }
        }
    }
    </style>"