<!-- 往期直播 -->
<template>
    <div class='pastLive'>
        <common-title title="往期直播" :isEnd="true" :sourceData="pastLiveData" :isShowMore="!(lastLayoutId==configuration.layoutId&&infiniteLoadLast==1)"></common-title>
        <div class="pastlive-content flex-left flex-wrap">
            <!-- <past-live-item v-for="pastItem,index in pastLiveData" :itemData="pastItem"></past-live-item> -->
        <liveListStyle :list="pastLiveData" :listStyle="param.listStyle" ></liveListStyle>
        </div>
    </div>
</template>

<script>
import CommonTitle from './commonTitle';
import PastLiveItem from './pastLiveItem';
import liveListStyle from './liveListStyle/liveListStyle';
import { getConferenceFront } from '@/api/medicine/homePage';
export default {
    name: '',
    props: ['configuration','lastLayoutId','infiniteLoadLast'],
    data () {
        return {
            pastLiveData: [],
            param: {},
        };
    },

    created () {
        this.getConferenceFront();
    },

    components: {
        CommonTitle,
        PastLiveItem,
        liveListStyle,
    },

    computed: {},

    watch: {
        configuration: {
            handler (val) {
              this.param = JSON.parse(val.param)
                this.getConferenceFront()
            },
            deep: true,
            immediate: true
        }
    },

    mounted () { },

    methods: {
        getConferenceFront () {
            let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
            getConferenceFront(params).then((res) => {
                console.log('往期直播', res);
                if (res.code == this.$successCode) {
                    this.pastLiveData = res.data.list || [];
                }
            })
        },
				updataList(data){
					this.pastLiveData = this.pastLiveData.concat(data);
				},
    }
}

</script>
<style  scoped>
	.pastLive{
		
		    margin-bottom: 20px;
	}
.pastlive-content {
    padding: 0 15px;
}
</style>