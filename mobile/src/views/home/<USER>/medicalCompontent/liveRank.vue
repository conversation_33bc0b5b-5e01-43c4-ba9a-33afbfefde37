<template>
  <div class="leader-boards-page">
    <!-- 头部 -->
    <div class="leader-boards-header">
      <div class="leader-boards-header-title flex-middle">
        <div class="leader-boards-header-item flex-top" v-for="(value, index) in leaderBoardsHeader" :key="index" :style="getHeaderItemStyle(index)" @click="onHeaderItemClick(index)">
          <img :src="value.icon" alt="" class="leader-boards-header-item-icon" :style="getHeaderIconStyle(index)" />
          <span class="leader-boards-header-item-title" :style="getHeaderTitleStyle(index)">{{ value.title }}</span>
        </div>
      </div>
    </div>
    <!-- 轮播图 -->
    <div class="leader-boards-carousel">
      <swiper :options="swiperOption" ref="mySwiper" class="custom-swiper">
        <swiper-slide v-for="(value, index) in leaderBoardsList" :key="index" class="leader-boards-slide" :style="{ background: value.background }">
          <div class="leader-boards-card">
            <div class="leader-boards-item" v-for="(item, index) in value.list">
              <component :is="index > 2 ? 'TextLeader' : 'GraphicLeader'" :index="index" :item="item" />
            </div>
          </div>
        </swiper-slide>
      </swiper>
    </div>
  </div>
</template>
<script>
import "swiper/dist/css/swiper.css";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import TextLeader from "@/components/common/textLeader";
import GraphicLeader from "@/components/common/graphicLeader";
import CONSTANT from "@/config/config_constant";
import { getInfoByDynamicUrl } from "@/api/configurable/common";
import { getLeaderBoardsList } from "@/api/medicine/homePage";
export default {
  name: "liveRank",
  props: {
    configuration: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    swiper,
    swiperSlide,
    TextLeader,
    GraphicLeader,
  },
  data() {
    return {
      swiperOption: {
        slidesPerView: 1.165, // 减少右侧显示空间，只显示15%的下一张
        spaceBetween: 14, // slide之间的间距
        centeredSlides: false, // 不居中显示
        loop: true, // 开启循环
        loopAdditionalSlides: 3, // 增加额外的循环slides，确保循环正常
        loopFillGroupWithBlank: false, // 不用空白填充分组
        watchSlidesProgress: true, // 监听slides进度
        watchSlidesVisibility: true, // 监听slide可见性
        observer: true, // 启用observer，监听DOM变化
        observeParents: true, // 监听父元素变化
        rebuildOnUpdate: true, // 数据更新时重建swiper
        allowTouchMove: true, // 允许滑动
        autoplay: {
          delay: 60000000,
          disableOnInteraction: false,
        },
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
      },
      // 排行榜数据
      leaderBoardsList: CONSTANT.leaderBoardsList,
      // 定义榜单的头部数据
      leaderBoardsHeader: [],
      // 当前选中的榜单索引
      currentSlideIndex: 0,
      // 头部标签的激活程度数组，用于渐变过渡
      headerActivationLevels: [1, 0, 0], // 对应 bestList, yearList, monthList 的激活程度（0-1之间）
      // 数据加载状态
      isDataLoading: false,

    };
  },
  watch: {
    configuration: {
      handler(newVal) {
        this.fetchLeaderBoardsList();
      },
      deep: true,
      immediate: true,
    },
    // 监听榜单数据变化，更新swiper
    leaderBoardsList: {
      handler(newVal, oldVal) {
        // 如果正在加载数据，则跳过更新
        if (this.isDataLoading) return;
        
        // 只有当数据真正改变且不是初始化状态时才更新swiper
        if (!newVal || newVal.length === 0) return;
        
        // 检查是否所有数据都已加载完成（避免部分数据加载时的更新）
        const hasAllData = newVal.every(item => item.list && item.list.length > 0);
        if (!hasAllData) return;
        
        this.$nextTick(() => {
          if (this.$refs.mySwiper && this.$refs.mySwiper.swiper && newVal && newVal.length > 0) {
            const swiperInstance = this.$refs.mySwiper.swiper;
            // 只进行必要的更新，避免过度操作
            swiperInstance.update();
            swiperInstance.updateSlides();
            swiperInstance.updateProgress();
            swiperInstance.updateSlidesClasses();
          }
        });
      },
      deep: true,
    },
  },
  mounted() {
    // 组件挂载后的初始化逻辑
    this.$nextTick(() => {
      if (this.$refs.mySwiper && this.$refs.mySwiper.swiper) {
        const swiperInstance = this.$refs.mySwiper.swiper;
        // 只监听slide切换事件
        swiperInstance.on("slideChange", () => {
          const realIndex = swiperInstance.realIndex;
          this.currentSlideIndex = realIndex;

          // 添加轻微延迟避免与swiper动画冲突
          setTimeout(() => {
            this.resetActivationLevels(realIndex);
          }, 50);
        });
      }
    });
  },
  beforeDestroy() {
    // 清理事件监听
    if (this.$refs.mySwiper && this.$refs.mySwiper.swiper) {
      this.$refs.mySwiper.swiper.off("slideChange");
    }
  },

  methods: {
    // 获取轮播数据
    async fetchLeaderBoardsList() {
      // 防止重复请求
      if (this.isDataLoading) return;
      
      this.isDataLoading = true;
      
      try {
        const params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
        const res = await getInfoByDynamicUrl(params);
        if (res.code == this.$successCode) {
          // 记录当前用户的滑动位置
          let currentUserIndex = 0;
          if (this.$refs.mySwiper && this.$refs.mySwiper.swiper) {
            currentUserIndex = this.$refs.mySwiper.swiper.realIndex;
          }
          
          // 定义表头的icon 标签
          this.leaderBoardsHeader = res.data.map((item, index) => {
            return {
              icon: CONSTANT.leaderBoardsHeader[index],
              title: item.title,
            };
          });
          
          // 创建新的榜单数据数组，避免直接修改原数组引起的问题
          const newLeaderBoardsList = [...this.leaderBoardsList];
          
          // 批量请求所有榜单数据
          const promises = res.data.map(async (item, index) => {
            if (newLeaderBoardsList[index]) {
              newLeaderBoardsList[index].title = item.title;
              // 根据其中的id 获取对应的榜单数据
              try {
                const response = await getLeaderBoardsList({ rankId: item.id });
                if (response.code == this.$successCode) {
                  newLeaderBoardsList[index].list = response.data.slice(0, 7);
                } else {
                  this.$toast(response.info);
                  // 如果请求失败，保持原有数据不变
                }
              } catch (error) {
                console.error(error);
              }
            }
          });
          
          // 等待所有请求完成后，一次性更新数据
          await Promise.all(promises);
          
          // 一次性更新榜单数据，避免多次触发watch
          this.leaderBoardsList = newLeaderBoardsList;
          
          // 数据更新完成后，恢复用户的滑动位置
          this.$nextTick(() => {
            if (this.$refs.mySwiper && this.$refs.mySwiper.swiper) {
              const swiperInstance = this.$refs.mySwiper.swiper;
              // 等待DOM更新完成后再恢复位置
              setTimeout(() => {
                swiperInstance.update();
                swiperInstance.updateSlides();
                // 如果开启了循环，重新初始化循环
                if (this.swiperOption.loop) {
                  swiperInstance.loopDestroy();
                  swiperInstance.loopCreate();
                }
                // 恢复用户之前的滑动位置
                if (currentUserIndex < newLeaderBoardsList.length) {
                  swiperInstance.slideToLoop(currentUserIndex, 0); // 0ms 立即切换，不要动画
                }
              }, 100);
            }
          });
        } else {
          this.$toast(res.info);
        }
      } catch (error) {
        this.$toast('加载榜单数据失败，请重试');
      } finally {
        this.isDataLoading = false;
      }
    },

    // 头部标签点击事件
    onHeaderItemClick(index) {
      if (this.$refs.mySwiper && this.$refs.mySwiper.swiper) {
        // 确保index在有效范围内
        const validIndex = Math.min(index, this.leaderBoardsList.length - 1);
        this.$refs.mySwiper.swiper.slideToLoop(validIndex, 300);
      }
    },

    // 获取头部标签项的样式
    getHeaderItemStyle(index) {
      return {
        marginRight: "15px",
        cursor: "pointer",
        transition: "all 0.3s ease-in-out",
      };
    },

    // 获取头部图标的样式
    getHeaderIconStyle(index) {
      const activationLevel = this.headerActivationLevels[index];
      const width = activationLevel > 0 ? 14 + (24 - 14) * activationLevel : 0; // 非激活时宽度为0
      const opacity = activationLevel;
      const marginRight = 4 * activationLevel;

      return {
        width: `${width}px`,
        opacity: opacity,
        marginRight: `${marginRight}px`,
        transition: "all 0.25s ease-in-out",
        display: "inline-block",
        overflow: "hidden", // 确保宽度为0时完全隐藏
      };
    },

    // 获取头部标题的样式
    getHeaderTitleStyle(index) {
      const activationLevel = this.headerActivationLevels[index];
      const fontSize = 14 + (18 - 14) * activationLevel; // 从14px到18px
      const lineHeight = 17 + (21 - 17) * activationLevel; // 从17px到21px

      // 颜色插值：从#666到#333
      const r = Math.round(102 + (51 - 102) * activationLevel);
      const g = Math.round(102 + (51 - 102) * activationLevel);
      const b = Math.round(102 + (51 - 102) * activationLevel);
      const color = `rgb(${r}, ${g}, ${b})`;

      return {
        fontSize: `${fontSize}px`,
        color: color,
        lineHeight: `${lineHeight}px`,
        transition: "all 0.25s ease-in-out", // 缩短过渡时间
        fontWeight: 500,
      };
    },

    // 更新头部激活程度
    updateHeaderActivation(currentIndex, nextIndex, progress) {
      this.headerActivationLevels = this.headerActivationLevels.map((_, index) => {
        if (index === currentIndex) {
          // 当前slide逐渐变小
          return 1 - progress;
        } else if (index === nextIndex) {
          // 下一个slide逐渐变大
          return progress;
        } else {
          // 其他保持最小状态
          return 0;
        }
      });
    },

    // 重置激活状态
    resetActivationLevels(activeIndex) {
      this.headerActivationLevels = this.headerActivationLevels.map((_, index) => {
        return index === activeIndex ? 1 : 0;
      });
    },
  },
};
</script>
<style scoped lang="scss">
.leader-boards-page {
  padding-left: 15px; // 左右padding一致
  margin-bottom: 20px;
  .leader-boards-header {
    margin-bottom: 15px;
    .leader-boards-header-title {
      .leader-boards-header-item {
        margin-right: 15px;
        cursor: pointer;
        color: #666;
      }
    }
  }
  :deep(.leader-boards-carousel) {
    width: 100%;
    height: 272px;
    .custom-swiper {
      padding: 0;
      margin: 0;
      width: 100%;
      overflow: visible; // 允许右侧内容可见
      .swiper-pagination {
        bottom: 10px !important;
        .swiper-pagination-bullet {
          background: #ccc;
          opacity: 0.5;
          &.swiper-pagination-bullet-active {
            background: #007aff;
            opacity: 1;
          }
        }
      }
      .leader-boards-slide {
        background-color: #fff;
        padding: 6px 6px 0 6px;
        box-sizing: border-box;
        border-radius: 6px;
        height: 272px;
        .leader-boards-card {
          .leader-boards-item {
            height: 100%;
            &:last-child {
              .text-leader-wrap {
                margin-bottom: 6px;
              }
            }
            &:nth-child(3) {
              .graphic-leader-wrap {
                margin-bottom: 8px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
