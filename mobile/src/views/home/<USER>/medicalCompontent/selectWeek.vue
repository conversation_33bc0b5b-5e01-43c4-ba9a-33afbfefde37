<!-- 本周精选 -->
<template>
    <div class='selectWeek'>
        <common-title title="本周精选" :isShowMore="false"  :sourceData="liveSwiperData"></common-title>
        <!-- <live-swiper :liveSwiperData="liveSwiperData"></live-swiper> -->
        <liveListStyle :list="liveSwiperData" :listStyle="param.listStyle" style="padding: 0px 15px" ></liveListStyle>
    </div>
</template>

<script>
import CommonTitle from './commonTitle';
import LiveSwiper from './liveSwiper';
import liveListStyle from './liveListStyle/liveListStyle';
import { getConferenceFront } from '@/api/medicine/homePage';
export default {
    name: 'selectWeek',
    props: ['configuration'],
    data () {
        return {
            liveSwiperData:[],
            param: {},
        };
    },

    created () {
        this.getConferenceFront();
    },

    components: {
        CommonTitle,
        LiveSwiper,
        liveListStyle,
    },

    computed: {},

    mounted () {

    },

    watch: {
        configuration: {
            handler (val) {
              this.param = JSON.parse(val.param)
                this.getConferenceFront()
            },
            deep: true,
            immediate: true
        }
    },

    methods: {
        getConferenceFront () {
            let params = this.isPreviewEnviroment?this.configuration.previewApiUrl:this.configuration.apiUrl;
            getConferenceFront(params).then((res) => {
                console.log('本周精选', res);
                if(res.code == this.$successCode){
                    this.liveSwiperData = res.data.list || [];
                }
            })
        }
    }
}

</script>
<style  scoped lang="scss">
.selectWeek {
	margin-bottom: 20px;
}
</style>