<template>
  <div v-show="swiperImageItems.length" class="custom-swiper" :class="swiperClass">
    <cube-slide
      :auto-play="!!(!isPlaying && interval)"
      :interval="interval"
      v-if="swiperImageItems.length"
      :data="swiperImageItems"
      @change="changePage"
      class="swipers"
      allowVertical
      ref="slideImage"
    >
      <cube-slide-item class="swiperItem-content" :key="indexs" @click.native="clickImage(item, indexs)" v-for="(item, indexs) in swiperImageItems" :style="`background-image: url(${item.imgUrl})`">
        <!-- <a :href="item.targetUrl"> -->
        <!-- <img class="swiperItem" :src="item.imgUrl"> -->
        <!-- 视频样式 -->
        <img v-if="!videoShow && item.fileType == 1" :src="require('@/assets/img/videoPlay.png')" class="video-icon" />
        <img src="" alt="" style="display: none" v-else />
        <div id="video" v-show="videoShow" v-if="item.fileType == 1"></div>
        <!-- </a> -->
      </cube-slide-item>
      <template slot="dots" slot-scope="props">
        <span :class="{ active: props.current === index, myDot: configuration.id }" v-for="(item, index) in props.dots">{{ index + 1 }}</span>
      </template>
    </cube-slide>
  </div>
</template>
<script>
import { getBannerList, getAdvertList } from "@/api/medicine/homePage";
import DPlayer from "dplayer";
export default {
  components: {},
  props: ["configuration"],
  data() {
    return {
      swiperImageItems: [],
      dplayer: null,
      videoShow: false,
      isPlaying: false, // 是否正在播放视频,  播放视频时禁止自动轮播
      interval: 2000,
      // 展示模式
      config: {
        displayNum: 1,
        listStyle: "rowOne",
      },
      pcConfig: {}, //pc端配置
    };
  },
  computed: {
    swiperClass() {
      return `swiper-${this.config.mode}-${this.config.mode == 1 ? this.pcConfig.type : this.config.picStyle}`;
    },
  },
  watch: {
    $route: {
      handler(val) {
        setTimeout(() => {
          // this.getInfo()
        }, 100);
        if (this.dplayer) {
          this.dplayer = null;
          this.videoShow = false;
          this.isPlaying = false;
        }
      },
      deep: true,
      immediate: true,
    },

    configuration: {
      handler(val) {
        this.getAdvertList();
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    
  },
  updated() {},
  methods: {
    getAdvertList() {
      let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      getAdvertList(params).then((res) => {
        if (res.code == this.$successCode) {
          this.swiperImageItems = res.data;
          this.config = JSON.parse(this.configuration.param);
          this.pcConfig = JSON.parse(this.configuration.pcParam); //pc端配置
          this.interval = this.config.mode == 1 ? this.pcConfig.param * 1000 : this.config.param * 1000;
        } else {
          this.$message.error(res.info);
        }
      });
    },
    clickImage(item, indexs) {
      if (item.fileType == 1) {
        // 是视频   目前只能添加一个视频
        if (!this.dplayer) {
          this.initDPlayer(item);
        }
        return;
      }
      if (item.targetUrl != "") {
        window.location.href = item.targetUrl;
      }
      return;
    },
    changePage(index) {},
    // 初始化 视频
    initDPlayer(item) {
      this.dplayer = new DPlayer({
        container: document.getElementById("video"),
        theme: "#FADFA3",
        video: {
          url: item.videoUrl,
          pic: item.imgUrl,
        },
      });
      this.$nextTick(() => {
        this.initVideo();
      });
    },
    initVideo() {
      let el = document.querySelector("#video");
      let player = el.querySelector("video");
      player.setAttribute("webkit-playsinline", "");
      player.setAttribute("playsinline", "");
      player.setAttribute("x5-playsinline", "");
      player.setAttribute("x-webkit-airplay", "allow");
      player.setAttribute("muted", "muted");
      this.videoShow = true;
      this.dplayer.play();
      this.dplayer.on("play", () => {
        this.isPlaying = true;
      });
      this.dplayer.on("pause", () => {
        this.isPlaying = false;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.custom-swiper {
  padding: 0 15px;
  border-radius: 4px;
  height: 140px;
  margin-bottom: 15px;

  &.swiper-1-1 {
    height: 35px;
  }

  &.swiper-1-2 {
    height: 132px;
  }

  &.swiper-2-1 {
    height: 84px;
  }

  &.swiper-2-2 {
    height: 200px;
  }
}

.swipers {
  height: 100%;
}

.swiperItem-content {
  height: 100%;
  position: relative;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 2px;

  a {
    border: 0 !important;
  }

  img {
    border: 0 !important;
  }

  .video-icon {
    position: absolute;
    top: 40px;
    left: calc(50% - 30px);
    width: 60px;
    height: 60px;
  }

  #video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 5px;
  }
}

.swiperItem {
  height: 100%;
  width: 100%;
  border-radius: 5px;
}
</style>

<style>
.swiperImageZly .cube-slide-dots {
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: row;
  /* align-items: right; */
  justify-content: center;
  width: 100%;
  bottom: 6px !important;
  padding-right: 15px;
}

/* .swiperImage.swiperImageZly .cube-slide-dots {
    bottom: 10px !important;
    justify-content: start;
		padding-left: 15px;
	
} */
.swiperImageZly .cube-slide-dots .myDot.active {
  height: 3px;
  width: 11px;
  background-size: 100% 100%;
  margin: 0px 4px;
  background-color: #fff;
  border-radius: 2.5px;
}

.swiperImageZly .cube-slide-dots .myDot {
  height: 3px;
  width: 4px;
  background-size: 100% 100%;
  margin: 0px 4px;
  background: #ffffff;
  border-radius: 2.5px;
}

/* .swiperImage.swiperImageZly .cube-slide-dots .myDot {
	height: 3px;
	width: 8px;
	background-color: rgba(255, 255, 255, 0.7);
}	
.swiperImage.swiperImageZly .cube-slide-dots .myDot.active {
	background-color: #FE1B1B;
} */
</style>

<style lang="scss">
.swiperImageZly {
  #video {
    &.dplayer-playing {
      .dplayer-mobile-play {
        svg {
          width: 24px;
          height: 24px;
          margin: 0;
        }
      }
    }

    &.dplayer-paused {
    }

    .dplayer-icons-right {
      display: none;
    }

    .dplayer-mobile-play {
      border-radius: 50%;
      border: 2px solid #fff;
      opacity: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        width: 36px;
        height: 36px;
        margin-left: 4px;
      }
    }

    .dplayer-controller {
      padding: 0;

      .dplayer-bar-wrap {
        bottom: 11px;
        left: 53px;
        width: 240px;
        padding: 0;

        .dplayer-played {
          background: #1890ff !important;
        }
      }

      .dplayer-icons-left {
        width: 100%;

        .dplayer-time {
          color: rgba(0, 0, 0, 0);
        }

        .dplayer-ptime {
          position: absolute;
          left: 10px;
          bottom: 5px;
          line-height: 16px;
          font-size: 12px;
          color: #fff;
        }

        .dplayer-dtime {
          position: absolute;
          right: 10px;
          bottom: 5px;
          line-height: 16px;
          font-size: 12px;
          color: #fff;
        }
      }
    }
  }
}
</style>
