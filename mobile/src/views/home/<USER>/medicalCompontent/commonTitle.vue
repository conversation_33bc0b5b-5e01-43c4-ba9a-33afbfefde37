<!-- 公共的头部样式 -->
<template>
  <div class="commonTitle flex-left flex-middle" v-show="sourceData.length || isAlwaysShow" :style="headerStyle">
    <div class="common-left unit">
      {{ title }}
    </div>
    <div class="common-right" v-if="isShowMore" @click="link_url">
      <span class="common-tip">{{ tipName }}</span>
      <img style="width: 5px; height: 10px; margin-left: 9px" src="@/assets/img/more_icon.png" alt="" />
    </div>
  </div>
</template>

<script>
export default {
  name: "commonTitle",
  props: {
    title: {
      default: "",
      type: String,
    },
    isShowMore: {
      default: true,
      type: Boolean,
    },
    tipName: {
      default: "更多",
      type: String,
    },
    isEnd: {
      default: false,
      type: Boolean,
    },
    sourceData: {
      default: [],
      type: Array,
    },
    // 头部其他特别的样式
    headerStyle: {
      type: Object,
      default: () => {},
    },
    // 跳转类型
    linkType: {
      default: "medicineSearch",
      type: String,
    },
    // 跳转参数
    linkQuery: {
      default: () => {},
      type: Object,
    },
    // 是够始终显示头部
    isAlwaysShow: {
      default: false,
      type: Boolean,
    },
  },
  data() {
    return {};
  },

  created() {},

  components: {},

  computed: {},

  mounted() {},

  methods: {
    link_url() {
      if (!this.isPreviewEnviroment) {
        const path = this.linkType ? '/' + this.linkType : "/medicineSearch";
        const query = !this.linkType ? { modelType: "live", status: this.isEnd } : {};
        this.$router.push({ path: path, query: { ...query, ...this.linkQuery } });
      }
    },
  },
};
</script>
<style scoped lang="scss">
.commonTitle {
  padding: 0 15px;
  .common-left {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    line-height: 25px;
  }
  .common-right {
    .common-tip {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
    }
  }
}
</style>
