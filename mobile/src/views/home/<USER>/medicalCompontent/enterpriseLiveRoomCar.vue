<template>
  <!-- 企业直播间 -->
  <div class="enterprise-live-room-nav" v-if="infoData.length">
    <div class="heavyLive-title-row">
      <!-- <div class="ent-live-title">{{configuration.name}}</div> -->
      <div class="heavyLive-title">
        <img src="../../../../assets/img//livingroom_title.png" width="81" />
      </div>
      <div class="more-icon" @click="showMore">
        <span>更多</span>
        <img style="width: 5px; height: 10px" src="@/assets/img/right1.png" alt="" />
      </div>
    </div>

    <div class="ent-live-swiper-nav" :class="{ onePage: infoData.length <= 1 }">
      <cube-slide :data="infoData" :interval="3000">
        <cube-slide-item v-for="(page, index) in infoData" :key="index">
          <div class="ent-live-item" v-for="item in page" :key="item.id" @click="toEnterpriseLiveRoom(item.id)">
            <div class="ent-live-item-img"><img :src="item.logo" /></div>
            <div v-if="configurationParam && configurationParam.showNameFlag == 1" class="ent-live-item-name">{{ item.name }}</div>
          </div>
        </cube-slide-item>
      </cube-slide>
    </div>
  </div>
</template>

<script>
import { getConferenceFront } from "@/api/medicine/homePage";
export default {
  props: ["configuration"],
  data() {
    return {
      infoData: [],
      configurationParam: "",
    };
  },
  methods: {
    // 获取直播间列表
    getConferenceFront() {
      let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      getConferenceFront(params).then((res) => {
        if (res.code == this.$successCode) {
          this.infoData = [];
          if (res.data.length) {
            for (let i = 0; i < res.data.length; i = i + 9) {
              this.infoData.push(res.data.slice(i, i + 9));
            }
          }
        } else {
          this.$message.error(res.info);
        }
      });
    },
    // 跳转企业直播间
    toEnterpriseLiveRoom(id) {
      //this.$router.push({ name: "liveRoom", params: { id } });
      location.assign(`${window.location.origin}${window.location.pathname}#/liveRoom/${id}?platformId=${this.$route.query.platformId}`);
    },
    showMore() {
      // this.$router.push({ name: 'liveRoomList' })
      location.assign(`${window.location.origin}${window.location.pathname}#/liveRoomList`);
    },
  },
  watch: {
    configuration: {
      handler(val) {
        this.getConferenceFront();
        this.configurationParam = this.configuration.param ? JSON.parse(this.configuration.param) : "";
      },
      deep: true,
      immediate: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.enterprise-live-room-nav {
  margin: 0 15px 30px;
  .heavyLive-title-row {
    margin-bottom: -18px;
    padding-right: 10px;
  }
  .ent-live-title {
    color: #333;
    font-size: 18px;
    line-height: 24px;
    font-weight: 500;
  }
  .ent-live-swiper-nav {
    margin-top: 13px;
    background: #fff;
    border-radius: 0;
    padding: 0 10px;
    .cube-slide {
      padding: 0;
      .cube-slide-item {
        display: flex;
        flex-wrap: wrap;
        .ent-live-item {
          margin-right: 15px;
          margin-bottom: 15px;
          width: 98px;
          border-radius: 4px;
          overflow: hidden;
          backdrop-filter: blur(8px);
          border: 1px solid rgba(238, 238, 238, 0.5);
          &:nth-child(3n) {
            margin-right: 0;
          }
          .ent-live-item-img {
            img {
              width: 98px;
              height: 40px;
              vertical-align: middle;
            }
          }
          .ent-live-item-name {
            padding-left: 10px;
            padding-right: 9px;
            line-height: 26px;
            background: rgba(90, 46, 132, 0.05);
            font-size: 13px;
            color: #333;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: 400;
          }
        }
      }
      .cube-slide-dots {
        bottom: 11px !important;
        span {
          width: 4px;
          height: 3px;
          background: rgba(90, 46, 132, 0.2);
          margin: 0 2px;
          &.active {
            width: 11px;
            background: #5a2e84;
          }
        }
      }
    }
    &.onePage {
      .cube-slide {
        padding-bottom: 5px;
      }
      .cube-slide-dots {
        display: none;
      }
    }
  }
}
.heavyLive-title-row {
  height: 57px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: -5px;
  background: url("../../../../assets/img/liveroom_bg.png");
  background-size: cover;
  padding-left: 10px;
}
.heavyLive-title {
  color: rgba(51, 51, 51, 1);
  line-height: 25px;
  span {
    font-size: 18px;
    font-weight: 500;
  }
}
.more-icon {
  display: flex;
  align-items: center;
  span {
    float: right;
    color: #666666;
    font-size: 14px;
  }
  img {
    margin-left: 5px;
    height: 16px;
  }
}
</style>
