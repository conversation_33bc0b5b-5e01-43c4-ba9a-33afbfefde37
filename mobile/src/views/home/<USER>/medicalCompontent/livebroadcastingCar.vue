<template>
	<div class="broadcasting" v-if="infoDataLimited.length">
		<div class="broadcasting-title">{{configuration.name}}</div>
		<div class="broadcasting-swiper-nav">
			<cube-slide
				:data="infoDataLimited"
				:interval="3000"
				:showDots="infoDataLimited.length>1"
				:auto-play="true" @change="changePage" class="swipers" ref="slide" allowVertical>
				<cube-slide-item :key="indexs" v-for="(item, indexs) in infoDataLimited">
					<div class="livebroadcasting" @click="clickHandler(item)" :style="{'background-image': 'url(' + item.bannerImg + ')'}">
						<div class="icon_live">
							<div class="liveing">
								<img src="@/assets/img/liveIng.gif" />
								<span>直播中</span>
							</div>
						</div>
						<div class="live-title">{{item.activityName || item.name}}</div>
					</div>
				</cube-slide-item>
				<template slot="dots" slot-scope="props">
					<span
					:class="{active: props.current === index,myDot:configuration.id}" v-for="(item, index) in props.dots">{{index + 1}}</span>
				</template>
			</cube-slide>
		</div>
	</div>
</template>

<script>

import { getLiveInProgressList } from '@/api/medicine/homePage'
export default {
    components: {
    },
    props: ['configuration'],
    data () {
        return {
            infoDataLimited: [],
			currentIndex: 0,
			configurationParam: {},
        };
    },
    computed: {
    },
    watch: {
			configuration: {
					handler (val) {
						this.getLiveInProgressList()
	          			this.configurationParam = this.configuration.param?JSON.parse(this.configuration.param):{};
					},
					deep: true,
					immediate: true
			}
    },
    mounted () {
    },
    methods: {
			getLiveInProgressList() {
				let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
				getLiveInProgressList(params).then(res=>{
						if(res.code == this.$successCode){
							this.infoDataLimited = res.data
						}else{
							this.$message.error(res.info);
						}
				})
			},
			changePage (index) {
					this.currentIndex = index
			},
			clickHandler(item){
						// if (item.itemType=="activity") {
						// 	item.id = item.activityId
						// 	this.naviToDetails(this.$route.query.platformId, item.itemType, item)
						// } else {
							this.naviToDetails(this.$route.query.platformId, item.itemType, item)
						// }
			},
    }
}
</script>

<style lang='scss' scoped>
	.broadcasting{
		margin: 0 15px 30px;
		background: #FFFFFF;
		border-radius: 4px;
		padding: 12px 10px;
	}
	.broadcasting-title {
		color: #333;
		font-size: 18px;
		line-height: 24px;
		font-weight: 500;
	  }
	  .broadcasting-swiper-nav{
		margin-top: 13px;
		height: 210px;
	  }
	.livebroadcasting{
		background-size: cover;
		height: 195px;
		color: #333333;
		border-radius: 4px;
		.ing_live{
			font-size: 16px;
			/* font-weight: 500; */
			font-weight: bold;
			line-height: 22px;
		}
		.icon_live{
			height: 32px;
			display: flex;
			justify-content: space-between;
			padding-right: 10px;
			.liveing{
				width: 62px;
				height: 24px;
				background: #F02617;
				border-radius: 4px 0px 2px 0px;
				color: #fff;
				line-height: 24px;
				padding-left: 0px;
				font-size: 12px;
				display: flex;
				img{
					height: 22px;
					/* margin-right: 3px; */
					width: auto;
				}
				>span{
					margin-left: -2px;
				}
			}
			.signUpNow{
				width: 109px;
				height: 32px;
				line-height: 32px;
				font-size: 14px;
				padding-left: 15px;
				border-radius: 16px;
				background-color: #1890FF;
				color: #fff;
				.circular{
					width: 32px;
					height: 32px;
					border-radius: 50%;
					background-color: rgba(255, 255, 255, 0.3);
					float: right;
					img{
						width: 16px;
						margin: 10px 8px;
					}
				}
			}
		}
		.live-title{
			color: #fff;
			font-size: 14px;
			padding: 0 12px;
			margin-top: 135px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
</style>
<style lang='scss'>
	.livebroadcasting{
		.item-body{
			background-color: transparent;
			box-shadow:none;
			padding: 0;
		}
		.item-right-container{
			margin-right: 5px;
			width: 196px;
		}
		.infoRecommend-detail{
			white-space: initial;
		}
	}
	.broadcasting{
		.cube-slide-dots{
			bottom: 0px !important;
		}
		.cube-slide-dots .myDot.active {
		    height: 3px;
		    width: 11px;
		    background-size: 100% 100%;
		    margin: 0px 2px;
		    background-color: #5A2E84;
				border-radius: 2.5px;
		}
		.cube-slide-dots .myDot {
		    height: 3px;
		    width: 4px;
		    background-size: 100% 100%;
		    margin: 0px 2px;
		    background: rgba(90, 46, 132, 0.5);
				border-radius: 2.5px;
		}
	}
</style>
