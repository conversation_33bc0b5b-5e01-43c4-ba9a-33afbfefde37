/** * 产品模块 */
<template>
  <!-- 产品模块 -->
  <div
    class="product-nav"
    v-if="productData.length"
    :style="{ background: configurationParam.listStyle == 'rowTwo' ? '' : '#fff', padding: configurationParam.listStyle != 'rowTwo' ? '10px' : '0px' }"
  >
    <div class="heavyLive-title-row">
      <div class="heavyLive-title">
        <span>{{ configuration.name }}</span>
      </div>
      <div class="more-icon" @click="showMore" v-show="configuration.layoutInfo.fixed">
        <span>更多</span>
        <img style="width: 5px; height: 10px" src="@/assets/img/right1.png" alt="" />
      </div>
    </div>
    <div class="product-list flex-wrap flex-left">
      <normal-product v-if="configurationParam && configurationParam.listStyle == 'rowTwo'" v-for="(item, index) in productData" :key="index" :item="item" />
      <medium-product v-if="configurationParam && configurationParam.listStyle == 'rowOne'" v-for="(item, index) in productData" :key="index" :item="item" />
      <large-product v-if="configurationParam && configurationParam.listStyle == 'bigImg'" v-for="(item, index) in productData" :key="index" :item="item" />
      <carousel-product v-if="configurationParam && configurationParam.listStyle == 'carousel'" :sourceData="productData" />
    </div>
  </div>
</template>

<script>
import { getConferenceFront } from "@/api/medicine/homePage";
import NormalProduct from "@/components/medicine/pruduct/normalProduct.vue";
import MediumProduct from "@/components/medicine/pruduct/mediumProduct.vue";
import LargeProduct from "@/components/medicine/pruduct/largeProduct.vue";
import CarouselProduct from "@/components/medicine/pruduct/carouselProduct.vue";
export default {
  name: "exhibitorProduct",
  props: ["configuration"],
  data() {
    return {
      productData: [],
      configurationParam: "",
    };
  },
  components: {
    NormalProduct,
    MediumProduct,
    LargeProduct,
    CarouselProduct,
  },
  methods: {
    // 获取产品间列表
    getConferenceFront() {
      let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      getConferenceFront(params).then((res) => {
        if (res.code == this.$successCode) {
          console.log("产品列表", res, this.configurationParam);
          this.productData = res.data.slice(0, this.configurationParam.displayNum) || [];
        } else {
          this.$message.error(res.info);
        }
      });
    },
    showMore() {
      this.$router.push({ name: "targetPage", params: { pageId: 17, subType: "0", typeId: "0" } });
    },
  },
  watch: {
    configuration: {
      handler(val) {
        this.getConferenceFront();
        this.configurationParam = this.configuration.param ? JSON.parse(this.configuration.param) : "";
      },
      deep: true,
      immediate: true,
    },
  },
};
</script>

<style lang="scss" scope>
.product-nav {
  margin: 0 15px 15px;
  .ent-live-title {
    color: #333;
    font-size: 18px;
    line-height: 24px;
    font-weight: 500;
  }
}
.heavyLive-title-row {
  height: 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: -5px;
}
.heavyLive-title {
  color: rgba(51, 51, 51, 1);
  line-height: 25px;
  span {
    font-size: 18px;
    font-weight: 500;
  }
}
.more-icon {
  display: flex;
  align-items: center;
  span {
    float: right;
    color: #666666;
    font-size: 14px;
  }
  img {
    margin-left: 5px;
    height: 16px;
  }
}
.product-list {
  margin-top: 12px;
  justify-content: space-between;
  gap: 10px;
}
</style>
