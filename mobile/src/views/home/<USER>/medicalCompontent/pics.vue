<template>
	<div class="recommend" v-if="list.length">
		<div class="title-row">
			<div class="recommend-title">
				<span>{{ configuration.name || configuration.subMenuName }}</span>
			</div>
			<!-- <div class="change-data" @click="changeData()" v-if="!(lastLayoutId==configuration[layoutIdField]&&infiniteLoadLast==1)">
					<span>更多</span>
					<img style='width:5px;height:10px;' src="@/assets/img/right1.png" alt="">
				</div> -->
		</div>
		<!--一行两个-->
		<div class="rowTwo" v-if="configurationParam.listStyle == 'rowTwo'">
			<div class="item-container" v-for="item of list" :key="item.id">
				<pic :itemInfo="item"></pic>
			</div>
		</div>
		<!--大图展示-->
		<div  class="bigImg" v-if="configurationParam.listStyle == 'bigImg'">
			<div class="item-container" v-for="item of list" :key="item.id">
				<pic :itemInfo="item"></pic>
			</div>
		</div>
		<!--轮播展示-->
		<div class="carousel" v-if="configurationParam.listStyle == 'carousel'">
			<cube-slide
				v-if="list.length"
				:data="list"
				:stopPropagation="true"
				:interval="3000"
				:auto-play="true" ref="slide" allowVertical>
				<cube-slide-item :key="item.id" v-for="(item,index) in list">
					<pic :itemInfo="item"></pic>
				</cube-slide-item>
				<template slot="dots" slot-scope="props">
					<span :class="{active: props.current === index,myDot:configuration.id}" v-for="(item, index) in props.dots">{{index + 1}}</span>
				</template>
		</cube-slide>
		</div>
	</div>
</template>

<script>
import pic from './pic';
import { getHomePageItemList } from '@/api/medicine/homePage'
export default {
	props: ['configuration', 'lastLayoutId', 'infiniteLoadLast', 'layoutIdField'],
	data() {
		return {
			configurationParam: {},
			list: [],
		};
	},

	created() { },

	components: {
		pic
	},
	watch: {
		configuration: {
			handler(val) {
				this.getHomePageItemList()
				this.configurationParam = this.configuration.param ? JSON.parse(this.configuration.param) : {};
			},
			deep: true,
			immediate: true
		}
	},
	computed: {
	},

	mounted() {
	},

	methods: {
		getHomePageItemList() {
			let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
			getHomePageItemList(params).then(res => {
				if (res.code == this.$successCode) {
					this.list = res.data
				} else {
					this.$message.error(res.info);
				}
			})
		},
		changeData() {
			this.$router.push({ name: 'medicineSearch', query: { type: 'custom', layoutId: this.configuration.layoutId } })
		},
	}
}

</script>
<style  scoped  lang='scss'>
.recommend {
	margin-bottom: 20px;
	padding: 0px 15px;

	.title-row {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding-top: 10px;
		span {
			font-size: 18px;
			font-weight: 500;
			color: rgba(51, 51, 51, 1);
			line-height: 25px;
		}
	}

	.recommend-title {
		display: flex;
		align-items: center;
		margin: 10px 0;

		img {
			height: 24px;
			width: 24px;
		}

		span {
			font-size: 18px;
			margin-left: 6px;
			font-weight: 500;
			color: rgba(51, 51, 51, 1);
			line-height: 25px;
		}
	}

	.change-data {
		display: flex;
		align-items: center;

		span {
			font-size: 14px;
			font-weight: 400;
			color: #666666;
			line-height: 17px;
			margin-right: 5px;
		}

		div {
			height: 16px;

			svg {
				height: 16px;
			}
		}
	}
	.rowTwo{
		display: flex;
		flex-flow: row;
		flex-wrap: wrap;
		.item-container{
			width: 167px;
			height: 100px;
			margin-right: 11px;
			margin-bottom: 15px;
			&:nth-child(2n){
				margin-right: 0;
			}
		}
	}
	.bigImg{
		.item-container{
			width: 100%;
			height: 207px;
			margin-bottom: 15px;
		}
	}
	.carousel{
		.cube-slide{
			height: 208px;
		}
	}
}</style>
<style lang="scss">
.recommend{
	.carousel{
		.cube-slide-dots {
			padding: 0;
			margin: 0;
			display: flex;
			flex-direction: row;
			/* align-items: right; */
			justify-content: center;
			width: 100%;
			bottom: 6px !important;
			padding-right: 15px;
			.myDot {
				height: 3px;
				width: 4px;
				background-size: 100% 100%;
				margin: 0px 4px;
				background: #ffffff;
				border-radius: 2.5px;
			}
			.myDot.active {
				height: 3px;
				width: 11px;
				background-size: 100% 100%;
				margin: 0px 4px;
				background-color: #fff;
				border-radius: 2.5px;
			}
		}
	}
	
}
</style>
