<template>
    <div class="header_wrap ">
        <div class="header_input" @click="showSearch">
            <img src="@/assets/img/search_icon.png" alt="" class="iconSearch">
            <span class="text">请输入关键字</span>
        </div>
        <div class="questioner-nav" @click="questionnairePage" v-if="sliderInfoStatus.questionnaire">
            <img :src="require('@/assets/img/questioner.png')">
        </div>
        <!-- <div class="share_wx" @click="showHidden = !showHidden" v-if="configuration.extraInfo.isShowIcon" >
            <img src="@/assets/img/weixin.png" alt="" class="weixin">
				</div>
        <officialCode style="bottom:45px;" :visiable.sync="showHidden"></officialCode> -->
    </div>
</template>

<script>
// import officialCode from '@/components/medicine/officialCode';
export default {
    props: ['configuration'],
    data () {
        return {
            showHidden: false
        }
    },
    watch: {
        showHidden (val) {
            this.$emit('changeScroll', val)
        }
    },
    components: {
        // officialCode
    },
    computed: {
    },
    methods: {
        showSearch () {
            if (!this.isPreviewEnviroment) {
                this.$router.push({ name: 'medicineSearch', query: { modelType: this.configuration.extraInfo.model } })
            }
        },
        // 跳转问卷
        questionnairePage(){
             window.open('https://www.wjx.cn/vm/ezyPTVz.aspx');
           // this.$router.push({name:'questionnaire'});
        }
    },
    created () {

    }
}
</script>

<style scoped lang="scss">
$color: #004da1;
.header_wrap {
    padding: 0 15px;
    width: 100%;
    height: 56px;
    display: flex;
    justify-content: center;
    align-items: center;
    // background-color: #fff;
    .share_wx {
        width: 25px;
        height: 25px;
        // border: 2px solid #08ba06;
        border-radius: 50%;
        position: relative;
        margin-left: 12px;
        z-index: 0;
        box-sizing: border-box;
        .weixin {
            width: 24px;
            height: 24px;
            position: absolute;
            top: -1px;
            left: -1px;
            z-index: 1;
        }
    }
    .questioner-nav {
        img {
            width: 42px;
            height: 35px;
            margin-left: 11px;
        }
    }
}
.header_input {
    height: 36px;
    background: #f3f3f3;
    border-radius: 4px;
    display: flex;
    padding-left: 10px;
    justify-content: left;
    align-items: center;
    flex: 8;
}
.iconSearch {
    width: 16px;
    height: 16px;
    margin-right: 7px;
}
.text {
    font-size: 16px;
    color: #999;
    font-weight: 400;
}
.head-right svg {
    margin-left: 15px;
    width: 24px;
}
</style>