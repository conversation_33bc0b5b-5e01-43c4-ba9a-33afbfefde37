<template>
  <div class="classify-list-container" :class="{ 'classify-list-container-new': $route.query.platformId == 5 }">
    <div v-if="configuration.extraInfo && configuration.extraInfo.hasIcon">
      <cube-slide :auto-play="false" :data="listHasIcon">
        <cube-slide-item v-for="(iconItem, iconIndex) in listHasIcon" :key="iconIndex">
          <ul :class="['hasIcon', configuration.extraInfo.model]">
            <li v-for="(item, index) in iconItem" :key="item.id" @click="toList(item.codeValueId)">
              <div v-if="configuration.extraInfo.hasIcon" class="icon">
                <img :src="item.codeValueDescEn" />
              </div>
              <div class="title">{{ item.codeValueDesc == "实验室仪器设备" ? "仪器设备" : item.codeValueDesc }}</div>
            </li>
          </ul>
        </cube-slide-item>
        <template slot="dots" slot-scope="props">
          <span class="my-dot" :class="{ active: props.current === index }" v-for="(item, index) in props.dots" :key="index"></span>
        </template>
      </cube-slide>
    </div>
    <div v-else>
      <ul :class="['noIcon', configuration.extraInfo && configuration.extraInfo.model]">
        <li v-for="item in list" :key="item.id" @click="toList(item.codeValueId)">
          <div class="title">{{ item.codeValueDesc }}</div>
        </li>
      </ul>
    </div>

    <!-- <ul :class="[configuration.extraInfo.hasIcon?'hasIcon':'noIcon', configuration.extraInfo.model]">
            <li v-for="item in list" :key="item.id" @click="toList(item.codeValueId)">
                <div v-if="configuration.extraInfo.hasIcon" class="icon">
                   <img :src="item.codeValueDescEn" />
                </div>
                <div class="title">{{item.codeValueDesc}}</div>
            </li>
        </ul> -->
  </div>
</template>

<script>
import { getClassifyLive } from "@/api/medicine/homePage";
export default {
  props: ["configuration"],
  data() {
    return {
      list: [],
      listHasIcon: [],
    };
  },
  mounted() {
    this.getClassifyLive();
  },
  watch: {
    configuration() {
      this.getClassifyLive();
    },
  },
  methods: {
    // 获取分类列表
    getClassifyLive() {
      let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      getClassifyLive(params).then((res) => {
        if (res.code == this.$successCode) {
          this.list = res.data || [];
          this.listHasIcon = [];
          //汽车时取前9个 + 更多
          if (this.$route.query.platformId == 5) {
            let list = this.list.slice(0, 9);
            list.push({
              codeValueDesc: "更多",
              codeValueDescEn: require("@/assets/img/live_more.png"),
              codeValueId: "",
            });
            this.listHasIcon = [list];
          } else {
            for (let i = 0; i < this.list.length; i += 10) {
              this.listHasIcon.push(this.list.slice(i, i + 10));
            }
          }
        }
      });
    },
    toList(codeValueId) {
      let query = {};
      query[this.configuration.extraInfo.type] = codeValueId;
      location.assign(
        `${window.location.origin}${window.location.pathname}#/medicineSearch?modelType=${this.configuration.extraInfo.model}&channel=${query.channel}&platformId=${this.$route.query.platformId}&domainType=1`
      );
    },
  },
};
</script>

<style lang="scss" scope>
.classify-list-container {
  background: #ffffff;
  .hasIcon {
    padding-bottom: 20px;
    padding-top: 10px;
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    &.activity {
      li {
        width: 66px;
        text-align: center;
        display: inline-block;
        & + li {
          margin-left: 10px;
        }
      }
      .icon {
        display: inline-block;
        width: 36px;
        height: 36px;
        padding: 6px;
        background: #eff4fe;
        border-radius: 18px;
        svg {
          width: 24px;
          height: 24px;
        }
      }
      .title {
        color: #333;
        font-size: 13px;
        // margin-top: 8px;
        line-height: 18px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        white-space: nowrap;
      }
    }

    &.live {
      li {
        text-align: center;
        display: inline-block;
        flex-basis: 20%;
        box-sizing: border-box;
        text-align: center;
        &:nth-of-type(5n) {
          width: 38px;
          clear: both;
        }
      }
      .icon {
        display: inline-block;
        width: 38px;
        height: 38px;
        svg {
          width: 24px;
          height: 24px;
        }
        img {
          width: 38px;
          height: 38px;
        }
      }
      .title {
        color: #333;
        font-size: 13px;
        margin-top: 10px;
        margin-bottom: 15px;
        line-height: 18px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        white-space: nowrap;
      }
    }
  }
  .noIcon {
    padding: 0px 0px 10px 5px;
    text-align: center;
    li {
      display: inline-block;
      min-width: 63px;
      padding: 0 7px;
      height: 24px;
      margin-right: 7px;
      margin-bottom: 10px;
      color: #333;
      font-size: 12px;
      background: #f6f7f7;
      border-radius: 12px;
      text-align: center;
      line-height: 24px;
      padding-right: 8px;
      padding-bottom: 10px;
    }
  }
}
</style>
<style lang="scss">
.classify-list-container {
  .cube-slide-dots > span.active {
    background: rgba(20, 100, 161, 1) !important;
    width: 11px;
    height: 3px;
  }
  .cube-slide-dots > span {
    width: 4px;
    height: 3px;
    background: rgba(20, 100, 161, 0.6);
    margin: 0 4px;
  }
  &.classify-list-container-new {
    .cube-slide-dots {
      display: none;
    }
  }
}
</style>
