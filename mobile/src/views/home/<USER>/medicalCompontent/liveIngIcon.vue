<template>
  <div>
		<div class="liveingDiv" v-if="true">
			<img src="@/assets/img/liveIng.gif" />
			<span>直播中</span>
		</div>
  </div>
</template>

<script>
// import {  } from '@/api/';
export default {
  data () {
    return {
    };
  },

  created () { },

  components: {},

  computed: {
  },

  mounted () { },

  methods: {
  }
}

</script>
<style  scoped  lang='scss'>
.liveingDiv{
	width: 62px;
	height: 24px;
	background: #F02617;
	border-radius: 2px;
	color: #fff;
	line-height: 24px;
	padding-left: 0px;
	font-size: 12px;
	display: flex;
	img{
		height: 22px;
		/* margin-right: 3px; */
		width: auto;
	}
	>span{
		margin-left: -1px;
	}
}
</style>
