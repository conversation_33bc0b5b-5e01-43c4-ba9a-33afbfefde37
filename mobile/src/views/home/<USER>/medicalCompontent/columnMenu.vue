<template>
  <div class="userCenter-column-menu1">
    <ul>
      <li class="flex-middle" v-for="(menu, index) in list" :key="menu.id" @click="toMenuPage(menu.menuKey, menu.children)" v-if="menu.menuKey == 'hezuo' ? (sliderInfoStatus.contact ? true : false) : true">
        <svg class="menu_icon icon svg-icon" aria-hidden="true">
          <use v-bind:xlink:href="'#' + menu.logo"></use>
        </svg>
        <div class="userCenter-menu-name flex-middle unit">
          <span class="userCenter-menu-name-text unit">{{ menu.name }}</span>
          <svg class="icon-right unit-0" aria-hidden="true">
            <use v-bind:xlink:href="'#icon-Right'"></use>
          </svg>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import Axios from "@/plugins/http";
export default {
  name: "columnMenu",
  props: ["configuration"],
  data() {
    return {
      rightNum: {}, // 菜单右侧数据
    };
  },
  computed: {
    list() {
      return JSON.parse(JSON.stringify(this.configuration.children));
    },
  },
  methods: {
    // 跳转菜单 内页
    toMenuPage(name, children) {
      this.$emit("toMenuPage", name, children);
    },
    getRightNum(index, apiUrl, menuParams) {
      let params = {};
      menuParams.forEach((item) => {
        params[item.menuParamKey] = item.menuParamValue;
      });
      Axios()
        .get(apiUrl, { params })
        .then((res) => {
          if (res.code == this.$successCode) {
            this.$set(this.rightNum, index, res.data);
          }
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.userCenter-column-menu1 {
  border-top: 15px solid #f4f5f9;
  padding: 0 15px;
  ul {
    margin: 0;
    border-radius: 4px;
    overflow: hidden;
    li {
      color: #333;
      font-size: 16px;
      line-height: 44px;
      background-color: #fff;
      padding-right: 12px;
      &:last-child {
        .userCenter-menu-name {
          border-bottom: none;
        }
      }
      .menu_icon {
        max-width: 20px;
        max-height: 21px;
        vertical-align: middle;
        margin: 0 16px;
        margin-top: -2px;
      }
      .userCenter-menu-name {
        border-bottom: 1px solid #f5f5f5;
        .userCenter-menu-name-text {
          font-weight: 400;
          font-size: 15px;
          color: #333333;
        }
      }
      .icon-right {
        color: #bababa;
        height: 16px;
        width: 16px;
      }
    }
  }
}
</style>
