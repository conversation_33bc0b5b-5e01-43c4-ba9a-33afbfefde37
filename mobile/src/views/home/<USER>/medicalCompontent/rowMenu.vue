<template>
  <div class="userCenter-row-menu">
    <ul>
      <li v-for="menu in list" :key="menu.id" @click="toMenuPage(menu.menuKey, menu.children)">
        <svg class="menu_icon icon svg-icon" aria-hidden="true">
          <use v-bind:xlink:href="'#' + menu.logo" class="userInfo-svg-icon"></use>
        </svg>
        <br />{{ menu.name }}
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: "rowMenu",
  props: ["configuration"],
  data() {
    return {};
  },
  computed: {
    list() {
      return this.configuration.children;
    },
  },
  mounted() {
    // 这里进行图标的fill
    this.$nextTick(() => {
      if (this.list && this.list.length > 0) {
        this.list.forEach((item) => {
          document.querySelector("#" + item.logo + " path").removeAttribute("fill");
        });
      }
    });
  },
  methods: {
    // 跳转菜单 内页
    toMenuPage(name, children) {
      this.$emit("toMenuPage", name, children);
    },
  },
};
</script>

<style lang="scss" scoped>
.userCenter-row-menu {
  padding: 15px 0 23px;
  margin: 0 15px;
  background-color: #fff;
  height: 90px;
  border-radius: 4px;
  ul {
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: space-around;
    li {
      display: inline-block;
      text-align: center;
      color: #666;
      font-size: 14px;
      width: 100%;
      img,
      .menu_icon {
        width: 36px;
        height: 33px;
        margin-bottom: 6px;
        margin-left: 2px;
      }
      .menu_icon:first-of-type {
        // width: 26px;
        width: 34px;
        height: 32px;
      }
      & + li {
        position: relative;
        &::before {
          position: absolute;
          content: "";
          left: 0;
          top: 0px;
          height: 53px;
          border-left: 1px solid #efefef;
        }
      }
      use.userInfo-svg-icon {
        fill: var(--color-columnBg);
      }
    }
  }
}
</style>
