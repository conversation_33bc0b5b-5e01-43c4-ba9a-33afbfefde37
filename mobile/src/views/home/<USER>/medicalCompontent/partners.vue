<template>
  <!-- 企业直播间 -->
  <div class="partner-nav" v-if="partnerList.length">
    <div class="partner-title">{{configuration.name}}</div>
    <div class="partner-swiper-nav" :class="{onePage: partnerList.length<=1}">
      <cube-slide :data="partnerList" :interval="3000">
        <cube-slide-item v-for="(page, index) in partnerList" :key="index">
          <div class="partner-item" v-for="item in page" :key="item.id">
            <div class="partner-item-img"><img :src="item.imgUrl"></div>
          </div>
        </cube-slide-item>
      </cube-slide>
    </div>
  </div>
</template>

<script>
import { getConferenceFront } from '@/api/medicine/homePage'
export default {
  props: ['configuration'],
  data() {
    return {
      partnerList: [],
      configurationParam: ''
    }
  },
  methods: {
    // 获取直播间列表
    getConferenceFront() {
      let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      getConferenceFront(params).then(res=>{
        if(res.code == this.$successCode){
          this.partnerList = [];
          if (res.data.length) {
            for(let i=0; i<res.data.length;i=i+15) {
              this.partnerList.push(res.data.slice(i, i+15))
            }
          }
        }else{
          this.$message.error(res.info);
        }
      })
    },
    // 跳转企业直播间
    toEnterpriseLiveRoom(id) {
      this.$router.push({name: 'liveRoom', params: {id}})
    }
  },
  watch: {
    configuration: {
      handler (val) {
        this.getConferenceFront()
        this.configurationParam = this.configuration.param?JSON.parse(this.configuration.param):'';
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style lang="scss" scope>
.partner-nav {
  margin: 0 15px 30px;
  .partner-title {
    color: #333;
    font-size: 18px;
    line-height: 24px;
    font-weight: 500;
  }
  .partner-swiper-nav {
    margin-top: 13px;
    border-radius: 6px;
    .cube-slide {
      margin-right: -10px;
      padding-bottom: 13px;
      .cube-slide-item {
        display: flex;
        flex-wrap: wrap;
        .partner-item {
          margin-right: 10px;
          margin-bottom: 10px;
          width: 108px;
          border-radius: 2px;
          overflow: hidden;
          .partner-item-img {
            img {
              width: 108px;
              height: 44px;
              vertical-align: middle;
            }
          }
          .partner-item-name {
            padding-left: 10px;
            padding-right: 9px;
            line-height: 26px;
            background: #EAF6FE;
            font-size: 13px;
            color: #333;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: 400;
          }
        }
      }
      .cube-slide-dots {
        bottom: 11px !important;
        span {
          width: 4px;
          height: 3px;
          background: rgba(0, 98, 159, 0.5);
          margin: 0 2px;
          &.active {
            width: 11px;
            background: #00629F;
          }
        }
      }
    }
    &.onePage {
      .cube-slide {
        padding-bottom: 5px;
      }
      .cube-slide-dots {
        display: none;
      }
    }
  }
}
</style>