<template>
    <div>
    </div>
</template>
<script>
import { getInfoByDynamicUrl } from '@/api/configurable/common'
import { platformBizApi } from '@/config/env';
import CONSTANT from '@/config/config_constant';
export default {
    components: {},
    props: [],
    data () {
        return {
        };
    },
    computed: {},
    mounted () {
        if (localStorage.getItem(CONSTANT.token)) {
            getInfoByDynamicUrl(platformBizApi + `/conferenceFront/getDomain`, { platformId: localStorage.getItem('platformId') }).then(res => {
								if (res.data.length) {
                    // 
                } else {
									// 易贸医疗
									// 没有关注就去关注领域
                    this.$router.push({ name: 'medicineIndustry', query: { platformId: localStorage.getItem('platformId') } })
                }
            }, () => {
                this.$router.push({ name: 'medicineIndustry', query: { platformId: localStorage.getItem('platformId') } })
            })
        }
    },
    methods: {}
}
</script>
<style lang='scss' scoped>
</style>
