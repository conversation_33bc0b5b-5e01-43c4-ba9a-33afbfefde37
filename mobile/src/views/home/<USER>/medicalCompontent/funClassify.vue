<template>
  <div class="fun-classify-wrap">
    <!-- 顶部banner部分 -->
    <div class="banner-wrap" :style="girdConfigStyle">
      <div class="banner-item" v-for="(item, index) in bannerList" :key="index" :style="{ gridArea: item.gridArea + '-' + index, aspectRatio: item.aspectRatio }">
        <img :src="item.data && item.data.coverImg" alt="" @click="handleClick(item.data)" />
      </div>
    </div>
    <!-- 底部职能部分 改成和classifyLive.vue 的一样 -->
    <CommonClassify :configuration="configuration" />
  </div>
</template>
<script>
import CONSTANT from "@/config/config_constant";
import CommonClassify from "@/components/common/commonClassify";
import { getFunClassifyBanner } from "@/api/medicine/homePage";
import { getInfoByDynamicUrl } from "@/api/configurable/common";
export default {
  name: "FunClassify",
  props: {
    configuration: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      bannerList: [],
      girdConfigStyle: {},
    };
  },
  components: {
    CommonClassify,
  },
  created() {
    // 获取banner主图
    this.getFunClassifyBanner();
  },
  mounted() {},
  computed: {},
  methods: {
    handleClick(item) {
      console.log(item);
      this.naviToDetails(this.$route.query.platformId, item.itemType, item);
    },
    /**
     * Banner类型配置映射
     * 提取配置便于扩展和维护
     */
    getBannerTypeConfig() {
      return {
        1: {
          gridStyle: CONSTANT.funClassifyBanner.big,
          getItemConfig: () => ({
            gridArea: "big-mode",
            aspectRatio: CONSTANT.funClassifyBanner.big["big-mode"]
          })
        },
        2: {
          gridStyle: CONSTANT.funClassifyBanner.oneAndFour,
          getItemConfig: (index) => ({
            gridArea: index === 0 ? "big-mode" : "small-mode",
            aspectRatio: index === 0 
              ? CONSTANT.funClassifyBanner.oneAndFour["big-mode"] 
              : CONSTANT.funClassifyBanner.oneAndFour["small-mode"]
          })
        },
        3: {
          gridStyle: CONSTANT.funClassifyBanner.oneAndSeven,
          getItemConfig: (index) => ({
            gridArea: index === 0 ? "big-mode" : "small-mode",
            aspectRatio: index === 0 
              ? CONSTANT.funClassifyBanner.oneAndSeven["big-mode"] 
              : CONSTANT.funClassifyBanner.oneAndSeven["small-mode"]
          })
        }
      };
    },
    // 获取banner配置
    async fetchBannerConfig() {
      try {
        const response = await getFunClassifyBanner({ 
          subMenu: this.configuration.subMenu, 
          webPlatform: 1 
        });
        
        if (response.code !== this.$successCode) {
          this.$toast(response.info);
          return null;
        }

        if (!response.data || !response.data.length) {
          console.warn('Banner配置数据为空');
          return null;
        }

        return response.data[0];
      } catch (error) {
        console.error('获取banner配置失败:', error);
        this.$toast('获取banner配置失败');
        return null;
      }
    },

    // 获取banner数据
    async fetchBannerData(apiUrl) {
      try {
        const response = await getInfoByDynamicUrl(apiUrl);
        
        if (response.code !== this.$successCode) {
          this.$toast(response.info);
          return null;
        }

        return response.data || [];
      } catch (error) {
        console.error('获取banner数据失败:', error);
        this.$toast('获取banner数据失败');
        return null;
      }
    },

    // 处理banner数据
    processBannerData(bannerData, bannerType) {
      const typeConfig = this.getBannerTypeConfig()[bannerType];
      
      if (!typeConfig) {
        console.warn(`未知的banner类型: ${bannerType}`);
        return [];
      }

      // 设置网格样式
      this.girdConfigStyle = typeConfig.gridStyle;

      // 处理每个banner项
      return bannerData.map((item, index) => ({
        ...typeConfig.getItemConfig(index),
        data: item
      }));
    },

    // 解析banner样式配置
    parseBannerStyleConfig(paramString) {
      try {
        return paramString ? JSON.parse(paramString) : {};
      } catch (error) {
        console.error('解析banner样式配置失败:', error);
        return {};
      }
    },

    /**
     * 获取职能分类Banner数据
     * 主入口方法，协调各个子方法完成banner数据获取和处理
     */
    async getFunClassifyBanner() {
      try {
        // 1. 获取banner配置
        const bannerConfig = await this.fetchBannerConfig();
        if (!bannerConfig) {
          return;
        }

        // 2. 解析配置参数
        const styleConfig = this.parseBannerStyleConfig(bannerConfig.param);
        const bannerType = styleConfig.type;

        if (!bannerType) {
          console.warn('Banner类型配置缺失');
          return;
        }

        // 3. 确定API地址
        const apiUrl = this.isPreviewEnviroment ? bannerConfig.previewApiUrl : bannerConfig.apiUrl;
        if (!apiUrl) {
          console.warn('Banner API地址缺失');
          return;
        }

        // 4. 获取banner数据
        const bannerData = await this.fetchBannerData(apiUrl);
        if (!bannerData || !bannerData.length) {
          return;
        }

        // 5. 处理并设置banner数据
        this.bannerList = this.processBannerData(bannerData, bannerType);

      } catch (error) {
        console.error('获取职能分类Banner失败:', error);
        this.$toast('获取Banner数据失败');
      }
    },
  },
};
</script>
<style scoped lang="scss">
.fun-classify-wrap {
  width: 100%;
  .banner-wrap {
    width: 100%;
    display: grid;
    gap: 10px;
    padding: 0 15px;
    box-sizing: border-box;
    .banner-item {
      width: 100%;
      height: 100%;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: fill;
        object-position: center top;
      }
    }
  }
}
</style>
