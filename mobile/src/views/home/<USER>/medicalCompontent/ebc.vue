<template>
  <!-- ebc -->
  <div class="ebc-nav" v-if="ebc.id">
    <div class="ebc-title">{{configuration.name}}</div>
    <div class="ebc-img-content" @click="toEbc">
      <img :src="ebc.bannerImg" class="ebc-img">
    </div>
  </div>
</template>

<script>
import { getConferenceFront } from '@/api/medicine/homePage'
export default {
  props: ['configuration'],
  data() {
    return {
      ebc: {}
    }
  },
  methods: {
    // 获取ebc设置
    getConferenceFront() {
      let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      getConferenceFront(params).then(res=>{
        if(res.code == this.$successCode){
         this.ebc = res.data;
        }else{
          this.$message.error(res.info);
        }
      })
    },
    // 跳转企业直播间
    toEbc() {
      window.location = this.ebc.conferenceUrl;
    }
  },
  watch: {
    configuration: {
      handler (val) {
        this.getConferenceFront()
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style lang="scss" scope>
.ebc-nav {
  margin: 0 15px 25px;
  .ebc-title {
    color: #333;
    font-size: 18px;
    line-height: 24px;
    font-weight: 500;
  }
  .ebc-img-content {
    width: 345px;
    height: 132px;
    margin-top: 10px;
    position: relative;
   
    .ebc-img {
      width: 100%;
      height: 100%;
       border-radius: 6px;
    }
  }
}
</style>