<template>
  <!-- 单个直播 活动的 新闻显示 -->
  <div v-if="itemData.activityNews" class="activity-news-nav" :class="from">
      <div class="activity-news-left" @click="toNewsDetail">
          <img :src="require('@/assets/img/news.png')">{{itemData.activityNews.title}}
      </div>
      <div class="activity-news-right" @click="toNewsList">
          全部<i class="cubeic-arrow"></i>
      </div>
  </div>
</template>

<script>
import {putConferenceNews} from '@/api/medicine/news'
export default {
  props: {
    itemData: Object,
    from: String,   // 来源  直播活动列表 的 四种样式
  },
  methods: {
    // 前往 新闻详情
    async toNewsDetail(e) {
      e.stopPropagation();
      await putConferenceNews({newsId:this.itemData.activityNews.id})
      if (this.itemData.activityNews.type == 1) {
        // 跳外链 新闻
        window.location = this.itemData.activityNews.content;
      } else {
        // 图文 新闻
        this.$router.push({name: 'medicineNewsDetail', params: {newsId: this.itemData.activityNews.id}})
      }
    },
    // 前往 新闻列表
    toNewsList(e) {
      e.stopPropagation();
      this.$router.push({name: 'medicineNewsList', params: {activityId: this.itemData.activityNews.activityId}})
    }
  }
}
</script>

<style lang="scss">
.activity-news-nav {
  display: flex;
  justify-content: space-between;
  line-height: 16px;
  height: 16px;
  margin-top: 10px;
  font-size: 12px;
  &.rowTwo {
    margin: 12px 6px;
  }
  &.carousel {
    margin: 10px 10px 0;
  }
  .activity-news-left {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #666;
    img {
      width: 29px;
      height: 12px;
      vertical-align: middle;
      margin-right: 6px;
      margin-top: -3px;
    }
  }
  .activity-news-right {
    color: #0C7EB5;
    width: 40px;
    flex-shrink: 0;
    text-align: right;
    i.cubeic-arrow {
      font-size: 12px !important;
      margin-left: 2px;
    }
  }
}
</style>