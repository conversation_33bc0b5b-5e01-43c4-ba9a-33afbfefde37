<template>
  <div class="single-line-wrap">
    <div v-for="(item, index) in list" :key="index">
      <HotLiveItem :itemData="item"></HotLiveItem>
    </div>
  </div>
</template>

<script>
import HotLiveItem from "../hotLiveItem";
export default {
  props: ["list"],
  data() {
    return {};
  },

  created() {},

  components: {
    HotLiveItem,
  },

  computed: {},

  mounted() {},

  methods: {},
};
</script>
<style scoped lang="scss"></style>
