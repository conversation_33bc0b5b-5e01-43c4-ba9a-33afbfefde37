<template>
  <div>
    <template v-if="listStyle == 'rowOne'">
      <singleLine :list="list"></singleLine>
    </template>
    <template v-if="listStyle == 'rowTwo'">
      <twoInRow :list="list"></twoInRow>
    </template>
    <template v-if="listStyle == 'bigImg'">
      <bigPicture :list="list"></bigPicture>
    </template>
    <template v-if="listStyle == 'carousel'">
      <Rotation :list="list"></Rotation>
    </template>
  </div>
</template>

<script>
import singleLine from "./singleLine";
import twoInRow from "./twoInRow";
import bigPicture from "./bigPicture";
import Rotation from "./Rotation";
export default {
  props: ["listStyle", "list"],
  data() {
    return {};
  },

  created() {},

  components: {
    singleLine,
    twoInRow,
    bigPicture,
    Rotation,
  },

  computed: {},

  mounted() {},

  methods: {},
};
</script>
<style scoped lang="scss"></style>
