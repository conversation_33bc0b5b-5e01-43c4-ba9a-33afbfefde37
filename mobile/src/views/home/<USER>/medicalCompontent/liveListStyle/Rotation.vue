<template>
  <div class="swiperAreaZly" v-show="!(list.length == 0)">
    <cube-slide v-if="list.length" :data="list" :stopPropagation="true" :interval="3000000" :auto-play="true" @change="changePage" class="swiper" ref="slide" allowVertical>
      <cube-slide-item :key="indexs" v-for="(item, indexs) in list">
        <div @click="itemClick(item)" class="swiper-block">
          <div class="swiper-block-item">
            <div class="left-content">
              <div class="left-content-time">
                <div class="icon" :style="isPreviewEnviroment ? 'margin-left: 0.28px;' : ''"></div>
                <!-- 兼容预览环境样式 -->
                <div class="name">
                  {{ item.name || item.activityName }}
                </div>
              </div>
              <div class="left-content-title" :style="{ marginTop: currentPlatformId == 3 ? '6px' : '27px' }">
                {{ formatTime(item.beginTime || item.publishTime || item.createTime, "date") }}
                <span style="margin-left: 9px" v-if="currentPlatformId != 3">{{ item.sponsorName || item.organizerName }}</span>
              </div>
              <div class="left-content-btn" v-if="currentPlatformId == 3">立即观看</div>
            </div>
            <img class="cover-image" :src="item.coverImg || item.bannerImg" alt="" />
            <div v-if="item.totalPaymentType == 0 && item.itemType !== 'activity'" class="item-left-payType">免费</div>
            <liveIngIcon class="liveing" v-if="item.liveInPorgress == 1"></liveIngIcon>
          </div>
          <div class="swiper-block-tag" v-if="currentPlatformId == 5">
            <div class="tagDetail" :key="indexs" :style="'background: rgba(90, 46, 132, 0.1);color:rgba(90, 46, 132, 1)'" v-if="tag" v-for="(tag, indexs) in item.channelList">
              {{ tag }}
            </div>
            <div class="tagDetail" :key="indexs + 'typeTag'" v-if="tag" :style="'background: rgba(0, 137, 134, 0.2);color:rgba(0, 137, 134, 1)'" v-for="(tag, indexs) in item.typeList">
              {{ tag }}
            </div>
            <div class="tagDetail" :key="indexs + 'tag'" :style="'background: rgba(243, 151, 1, 0.1);color:rgba(243, 151, 1, 1)'" v-if="tag" v-for="(tag, indexs) in item.classifyList">
              {{ tag }}
            </div>
          </div>
          <!-- 新闻 -->
          <liveItemNews :itemData="item" from="carousel"></liveItemNews>
        </div>
      </cube-slide-item>
    </cube-slide>
    <div class="dots" v-if="list.length">
      <span :class="currentIndex == index ? 'active' : ''" v-for="(item, index) in list"></span>
    </div>
  </div>
</template>
<script>
import CONSTANT from "@/config/config_constant";
import { getInfoByDynamicUrl } from "@/api/configurable/common";

import { getConferenceFront } from "@/api/medicine/homePage";
import liveIngIcon from "../liveIngIcon";
import liveItemNews from "./liveItemNews";
export default {
  components: {
    liveIngIcon,
    liveItemNews,
  },
  props: ["list"],
  data() {
    return {
      infoData: [],
      currentIndex: 0,
      params: {},
      platformId: this.$route.query.platformId,
    };
  },
  computed: {
    tokenFlag() {
      return localStorage.getItem(CONSTANT.token) || false;
    },
  },
  watch: {},
  mounted() {},
  methods: {
    getConferenceFront() {
      let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      getConferenceFront(params).then((res) => {
        if (res.code == this.$successCode) {
          this.infoData = res.data;
        } else {
          this.$message.error(res.info);
        }
      });
    },
    changePage(index) {
      this.currentIndex = index;
    },
    itemClick(item) {
      if (item.itemType == "activity") {
        item.id = item.activityId;
        this.naviToDetails(this.$route.query.platformId, item.itemType, item);
      } else {
        item.id = item.itemId;
        this.naviToDetails(this.$route.query.platformId, item.itemType, item);
      }
    },
    showMore() {
      if (this.configuration.name == "我的活动") {
        this.$router.push({ name: "myActivity" });
      } else {
        this.naviToCommonList(this.params);
      }
    },
    showLoginPage() {
      this.loginShowFunction();
      this.$store.state.loginOption = "touch";
    },
  },
};
</script>
<style lang="scss" scoped>
.swiperAreaZly {
  margin-top: 10px;
}
.swiperAreaZly-title-row {
  display: flex;
  justify-content: space-between;
  height: 30px;
  align-items: center;
  margin-bottom: 15px;
}
.swiperAreaZly-title {
  font-size: 18px;
  color: rgba(51, 51, 51, 1);
  line-height: 25px;
  span {
    font-weight: 500;
  }
}
.liveing {
  position: absolute;
  top: 0px;
  right: 88px;
}
.swiper-block-tag {
  margin-top: 10px;
  height: 16px;
  font-size: 11px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  justify-content: space-between;
  flex-direction: row;
  display: -webkit-box;
  white-space: initial;
  padding-right: 10px;
  .tagDetail {
    margin-right: 6px;
    margin-bottom: 5px;
    padding: 0 6px;
    line-height: 16px;
    display: inline-block;
    border-radius: 1px;
  }
}
.login-content {
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
}
.swiper {
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
}
.swiper-block {
  min-height: 114px;
  border-radius: 4px;
  width: 100%;
  background-color: white;
  padding: 15px 0 10px;
}
.swiper-block-item {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
  img {
    width: 140px;
    height: 84px;
    margin-right: 10px;
  }
}
.left-content {
  width: 182px;
  position: relative;
}
.left-content-time {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  line-height: 22px;
  /* display: flex;
    align-items: center; */
  white-space: normal;
  svg {
    margin-right: 10px;
    float: left;
    margin-left: 5px;
  }
  .icon {
    margin-right: 10px;
    float: left;
    width: 4px;
    height: 20px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    background-color: #1464a1;
  }
  .name {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    width: 167px;
    height: 36px;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 18px;
  }
}
.left-content-title {
  margin-left: 10px;
  font-size: 12px;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
  line-height: 17px;
  width: 172px;
  margin-top: 6px;
}
.cover-image {
  width: 140px;
  height: 84px;
  border-radius: 4px;
}
.more-icon {
  font-size: 14px;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
  line-height: 20px;
  img {
    margin-left: 9px;
  }
}
.login-content {
  width: 345px;
  height: 114px;
  /* background: url("./image/myActivity-background.png") 100% 100%; */
  background-size: cover;
  color: #dadcff;
  display: flex;
  flex-direction: column;
  align-items: center;
  span {
    margin-top: 22px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(218, 220, 255, 1);
    line-height: 20px;
  }
  div {
    margin-top: 20px;
    width: 80px;
    height: 30px;
    border-radius: 15px;
    border: 1px solid rgba(218, 220, 255, 1);
    line-height: 28px;
    text-align: center;
  }
}
.item-left-payType {
  position: absolute;
  top: 2px;
  right: 12px;
  width: 40px;
  height: 20px;
  background: rgba(0, 0, 0, 0.4);
  color: #fff;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 20px;
  text-align: center;
}
.left-content-btn {
  width: fit-content;
  font-weight: 400;
  font-size: 12px;
  color: #ffffff;
  line-height: 16px;
  padding: 5px 10px;
  background: var(--color-primary);
  border-radius: 2px;
  margin-left: 10px;
  margin-top: 4px;
}
</style>

<style>
.swiperAreaZly .cube-slide-dots > span.active {
  display: none;
}
.swiperAreaZly .cube-slide-dots > span {
  display: none;
}
.dots {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
.dots .active {
  width: 11px;
  background: rgba(20, 100, 161, 1);
}
.dots > span {
  width: 4px;
  height: 3px;
  background: rgba(20, 100, 161, 0.6);
  margin: 0 4px;
}
</style>
