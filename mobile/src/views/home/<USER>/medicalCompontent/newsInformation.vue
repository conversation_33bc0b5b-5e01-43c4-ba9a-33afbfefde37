<template>
  <div class="information-wrap-mobile">
    <div class="information-wrap" v-if="pcConfigParam.showType == 0">
      <common-title linkType="moduleList/news" :title="configuration.name" :isShowMore="true" :sourceData="newsData"></common-title>
      <div class="information-list">
        <div class="information-list-item" v-for="(item, index) in newsData" :key="index">
          <common-information :item="item"></common-information>
        </div>
      </div>
    </div>
    <classification v-else :configuration="configuration"></classification>
  </div>
</template>
<script>
import CommonTitle from "./commonTitle";
import { getInfoByDynamicUrl } from "@/api/configurable/common";
import CommonInformation from "@/components/common/commonInformation";
import Classification from "./classification";
export default {
  name: "newsInformation",
  props: {
    configuration: {
      type: Object,
      default: () => {
        return {
          name: "新闻资讯",
          param: {
            displayNum: 1,
            listStyle: "rowOne",
          },
        };
      },
    },
  },
  components: {
    CommonTitle,
    CommonInformation,
    Classification,
  },
  data() {
    return {
      newsData: [],
      // 展示模式
      config: {
        displayNum: 1,
        listStyle: "rowOne",
      },
      // 存储上一次的配置，用于比较变化
      prevConfig: {
        displayNum: 1,
        listStyle: "rowOne",
      },
    };
  },
  created() {
    // 获取新闻资讯的数据
    this.getNewsData();
  },
  mounted() {},
  computed: {
    pcConfigParam() {
      return this.configuration.pcParam ? JSON.parse(this.configuration.pcParam) : {};
    },
  },
  methods: {
    async getNewsData() {
      const apiUrl = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      const res = await getInfoByDynamicUrl(apiUrl);
      if (res.code == "001") {
        this.config = JSON.parse(this.configuration.param);
        // 更新 prevConfig，用于下次比较
        this.prevConfig = {
          displayNum: this.config.displayNum,
          listStyle: this.config.listStyle,
        };
        this.newsData = res.data || [];
      } else {
        this.$toast(res.info);
      }
    },
  },
};
</script>
<style scoped lang="scss">
.information-wrap {
  margin-bottom: 13px;
  .information-list {
    width: 100%;
    padding: 0 15px;
    box-sizing: border-box;
    .information-list-item {
      margin: 10px 0;
    }
  }
}
</style>
