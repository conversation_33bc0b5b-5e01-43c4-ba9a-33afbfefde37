<!-- 热门直播 -->
<template>
  <div class='hotLive' v-if="liveData&&liveData.length!==0">
      <common-title title="热门直播" :sourceData="liveData" :isShowMore="!(lastLayoutId==configuration.layoutId&&infiniteLoadLast==1)"/>
      <!-- <hot-live-item v-for="item,index in liveData" :itemData="item" :index="index"></hot-live-item> -->
        <liveListStyle :list="liveData" :listStyle="param.listStyle" style="padding: 0px 15px" ></liveListStyle>
  </div>
</template>

<script>
import CommonTitle from './commonTitle';
import HotLiveItem from './hotLiveItem';
import liveListStyle from './liveListStyle/liveListStyle';
import { getConferenceFront } from '@/api/medicine/homePage';
export default {
  name: 'hotLive' ,
  props:['configuration','lastLayoutId','infiniteLoadLast'],
  data () {
    return {
        liveData:[],
            param: {},
    };
  },

  created(){
      this.getConferenceFront();
  },

  components: {
    CommonTitle,
    HotLiveItem,
        liveListStyle,
  },

  computed: {},

  mounted(){},

  watch:{
        configuration: {
            handler (val) {
              this.param = JSON.parse(val.param)
                this.getConferenceFront()
            },
            deep: true,
            immediate: true
        }
  },

  methods: {
       getConferenceFront () {
            let params = this.isPreviewEnviroment?this.configuration.previewApiUrl:this.configuration.apiUrl;
            getConferenceFront(params).then((res) => {
                console.log('热门直播', res);
                if(res.code == this.$successCode){
                    this.liveData = res.data.list || [];
                    this.liveData = this.liveData.map((item)=>{
                        return {
                          ...item,
                          isExpand:false
                        }
                    })
                }
            })
        },
				updataList(data){
					this.liveData = this.liveData.concat(data);
				},
  }
}

</script>
<style  scoped lang="scss">
.hotLive{
    margin-bottom: 20px;
}
</style>