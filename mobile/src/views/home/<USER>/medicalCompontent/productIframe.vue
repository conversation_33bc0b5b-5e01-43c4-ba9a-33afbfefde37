<template>
  <div class="product-iframe" >
    <iframe :src="configuration.url"  frameborder="0" class="product-content" scrolling="auto" id="iframe" @load="iframeLoad"></iframe>
    <!-- <iframe :src="'http://localhost:8080/'"  frameborder="0" class="product-content" scrolling="auto" id="iframe" @load="iframeLoad"></iframe> -->
  </div>
</template>
<script>
export default {
  props: ['configuration'],
  name: 'productIframe',
  data() {
    return {
      
    }
  },
  mounted() {
    this.$nextTick(() => {
      document.querySelector('.product-iframe').style.height = window.innerHeight - 53 + 'px'
    })
  },
  computed: {},
  methods: {
    iframeLoad(){
      try{
        let url = new URLSearchParams(location.hash);
        let params={
          invitationCodeId:url.get("invitationCodeId")
        }
        document.getElementById('iframe').contentWindow.postMessage(JSON.stringify(params),this.configuration.url)

      }catch(e){
      console.log(e);
      
      }
    }
  },
}
</script>
<style scoped lang="scss">
.product-iframe {
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  .product-content {
    width: 100%;
    height: calc(100%);
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
  }
}
</style>
