import Vue from 'vue';
//  引入同级目录下面的vue文件   
const contexts = require.context('.', true, /\.vue$/)

//  注册渲染组件
contexts.keys().forEach(component=>{
    let componentEntity = contexts(component).default;
    //console.log('componentEntity', componentEntity);
    //console.log(component.split('./')[1].split('.')[0].split('/').slice(-1)[0]);
    Vue.component(component.split('./')[1].split('.')[0].split('/').slice(-1)[0]+'0513', componentEntity);
})