<template>
    <div class="swiperAreaZly" v-show="!(infoData.length==0)">
        <div class="swiperAreaZly-title-row">
            <div class="swiperAreaZly-title">
                <span>企业直播间</span>
            </div>
        </div>
        <cube-slide
					v-if="infoData.length"
					:data="infoData"
					:stopPropagation="true"
					:interval="3000"
					:auto-play="true" @change="changePage" class="swipers" ref="slide" allowVertical>
            <cube-slide-item :key="indexs" v-for="(item, indexs) in infoData">
                <div @click="itemClick(item)" class="swiper-block">
									<div class="swiper-block-item">
                    <div class="left-content">
                        <div class="left-content-time">
													<div class="icon" :style="isPreviewEnviroment?'margin-left: 0.28px;':''"></div>
                            <!-- 兼容预览环境样式 -->
														<div class="name">
															{{item.name}}
														</div>
                        </div>
                        <div class="left-content-title">
													{{formatTime(item.beginTime || item.publishTime || item.createTime,'date')}}
													<span style="margin-left: 9px;">{{item.sponsorName}}</span>
													<!-- {{item.conferenceType}} -->
												</div>

                    </div>
                    <img class="cover-image" :src="item.coverImg" alt="">
									</div>
									<div class="swiper-block-tag">
										&nbsp;
										<div class="tagDetail"
											:key="indexs"
											:style="'background: rgba(235, 123, 33, 0.2);color:rgba(235, 123, 33, 1)'"
											v-if="tag"
											v-for="(tag, indexs) in item.channelList">
											{{tag}}
										</div>
										<div class="tagDetail"
											:key="indexs+'tag'"
											:style="'background: rgba(6, 91, 152, 0.2);color:rgba(6, 91, 152, 1)'"
											v-if="tag"
											v-for="(tag, indexs) in item.classifyList">
											{{tag}}
										</div>

									</div>
                </div>
            </cube-slide-item>
        </cube-slide>
        <div class="dots" v-if="infoData.length">
            <span :class="currentIndex == index?'active':''" v-for="(item,index) in infoData"></span>
        </div>
    </div>
</template>
<script>
import CONSTANT from '@/config/config_constant';
import { getInfoByDynamicUrl } from '@/api/configurable/common'

import { getConferenceFront } from '@/api/medicine/homePage'
export default {
    components: {},
    props: ['configuration'],
    data () {
        return {
            infoData: [],
            currentIndex: 0,
            params: {}
        };
    },
    computed: {
        tokenFlag () {
            return localStorage.getItem(CONSTANT.token) || false
        }
    },
    watch: {
        $route: {
            handler (val) {
                // setTimeout(() => {
                //     this.getInfo()
                // }, 100);
            },
            deep: true,
            immediate: true
        },

				configuration: {
						handler (val) {
							this.getConferenceFront()
						},
						deep: true,
						immediate: true
				}
    },
    mounted () {

    },
    methods: {
				getConferenceFront(){
					let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
					getConferenceFront(params).then(res=>{
							if(res.code == this.$successCode){
								this.infoData = res.data
							}else{
								this.$message.error(res.info);
							}
					})
				},
        clickImage (item, indexs) {

        },
        changePage (index) {
            this.currentIndex = index
        },
        itemClick (item) {
            if (item.type == null && item.itemType !== null) {
                item.id = item.itemId
                item.type = item.itemType
            }
            this.naviToDetails(localStorage.getItem('platformId'), item.type, item)
        },
        showMore () {
            if (this.configuration.name == '我的活动') {
                this.$router.push({ name: 'myActivity' })
            } else {
                this.naviToCommonList(this.params)
            }
        },
        showLoginPage () {
            this.loginShowFunction()
            this.$store.state.loginOption = 'touch'
        }
    }
}
</script>
<style lang='scss' scoped>
.swiperAreaZly {
    margin: 0 15px;
    padding: 15px 0;
    // height: 140px;
    // background: #fff;
}
.swiperAreaZly-title-row {
    display: flex;
    justify-content: space-between;
    height: 30px;
    align-items: center;
    margin-bottom: 15px;
}
.swiperAreaZly-title {
    font-size: 18px;
    color: rgba(51, 51, 51, 1);
    line-height: 25px;
    span {
        font-weight: 500;
    }
}

		.swiper-block-tag{
			margin-top: 10px;
			height: 16px;
			font-size: 11px;
			overflow: hidden;
			text-overflow: ellipsis;
			-webkit-line-clamp: 1;
			-webkit-box-orient: vertical;
			justify-content: space-between;
			flex-direction: row;
      display: -webkit-box;
			white-space: initial;
			padding-right: 10px;
			.tagDetail{
				margin-right: 6px;
				margin-bottom: 5px;
				padding: 0 6px;
				line-height: 16px;
				display: inline-block;
				border-radius: 1px;
			}
		}
.login-content {
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
}
.swiper {
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
}
.swiper-block{

    min-height: 114px;
    border-radius: 4px;
    width: 100%;
    background-color: white;
    padding: 15px 0 10px;
}
.swiper-block-item {
    width: 100%;
    display: flex;
    justify-content: space-between;
    img {
        width: 140px;
        height: 84px;
        margin-right: 10px;
    }
}
.left-content {
    width: 182px;
		position: relative;
}
.left-content-time {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
    /* display: flex;
    align-items: center; */
		white-space: normal;
    svg {
        margin-right: 10px;
				float: left;
				margin-left: 5px;
    }
		.icon{
        margin-right: 10px;
				float: left;
				width: 4px;
				height: 20px;
				border-top-right-radius: 4px;
				border-bottom-right-radius: 4px;
				background-color: #1464A1;
		}
		.name {
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			width: 167px;
			height: 40px;
		}
}
.left-content-title {
    margin-left: 10px;
    font-size: 12px;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    line-height: 17px;
    width: 172px;/*
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    white-space: pre-wrap; */
		margin-top: 27px;
}
.cover-image {
    width: 140px;
    height: 84px;
    border-radius: 4px;
}
.more-icon {
    font-size: 14px;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    line-height: 20px;
    img {
        margin-left: 9px;
    }
}
.login-content {
    width: 345px;
    height: 114px;
    /* background: url("./image/myActivity-background.png") 100% 100%; */
    background-size: cover;
    color: #dadcff;
    display: flex;
    flex-direction: column;
    align-items: center;
    span {
        margin-top: 22px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(218, 220, 255, 1);
        line-height: 20px;
    }
    div {
        margin-top: 20px;
        width: 80px;
        height: 30px;
        border-radius: 15px;
        border: 1px solid rgba(218, 220, 255, 1);
        line-height: 28px;
        text-align: center;
    }
}
</style>


<style >
.swiperAreaZly .cube-slide-dots > span.active {
    display: none;
}
.swiperAreaZly .cube-slide-dots > span {
    display: none;
}
.dots {
    margin-top: 10px;
    display: flex;
    justify-content: center;
}
.dots .active {
    width: 11px;
    background: rgba(20, 100, 161, 1);
}
.dots > span {
    width: 4px;
    height: 3px;
    background: rgba(20, 100, 161, 0.6);
    margin: 0 4px;
}
</style>
