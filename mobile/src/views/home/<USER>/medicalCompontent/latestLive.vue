<template>
  <div class="latest-live-wrap">
    <commonTitle :title="configuration.name" :sourceData="infoData" :isShowMore="false" />
    <div class="latest-live-content-wrap">
      <scroll :scrollX="true" ref="scroll" :data="infoData" style="overflow:hidden">
        <div class="latest-live-content flex-middle">
          <div class="latest-live-line"></div>
          <div class="latest-live-item flex-vertical flex-center" v-for="(item, index) in infoData" :key="index"
            @click="clickItem(item)">
            <div class="latest-live-item-top flex-center flex-middle"
              :class="{ orange: item.beginTimeText == '今天', blue: item.beginTimeText == '明天' }">
              <img v-if="item.beginTimeText == '今天' || item.beginTimeText == '明天'"
                src="https://oss.ienmore.com/frontUpload/partUpload/2025-06-03/e85707fdc2ef7b4cb479ac9b8ca7506e.png" />
              <img v-else src="https://oss.ienmore.com/resources/public/icon/yiliao/medical_zxzb_icon.png" />
              <span class="latest-live-item-top-text">{{ item.beginTimeText }}</span>
            </div>
            <div class="latest-live-item-bottom">
              <div class="latest-live-item-poster">
                <live-status :info="item"></live-status>
                <img :src="item.bannerImg || item.coverImg" alt="" />
              </div>
              <div class="latest-live-item-info">{{ item.name }}</div>
              <div class="latest-live-item-button flex-center">
                <van-button>立即观看</van-button>
              </div>
            </div>
          </div>
        </div>
      </scroll>
    </div>
  </div>
</template>

<script>
import { getConferenceFront } from "@/api/medicine/homePage";
import commonTitle from "./commonTitle.vue";
import LiveStatus from "@/components/common/liveStatus.vue";
import scroll from '@/components/common/scroll';
export default {
  props: ["configuration", "lastLayoutId", "infiniteLoadLast"],
  data() {
    return {
      infoData: [],
    };
  },
  components: {
    commonTitle,
    LiveStatus,
    scroll
  },
  watch:{
    configuration: {
      handler(val) {
        this.param = JSON.parse(val.param);
        this.getConferenceFront();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    async getConferenceFront() {
      const params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      const res = await getConferenceFront(params);
      if (res.code == this.$successCode) {
        this.infoData = res.data;
      } else {
        this.$toast(res.info);
      }
    },
    clickItem(item) {
      if (item.itemType == "activity") {
        item.id = item.activityId;
        this.naviToDetails(this.$route.query.platformId, item.itemType, item);
      } else {
        item.id = item.itemId;
        this.naviToDetails(this.$route.query.platformId, item.itemType, item);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.latest-live-wrap {
  // background: #f2f1f6;
  padding-top: 10px;
  padding-bottom: 20px;
  .latest-live-content-wrap {
    margin: 10px 15px 0;
    .latest-live-content {
      width: max-content;
      padding: 10px;
      padding-right: 0;
      border-radius: 4px;
      background: #ffffff;
      position: relative;
      .latest-live-line {
        width: calc(100% - 20px);
        height: 1px;
        background: #f5f5f5;
        position: absolute;
        top: 24px;
        left: 10px;
        z-index: 1;
        transform: scaleY(0.5);
        transform-origin: 0 0;
      }
      .latest-live-item {
      width: 140px;
        flex-shrink: 0;
        margin-right: 10px;

        .latest-live-item-top {
          width: 86px;
          height: 28px;
          background: #E5E5E5;
          border-radius: 15px;
          margin-bottom: 10px;
          position: relative;
          z-index: 3;
          border: 2px solid #ffffff;

          img {
            width: 14px;
            height: 14px;
            vertical-align: middle;
            margin-right: 4px;
          }

          .latest-live-item-top-text {
            font-weight: 400;
            font-size: 12px;
            color: #333;
            line-height: 14px;
          }

          &.orange {
            background: #ff6b00;

            .latest-live-item-top-text {
              color: #fff;
            }
          }

          &.blue {
            background: var(--color-primary);

            .latest-live-item-top-text {
              color: #fff;
            }
          }
        }

        .latest-live-item-bottom {
          width: 140px;

          .latest-live-item-poster {
            width: 100%;
            height: 84px;
            position: relative;
            margin-bottom: 6px;

            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
              border-radius: 4px;
            }

            .latest-live-tip-text {
              font-weight: 400;
              font-size: 10px;
              color: #ffffff;
              padding: 4px;
            }

            .status-text {
              background: #52c41a;
              left: 6px;
              top: 6px;
              position: absolute;
              border-radius: 2px;
            }

            .type-text {
              background: rgba(0, 0, 0, 0.5);
              right: 40px;
              top: 6px;
              position: absolute;
              border-radius: 2px;
            }

            .live-text {
              background: rgba(0, 0, 0, 0.5);
              right: 6px;
              top: 6px;
              position: absolute;
              border-radius: 2px;
            }
          }

          .latest-live-item-info {
            font-weight: 400;
            font-size: 12px;
            color: #333333;
            line-height: 17px;
            height: 34px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            margin-bottom: 8px;
          }

          .latest-live-item-button {
            width: 100%;

            .van-button {
              height: 28px;
              background: var(--color-primary);
              border-radius: 4px;
              padding: 5px 10px;
              font-weight: 400;
              font-size: 12px;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}
</style>
