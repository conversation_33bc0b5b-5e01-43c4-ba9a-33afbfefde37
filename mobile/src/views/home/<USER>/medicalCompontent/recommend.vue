<template>
  <!-- 为你推荐 -->
  <div class="recommend" v-if="infoDataLimited && infoDataLimited.length !== 0">
    <!-- 根据类型分为两种item -->
    <div>
      <div class="title-row">
        <div class="recommend-title">
          <span>{{ configuration.name || configuration.subMenuName }}</span>
        </div>
        <div class="change-data" @click="changeData()" v-if="!(lastLayoutId == configuration[layoutIdField] && infiniteLoadLast == 1)">
          <span>更多</span>
          <img style="width: 5px; height: 10px" src="@/assets/img/right1.png" alt="" />
        </div>
      </div>
      <liveListStyle :list="infoDataLimited" :listStyle="param.listStyle"> </liveListStyle>

      <!-- <div v-for="(item,index) in infoDataLimited" @click="clickItem(item)">
                <infoRecommend :info='item' :iflast='index == infoDataLimited.length-1' :showTag='true'></infoRecommend>
            </div> -->
    </div>
  </div>
</template>
<script>
import { getInfoByDynamicUrl } from "@/api/configurable/common";
import infoRecommend from "@/components/medicine/infoRecommend.vue";
import liveListStyle from "./liveListStyle/liveListStyle";

import { getActivity } from "@/api/medicine/homePage";
export default {
  components: {
    infoRecommend,
    liveListStyle,
  },
  props: ["configuration", "lastLayoutId", "infiniteLoadLast", "layoutIdField"],
  data() {
    return {
      infoData: [],
      infoDataLimited: [],
      tagList: [{ tagName: "标签1" }, { tagName: "标签1" }, { tagName: "标签1" }],
      changedDataIndex: 0,
      param: {},
    };
  },
  computed: {},
  watch: {
    $route: {
      handler(val) {},
      deep: true,
      immediate: true,
      params: {},
    },
    configuration: {
      handler(val) {
        this.param = JSON.parse(val.param);
        this.getActivity();
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    getActivity() {
      let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      getActivity(params).then((res) => {
        if (res.code == this.$successCode) {
          this.infoDataLimited = res.data;
        } else {
          this.$message.error(res.info);
        }
      });
    },
    updataList(data) {
      this.infoDataLimited = this.infoDataLimited.concat(data);
    },
    showMore() {
      this.naviToCommonList(this.params);
    },
    clickItem(item) {
      item.id = item.activityId;
      this.naviToDetails(this.$route.query.platformId, item.itemType, item);
    },
    changeData() {
      // this.$router.push({ name: 'medicineActivityList' })
      location.assign(`${window.location.origin}${window.location.pathname}#/medicineActivityList?platformId=${localStorage.getItem('platformId')}`);
    },
  },
};
</script>
<style lang="scss" scoped>
.around {
  animation: rotate 1s ease-in-out;
}
@keyframes rotate {
  from {
    transform: rotateZ(0deg);
  }
  to {
    transform: rotateZ(360deg);
  }
}
.recommend {
  // background-color: #fff;
  margin-bottom: 20px;
  padding: 0px 15px 0;
}
.recommend-title {
  display: flex;
  align-items: center;
  margin: 0px 0;
  img {
    height: 24px;
    width: 24px;
  }
  span {
    font-weight: 400;
    font-size: 16px;
    margin-left: 6px;
  }
}

.title-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-top: 10px;
  span {
    font-size: 18px;
    font-weight: 500;
    color: rgba(51, 51, 51, 1);
    line-height: 25px;
  }
}
.change-data {
  display: flex;
  align-items: center;
  span {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 17px;
    margin-right: 5px;
  }
  div {
    height: 16px;
    svg {
      height: 16px;
    }
  }
}
.info-item {
  padding: 3px 0;
  display: flex;
  flex-direction: left;
  width: 100%;
  padding: 10px 0;
}
.info-right {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 225px;
}

.info-coverImg {
  width: 113px;
  height: 80px;
  border-radius: 5px;
  margin-right: 10px;
  border: 1px solid #eee;
}

.marker-radio-orange {
  border-radius: 8px;
  height: 16px;
  font-size: 12px;
  line-height: 14px;
  padding: 0 5px;
  border: 1px solid #ff620d;
  color: #999;
}
.marker-radio-group-blue {
  display: flex;
  flex-wrap: wrap;
  span {
    font-size: 12px;
    border: 1px solid #4e6ba4;
    border-radius: 9px;
    color: #4e6ba4;
    padding: 3px 5px 1px 5px;
    margin-right: 5px;
  }
}
.info-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  span {
    font-size: 12px;
    line-height: 16px;
    color: #999;
    margin-right: 10px;
  }
}
.marker-square-orange {
  font-size: 12px;
  // height: 16px;
  line-height: 16px;
  border-radius: 2px;
  background: #ff620d;
  color: white !important;
  padding: 2px 5px 0px 5px;
}
.marker-radio-group-red {
  display: flex;
  flex-wrap: wrap;
  span {
    font-size: 10px;
    height: 14px;
    line-height: 12px;
    border: 1px solid #af2e07;
    border-radius: 7px;
    color: #af2e07;
    padding: 0 5px;
    margin-right: 5px;
  }
}
.info-title-box {
  min-height: 36px;
}
.info-title {
  font-weight: 400;
  word-break: break-word;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  font-size: 13px;
  line-height: 18px;
}
</style>
