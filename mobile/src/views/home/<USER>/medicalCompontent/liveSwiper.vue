<!-- 直播轮播 -->
<template>
    <div class='liveSwiper'>
        <div class="swiperContent">
            <div class="swiper-top">
                <cube-slide ref="slide" :data="items" @change="changePage" allowVertical>
                    <cube-slide-item v-for="(item, index) in items" :key="index"
                                     @click.native="clickHandler(item, index)">
                        <a class="flex-vertical" style="display:block;height:100%">
                            <img :src="item.image" class="img-item ">
                            <div class="slide-title ">{{ item.name }}</div>
                            <div class="flex-left flex-middle slide-bottom">
                                <span class="slide-text unit">{{ item.time }}</span>
                                <span class="slide-text">{{ item.bizName }}</span>
                            </div>
                        </a>
                    </cube-slide-item>
                    <template slot="dots" slot-scope="props">
                        <div class="dot-content">
                            <span :class="['my-dot',{'active':props.current === index}]"
                                  v-for="(item, index) in items"></span>
                        </div>
                    </template>
                </cube-slide>
            </div>
        </div>
    </div>
</template>

<script>
import {liveUrl} from '@/config/env';

export default {
    name: '',
    props: ['liveSwiperData'],
    data() {
        return {
            items: []
        };
    },

    created() {

    },

    watch: {
        liveSwiperData: {
            handler() {
                this.changeSwiperData();
            },
            deep: true,
            immediate: true
        }
    },

    components: {},

    computed: {},

    mounted() {

    },

    methods: {
        changePage() {

        },
        clickHandler(item) {
            if (!this.isPreviewEnviroment) {
                window.location.href = item.url;
            }
        },
        changeSwiperData() {
            this.items = this.liveSwiperData.map((item) => {
                let itemType = item.itemType == 'live' ? '/liveDetail' : '/seriesLive';
                let url;
                if (item.itemType == 'live') {
                    url = liveUrl + `${itemType}?liveId=${item.itemId}&platformId=3&bizId=${item.bizId}&domainType=1`;
                } else {
                    url = liveUrl + `${itemType}/${item.itemId}?platformId=3&bizId=${item.bizId}&domainType=1`;
                }
                return {
                    url,
                    image: item.coverImg,
                    name: item.name,
                    time: this.moment(item.beginTime, 0, '/'),
                    bizName: item.bizName
                }
            })
        }
    }
}

</script>
<style scoped lang="scss">
.liveSwiper {
    padding: 0 15px;

    .swiperContent {
        width: 100%;
        height: 270px;
        background: #ffffff;
        border-radius: 4px;

        .swiper-top {
            height: 300px;
            padding: 10px;
            box-sizing: border-box;
            position: relative;

            .img-item {
                height: 195px;
                border-radius: 2px;
            }

            .slide-title {
                font-size: 16px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 22px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                height: 22px;
                margin-top: 10px;
            }

            .slide-bottom {
                margin-top: 6px;

                .slide-text {
                    font-size: 12px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #999999;
                }
            }

            .dot-content {
                position: absolute;
                left: 0;
                top: 15px;
                height: 3px;
                width: 100%;
                z-index: 100;

                .my-dot {
                    width: 4px;
                    height: 3px;
                    background-color: rgba(20, 100, 161, 0.6);
                    display: inline-block;
                    margin-right: 4px;
                }

                .active {
                    width: 11px;
                    background-color: rgba(20, 100, 161, 1);
                }
            }
        }
    }
}
</style>
