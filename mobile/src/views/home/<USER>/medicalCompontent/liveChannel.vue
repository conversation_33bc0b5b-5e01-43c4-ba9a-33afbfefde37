<template>
  <div class="tags-container">
    <scroll :scrollX="true" ref="scroll" :data="liveType" style="overflow: hidden">
      <div class="tags">
        <div v-for="(item, index) of liveType" class="liveTypeList" @click="toList(item.codeValueId)">
          <img :src="item.remark" class="liveType" />
          <div class="live_type_name">
            {{ item.codeValueDesc }}
          </div>
        </div>
      </div>
    </scroll>
  </div>
</template>

<script>
import { getList } from "@/api/medicine/homePage";
import scroll from "@/components/common/scroll";
export default {
  props: ["configuration"],
  data() {
    return {
      liveType: [],
      configurationParam: {},
    };
  },

  created() {},

  components: {
    scroll,
  },

  computed: {},
  watch: {
    configuration: {
      handler(val) {
        this.getList();
        this.configurationParam = this.configuration.param ? JSON.parse(this.configuration.param) : "";
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},

  methods: {
    getList() {
      let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      getList(params).then((res) => {
        if (res.code == this.$successCode) {
          this.liveType = res.data || [];
        } else {
          this.$toast(res.info);
        }
      });
    },
    toList(codeValueId) {
      const defaultQuery = { modelType: "live" };
      const extraInfoQuery = this.configuration.extraInfo
        ? {
            [this.configuration.extraInfo.type]: codeValueId,
          }
        : {};

      this.$router.push({
        name: "medicineSearch",
        query: Object.assign(defaultQuery, extraInfoQuery),
      });
    },
  },
};
</script>
<style scoped lang="scss">
.tags-container {
  width: 100%;
  // overflow-x: auto;
  white-space: nowrap;
  padding: 0 0 0 15px;
  // background: #FFFFFF;
}
.tags {
  width: 675px;
  overflow-y: scroll;
  display: flex;
  margin-bottom: 20px;
  .liveTypeList {
    width: 63px;
    height: 58px;
    border-radius: 4px;
    background-color: #f5f5f5;
    position: relative;
    margin-right: 6px;
    .liveType {
      display: block;
      width: 24px;
      height: 24px;
      margin: 7px auto 6px;
    }
    .live_type_name {
      color: #333333;
      font-size: 11px;
      line-height: 16px;
      text-align: center;
    }
    .type_right {
      width: 10px;
      float: right;
      margin-top: 5px;
    }
  }
}
</style>
