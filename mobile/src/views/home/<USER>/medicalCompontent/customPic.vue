<template>
  <!-- 自定义图文模块 -->
  <div v-if="moduleShow" class="custom-pic-container">
    <div class="custom-pic-title">{{configuration.name||configuration.subMenuName}}</div>
    <ul class="custom-pic-detail">
      <li v-for="item in detailList" :key="item.id">
        <p v-if="item.type == 'text' && item.text" v-html="item.text"></p>
        <video v-if="item.type == 'video' && item.url" :src="item.url" :poster="item.coverImg"  controls="controls" controlslist="nodownload"  x5-playsinline="true" webkit-playsinline="true" playsinline="true"  @click="clickVideo"  @canplay="imgLoad"></video>
        <img v-if="item.type == 'img' && item.url" :src="item.url" @load="imgLoad">
      </li>
    </ul>
  </div>
</template>

<script>
import { getList, getCustomImageTextInfo } from '@/api/medicine/homePage'
export default {
  props: ['configuration'],
  data() {
    return {
      detailList: [],
      moduleShow: false
    }
  },
  watch: {
    configuration: {
      handler (val) {
        this.getDetail()
      },
      deep: true,
      immediate: true
    }
	},
  methods: {
		getDetail() {
      let fn;
      let params
      if (this.configuration.previewApiUrl||this.configuration.apiUrl) {
        fn = getList
        params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      } else {
        fn = getCustomImageTextInfo
        params = {
          layoutId: this.configuration.id,
        }
      }
      fn(params).then(res=>{
          if(res.code == this.$successCode){
            this.detailList = res.data;
            this.detailList.forEach(item => {
              if (item.url || item.text) {
                this.moduleShow = true;
              }
            })
          }else{
            this.$message.error(res.info);
          }
      })
    },
    clickVideo(e) {
      let isIOS = !!window.navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
      if (!isIOS && !e.target.paused) {
        // 安卓手机  如果视频正在播放  点击暂停
        e.target.pause();
      }
    },
    // 图片 视频 加载完成后 刷新scroll，防止上拉 拉不动
    imgLoad() {
      this.$emit('refreshScroll')
    }
  }
}
</script>

<style lang="scss">
.custom-pic-container {
  margin-bottom: 20px;
  padding: 0px 15px;
  .custom-pic-title {
    font-size: 18px;
    margin-left: 6px;
    font-weight: 500;
    color: rgba(51, 51, 51, 1);
    line-height: 25px;
  }
  .custom-pic-detail {
    margin: 0;
    padding: 0;
    li {
      margin-top: 15px;
      img, video {
        width: 100%;
        height: auto;
        object-fit: cover;
      }
    }
  }
}
  
</style>