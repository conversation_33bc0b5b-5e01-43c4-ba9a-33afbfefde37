<template>
  <div class="typeArea">
    <div class="type-group">
      <div @click="itemClick(item)" v-for="(item, index) in IconList" :key="item.id" :style="'width:' + 100 / (configurationParam.displayNum ? configurationParam.displayNum : 4) + '%'">
        <div>
          <img class="item-icon" v-if="item.iconUrl" :src="item.iconUrl" alt="" />
          <img v-if="item.cornerMark === 'hot'" class="hot-icon" :src="require('@/assets/img/hot-icon.png')" />
          <img v-if="item.cornerMark === 'new'" class="hot-icon" :src="require('@/assets/img/new-icon.png')" />
        </div>
        <span>{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import { getIconList, getEBCLink, getKingKongItemStatus } from "@/api/medicine/homePage";
export default {
  components: {},
  props: ["configuration", "allConfig"],
  data() {
    return {
      IconList: [],
      configurationParam: "",
      platformId: this.$route.query.platformId,
    };
  },
  computed: {},
  watch: {
    configuration: {
      handler(val) {
        this.getIconList();
        this.configurationParam = this.configuration.param ? JSON.parse(this.configuration.param) : "";
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    getIconList() {
      let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      getIconList(params).then((res) => {
        if (res.code == this.$successCode) {
         // console.log("getIconList", res);
          this.IconList = res.data;
        } else {
          this.$message.error(res.info);
        }
      });
    },
    itemClick(item) {
      if (item.targetUrl) {
        window.location.href = item.targetUrl;
      } else {
        this.$createToast({ time: 3000, type: "txt", txt: "内容建设中，敬请期待～" }).show();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.typeArea {
  margin: 0px 0px 17px;
}
.type-group {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-top: -15px;
  > div {
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 15px;
    > div {
      border-radius: 15px;
      width: 44px;
      height: 44px;
      position: relative;
      margin-bottom: 6px;
      .item-icon {
        width: 44px;
        height: 44px;
        margin: 0 auto;
        display: inherit;
      }
      .hot-icon {
        position: absolute;
        top: -10px;
        left: 25px;
        width: 42px;
        height: 20px;
        animation: hotmove 2s linear infinite alternate;
        -webkit-animation: hotmove 2s linear infinite alternate;
      }
    }
    span {
      font-size: 13px;
      color: #333;
    }
  }
}
@keyframes hotmove {
  from {
    transform: translateY(0px);
  }
  to {
    transform: translateY(5px);
  }
}
@-webkit-keyframes hotmove {
  from {
    transform: translateY(0px);
  }
  to {
    transform: translateY(5px);
  }
}

.menu-group {
  margin-top: 6px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  > div {
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    > div {
      margin: 14px;
      border-radius: 50%;
      width: 30px;
      height: 30px;
    }
    span {
      font-size: 12px;
      color: #666;
    }
  }
}
</style>
