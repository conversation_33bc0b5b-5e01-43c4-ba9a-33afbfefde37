<template>
	<div class="broadcasting">
		<cube-slide
			v-if="infoDataLimited.length"
			:data="infoDataLimited"
			:interval="3000"
			:showDots="infoDataLimited.length>1"
			:auto-play="true" @change="changePage" class="swipers" ref="slide" allowVertical>
			<cube-slide-item :key="indexs" v-for="(item, indexs) in infoDataLimited">
				<div class="livebroadcasting" @click="clickHandler(item)">
					<p class="ing_live">正在直播</p>
					<div class="icon_live">
						<div class="liveing">
							<img src="@/assets/img/liveIng.gif" />
							<span>直播中</span>
						</div>
						<div class="signUpNow" >
							马上报名
							<div class="circular">
								<img src="@/assets/img/live_view.png" />
							</div>
						</div>
					</div>
					<infoRecommend :info='item' :iflast='false' :showTag='true' :styleType="'right'"></infoRecommend>
				</div>
			</cube-slide-item>
			<template slot="dots" slot-scope="props">
				<span
				:class="{active: props.current === index,myDot:configuration.id}" v-for="(item, index) in props.dots">{{index + 1}}</span>
			</template>
		</cube-slide>
		<!-- <div class="dots" v-if="infoDataLimited.length">
				<span :class="currentIndex == index?'active':''" v-for="(item,index) in infoDataLimited"></span>
		</div> -->
	</div>
</template>

<script>
import infoRecommend from '@/components/medicine/infoRecommend.vue'

import { getLiveInProgressList } from '@/api/medicine/homePage'
export default {
    components: {
        infoRecommend,
    },
    props: ['configuration'],
    data () {
        return {
            infoDataLimited: [],
						currentIndex: 0,
						configurationParam: {},
        };
    },
    computed: {
    },
    watch: {
			configuration: {
					handler (val) {
						this.getLiveInProgressList()
	          this.configurationParam = this.configuration.param?JSON.parse(this.configuration.param):{};
					},
					deep: true,
					immediate: true
			}
    },
    mounted () {
    },
    methods: {
			getLiveInProgressList() {
				let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
				getLiveInProgressList(params).then(res=>{
						if(res.code == this.$successCode){
							this.infoDataLimited = res.data
						}else{
							this.$message.error(res.info);
						}
				})
			},
			changePage (index) {
					this.currentIndex = index
			},
			clickHandler(item){
						// if (item.itemType=="activity") {
						// 	item.id = item.activityId
						// 	this.naviToDetails(this.$route.query.platformId, item.itemType, item)
						// } else {
							this.naviToDetails(this.$route.query.platformId, item.itemType, item)
						// }
			},
    }
}
</script>

<style lang='scss' scoped>
	.broadcasting{
		margin-bottom: 20px;
	}
	.livebroadcasting{
		background: url('../../../../assets/img/livebroadcasting_setting.png');
		background-size: cover;
    margin: 0px 15px 0;
		padding: 10px 0 10px 10px;
		height: 194px;
		color: #333333;
		.ing_live{
			font-size: 16px;
			/* font-weight: 500; */
			font-weight: bold;
			line-height: 22px;
		}
		.icon_live{
			margin-top: 15px;
			height: 32px;
			display: flex;
			justify-content: space-between;
			padding-right: 10px;
			.liveing{
				width: 62px;
				height: 24px;
				background: #F02617;
				border-radius: 2px;
				color: #fff;
				line-height: 24px;
				padding-left: 0px;
				font-size: 12px;
				display: flex;
				img{
					height: 22px;
					/* margin-right: 3px; */
					width: auto;
				}
				>span{
					margin-left: -2px;
				}
			}
			.signUpNow{
				width: 109px;
				height: 32px;
				line-height: 32px;
				font-size: 14px;
				padding-left: 15px;
				border-radius: 16px;
				background-color: #1890FF;
				color: #fff;
				.circular{
					width: 32px;
					height: 32px;
					border-radius: 50%;
					background-color: rgba(255, 255, 255, 0.3);
					float: right;
					img{
						width: 16px;
						margin: 10px 8px;
					}
				}
			}
		}
	}
</style>
<style lang='scss'>
	.livebroadcasting{
		.item-body{
			background-color: transparent;
			box-shadow:none;
			padding: 0;
		}
		.item-right-container{
			margin-right: 5px;
			width: 196px;
		}
		.infoRecommend-detail{
			white-space: initial;
		}
	}
	.broadcasting{
		.cube-slide-dots{
			bottom: 10px !important;
		}
		.cube-slide-dots .myDot.active {
		    height: 3px;
		    width: 11px;
		    background-size: 100% 100%;
		    margin: 0px 2px;
		    background-color: #00629F;
				border-radius: 2.5px;
		}
		.cube-slide-dots .myDot {
		    height: 3px;
		    width: 4px;
		    background-size: 100% 100%;
		    margin: 0px 2px;
		    background: rgba(0, 98, 159, 0.5);
				border-radius: 2.5px;
		}
	}
</style>
