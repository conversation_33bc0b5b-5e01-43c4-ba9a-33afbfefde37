<template>
    <!--资讯列表展示 -->
    <div class="information">
        <div style="height: 35px;border-radius: 6px;overflow: hidden;">
            <cube-slide :showDots="false" :auto-play="!!(configurationParam.param&& configurationParam.param!='0'&& configurationParam.param!='')" :interval="configurationParam.param*1000" v-if="contentList.length" :data="contentList" class="swipers" allowVertical :direction="'vertical'" ref="slideImage">
                <cube-slide-item class="swiperItem-content" :key="indexs" @click.native="clickImage(item, indexs)" v-for="(item, indexs) in contentList">
                    <!-- <a :href="item.targetUrl"> -->
                    <div class="newList">
                        <img :src="require('./img/horn.png')" style="" class="horn" />
                        <!-- <i class="icon iconfont icon-bianzu"></i> -->
                        <p class="detail">{{item.content}}</p>
                        <!-- <div class="blue"></div> -->
                    </div>
                    <!-- </a> -->
                </cube-slide-item>
            </cube-slide>
        </div>
    </div>
</template>

<script>
import 'swiper/dist/css/swiper.css'
import { swiper, swiperSlide } from 'vue-awesome-swiper'
import { getCarouselList } from '@/api/medicine/homePage'
export default {
    name: "information",
    props: ['configuration'],
    data () {
        return {
            contentList: [],
            swiperOption: {
                // 所有的参数同 swiper 官方 api 参数
                direction: 'vertical',
                autoplay: {
                    delay: 0,
                    stopOnLastSlide: false,
                    disableOnInteraction: true,
                },// 可选选项，自动滑动
                // effects: {
                // 	effect: 'cube'
                // }
            },
            configurationParam: {}
        }
    },
    watch: {
        configuration: {
            handler (val) {
                this.getContentList()
                this.configurationParam = this.configuration.param ? JSON.parse(this.configuration.param) : {};
            },
            deep: true,
            immediate: true
        }
    },
    components: {
        swiper,
        swiperSlide
    },
    mounted () {
        this.swiperOption.autoplay.delay = this.contentList.param
    },
    methods: {
        getContentList () {
            let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
            getCarouselList(params).then(res => {
                if (res.code == this.$successCode) {
                    this.contentList = res.data
                } else {
                    this.$message.error(res.info);
                }
            })
        },
        clickImage (item, indexs) {
            if (item.targetUrl != '') {
                window.location.href = item.targetUrl
            }
        },
    }
}
</script>

<style scoped lang="scss">
.information {
    width: 100%;
    margin: 0px 0 20px;
    padding: 0 15px;
    .newList {
        position: relative;
        height: 35px;
        line-height: 35px;
        width: 100%;
        color: #333333;
        font-size: 13px;
        display: flex;
        margin: 0;
        background: #fff;
        border-radius: 6px;
        overflow: hidden;
        .horn {
            display: inline-block;
            line-height: 35px;
            width: 17px;
            height: 16px;
            margin: 9px 9px 9px 14px;
        }
        .detail {
            margin: 0px;
            display: inline-block;
            width: 290px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
        .blue {
            position: absolute;
            top: 0;
            right: 0px;
            width: 180px;
            height: 35px;
            background-color: rgba(170, 213, 255, 0.1);
            // border-radius: 6px;
        }
    }
}
</style>
<style lang="scss">
.swiper-slide {
    height: 35px;
}
</style>
