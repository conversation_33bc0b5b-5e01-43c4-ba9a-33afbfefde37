<template>
  <div class="tags-container">
    <scroll :scrollX="true" ref="scroll" :data="liveType" style="overflow: hidden">
      <div class="tags">
        <div v-for="(item, index) of liveType" class="liveTypeList" @click="toList(item.codeValueId)">
          <img :src="item.remark" class="liveType" />
          <div class="live_type_name">
            {{ item.codeValueDesc }}
          </div>
        </div>
      </div>
    </scroll>
  </div>
</template>

<script>
// import {  } from '@/api/';
import { getList } from "@/api/medicine/homePage";
import scroll from "@/components/common/scroll";
export default {
  props: ["configuration"],
  data() {
    return {
      liveType: [
        {
          name: "热点直播",
        },
        {
          name: "系列课程",
        },
        {
          name: "在线访谈",
        },
        {
          name: "项目路演",
        },
        {
          name: "园区行",
        },
      ],
      configurationParam: {},
    };
  },

  created() {},

  components: {
    scroll,
  },

  computed: {},
  watch: {
    configuration: {
      handler(val) {
        this.getList();
        this.configurationParam = this.configuration.param ? JSON.parse(this.configuration.param) : "";
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},

  methods: {
    getList() {
      let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      getList(params).then((res) => {
        if (res.code == this.$successCode) {
          //闪光新发布，园区行不要
          this.liveType = res.data.filter((item) => {
            return item.codeValueId != "root_system_info_medical_live_type_publish_flash" && item.codeValueId != "root_system_info_medical_live_type_publish_parkline";
          });
        } else {
          this.$message.error(res.info);
        }
      });
    },
    toList(codeValueId) {
      console.log(this.configuration);
      let query = {};
      query[this.configuration.extraInfo.type] = codeValueId;
      this.$router.push({
        name: "medicineSearch",
        query: Object.assign({ modelType: this.configuration.extraInfo.model }, query),
      });
    },
  },
};
</script>
<style scoped lang="scss">
.tags-container {
  width: 100%;
  // overflow-x: auto;
  white-space: nowrap;
  padding: 0 0 0 15px;
  // background: #FFFFFF;
}
.tags {
  width: 675px;
  overflow-y: scroll;
  display: flex;
  margin-bottom: 20px;
  .liveTypeList {
    width: 68px;
    height: 50px;
    border-radius: 4px;
    background-color: #f5f5f5;
    position: relative;
    margin-right: 6px;
    .liveType {
      display: block;
      width: 16px;
      height: 16px;
      margin: 7px auto 6px;
    }
    .live_type_name {
      color: #333333;
      font-size: 12px;
      line-height: 14px;
      text-align: center;
    }
    .type_right {
      width: 10px;
      float: right;
      margin-top: 5px;
    }
  }
}
</style>
