<template>
    <div class="userInfo-container" :class="{'userInfo-container-car':$route.query.platformId == 5}">
        <div class="userInfo-content flex-middle" @click="jumpInfo">
            <img :src="personProfile.headImg?personProfile.headImg:require('@/assets/img/default.png')" class="userInfo-avater">
            <div class="userInfo-info" v-if="loginStatus">
                <div class="userInfo-info-name lineElipsis">{{personProfile.memberName}}</div><span class="user-info-memberPoints" v-if="this.$route.query.platformId==3"><img src="@/assets/img/memberPoints.png" alt=""><span>积分：{{personProfile.memberPoints==null||personProfile.memberPoints==''?0:personProfile.memberPoints}}</span></span>
                <div class="userInfo-info-company lineElipsis">{{personProfile.company}}</div>
                <div class="userInfo-info-company lineElipsis">{{personProfile.position}}</div>
            </div>
            <div v-else class="userInfo-login unit" @click.stop="pageLogin">
                点击登录/注册
            </div>
            <div class="userInfo-info-progress-nav">
                <svg t="1623205766137" class="cubeic-arrow icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4496" width="200" height="200">
                    <path d="M307.2 993.28c-10.24 0-30.72 0-40.96-10.24-20.48-20.48-20.48-51.2 0-71.68L665.6 512 266.24 112.64C245.76 92.16 245.76 61.44 266.24 40.96s51.2-20.48 71.68 0l430.08 430.08c20.48 20.48 20.48 51.2 0 71.68L348.16 983.04c-10.24 10.24-30.72 10.24-40.96 10.24z" fill="#666666" p-id="4497"></path>
                </svg>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'userInfo',
    props: [],
    data () {
        return {}
    },
    computed: {
        personProfile () {
            return this.$store.state.personal.personProfile;
        },
        loginStatus () {
            return this.$store.state.loginStatus;
        }
    },
    mounted () {
        this.$store.dispatch('getPerprofile');
    },
    methods: {
        pageLogin () {
          this.$store.commit('changeLoginShow',true);
            //this.$store.state.loginShow = true;
        },
        jumpInfo(){
          if(this.loginStatus){
            this.$router.push({name:'userInfoSet'});
          }
        }
    }
}
</script>
<style lang="scss" scoped>
.userInfo-container {
    width: 100%;
    padding: 15px;
    .lineElipsis {
        white-space: nowrap;
        overflow-x: hidden;
        text-overflow: ellipsis;
    }
    .userInfo-content {
        padding: 21px 10px 18px;
        border-radius: 4px;
        background-image: url(../../../../assets/img/personal.png);
        border-radius: 4px;
        box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.1);
        position: relative;
        height: 120px;
        .userInfo-avater {
            width: 70px;
            height: 70px;
            margin-right: 15px;
            border-radius: 50%;
        }
        .userInfo-info {
            color: #333333;
            width: calc(100% - 85px - 16px);
            .userInfo-info-name {
                font-size: 17px;
                line-height: 25px;
                font-weight: 400;
                margin-bottom: 10px;
                display: inline-block;
                max-width: 150px;
            }
            .userInfo-info-company {
                font-size: 14px;
                line-height: 22px;
                margin-bottom: 6px;
            }
            .user-info-memberPoints {
                display: inline-block;
                width: max-content;
                height: 18px;
                background: rgba(255, 255, 255, 0.4);
                border-radius: 2px;
                vertical-align: top;
                margin-top: 2px;
                margin-left: 8px;
                padding: 2px 6px;
                span {
                    font-size: 12px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #666666;
                    margin-left: 2px;
                }
                img {
                    width: 12px;
                    height: 12px;
                    vertical-align: middle;
                }
            }
        }
        .userInfo-login {
            font-size: 18px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #ffffff;
        }
        .userInfo-info-progress-nav {
            // float: right;
            line-height: 100px;
            font-size: 10px;
            color: #333;
            .userInfo-info-progress-bg {
                width: 120px;
                height: 6px;
                border-radius: 3px;
                background-color: #eee;
                display: inline-block;
                .userInfo-info-progress {
                    width: 58%;
                    height: 100%;
                    background: linear-gradient(
                        90deg,
                        rgba(255, 128, 13, 1) 0%,
                        rgba(255, 98, 13, 1) 100%
                    );
                    border-radius: 3px;
                }
            }
            .cubeic-arrow {
                float: right;
                color: #333;
                height: 26px;
                width: 15px;
                // margin-top: 40px;
            }
        }
    }
    .userInfo-white-bottom {
        height: 60px;
        background-color: #fff;
        margin-top: -50px;
        border-radius: 5px 5px 0 0;
    }
    &.userInfo-container-car{
        .userInfo-content {
            background: linear-gradient( 180deg, #F5EEFF 0%, #FFFFFF 100%);
            .userInfo-login{
                color: #333;
            }
        }
    }
}
</style>