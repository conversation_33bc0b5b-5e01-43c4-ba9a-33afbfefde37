<!-- 往期直播的每一项 -->
<template>
    <div class='pastLiveItem' @click="linkUrl(itemData)">
        <img class="imgLive" :src="itemData.coverImg" alt="">
        <div class="past-text">
            {{itemData.name}}
        </div>
        <div class="past-bottom flex-left flex-middle">
            <div class="text unit">{{moment(itemData.beginTime,0,'/','/')}}</div>
            <div class="text lines1">{{itemData.sponsorName}}</div>
        </div>
    </div>
</template>

<script>
import { liveUrl } from '@/config/env';
export default {
    name: '',
    props: ['itemData'],
    data() {
        return {};
    },

    created() {},

    components: {},

    computed: {},

    mounted() {},

    methods: {
        linkUrl(itemData) {
            if (!this.isPreviewEnviroment) {
                let itemType = itemData.itemType == 'live' ? '/liveDetail' : '/seriesLive';
                let url;
                if (itemData.itemType == 'live') {
                    url = liveUrl + `${itemType}?liveId=${itemData.itemId}&platformId=3&bizId=${itemData.bizId}&domainType=1`;
                } else {
                    url = liveUrl + `${itemType}/${itemData.itemId}?platformId=3&bizId=${itemData.bizId}&domainType=1`;
                }
                window.location.href = url;
            }
        }
    }
}
</script>

<style scoped lang="scss">
.pastLiveItem {
    margin: 0 6px;
    height: 200px;
    background: #ffffff;
    border-radius: 4px;
    margin-bottom: 15px;
    width: 164px;
    .imgLive {
        width: 164px;
        height: 99px;
        border-radius: 6px 6px 0px 0px;
    }
    .past-text {
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
        line-height: 20px;
        margin-top: 9px;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        height: 60px;
        padding: 0 6px;
    }
    .past-bottom {
        margin-top: 5px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        padding: 0 6px;
    }
		.lines1{
			white-space: nowrap;
		}
}
</style>
