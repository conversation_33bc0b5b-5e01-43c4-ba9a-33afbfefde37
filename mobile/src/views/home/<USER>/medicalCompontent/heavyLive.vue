<template>
  <div class="heavyLive" v-show="infoData.length">
    <commonTitle :title="configuration.name" :sourceData="infoData" :isShowMore="!(lastLayoutId == configuration.layoutId && infiniteLoadLast == 1)" :headerStyle="{ padding: '0px' }" />
    <liveListStyle :list="infoData" :listStyle="param.listStyle" style="margin-top: 15px"></liveListStyle>
  </div>
</template>
<script>
import { getConferenceFront } from "@/api/medicine/homePage";
import liveListStyle from "./liveListStyle/liveListStyle";
import commonTitle from "./commonTitle.vue";
export default {
  components: {
    liveListStyle,
    commonTitle,
  },
  props: ["configuration", "lastLayoutId", "infiniteLoadLast"],
  data() {
    return {
      infoData: [],
      param: {},
    };
  },
  watch: {
    configuration: {
      handler(val) {
        this.param = JSON.parse(val.param);
        this.getConferenceFront();
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    async getConferenceFront() {
      const params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      const res = await getConferenceFront(params);
      if (res.code == this.$successCode) {
        this.infoData = res.data;
      } else {
        this.$toast(res.info);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.heavyLive {
  margin-bottom: 20px;
  padding: 0px 15px;
}
</style>
