<!-- 登录页面 -->
<template>
    <div class='loginWrap' v-show="loginShow">
        <div class="loginTop flex-middle flex-center">
            <img :src="wx_biz.loginLogo" alt="" :style="{'width':wx_biz.loginLogoWidth+'px','height':wx_biz.loginLogoHeight+'px'}">
        </div>
        <div class="loginmiddle"></div>
        <div class="contentFrom">
            <div class="loginForm">
                <div class="loginPhone flex-left flex-middle" v-if="!loginStatus">
                    <!-- <div class="loginPhoneLeft  flex-middle unit-0">
                        <img src="../assets/img/phone.png" alt="" class="icons">
                    </div> -->
                    <div class="numbers flex-middle flex-center">+86</div>
                    <div class="loginPhoneRight flex-middle unit">
                        <input @blur="changePhone" v-model="phoneNumber" type="tel" placeholder="请输入手机号" class="phoneInput">
                    </div>
                </div>
                <div v-else class="loginPhone flex-left flex-middle">
                    <!-- <div class="loginPhoneLeft  flex-middle unit-0">
                        <img src="../assets/img/email.png" alt="" style="height:70%">
                    </div> -->
                    <div class="loginPhoneRight flex-middle unit">
                        <input @blur="changePhone" v-model="email" type="email" placeholder="请输入邮箱" class="phoneInput">
                    </div>
                </div>
                <div class="loginCode flex-middle flex-left">
                    <!-- <div class="loginPhoneLeft  flex-middle unit-0">
                        <img src="../assets/img/lock.png" alt="" class="icons">
                    </div> -->
                    <div class="loginCodeLeft unit flex-middle">
                        <input type="number" placeholder="请输入验证码" class="phoneInput" v-model="code" @blur="changePhoneios">
                    </div>
                    <div class="loginCodeRight unit-0 flex-middle flex-center" @click="getCodes" v-if="isCode">获取验证码</div>
                    <div class="Countdown" v-else>{{time}}重新获取</div>
                </div>
            </div>
            <div @click="submit" class="loginBtn">登录</div>
            <div class="intructor" v-if="!loginStatus">
                非大陆手机请切换至港澳台界面登录
            </div>
            <div class="emailText" @click="changeLoginStatus">
                {{loginStatus?'大陆':'港澳台'}}用户登录
            </div>
        </div>
    </div>
</template>

<script>
import { validateCaptcha, getCaptcha, autoLogin } from '../../api/login';
import CONSTANT from '../../config/config_constant';
import setCookie from '../../utils/setCookie';
export default {
    name: 'login',
    data () {
        return {
            phoneNumber: '',
            email: '',
            code: "",
            isCode: true,
            time: 60,
            isPhoneCorrect: false,
            timer: null,
            loginStatus: 0,  // 0默认是手机号   1 邮箱
            isEmailError: false
        };
    },

    created () {
        // 这里要实现自动登录功能
        autoLogin({ platformId: this.platformId }).then((res) => {
            if (res.code == '001') {
                if (res.data) {
                    localStorage.setItem(CONSTANT.token, res.data);
                    setCookie('Authorization', res.data, 1);
                    this.loginShowFunction()
                    location.reload()
                    // 跳转返回的页面
                    // if (localStorage.getItem('currentPage') != '/login' && localStorage.getItem('currentPage')) {
                    //     this.$router.replace(localStorage.getItem('currentPage'));
                    // }
                }
            }
        })
    },

    components: {

    },

    computed: {
        // 获取logo图片 其实是地址
        wx_biz () {
            try {
                return JSON.parse(localStorage.getItem(CONSTANT.USERINFO)) 
            } catch(e){
                return {}
            }
        },
        loginShow () {
            return this.$store.state.loginShow
        }
    },

    mounted () { },

    methods: {
        async submit () {
            // 进行登录的操作
            let res = await validateCaptcha({ mobile: !this.loginStatus ? this.phoneNumber : this.email, captcha: this.code, from: `info_${this.platformId}` });
            if (res.code == '001') {
                localStorage.setItem(CONSTANT.token, res.data);
                setCookie('Authorization', res.data, 1);
                this.loginShowFunction()
                location.reload()
                // 跳转返回的页面
                // if (localStorage.getItem('currentPage' && !localStorage.getItem('currentPage').includes('register'))) {
                //     location.replace(localStorage.getItem('currentPage'))
                // } else {
                //     this.$router.go(-1)
                // }
            } else {
                console.log(res.info);
                this.$createToast({ txt: res.info, type: 'txt' }).show()
            }
        },
        // 获取验证码
        async  getCodes () {
            this.changePhoneios();
            if (!this.loginStatus) {
                if (this.isPhoneCorrect) {
                    this.cutTimer();
                } else {
                    this.$createToast({ txt: '请输入正确手机号', type: 'txt' }).show()
                }
            } else {
                if (this.isEmailError) {
                    this.cutTimer();
                } else {
                    this.$createToast({ txt: '请输入正确邮箱', type: 'txt' }).show()
                }
            }
        },
        changePhone () {
            if (!this.loginStatus) {
                let regPhone = /^(13[0-9]|14[5-9]|15[012356789]|166|17[0-8]|18[0-9]|19[8-9])[0-9]{8}$/;
                if (!regPhone.test(this.phoneNumber)) {
                    this.$createToast({ txt: '手机格式错误', type: 'txt' }).show()
                    this.isPhoneCorrect = false;
                } else {
                    this.isPhoneCorrect = true;
                }
            } else {
                let reg = /^[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*@[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*\.[a-z]{2,}$/;
                if (!reg.test(this.email)) {
                    this.$createToast({ txt: '邮箱格式错误', type: 'txt' }).show()
                    this.isEmailError = false;
                } else {
                    this.isEmailError = true;
                }
            }
            this.changePhoneios();
        },
        changeLoginStatus () {
            this.loginStatus = this.loginStatus ? 0 : 1;
            clearInterval(this.timer);
            this.isCode = true;
            this.time = 60;
            this.phoneNumber = "";
            this.email = '';
            localStorage.setItem('loginMethod', this.loginStatus);
        },
        async cutTimer () {
            let res = await getCaptcha({ "mobile": !this.loginStatus ? this.phoneNumber : this.email, from: `info_${this.platformId}` });
            if (res.code == '001' && res.data) {
                this.$createToast({ txt: '注意查收验证码', type: 'txt' }).show()
                this.isCode = false;
                clearInterval(this.timer);
                this.timer = setInterval(() => {
                    this.time--;
                    if (this.time == 0) {
                        clearInterval(this.timer);
                        this.isCode = true;
                        this.time = 60;
                    }
                }, 1000)
            } else {
                this.$createToast({ txt: res.info, type: 'txt' }).show();
            }
        },
        //ios底部返回兼容
        changePhoneios () {
            var u = navigator.userAgent,
                app = navigator.appVersion;
            var isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
            if (isIOS) {
                this.temporaryRepair();
            }
        },
    },
    destroyed () {
        clearInterval(this.timer);
        this.timer = null;
    }
}

</script>
<style  scoped>
.loginWrap {
    width: 100%;
    height: 100%;
}
.loginTop {
    width: 100%;
    height: 152px;
    background-image: linear-gradient(to bottom, #ffbd76 0%, #ff833f);
}
.loginmiddle {
    width: 100%;
    height: 53px;
    background: url("../../assets/img/bg.jpg") no-repeat;
    background-size: 100% 100%;
}
.contentFrom {
    padding: 0 20px;
    box-sizing: border-box;
    width: 100%;
}
.loginForm {
    width: 100%;
}
.imgLogin {
    width: 78px;
    height: 90px;
}
.hangjia_img {
    width: 118px;
    height: 35px;
}
.loginPhone {
    width: 100%;
    height: 60px;
    padding: 15px 0px;
    border-bottom: 1px solid #e7e7e7;
}
.loginPhoneLeft {
    height: 100%;
}
.loginPhoneRight {
    height: 100%;
    padding-left: 12px;
    box-sizing: border-box;
}
.phoneInput {
    border: none;
    outline: none;
    font-size: 16px;
    color: #5a5a5a;
    width: 100%;
}
.loginCode {
    width: 100%;
    height: 60px;
    padding: 15px 0px;
    border-bottom: 1px solid #e7e7e7;
}

.loginCodeLeft {
    height: 100%;
    padding-left: 12px;
    box-sizing: border-box;
}
.loginCodeRight {
    width: 100px;
    font-size: 16px;
    font-weight: 400;
    color: rgba(153, 153, 153, 1);
}
.loginBtn {
    margin-top: 30px;
}
.loginChangeEmail {
    color: #008bff;
    font-size: 15px;
    line-height: 50px;
}
.Countdown {
    color: #9b9b9b;
    font-size: 14px;
}
.loginBtn {
    background: #ff833f;
    border-radius: 50px;
    color: #fff;
    border: inherit;
    text-align: center;
    line-height: 50px;
    font-size: 16px;
}
.icons {
    width: 16px;
    height: 16px;
}
input::-webkit-input-placeholder {
    font-size: 16px;
    font-weight: 400;
    color: rgba(204, 204, 204, 1);
}
.intructor {
    margin-top: 20px;
    font-size: 15px;
    color: #7e7e83;
    width: 100%;
    text-align: center;
}
.emailText {
    color: #7e7e83;
    margin-top: 20px;
    width: 100%;
    text-align: center;
    font-size: 15px;
}
.numbers {
    width: 50px;
    color: #7e7e83;
    font-size: 15px;
    height: 100%;
    border-right: 1px solid #e7e7e7;
}
</style>

<style lang="scss">
.cube-toast .cube-popup-content {
    padding: 8px 35px !important;
    border-radius: 4px !important;
    background-color: rgba($color: #000000, $alpha: 0.6) !important;
}
</style>