<template>
    <div class="activity-iframe-container">
        <div class="activity-iframe">
            <iframe ref="iframe" src=""></iframe>
        </div>
        <div class="activity-iframe-footer" v-show="false">
            <div class="activity-iframe-platform-info" @click="goHome">
                <img :src="platformInfo.platformIcon">
                &nbsp;{{platformInfo.platformName}}
            </div>
            <div class="activity-iframe-footer-tools">
                <div @click="collect" class="collect-button">
                    {{collectStatus?'取消':''}}收藏
                </div>
                &nbsp;
                <span style="color:#d8d8d8">|</span>
                &nbsp;
                <div @click="toUserInfoPage">
                   {{platformId==3?'我的':'个人中心'}}
                </div>
                <div @click="toContactPage" v-show="contactStatus">
                    <svg width="18px" height="20px" viewBox="0 0 37 41" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
                        <title>编组</title>
                        <desc>Created with Sketch.</desc>
                        <g id="行家活动0507/我的收藏修改备份" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <g id="会议预告" transform="translate(-615.000000, -153.000000)" stroke="#666666" stroke-width="2">
                                <g id="编组-2" transform="translate(615.000000, 153.000000)">
                                    <g id="编组" transform="translate(1.000000, 1.000000)">
                                        <path d="M2.6484,14.8076 C2.6484,6.6296 9.2774,-0.0004 17.4564,-0.0004 C25.6344,-0.0004 32.2634,6.6296 32.2634,14.8076" id="Stroke-1"></path>
                                        <path d="M32.3076,24.8847 C32.3076,28.5157 30.2096,31.7597 26.9186,33.9037 C25.8226,34.6177 24.5926,35.2097 23.2636,35.6547" id="Stroke-3"></path>
                                        <path d="M2.7866,25.2304 L2.5976,25.2304 C1.1626,25.2304 -0.0004,24.0674 -0.0004,22.6324 L-0.0004,17.0594 C-0.0004,15.6254 1.1626,14.4614 2.5976,14.4614 L2.7866,14.4614 C4.2216,14.4614 5.3846,15.6254 5.3846,17.0594 L5.3846,22.6324 C5.3846,24.0674 4.2216,25.2304 2.7866,25.2304 Z" id="Stroke-5"></path>
                                        <path d="M32.4023,25.2304 L32.2133,25.2304 C30.7783,25.2304 29.6153,24.0674 29.6153,22.6324 L29.6153,17.0594 C29.6153,15.6254 30.7783,14.4614 32.2133,14.4614 L32.4023,14.4614 C33.8373,14.4614 35.0003,15.6254 35.0003,17.0594 L35.0003,22.6324 C35.0003,24.0674 33.8373,25.2304 32.4023,25.2304 Z" id="Stroke-7"></path>
                                        <path d="M24.231,35.6542 C24.231,37.1402 23.025,38.3452 21.539,38.3452 C20.052,38.3452 18.846,37.1402 18.846,35.6542 C18.846,34.1672 20.052,32.9622 21.539,32.9622 C23.025,32.9622 24.231,34.1672 24.231,35.6542 Z" id="Stroke-9"></path>
                                        <path d="M22.7505,22.7656 C21.5165,24.2706 19.6425,25.2306 17.5435,25.2306 C15.3435,25.2306 13.3895,24.1746 12.1615,22.5416" id="Stroke-11" stroke-linecap="round"></path>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </svg>
                </div>
                <div @click="toLiveList" v-show="liveStatus">
                    <svg width="20px" height="20px" viewBox="0 0 40 41" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
                        <title>编组</title>
                        <desc>Created with Sketch.</desc>
                        <defs>
                            <polygon id="path-1" points="0.6049 0.1974 25.8259 0.1974 25.8259 3 0.6049 3"></polygon>
                            <polygon id="path-3" points="0.2032 0.7846 18.418 0.7846 18.418 19 0.2032 19"></polygon>
                        </defs>
                        <g id="行家活动0507/我的收藏修改备份" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <g id="会议预告" transform="translate(-680.000000, -154.000000)">
                                <g id="编组-3" transform="translate(680.000000, 154.000000)">
                                    <g id="编组" transform="translate(1.000000, 1.000000)">
                                        <path d="M33.4308,28.0235 L2.9998,28.0235 C1.3438,28.0235 -0.0002,26.6805 -0.0002,25.0235 L-0.0002,3.0005 C-0.0002,1.3425 1.3438,0.0005 2.9998,0.0005 L33.4308,0.0005 C35.0878,0.0005 36.4308,1.3425 36.4308,3.0005 L36.4308,25.0235 C36.4308,26.6805 35.0878,28.0235 33.4308,28.0235 Z" id="Stroke-1" stroke="#666666" stroke-width="2"></path>
                                        <path d="M24.797,13.1924 L14.78,7.4084 C14.152,7.0464 13.368,7.4994 13.368,8.2234 L13.368,19.7904 C13.368,20.5144 14.152,20.9674 14.78,20.6054 L24.797,14.8224 C25.424,14.4604 25.424,13.5544 24.797,13.1924 Z" id="Stroke-3" stroke="#666666" stroke-width="2"></path>
                                        <g transform="translate(5.000000, 32.029900)">
                                            <mask id="mask-2" fill="white">
                                                <use xlink:href="#path-1"></use>
                                            </mask>
                                            <g id="Clip-6"></g>
                                            <path d="M24.4249,3.0004 L2.0059,3.0004 C1.2319,3.0004 0.6049,2.3724 0.6049,1.5994 C0.6049,0.8244 1.2319,0.1974 2.0059,0.1974 L24.4249,0.1974 C25.1989,0.1974 25.8259,0.8244 25.8259,1.5994 C25.8259,2.3724 25.1989,3.0004 24.4249,3.0004" id="Fill-5" fill="#666666" mask="url(#mask-2)"></path>
                                        </g>
                                        <path d="M29.3102,34.0303 C24.8402,34.0303 21.2032,30.3923 21.2032,25.9223 C21.2032,21.4513 24.8402,17.8143 29.3102,17.8143 C33.7812,17.8143 37.4182,21.4513 37.4182,25.9223 C37.4182,30.3923 33.7812,34.0303 29.3102,34.0303" id="Fill-7" fill="#FFFFFF"></path>
                                        <g transform="translate(20.000000, 16.029900)">
                                            <mask id="mask-4" fill="white">
                                                <use xlink:href="#path-3"></use>
                                            </mask>
                                            <g id="Clip-10"></g>
                                            <path d="M9.3102,0.7846 C4.2802,0.7846 0.2032,4.8616 0.2032,9.8916 C0.2032,14.9226 4.2802,19.0006 9.3102,19.0006 C14.3402,19.0006 18.4182,14.9226 18.4182,9.8916 C18.4182,4.8616 14.3402,0.7846 9.3102,0.7846 M9.3102,2.7846 C13.2292,2.7846 16.4182,5.9726 16.4182,9.8916 C16.4182,13.8116 13.2292,17.0006 9.3102,17.0006 C5.3912,17.0006 2.2032,13.8116 2.2032,9.8916 C2.2032,5.9726 5.3912,2.7846 9.3102,2.7846" id="Fill-9" fill="#666666" mask="url(#mask-4)"></path>
                                        </g>
                                        <path d="M28.089,27.7295 C28.371,28.0105 28.826,28.0105 29.109,27.7295 L31.148,25.6905 C31.429,25.4085 31.429,24.9525 31.148,24.6705 C30.865,24.3885 30.41,24.3885 30.128,24.6705 L28.089,26.7095 C27.807,26.9915 27.807,27.4475 28.089,27.7295 M29.618,28.2395 L28.599,29.2585 C28.036,29.8215 27.122,29.8215 26.56,29.2585 C25.997,28.6955 25.997,27.7825 26.56,27.2195 L27.579,26.2005 C27.861,25.9175 27.86,25.4615 27.579,25.1805 C27.297,24.8985 26.841,24.8985 26.56,25.1805 L25.54,26.2005 C24.414,27.3265 24.414,29.1525 25.54,30.2785 C26.666,31.4045 28.492,31.4045 29.618,30.2785 L30.638,29.2585 C30.92,28.9765 30.92,28.5205 30.638,28.2395 C30.356,27.9575 29.9,27.9575 29.618,28.2395 M29.618,22.1225 L28.599,23.1415 C28.317,23.4225 28.317,23.8785 28.599,24.1615 C28.88,24.4425 29.336,24.4425 29.618,24.1615 L30.638,23.1415 C31.2,22.5795 32.114,22.5795 32.677,23.1415 C33.239,23.7045 33.239,24.6185 32.677,25.1805 L31.657,26.2005 C31.375,26.4815 31.375,26.9375 31.657,27.2195 C31.939,27.5005 32.395,27.5005 32.677,27.2195 L33.696,26.2005 C34.822,25.0745 34.822,23.2485 33.696,22.1225 C32.57,20.9965 30.744,20.9965 29.618,22.1225" id="Fill-11" fill="#666666"></path>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </svg>
                </div>
            </div>
        </div>
        <!-- 易贸医疗 二维码弹框 -->
        <officialCodeMedicine v-if="platformId==3" style="bottom:0px;" :visiable.sync="officialCodeShow"></officialCodeMedicine>
        <!-- 行家 二维码弹框 -->
        <officialCode v-else style="bottom:0px;" :visiable.sync="officialCodeShow"></officialCode>
        <warnTip v-if="dialogInfo" :warnData='dialogInfo' @quit='dialogInfo = null' @confirm='confirmClick'></warnTip>
    </div>
</template>

<script>
import { checkCollect, checkLiveList, checkContect, collectActivity, getActivityUrl } from '@/api/activityIframe.js';
import officialCode from '@/components/newCommon/officialCode';
import officialCodeMedicine from '@/components/medicine/officialCode';
import warnTip from './components/warnTip'
import CONSTANT from '@/config/config_constant';

export default {
    name: 'activityIframe',
    data () {
        return {
            activityInfo: {}, // 活动详情
            iframeUrl: null, // iframe 路径
            platformInfo: JSON.parse(localStorage.getItem(CONSTANT.USERINFO)), // 公众号信息
            activityId: this.$route.query.activityId, // 会议id
            source: this.$route.query.source, // 来源
            collectStatus: false, // 是否已收藏
            liveStatus: false,
            contactStatus: false,
            collectDialogInfo: {
                content: '成功关注该会议，相关通知提醒将会通过' + JSON.parse(localStorage.getItem(CONSTANT.USERINFO)).platformName + '(公众号)下发',
                confirmBtn: '去公众号查看',
                cancleBtn: '暂时不'
            },
            focusDialogInfo: {
                content: '关注公众号，可及时接收私信通知提醒',
                confirmBtn: '去关注',
                cancleBtn: '暂时不'
            },
            officialCodeShow: false, // 关注二维码 是否显示
            dialogInfo: null,
            platformId: localStorage.getItem('platformId')
        }
    },
    components: { officialCode, officialCodeMedicine, warnTip },
    mounted () {
        this.getIframeUrl();
        this.checkAll();
        // 埋点
        this.enterExposure("activityIframe", this.activityId, "activity", [{ id: this.activityId, type: "activity" }]);
    },
    beforeRouteLeave (to, from, next) {
        this.computTimeDiff("activityIframe", this.activityId, 'activity', [{ id: this.activityId, type: 'activity' }]);
        next()
    },
    methods: {
        // 获取 iframe 路径
        getIframeUrl () {
            getActivityUrl({ activityId: this.$route.query.activityId, source: this.source, bizId: this.$route.query.bizId }).then(res => {
                if (res.code == this.$successCode) {
                    this.iframeUrl = res.data.conferenceUrl;
                    this.activityInfo = res.data;
                    this.$nextTick(() => {
                        //this.$refs.iframe.contentWindow.location.replace(this.iframeUrl)
                        window.location.replace(this.iframeUrl);
                    })
                }
            })
        },
        checkAll () {
            let params = {
                itemId: this.activityId,
                activityId: this.activityId,
                itemType: this.TYPE[0],
                source: this.source,
                bizId: this.activityInfo.bizId
            }
            // 查询 是否已收藏
            checkCollect(params).then(res => {
                if (res.code == this.$successCode) {
                    this.collectStatus = res.data;
                }
            })
            //查询是否存在相关直播
            checkLiveList(params).then(res => {
                if (res.code == this.$successCode) {
                    if (res.data > 0) {
                        this.liveStatus = true
                    }

                }
            })
            //查询是开启联系主办方
            checkContect(params).then(res => {
                if (res.code == this.$successCode) {
                    if (res.data == 1) {
                        this.contactStatus = true
                    }
                }
            })
        },
        // 收藏
        collect () {
            let params = {
                itemId: this.activityId,
                itemType: this.TYPE[0],
                source: this.source
            }
            collectActivity(params, this.collectStatus ? 0 : 1).then(res => {
                if (res.code == this.$successCode) {
                    this.collectStatus = !this.collectStatus;
                    if (this.collectStatus) {
                        //从缓存中获取是否第一次收藏
                        if (localStorage.getItem('collectedActivityList') && localStorage.getItem('collectedActivityList').includes(this.activityId)) {
                            //不是第一次
                            this.$createToast({
                                txt: '收藏成功',
                                type: 'txt'
                            }).show();
                        } else {
                            //第一次
                            this.toOfficialDialog('collect');
                            let tempArray
                            if (localStorage.getItem('collectedActivityList')) {
                                tempArray = localStorage.getItem('collectedActivityList').split(',')
                            } else {
                                tempArray = []
                            }
                            tempArray.push(this.activityId)
                            localStorage.setItem('collectedActivityList', tempArray)
                        }
                    } else {
                        this.$createToast({
                            txt: '取消收藏',
                            type: 'txt'
                        }).show();
                    }
                }
            })
        },
        // 去关注公众号  弹框
        toOfficialDialog (type) {
            this.dialogInfo = (type == 'collect' ? this.collectDialogInfo : this.focusDialogInfo);
        },
        confirmClick () {
            this.dialogInfo = null
            this.officialCodeShow = true
        },
        // 跳转线上直播列表
        toLiveList () {
            this.naviToCommonList(this.$route.query)
        },
        // 跳转 联系主办方 页面
        toContactPage () {
            // 这里跳转联系主办方页面
            this.$router.push({ name: 'contectSponsor', params: { activityId: this.activityId, source: this.source }, query: { platformId: this.platformId } })
        },
        toUserInfoPage () {
            this.$router.push({ name: 'home', query: { targetTab: this.platformId==3?'我的':'个人中心', local: true } })
        },
        goHome () {
            this.$router.push({ name: 'home', query: { targetTab: '首页', local: true } })
        }
    }
}
</script>
<style lang="scss">
$primaryColor: #ff620d;
.activity-iframe-container {
    width: 100%;
    height: 100%;
    position: relative;
    .activity-iframe {
        position: absolute;
        top: 45px;
        bottom: 0;
        left: 0;
        width: 100%;
        height: calc(100% - 45px);
        -webkit-overflow-scrolling: touch;
        overflow: auto;
        iframe {
            width: 100%;
            height: 100%;
        }
    }
    .activity-iframe-footer {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 45px;
        padding: 0 15px;
        background-color: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid #f2f2f2;
        .activity-iframe-platform-info {
            max-width: 40%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 14px;
            font-weight: 400;
            line-height: 30px;
            img {
                width: 24px;
                height: 24px;
                border-radius: 12px;
                vertical-align: middle;
                margin-top: -4px;
            }
        }
        .activity-iframe-footer-tools {
            color: #666;
            width: 60%;
            float: right;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-end;
            svg {
                margin-left: 15px;
            }
        }
    }
    .official-code {
        position: relative;
        padding: 20px;
        background-color: #fff;
        border-radius: 15px;
        text-align: center;
        .cubeic-close {
            position: absolute;
            right: 10px;
            top: 10px;
        }
        img {
            width: 200px;
            height: 200px;
        }
    }
}

.cube-toast .cube-popup-content {
    padding: 8px 35px !important;
    border-radius: 4px !important;
    background-color: rgba($color: #000000, $alpha: 0.6) !important;
}
</style>