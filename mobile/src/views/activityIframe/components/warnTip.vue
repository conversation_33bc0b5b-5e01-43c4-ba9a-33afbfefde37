<template>
    <div class="warnTipWrap flex-middle flex-center">
        <div class="warnMiddle">
            <div class="warnTop  flex-middle flex-end">
                <div @click="quit" style="position:absolute;right:0;">
                    <svg t="1589336189581" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2360" width="18" height="18">
                        <path d="M573.44 512.128l237.888 237.696a43.328 43.328 0 0 1 0 59.712 43.392 43.392 0 0 1-59.712 0L513.728 571.84l-247.872 247.872a44.672 44.672 0 0 1-61.568 0 44.672 44.672 0 0 1 0-61.568l247.872-247.872-237.952-237.824a43.328 43.328 0 0 1 0-59.648 43.392 43.392 0 0 1 59.712 0L511.872 450.56l246.272-246.272a44.672 44.672 0 0 1 61.568 0 44.672 44.672 0 0 1 0 61.568L573.44 512.128z" p-id="2361" fill="#dbdbdb"></path>
                    </svg>
                </div>
            </div>
            <div class="warnTitle">温馨提示</div>
            <div class="warnContent">{{warnData.content}}</div>
            <div class="btnGroup flex-left">
                <div class="unit flex-center">
                    <div class="quit flex-center flex-middle" @click="quit">{{warnData.cancleBtn}}</div>
                </div>
                <div class="unit flex-center">
                    <div class="retaket flex-center flex-middle" @click="confirm">{{warnData.confirmBtn}}</div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    props: ['warnData'],
    data () {
        return {
            // 是否字体剧中显示
            isStyleCenter: true
        };
    },
    components: {},
    computed: {
    },
    mounted () {

    },
    methods: {
        quit () {
            this.$emit('quit')
        },
        confirm () {
            this.$emit('confirm')
        }
    }
}
</script>

<style lang='scss' scoped>
.warnTipWrap {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    position: absolute;
    z-index: 250;
    left: 0;
    top: 0;
}
.warnMiddle {
    width: 280px;
    background: #fff;
    border-radius: 8px;
    padding: 8px;
    box-sizing: border-box;
}
.warnTop {
    width: 100%;
    height: 16px;
    position: relative;
}
.warnTitle {
    width: 100%;
    height: 24px;
    font-size: 17px;

    font-weight: 400;
    color: rgba(51, 51, 51, 1);
    line-height: 24px;
    text-align: center;
    margin-bottom: 12px;
}
.warnContent {
    width: 100%;
    padding: 0 12px;
    box-sizing: border-box;
    font-size: 15px;

    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    line-height: 24px;
    margin-bottom: 20px;
    text-align: center;
}
.quit {
    height: 40px;
    width: 110px;
    border-radius: 40px;
    border: 1px solid #ccc;
    color: rgba(102, 102, 102, 1);
}
.retaket {
    height: 40px;
    width: 110px;
    border-radius: 40px;
    background: var(--color-primary);
    color: #fff;
}
.btnGroup {
    height: 40px;
    margin-bottom: 12px;
}
</style>
