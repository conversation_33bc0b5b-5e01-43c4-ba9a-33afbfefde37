<template>
  <div class="my-collect-wrap">
    <detail-page ref="detailPage" :openIndex="openIndex" :hasSearch="true" :hasFilter="true" @onSubSelect="onSubSelect" :optionFilter="optionFilter" @tabClick="tabClick" @searchKeyWord="searchKeyWord">
      <template #filterItem>
        <commonScroll ref="commonScroll" v-for="(item, index) in optionFilter" :key="index" v-if="activeTab == index" :getListData="getListData" :listParams="index == 0 ? liveConfiguration : index == 1 ? productConfiguration : businessConfiguration">
          <template #default="slotProps">
            <collectLiveItem v-if="activeTab == 0" :info="slotProps.item"></collectLiveItem>
            <collectProductItem v-if="activeTab == 1" :info="slotProps.item"></collectProductItem>
            <collectExhibitorItem v-if="activeTab == 2" :info="slotProps.item"></collectExhibitorItem>
          </template>
        </commonScroll>
      </template>
    </detail-page>
  </div>
</template>
<script>
import DetailPage from "./components/detailPage";
import CommonScroll from "./components/detailPage/commonScroll";
import { getMyCollectListNew } from "@/api/userCenter";
import CollectLiveItem from "../../components/newCommon/collectLiveItem";
import CollectProductItem from "../../components/newCommon/collectProductItem";
import CollectExhibitorItem from "../../components/newCommon/collectExhibitorItem.vue";
export default {
  data() {
    return {
      optionFilter: [
        {
          name: "直播",
          hasFilter: false,
          subFilter: [
            {
              name: "直播活动",
              hasFilter: false,
            },
            {
              name: "直播产品服务",
              hasFilter: false,
            },
            {
              name: "直播间",
              hasFilter: false,
            },
          ],
        },
        {
          name: "产品",
          hasFilter: false,
        },
        {
          name: "商家",
          hasFilter: false,
        },
      ],
      // 开启选中索引从第几个开始
      openIndex: true,
      // 当前激活
      activeTab: 0,
      // 直播配置
      liveConfiguration: {
        itemType: "live",
        source: "enmore",
        keyword: "",
      },
      // 产品配置
      productConfiguration: {
        itemType: "product",
        source: "exhibitor",
        keyword: "",
      },
      // 商家配置
      businessConfiguration: {
        itemType: "shop",
        source: "exhibitor",
        keyword: "",
      },
      // 接口配置
      getListData: getMyCollectListNew,
    };
  },
  created() {},
  components: { DetailPage, CommonScroll, CollectLiveItem, CollectProductItem, CollectExhibitorItem },
  mounted() {},
  computed: {},
  methods: {
    tabClick(index) {
      if (this.activeTab == index) return;
      this.activeTab = index;
    },
    searchKeyWord(keyword) {
      this.liveConfiguration.keyword = keyword;
      this.productConfiguration.keyword = keyword;
      this.businessConfiguration.keyword = keyword;
      this.reload();
    },
    reload() {
      this.$nextTick(() => {
        this.$refs.commonScroll[this.activeTab].getList(true);
      });
    },
    onSubSelect(item, index) {
      switch (index) {
        case 0:
          this.liveConfiguration.itemType = "live";
          break;
        case 1:
          this.liveConfiguration.itemType = "product";
          break;
        case 2:
          this.liveConfiguration.itemType = "liveRoom";
          break;
      }
      this.reload();
    },
  },
};
</script>
<style scoped lang="scss">
.my-collect-wrap {
  width: 100%;
  height: 100%;
  :deep(.filter-content) {
    background: #f5f6fa !important;
  }
}
</style>
