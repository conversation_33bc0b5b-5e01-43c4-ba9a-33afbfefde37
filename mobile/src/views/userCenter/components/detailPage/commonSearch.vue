<!-- 页面公共查询 -->
<template>
  <div class="common-search-wrap">
    <div class="search-top">
      <cube-input v-model="keyWord" placeholder="请输入关键字" @input="searchKeyWord">
        <template #prepend>
          <i class="cubeic-search" style="color: #999"></i>
        </template>
      </cube-input>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      keyWord: "",
    };
  },
  mounted() {},
  computed: {},
  methods: {
    searchKeyWord() {
      console.log("回车", )
      this.$emit("searchKeyWord", this.keyWord);
    },
  },
};
</script>
<style scoped lang="scss">
.common-search-wrap {
  width: 100%;
  padding: 12px 15px 0;
  box-sizing: border-box;
  :deep(.search-top) {
    width: 100%;
    .cube-input {
      width: 100%;
      background: #f5f5f5;
      border-radius: 4px;
      padding-left: 15px;
      box-sizing: border-box;
      .cube-input-field {
        font-size: 14px;
        color: #999;
        font-weight: 400;
        padding: 7px 10px;
        border: none;
      }
    }
    .cube-input::after {
      border: none;
    }
  }
}
</style>
