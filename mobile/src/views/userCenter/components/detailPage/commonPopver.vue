<template>
  <div class="flex-middle filter-sub-content">
    <div class="filter-item-name">{{ subItem.name }}</div>
    <!-- svg 箭头 -->
    <svg class="svg-icon" aria-hidden="true" v-if="subItem.hasFilter">
      <use v-bind:xlink:href="'#icon-zhankai1'"></use>
    </svg>
    <div v-if="subItem.autoChange" class="filter-item-auto flex-vertical flex-center">
      <svg class="svg-icon-up" :class="{ 'svg-icon-up-active': subItem.value == 'asc' }" aria-hidden="true">
        <use v-bind:xlink:href="'#icon-zhankai1'"></use>
      </svg>
      <svg class="svg-icon-down" :class="{ 'svg-icon-up-active': subItem.value == 'desc' }" aria-hidden="true">
        <use v-bind:xlink:href="'#icon-zhankai1'"></use>
      </svg>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    subItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {};
  },
  mounted() {
    // 去掉svg 的填充色
    document.querySelector("#icon-zhankai1 path").removeAttribute("fill");
  },
  computed: {},
  methods: {},
};
</script>
<style scoped lang="scss">
.filter-sub-content {
  .filter-item-name {
    font-weight: 400;
    font-size: 13px;
    color: #666;
  }
  .svg-icon {
    width: 11px;
    height: 11px;
    fill: #ddd;
    margin-left: 3px;
  }
  .filter-item-auto {
    margin-left: 2px;
    .svg-icon-up {
      width: 10px;
      height: 10px;
      fill: #ddd;
      transform: rotate(180deg);
      margin-bottom: -3px;
    }
    .svg-icon-down {
      width: 10px;
      height: 10px;
      fill: #ddd;
    }
    .svg-icon-up-active {
      fill: var(--color-primary);
    }
  }
}
</style>
