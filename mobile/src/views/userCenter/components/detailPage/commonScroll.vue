<template>
  <cube-scroll ref="scroll" :data="sourceList" :options="options" @pulling-up="getListMore">
    <slot name="noData">
      <div v-if="!sourceList.length" class="no-data-tips">
        <img src="@/assets/img/no_data_icon.png" alt="" />
        <div>暂无数据</div>
      </div>
    </slot>
    <div v-for="(item, index) in sourceList" :key="index">
      <slot :item="item"></slot>
    </div>
  </cube-scroll>
</template>
<script>
export default {
  props: {
    getListData: Function,
    listParams: {
      type: Object,
      default: () => {
        return {};
      },
    },
    conversion: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      options: {
        // 上拉加载设置
        pullUpLoad: {
          threshold: 30,
          visible: false,
          txt: {
            more: "上拉加载更多",
            noMore: "没有更多信息啦！",
          },
        },
      },
      pageInfo: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      sourceList: [],
      // 是否加载完成
      hasLoadAll: false,
      // 参数
      scrollParams: {},
    };
  },
  created() {
    // 深拷贝
    this.scrollParams = JSON.parse(JSON.stringify(this.listParams));
    this.getList();
  },
  watch: {
    listParams: {
      handler(val) {
        this.scrollParams = JSON.parse(JSON.stringify(val));
      },
      deep: true,
    },
  },
  mounted() {},
  computed: {},
  methods: {
    getListMore() {
      if (!this.hasLoadAll) {
        this.pageInfo.pageNum++;
        this.getList();
      } else {
        this.$refs.scroll.forceUpdate();
      }
    },
    getList(clearStatus) {
      if (clearStatus) {
        this.pageInfo.pageNum = 1;
        this.sourceList = [];
        this.hasLoadAll = false;
      }
      let params = Object.assign({}, this.pageInfo, this.scrollParams);
      this.getListData(params).then((res) => {
        if (res.code == this.$successCode) {
          this.pageInfo.total = res.data.total;
          if (res.data.list && res.data.list.length && this.pageInfo.total > this.sourceList.length) {
            this.sourceList = this.sourceList.concat(res.data.list);
            if (this.conversion) {
              this.conversion(this.sourceList);
            }
          } else {
            this.hasLoadAll = true;
            console.log("没有更多数据了");
            this.$refs.scroll.refresh();
            this.$refs.scroll.forceUpdate();
          }
          this.$emit("handleNoData", !this.sourceList.length);
        } else {
          this.$toast(res.info);
        }
      });
    },
    // 数据转换
    // convertList() {
    //   this.sourceList.forEach((item, index) => {
    //     this.sourceList[index].id = item.itemId;
    //     this.sourceList[index].type = item.itemType;
    //     this.sourceList[index].conferenceType = item.itemTag;
    //   });
    // },
  },
};
</script>
<style scoped lang="scss">
.no-data-tips {
  > img {
    width: 185px;
    height: 142px;
  }
  > div {
    margin-top: 20px;
  }
  margin-top: 170px;
  text-align: center;
  color: #999;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-wrap: wrap;
}
</style>
