<template>
  <div class="common-filter-wrap flex-middle">
    <van-tabs v-model="activeTab" animated title-active-color="#065b98" color="#065b98" line-width="30px" line-height="2px" @click="tabClick">
      <van-tab ref="tabs" v-for="(item, index) in optionFilter" :key="index" :disabled="item.disable">
        <template #title>
          <span>{{ item.name }}</span>
          <van-popover :class="{ 'filter-popover': item.hasFilter }" v-model="showPopover" trigger="click" :actions="item.option" :offset="[-30, 8]" v-if="item.hasFilter">
            <div @click="onSelect(subItem)" class="filter-item-option" :class="{ 'filter-item-option-active': subItem.select }" v-for="(subItem, subIndex) in item.option" :key="subIndex">{{ subItem.text }}</div>
            <template #reference>
              <svg class="svg-icon" aria-hidden="true" v-if="item.hasFilter">
                <use v-bind:xlink:href="'#icon-screen'"></use>
              </svg>
            </template>
          </van-popover>
        </template>
        <div class="filter-sub-filter flex-left" v-if="item.subFilter">
          <div
            @click="filterSub(subItem, subIndex)"
            :class="{ 'filter-sub-filter-item-active': subIndex == tabListIndex[activeTab]}"
            class="filter-sub-filter-item unit flex-middle flex-center"
            v-for="(subItem, subIndex) in item.subFilter"
            :key="subIndex"
          >
            <van-popover v-model="subItem.showPopover" trigger="click" :actions="subItem.option" :offset="[0, 15]" v-if="subItem.hasFilter">
              <div @click="onSubSelect(deepItem)" class="filter-item-option" :class="{ 'filter-item-option-active': deepItem.select }" v-for="(deepItem, deepIndex) in subItem.option" :key="deepIndex">{{ deepItem.text }}</div>
              <template #reference>
                <common-popver :subItem="subItem"></common-popver>
              </template>
            </van-popover>
            <common-popver :subItem="subItem" v-else @click.native="selectSubItem(subItem, subIndex)"></common-popver>
          </div>
        </div>
        <div class="filter-content">
          <slot name="filterItem"></slot>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>
<script>
import commonPopver from "./commonPopver";
export default {
  props: {
    optionFilter: {
      type: Array,
      default: () => {
        return [];
      },
    },
    //开启索引
    openIndex: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 当前激活的tab 索引
      activeTab: 0,
      showPopover: false,
      // 是否全部加载完毕
      isAllLoaded: false,
      // 根据传入的数据动态生成下拉索引
      tabListIndex: [],
    };
  },
  created() {
    // 生成结构
    this.optionFilter.forEach((item, index) => {
      if (this.openIndex) {
        this.tabListIndex.push(index);
      } else {
        this.tabListIndex.push(index - 1);
      }
    });
  },
  mounted() {},
  computed: {},
  components: {
    commonPopver,
  },
  methods: {
    tabClick(index) {
      this.$emit("tabClick", index);
    },
    onSelect(action) {
      this.$emit("onSelect", action);
    },
    filterSub(item, index) {
      this.$set(this.tabListIndex, this.activeTab, index);
    },
    onSubSelect(item) {
      this.$emit("onSubSelect", item, this.tabListIndex[this.activeTab]);
    },
    selectSubItem(item, index) {
      this.$emit("onSubSelect", item, index);
    },
  },
};
</script>
<style scoped lang="scss">
.common-filter-wrap {
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: hidden;
  :deep(.van-tabs) {
    width: 100%;
    height: 100%;
    .van-tabs__wrap {
      height: 40px;
      box-shadow: 0px 1px 0px 0px rgba(238, 238, 238, 0.5);
      .van-tab {
        .van-tab__text {
          display: flex;
          align-items: center;
          .svg-icon {
            margin-left: 4px;
            width: 14px;
            height: 14px;
            fill: #999;
            margin-top: 4px;
          }
        }
      }
      .van-tab--disabled {
        color: #666;
      }
      .van-tab--active {
        .van-tab__text {
          .svg-icon {
            fill: var(--color-primary);
          }
        }
      }
    }
    .van-tabs__content {
      height: calc(100% - 40px);
      .van-tab__pane {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        .filter-sub-filter {
          width: 100%;
          height: 40px;
          background: #ffffff;
          box-shadow: 0px 1px 0px 0px rgba(238, 238, 238, 0.5);
          margin-top: 1px;
          .filter-sub-filter-item {
          }
          .filter-sub-filter-item-active {
            .filter-item-name {
              color: var(--color-primary);
            }
            .svg-icon {
              fill: var(--color-primary);
            }
          }
        }
        .filter-content {
          flex: 1;
          min-height: 0;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.van-popover__content {
  .van-popover__action {
    width: auto !important;
    min-width: 100px;
  }
  .filter-item-option {
    min-width: 100px;
    padding: 0 10px;
    height: 32px;
    font-weight: 400;
    font-size: 14px;
    color: #999;
    line-height: 32px;
    text-align: center;
    text-shadow: 0px 6px 16px rgba(0, 0, 0, 0.08);
    &:last-child {
      margin-bottom: 6px;
    }
  }

  .filter-item-option-active {
    color: var(--color-primary);
  }
}
</style>
