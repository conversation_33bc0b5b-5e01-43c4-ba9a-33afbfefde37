<template>
  <div class="detail-page-wrap flex-vertical" >
    <common-search v-if="hasSearch" class="unit-0" @searchKeyWord="searchKeyWord" />
    <common-filter :openIndex="openIndex" ref="commonFilter" v-if="hasFilter" :optionFilter="optionFilter" @tabClick="tabClick" @onSelect="onSelect" @onSubSelect="onSubSelect" class="unit">
      <template #filterItem>
        <slot name="filterItem"></slot>
      </template>
    </common-filter>
  </div>
</template>
<script>
import commonSearch from "./commonSearch";
import commonFilter from "./commonFilter";
export default {
  props: {
    // 是否有查询选项
    hasSearch: {
      type: Boolean,
      default: true,
    },
    // 是否有筛选列
    hasFilter: {
      type: Boolean,
      default: true,
    },
    // 是否有筛选列
    optionFilter: {
      type: Array,
      default: () => {
        return [];
      },
    },
    //开启索引
    openIndex: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
    };
  },
  components: { commonSearch, commonFilter },
  mounted() {},
  computed: {},
  methods: {
    tabClick(index) {
      this.$emit("tabClick", index);
    },
    onSelect(action) {
      this.$emit("onSelect", action);
    },
    searchKeyWord(keyWord) {
      this.$emit("searchKeyWord", keyWord);
    },
    onSubSelect(item,index) {
      this.$emit("onSubSelect", item, index);
    },
  },
};
</script>
<style scoped lang="scss">
.detail-page-wrap {
  width: 100%;
  height: 100%;
}
</style>
