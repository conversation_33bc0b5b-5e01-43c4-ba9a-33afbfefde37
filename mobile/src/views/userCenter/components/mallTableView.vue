<template>
    <div class="bot_recome_body">
        <div class="top_img_box" v-for="item in newList" @click="jumpBtn(item)">
            <div class="img_box_item">
                <img :src="item.logo" alt="">
            </div>
            <p class="hui_color">{{ item.productName }}</p>
            <p class="red_color">
                <span class="price_font">{{ item.productPriceCoin }}<span>易币</span></span>
                <span class="old_price_font" v-if="item.dashingCoinStatus">{{ item.dashingDescribeCoin }}易币</span>
            </p>
            <img class="posi_ab_class" v-if="item.productType == '虚拟商品'" src="@/assets/img/xu_icon.png" alt="">
            <img class="posi_ab_class" v-if="item.productType == '实物商品'" src="@/assets/img/shi_icon.png" alt="">
        </div>
    </div>
</template>



<script>
import moment from 'moment';
export default {
    props: ["newList"],
    data(){
        return {
            
        }
    },
    watch: {
        
    },
    components: {
    },
    mounted(){
    },
    methods: {
        // 跳转商品详情页
        jumpBtn(e) {
            const productId = e.productId;
            this.$router.push({
                path: `/mall/goods/${productId}`,
            });
        },
    },
}
</script>



<style lang='scss' scoped>
.bot_recome_body {
    margin-top: 12px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .top_img_box {
        width: 170px;
        // padding: 8px;
        background-color: #FFFFFF;
        margin-bottom: 10px;
        position: relative;
        border-radius: 4px;
        padding-bottom: 10px;
        .img_box_item {
            width: 171px;
            height: 170px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 4px 4px 0 0;
            img {
                border-radius: 4px 4px 0 0;
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }
        .posi_ab_class {
            position: absolute;
            top: 6px;
            right: 6px;
            width: 56px;
            height: 16px;
        }
    }
    .hui_color {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        text-align: left;
        font-style: normal;
        margin-top: 8px;
        height: 40px; /* 两行的高度 */
        white-space: normal; /* 确保文本可以折行 */
        overflow: hidden; /* 隐藏超出的内容 */
        display: -webkit-box; /* 使用弹性盒子模型 */
        -webkit-line-clamp: 2; /* 限制显示的行数为2行 */
        -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
        line-height: 20px; /* 行高 */
        text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
        padding: 0px 8px;
    }
    .red_color {
        font-weight: 500;
        font-size: 16px;
        color: #FF4D4F;
        text-align: left;
        font-style: normal;
        margin-top: 8px;
        padding: 0px 8px;
        .price_font {
            font-weight: 500;
            font-size: 16px;
            color: #FF4D4F;
            line-height: 22px;
            text-align: left;
            font-style: normal;
            span {
                font-size: 12px;
            }
        }
        .old_price_font {
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 16px;
            text-align: left;
            font-style: normal;
            text-decoration-line: line-through;
            margin-left: 6px;
        }
    }
}
.que_class {
    width: 100%;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    // background-color: #FFFFFF;
    img {
        width: 120px;
    }
    p {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        text-align: left;
        font-style: normal;
        margin-top: 16px;
    }
}
.table_scroll {
    margin-top: 12px;
    max-height: 90vh;
    overflow-y: scroll;
    // ::v-deep .cube-scroll-list-wrapper {
    //     display: flex;
    //     flex-wrap: wrap;
    //     justify-content: space-between;
    // }
    // ::v-deep .cube-scroll-list-wrapper {
    //     min-height: fit-content !important;
    // }
}
</style>