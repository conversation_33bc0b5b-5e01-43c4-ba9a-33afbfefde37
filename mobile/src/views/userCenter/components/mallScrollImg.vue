<template>
  <div class="member-user-banner-wrap" v-if="bannerInfo.length && formData.status">
    <cube-slide :auto-play="formData.param.param == 0 ? false:true" :interval="formData.param.param * 1000" :data="bannerInfo" allowVertical ref="slideImage">
      <cube-slide-item class="swiperItem-content" :key="index" @click.native="openUrl(item.bannerInfo.url)" v-for="(item, index) in bannerInfo" >
        <img :src="item.bannerInfo.img" alt=""/>
      </cube-slide-item>
    </cube-slide>
  </div>
</template>
<script>
import { mapActions } from 'vuex';
import { getInfoByDynamicUrl } from "@/api/configurable/common";
export default {
  name: "memberUserBanner",
  props: ["configuration"],
  data() {
    return {
      bannerInfo: [],
      formData: {},
    };
  },
  created() {
    // 获取配置信息数据
    // this.getConferenceFront();
  },
  mounted() {
    this.getLayoutInfoList("banner");
  },
  computed: {
    getLayoutInfoBannerForm() {
      return this.$store.state.getLayoutInfoBannerForm;
    },
  },
  watch: {
    getLayoutInfoBannerForm: {
      handler() {
        this.$store.state.getLayoutInfoBannerForm.layoutInfoList.forEach(item => {
               if (item.modelType == "banner") {
                   this.bannerInfo = item.layoutContentRelList;
                   this.formData = item;
               }
        }   );
      },
      deep: true,
    },
  },
  methods: {
      ...mapActions(['getLayoutInfoList']),
    // 获取配置信息数据
    async getConferenceFront() {
      let res = await getInfoByDynamicUrl(this.configuration.apiUrl);
      if (res.code == this.$successCode) {
        this.bannerInfo = res.data || [];
      } else {
        this.$toast(res.info);
      }
    },
    openUrl(item) {
      window.open(item);
    },
  },
};
</script>
<style scoped lang="scss">
.member-user-banner-wrap {
  width: 100%;
  height: 85px;
  // padding: 0 15px;
  box-sizing: border-box;
  margin-top: 13px;
  img {
    height: 100% !important;
    width: auto !important;
    object-fit: cover;
  }
  :deep(.cube-slide-dots) {
    .active {
      background: var(--color-primary);
    }
    span{
      height: 2px;
      margin-bottom: 5px;
      margin-right: 2px;
    }
  }
}
.swiperItem-content {
  height: 100%;
  border-radius: 4px;
  img {
    border-radius: 4px;
  }
}
</style>
