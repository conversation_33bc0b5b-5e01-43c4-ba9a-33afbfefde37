/** 购买信息 底部弹窗 */
<template>
  <div class="purchase-info-wrap">
    <div class="purchase-info-title">{{ customInfo.label }}</div>
    <div class="purchase-info-content">{{ customInfo.value }}</div>
  </div>
</template>
<script>
import { getCustomInfo } from "@/api/monetary";
export default {
  props: {
    orderId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      customInfo: {},
    };
  },
  mounted() {
    this.getCustomInfo();
  },
  computed: {},
  methods: {
    async getCustomInfo() {
      const res = await getCustomInfo({ orderId: this.orderId });
      this.customInfo = res.data || {};
    },
  },
};
</script>
<style scoped lang="scss">
.purchase-info-wrap {
  width: 100%;
  padding: 20px 15px;
  box-sizing: border-box;
  min-height: 120px;
  border-top: 1px solid #eeeeee;
  .purchase-info-title {
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    line-height: 16px;
  }
  .purchase-info-content {
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 16px;
    margin-top: 12px;
  }
}
</style>
