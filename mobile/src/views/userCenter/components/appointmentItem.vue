<template>
  <div class="appointment-item-wrap" @click.stop="handleMergeAppointment(item.codeUrl)">
    <div class="appointment-item-content">
      <div class="item-top">
        <div class="item-top-content flex-left">
          <img class="item-top-left-img" :src="item.bannerImg" alt="" />
          <div class="item-top-left-text unit flex-vertical">
            <div class="item-top-left-text-title">{{ item.name }}</div>
            <div class="unit"></div>
            <div class="item-top-left-text-content flex-right">
              <div class="item-top-left-text-btn" v-if="!status">去约见</div>
              <span class="item-bottom-bottom-history" v-if="status == 1" @click.stop="handleMergeAppointment(item.itemUrl)">查看历史达成约见</span>
              <img @click.stop="handleMergeAppointment(item.itemUrl)" v-if="status == 1" src="https://oss.ienmore.com/frontUpload/partUpload/2025-03-12/dcb0576748383080c39990a5b1d0d805.png" alt="" />
            </div>
          </div>
        </div>
      </div>
      <div class="item-bottom">
        <div class="item-bottom-top flex-middle">
          <div class="item-bottom-top-left">
            <span>活动时间:</span>
            <span>{{ formatTime(item.beginTime, "YYYY-MM-DD HH:mm") }}</span>
          </div>
          <div class="unit"></div>
          <div class="item-bottom-top-right" v-if="status == 1">
            <span>活动地点: {{ item.address }}</span>
          </div>
        </div>
        <div class="item-bottom-bottom flex-middle" v-if="!status">
          <span>活动地点: {{ item.address || "--" }} </span>
          <span class="unit"></span>
          <!-- <span class="item-bottom-bottom-history" @click.stop="handleMergeAppointment(item.itemUrl)">查看历史达成约见</span>
          <img @click.stop="handleMergeAppointment(item.itemUrl)" src="https://oss.ienmore.com/frontUpload/partUpload/2025-03-12/dcb0576748383080c39990a5b1d0d805.png" alt="" /> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => {},
    },
    status: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {
    // 合并约见
    handleMergeAppointment(url) {
      window.location.href = url;
    },
  },
};
</script>
<style scoped lang="scss">
.appointment-item-wrap {
  width: 100%;
  margin-top: 12px;
  padding: 0 15px;
  box-sizing: border-box;
  .appointment-item-content {
    width: 100%;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #d9d9d9;
    padding: 9px;
    box-sizing: border-box;
    .item-top {
      padding-bottom: 4px;
      border-bottom: 1px dashed #e5e5e5;
      .item-top-content {
        .item-top-left-img {
          width: 105px;
          height: 63px;
          object-fit: contain;
          margin-right: 8px;
        }
        .item-top-left-text {
          .item-top-left-text-title {
            font-weight: 400;
            font-size: 13px;
            color: #333333;
            line-height: 16px;
            height: 32px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            margin-bottom: 4px;
            word-break: break-all;
          }
          .item-top-left-text-content {
            .item-top-left-text-btn {
              width: 66px;
              height: 27px;
              background: linear-gradient(231deg, #70e2f3 0%, #3378f7 100%);
              border-radius: 2px 2px 2px 2px;
              text-align: center;
              font-weight: 400;
              font-size: 12px;
              color: #ffffff;
              line-height: 27px;
            }
            img {
              width: 16px;
              height: 16px;
              margin-left: 2px;
            }
            .item-bottom-bottom-history {
              font-weight: 400;
              font-size: 13px;
              color: #097db4;
            }
          }
        }
      }
    }
    .item-bottom {
      margin-top: 4px;
      .item-bottom-top {
        margin-bottom: 2px;
        font-weight: 400;
        font-size: 11px;
        color: #666666;
        line-height: 16px;
      }
      .item-bottom-bottom {
        font-weight: 400;
        font-size: 11px;
        color: #666666;
        line-height: 16px;
        img {
          width: 16px;
          height: 16px;
          margin-left: 2px;
        }
        .item-bottom-bottom-history {
          font-weight: 400;
          font-size: 13px;
          color: #097db4;
        }
      }
    }
  }
}
</style>
