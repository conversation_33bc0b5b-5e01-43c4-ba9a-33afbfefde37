<template>
    <div :class="type == 'new' ? 'body_box':'body_two_box'" v-if="formData.status && newList.length">
        <div class="top_title">{{ formData.layoutName }}</div>
        <div class="img_mrec_body">
            <cube-slide :auto-play="false" :data="newList" allowVertical ref="slideImage" :slides-per-view="3">
              <cube-slide-item class="swiperItem_content" :key="index" v-for="(item, index) in newList" >
                <div class="img_mrec_item" v-for="(demo,desc) in item" @click="jumpBtn(demo)" :style="desc == 0 ? 'margin-left: 0;':''">
                    <div class="img_box">
                        <img :src="demo.logo" alt="">
                    </div>
                    <p class="hui_text">{{ demo.productName }}</p>
                    <p class="red_color">{{ demo.productPriceCoin }}易币</p>
                    <p class="hui_color" v-if="demo.dashingCoinStatus">{{ demo.dashingDescribeCoin }}易币</p>
                    <img class="posi_ab_class" v-if="demo.productType == 'physical'" src="@/assets/img/shi_icon.png" alt="">
                    <img class="posi_ab_class" v-if="demo.productType == 'virtual'" src="@/assets/img/xu_icon.png" alt="">
                </div>
              </cube-slide-item>
            </cube-slide>
        </div>
        
    </div>
</template>



<script>
import { mapActions } from 'vuex';
import moment from 'moment';
export default {
    props: ["title", "type"],
    data(){
        return {
            bannerInfo: [
                {

                },
                {

                },
                {

                },
            ],
            newList: [],
            formData: {},
        }
    },
    computed: {
        getLayoutInfoForm() {
            return this.$store.state.getLayoutInfoForm;
        },
    },
    watch: {
        getLayoutInfoForm: {
            handler() {
                this.$store.state.getLayoutInfoForm.layoutInfoList.forEach(item => {
                    if (item.modelType == this.type && item.webPlatform == 2 && Array.isArray(item.layoutContentRelList)) {
                        const groups = [];
                        for (let i = 0; i < item.layoutContentRelList.length; i += 3) {
                            item.layoutContentRelList.forEach((demo,desc) => {
                                item.layoutContentRelList[desc].logo = demo.logo ? demo.logo.split(",")[0]:[];

                            })
                            groups.push(item.layoutContentRelList.slice(i, i + 3));
                        }
                       this.newList = groups;
                       this.formData = item;
                       this.$forceUpdate();
                    }
            }   );
            },
            deep: true
        }
    },
    components: {
    },
    mounted(){
        this.getLayoutInfoList(this.type);
    },
    methods: {
      ...mapActions(['getLayoutInfoList']),
        // 跳转商品详情页
        jumpBtn(e) {
            const productId = e.itemId;
            this.$router.push({
                path: `/mall/goods/${productId}`,
            });
        }
    },
}
</script>



<style lang='scss' scoped>
.left_text {
    background-color: #fc6331;
    color: #FFFFFF;
    padding: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    width: 20px;
    height: 100%;
    flex-wrap: nowrap;
}
.body_box {
    // min-height: 242px;
    width: 100%;
    background: url("../../../assets/img/back_icon.png") no-repeat right center, 
                linear-gradient(180deg, #E0F1FF 0%, #FFFFFF 46%);
    background-size: 100% 100%;
    padding: 12px;
    border-radius: 4px;
}
.body_two_box {
    // min-height: 242px;
    width: 100%;
    background: url("../../../assets/img/back_icon.png") no-repeat right center, 
                linear-gradient( 180deg, #DBE2FF 0%, #FFFFFF 46%);
    background-size: 100% 100%;
    padding: 12px;
    border-radius: 4px;
}
.top_title {
    width: 100%;
    font-weight: 500;
    font-size: 15px;
    color: #333333;
    text-align: left;
    font-style: normal;
    margin-bottom: 12px;
}
.img_mrec_item {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #FFFFFF;
    border-radius: 4px;
    width: 32%;
    margin-bottom: 6px;
    position: relative;
    margin-left: 14px;
    padding-bottom: 3px;
    .posi_ab_class {
        position: absolute;
        right: 6px;
        top: 6px;
        height: 16px;
        width: 56px;
    }
    .img_box {
        width: 100px;
        height: 100px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        img {
            max-width: 100%;
            max-height: 100%;
            border-radius: 4px;
        }
    }
    .red_color {
        width: 84px;
        height: 28px;
        background: rgba(255,77,79,0.06);
        border-radius: 14px;
        font-weight: 500;
        font-size: 12px;
        color: #FF4D4F;
        text-align: left;
        font-style: normal;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 6px;
    }
    .hui_text {
        margin-top: 4px;
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        text-align: left;
        font-style: normal;
        white-space: normal; /* 确保文本可以折行 */
        overflow: hidden; /* 隐藏超出的内容 */
        display: -webkit-box; /* 使用弹性盒子模型 */
        -webkit-line-clamp: 1; /* 限制显示的行数为2行 */
        -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
        line-height: 20px; /* 行高 */
        text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
    }
}
.img_mrec_body {
    display: flex;
    ::v-deep .cube-slide-dots  {
        margin-top: 6px;
        span {
            width: 4px;
            height: 3px;
            background-color: #00629F;
            opacity: 0.5;
        }
    }
    ::v-deep .active {
        width: 11px !important;
        height: 3px;
        background-color: #00629F;
        opacity: 1 !important;
    }
}
.swiperItem_content {
    display: flex;
}
::v-deep .cube-slide-group {
    min-width: 100%;
}
::v-deep .cube-slide {
    min-width: 100%;
}
.hui_color {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-decoration-line: line-through;
    margin-top: 6px;
}
</style>