<template>
    <div class="body">
        <div class="top_search">
            <cube-input v-model="keyWord" placeholder="请输入关键字" @input="searchKeyWord" class="input_width" clearable>
                <template #prepend>
                    <img style="width: 15px;height: 15px;" src="@/assets/img/search_top_icov.png" alt="">
                </template>
            </cube-input>
            <p @click="canBtn">取消</p>
        </div>
        <botMearDataView class="botMearDataView" :scrollType="true"></botMearDataView>
    </div>
</template>



<script>
import { mapActions } from 'vuex';
import botMearDataView from "../components/botMearDataView.vue";
import moment from 'moment';
export default {
    data(){
        return {
            keyWord: "",
        }
    },
    computed: {
        mallPopKeyString() {
            return this.$store.state.mallPopKeyString;
        },
        pageMallForm() {
            return this.$store.state.pageMallForm;
        },
    },
    watch: {
        pageMallForm: {
            handler() {
                this.getProductInfoFun();
            },
            deep: true
        },
    },
    components: {
        botMearDataView,
    },
    mounted(){
        this.keyWord = this.mallPopKeyString;
        this.getProductInfoFun();
    },
    methods: {
        ...mapActions(['getLayoutInfoList', 'getProductInfoList']),
        // 取消
        canBtn() {
            this.$router.push({
                path: '/mall',
            });
        },
        // 输入
        searchKeyWord() {
            this.$store.commit("pageMallFun", {
                pageNum: 1,
                type: "rest",
            });
            this.$store.commit("mallPopKeyWordFun",this.keyWord)
            // this.getProductInfoFun();
        },
        getProductInfoFun() {
            let demo = {
                pageNum: this.pageMallForm.pageNum,
                pageSize: 10,
                keyword: this.keyWord,
            }
            this.getProductInfoList(demo);
        },
    },
}
</script>



<style lang='scss' scoped>
.body {
    background-color: #F5F6FA;
}
.top_search {
    display: flex;
    align-items: center;
    padding: 0px 13px;
    background-color: #FFFFFF;
    justify-content: space-between;
    height: 57px;
    :deep(.input_width) {
        width: calc(100% - 57px);
        padding: 0px 15px;
        background-color: #F5F5F5;
        border: 0px !important;
        width: 100%;
        margin-right: 15px;
        border-radius: 4px;
        border: 0px !important;
        ::v-deep .cube-input-field {
            height: 33px;
        }
        .cubeic-wrong{
            right: 14px;
            top: 10px;
        }
    }
    ::v-deep .cube-input_active::after {
        border-color: #FFF !important;
    }
    p {
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        text-align: left;
        font-style: normal;
        width: 35px;
    }
}
.botMearDataView {
    padding: 0px 12px;
    margin-top: 15px;
    ::v-deep .bot_recome_body {
        margin-top: 0px;
    }
    ::v-deep .table_scroll {
        margin-top: 0px;
    }
}
</style>