<template>
  <div class="business-card-wrap">
    <div class="business-text">可上传名片/工牌/企业微信，图片大小不超过5M，支持jpg、jpeg、png格式</div>
    <cube-upload accept="image/png, image/jpeg, image/jpg" @files-added="addedHandler" v-if="!imgValue" ref="upload" class="cube-upload-content" :action="action" :simultaneous-uploads="1" :max="1" @file-success="successLoad($event)"> </cube-upload>
    <div class="upload-content-img flex-middle" v-else>
      <img :src="imgValue" alt="" />
      <div class="icon-close flex-middle flex-center" @click="clearImg">
        <i class="iconfont iconguanbi"></i>
      </div>
    </div>
  </div>
</template>
<script>
import { uploadUrl } from '@/config/env'
export default {
  data() {
    return {
      action: uploadUrl,
      imgValue: '',
    }
  },
  watch: {
    $attrs(val) {
      this.imgValue = val.value
    },
  },
  mounted() {},
  computed: {},
  methods: {
    successLoad(res) {
      this.imgValue = res.response.data
      this.$root.$emit('changeUpload', this.imgValue)
    },
    clearImg() {
      this.imgValue = ''
    },
    addedHandler(files) {
      const maxSize = 5 * 1024 * 1024
      console.log(files)
      for (let k in files) {
        const file = files[k]
          if (file.size > maxSize) {
            this.$toast('选取文件过大,请重新选取')
            file.ignore = true
          }
      }
    },
  },
}
</script>
<style scoped lang="scss">
.business-card-wrap {
  overflow: hidden;
  margin-bottom: 10px;
  .business-text {
    font-size: 13px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #999999;
    line-height: 17px;
    margin: 10px 0;
  }
  :deep(.cube-upload-content) {
    .cube-upload-btn-def {
      width: 110px;
      height: 110px;
    }
    .cube-upload-file-def {
      width: 110px;
      height: 110px;
      background: #fff !important;
      border: 1px solid #eee;
      .cubeic-wrong {
        display: none;
      }
      .cube-upload-file_stat {
        display: none;
      }
    }
  }
  .upload-content-img {
    width: 110px;
    height: 110px;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #eeeeee;
    position: relative;
    overflow: hidden;
    img {
      width: 100%;
      height: auto;
    }
    .icon-close {
      position: absolute;
      right: 0;
      top: 0;
      z-index: 10;
      background: #000;
      width: 16px;
      height: 16px;
      i {
        font-size: 12px;
        color: #fff;
      }
    }
  }
}
</style>
