<template>
    <div>
        <div class="body">
            <img src="@/assets/img/order_icon.png" alt="">
            <div class="title_class">兑换成功！</div>
            <div class="text_body" v-if="type == '实物商品'">您兑换的实体商品已经成功提交，我们人员会尽快给您寄出，详询客服人员</div>
            <div class="text_body" v-if="type == '虚拟商品' && auditFlag == 0">您兑换的商品为虚拟商品，请在 <span class="blue_class" @click="seeInfoBtn">“易币商城→我的兑换历史”</span> 中查看</div>
            <div class="text_body" v-if="type == '虚拟商品' && auditFlag == 1">您兑换的商品为虚拟商品（待审核），待审核通过后可在 <span class="blue_class" @click="seeInfoBtn">“易币商城→我的兑换历史”</span> 中查看</div>
            <div class="btn_class" @click="seeInfoBtn">{{ type =="实物商品" ? "查看订单详情":"知道了" }}</div>
        </div>
    </div>
</template>



<script>
export default {
    data(){
        return {
            type: null,
            auditFlag: null,
        }
    },
    watch: {
        
    },
    components: {
    },
    mounted(){
        this.type = this.$route.query.type;
        this.auditFlag = Number(this.$route.query.auditFlag);
    },
    methods: {
        // 跳转路由事件
        seeInfoBtn() {
            this.$router.replace({
                path: '/mall/exchange',
            })
        },
    },
}
</script>



<style lang='scss' scoped>
.body {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 50px;
    img {
        width: 80px;
    }
}
.btn_class {
    width: 325px;
    height: 42px;
    background: linear-gradient( 90deg, #15B4DB 0%, #097DB4 100%);
    border-radius: 3px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 400;
    font-size: 15px;
    color: #FFFFFF;
    text-align: left;
    font-style: normal;
    margin-top: 50px;
}
.text_body {
    margin: 0px 55px;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    text-align: center;
    font-style: normal;
    margin-top: 15px;
    line-height: 22px;
}
.title_class {
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    text-align: right;
    font-style: normal;
    margin-top: 26px;
}
.blue_class {
    color: #097DB4;
}
</style>