<template>
  <div class="member-mission-center-middle-wrap" v-if="productInfo.id">
    <div class="middle-content">
      <mission-title title="每日浏览产品" desc="每日浏览产品可获得易币"></mission-title>
      <mission-product :info="productInfo"></mission-product>
    </div>
  </div>
</template>
<script>
import { getProductTask } from "@/api/memberApi";
import MissionTitle from "./component/missionTitle";
import MissionProduct from "./component/missionProduct";
export default {
  data() {
    return {
      // 直播任务
      productInfo: {},
    };
  },
  created() {
    this.getProductTask();
  },
  components: {
    MissionTitle,
    MissionProduct,
  },
  mounted() {},
  computed: {},
  methods: {
    async getProductTask() {
      let { data } = await getProductTask();
      if (data) {
        this.productInfo = data;
      }
      console.log("产品任务", data);
    },
  },
};
</script>
<style scoped lang="scss">
.member-mission-center-middle-wrap {
  width: 100%;
  padding: 12px 15px 0 15px;
  box-sizing: border-box;
  .middle-content {
    width: 100%;
    background: #ffffff;
    border-radius: 6px;
    padding: 15px 12px;
    box-sizing: border-box;
  }
}
</style>
