<template>
  <div class="member-mission-center-bottom-wrap">
    <div class="center-bottom-content">
      <mission-title title="新人任务" desc="全部任务易币仅可获得一次"></mission-title>
      <mission-item :item="item" v-for="(item, index) in taskList" :key="index" @handleClick="handleClick" :isNeedDisabled="true"></mission-item>
    </div>
    <!-- 关注公众号弹窗 -->
    <van-dialog @close="closeEmit" v-model="codeShow" class="mission_dia_body" :showConfirmButton="false">
      <companyCodeView v-if="codeShow" :settingForm="currentData" @closeEmit="closeEmit"></companyCodeView>
    </van-dialog>
  </div>
</template>
<script>
import { getNewTask, postTriggerPoints } from "@/api/memberApi";
import MissionItem from "./component/missionItem";
import MissionTitle from "./component/missionTitle";
import CompanyCodeView from "@/components/common/companyCodeView";
export default {
  data() {
    return {
      // 完成任务数据
      taskList: [],
      // 关闭弹窗
      codeShow: false,
      // 当前选择数据
      currentData: {},
      // 开启任务定时器
      taskTimer: null,
    };
  },
  created() {
    this.getNewTask();
    this.startTimer();
  },
  components: {
    MissionItem,
    MissionTitle,
    CompanyCodeView,
  },
  mounted() {},
  computed: {},
  methods: {
    // 开启30秒定时器
    startTimer() {
      clearInterval(this.taskTimer);
      this.taskTimer = setInterval(() => {
        this.getNewTask();
      }, 30000);
    },
    async getNewTask() {
      const { data } = await getNewTask();
      //console.log("新人任务", data);
      if (data && data.length) {
        this.taskList = data.map((item, index) => {
          return {
            ...item,
            btnName: this.getBtnName(item, index),
          };
        });
      }
    },
    getBtnName(item, index) {
      let name;
      if (index < 2) {
        if (item.status) {
          name = "已完成";
        } else {
          name = "去完善";
        }
      } else {
        if (item.status) {
          name = "已关注";
        } else {
          name = "去关注";
        }
      }
      return name;
    },
    closeEmit() {
      this.codeShow = false;
    },
    handleClick(item) {
      this.postTriggerPoints(item.ruleCode);
      let info = Object.assign(item, { serviceTips: item.expression, serviceQrcode: item.targetUrl });
      this.currentData = info;
      this.codeShow = true;
    },
    // 积分触发
    async postTriggerPoints(ruleCode) {
      let params = {
        platformId: this.$route.query.platformId || 3,
        ruleCode,
      };
      await postTriggerPoints(params);
      //console.log("积分触发", data);
    },
  },
  beforeDestroy() {
    clearInterval(this.taskTimer);
  },
};
</script>
<style scoped lang="scss">
.member-mission-center-bottom-wrap {
  width: 100%;
  padding: 0px 15px 24px 15px;
  margin-top: 15px;
  margin-bottom: 24px;
  box-sizing: border-box;
  height: 358px;
  .center-bottom-content {
    width: 100%;
    background-color: white;
    background-size: 100%;
    background-image: url("https://oss.ienmore.com/resources/public/img/health/new/quest_chest.png");
    background-repeat: no-repeat; /* 背景不重复 */
    background-position: top; /* 背景图像放置在顶部 */
    background-size: contain; /* 保持背景图像比例，不拉伸 */
    padding: 15px 12px;
    box-sizing: border-box;
    border-radius: 6px;
  }
  :deep(.van-dialog) {
    width: 260px;
  }
}
</style>
