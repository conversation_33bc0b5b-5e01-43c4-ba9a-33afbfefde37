<template>
  <div class="member-mission-center-middle-wrap" v-if="liveTaskInfo && liveTaskInfo.itemId">
    <div class="middle-content">
      <mission-title title="每日看直播" desc="每日进入直播间均可获得易币 (注册直播为前提)"></mission-title>
      <mission-live :info="liveTaskInfo"></mission-live>
    </div>
  </div>
</template>
<script>
import { getLiveTask } from "@/api/memberApi";
import MissionTitle from "./component/missionTitle";
import MissionLive from "./component/missionLive";
export default {
  data() {
    return {
      // 直播任务
      liveTaskInfo: null,
    };
  },
  created() {
    this.getLiveTask();
  },
  components: {
    MissionTitle,
    MissionLive,
  },
  mounted() {},
  computed: {},
  methods: {
    async getLiveTask() {
      let { data } = await getLiveTask();
      if (data) {
        this.liveTaskInfo = data;
      }
      console.log("直播任务", data);
    },
   
  },
};
</script>
<style scoped lang="scss">
.member-mission-center-middle-wrap {
  width: 100%;
  padding: 12px 15px 0 15px;
  box-sizing: border-box;
  .middle-content {
    width: 100%;
    background: #ffffff;
    border-radius: 6px;
    padding: 15px 12px;
    box-sizing: border-box;
  }
}
</style>
