<template>
  <div class="center-bottom-content-bottom">
    <div class="center-bottom-content-item flex-middle">
      <img :src="item.icon" alt="" />
      <div class="center-bottom-content-item-text unit">
        <div class="center-bottom-content-item-title">{{ item.expression }}</div>
        <div class="center-bottom-content-item-desc">{{ item.specificRule }}</div>
      </div>
      <div @click="handleClick" class="center-bottom-content-item-btn" :class="{ 'item-btn-disable': !!item.status && isNeedDisabled }">{{ item.btnName }}</div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => {},
    },
    // 是否需要显示禁止的按钮颜色状态
    isNeedDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {
    handleClick() {
      if (this.item.urlType === "url") {
        window.open(this.item.targetUrl, "_blank");
      } else {
        this.$emit("handleClick", this.item);
      }
    },
  },
};
</script>
<style scoped lang="scss">
.center-bottom-content-bottom {
  .center-bottom-content-item {
    img {
      width: 36px;
      margin-right: 10px;
      margin-bottom: 24px;
    }
    .center-bottom-content-item-btn {
      margin-bottom: 13px;
      width: 60px;
      height: 30px;
      background: linear-gradient(135deg, #ffa400 0%, #ff6b00 100%);
      border-radius: 2px;
      font-weight: 500;
      font-size: 12px;
      line-height: 30px;
      text-align: center;
      color: #ffffff;
      margin-left: 15px;
    }
    .item-btn-disable {
      background: #eeeeee;
      color: #999999;
    }
    .center-bottom-content-item-text {
      .center-bottom-content-item-title {
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        margin-bottom: 4px;
      }
      .center-bottom-content-item-desc {
        font-weight: 400;
        font-size: 11px;
        color: #868ea8;
        line-height: 14px;
        // css 2行省略
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        margin-bottom: 26px;
      }
    }
  }
  &:last-child {
    .center-bottom-content-item {
      img {
        margin-bottom: 6px;
      }
      .center-bottom-content-item-desc {
        margin-bottom: 0;
      }
      .center-bottom-content-item-btn {
        margin-bottom: 0;
      }
    }
  }
}
</style>
