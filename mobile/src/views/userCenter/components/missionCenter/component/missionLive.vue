<template>
  <div class="middle-content-bottom flex-left">
    <img class="middle-content-bottom-img" :src="info.coverImg" alt="" />
    <div class="middle-content-bottom-content unit">
      <div class="middle-content-bottom-title">{{ info.name }}</div>
      <div class="middle-content-bottom-text flex-bottom">
        <div class="middle-content-bottom-time">{{ formatTime(info.beginTime, "dateTime") }}</div>
        <div class="unit"></div>
        <div class="middle-content-bottom-btn flex-middle flex-center" :class="info.status == 1 ? 'middle-content-bottom-btn-disabled' : ''" @click="goLive">
          {{ info.status == 1 ? "已查看" : "立即观看" }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {
    goLive() {
      if (this.info.status != 1) {
        window.location.href = location.origin + `/mobile/#/zh-cn/liveDetail?liveId=${this.info.itemId}&platformId=3&bizId=3&domainType=1&ruleCode=${this.info.ruleCode}`;
      }
    },
  },
};
</script>
<style scoped lang="scss">
.middle-content-bottom {
  .middle-content-bottom-img {
    width: 120px;
    height: 72px;
    margin-right: 10px;
  }
  .middle-content-bottom-content {
    .middle-content-bottom-title {
      //  css 2行省略
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      height: 40px;
    }
    .middle-content-bottom-text {
      margin-top: 2px;
      .middle-content-bottom-time {
        font-weight: 400;
        font-size: 12px;
        color: #666666;
      }
      .middle-content-bottom-btn {
        width: 72px;
        height: 30px;
        background: linear-gradient(135deg, #ffa400 0%, #ff6b00 100%);
        border-radius: 2px;
        font-weight: 500;
        font-size: 12px;
        color: #ffffff;
        line-height: 30px;
      }
      .middle-content-bottom-btn-disabled {
        background: #eeeeee;
        color: #999999;
      }
    }
  }
}
</style>
