<template>
  <div class="member-mission-center-signIn">
    <div class="signIn-top flex-middle">
      <img src="https://oss.ienmore.com/resources/public/img/health/new/signin.png" alt="" />
      <div class="unit"></div>
      <div class="signIn-top-text">
        <span
          >已连续签到 <span class="signIn-top-text-num">{{ signInTime }}</span> / 7 天，</span
        >
        <span v-if="signInTime && signInTime < 7">再签到{{ 7 - signInTime }}天开宝箱</span>
        <span v-if="!signInTime">连续签到7天得更多易币</span>
      </div>
    </div>
    <div class="signIn-middle flex-middle">
      <div class="signIn-middle-item unit-1-7" v-for="(item, index) in signInList" :key="index" :class="{ 'signIn-middle-item-active': item.signInStatus == 2 }">
        <div class="signIn-middle-item-content flex-vertical">
          <div class="signIn-middle-item-top flex-center flex-middle">{{ index != 6 ? index + 1 + "天" : "开宝箱" }}</div>
          <div class="signIn-middle-item-bottom unit flex-center flex-middle flex-vertical">
            <img class="signIn-middle-item-img" :class="{'signIn-middle-item-img-last': item.signInStatus != 2 && index == 6}" :src="getSignInImg(item, index)" alt="" :style="{ 'marginTop': index == 6 &&  item.signInStatus != 2 ? '4px' : '' }"/>
            <span class="signIn-middle-item-txt" v-if="item.signInStatus == 2">已签+{{ item.points }}</span>
          </div>
          <div class="sign-in-for-7 flex-middle flex-center" v-if="index == 6">连签7天</div>
        </div>
      </div>
    </div>
    <div class="signIn-bottom">
      <div @click="handleSignIn" class="signIn-bottom-btn" :class="{ 'signIn-bottom-disable': signInInfo.signInStatus }">{{ !signInInfo.signInStatus ? "签到领易币" : "今日已签到，明日再来" }}</div>
    </div>
  </div>
</template>
<script>
import { getCenterSignInInfo, getSignInfo, userSignIn } from "@/api/memberApi";
import { platformApi } from "../../../../../config/env";
export default {
  data() {
    return {
      signInList: [],
      signInTime: 0,
      // 用户签到
      signInInfo: {},
    };
  },
  created() {
    this.getCenterSignInInfo();
    // 签到信息
    this.getSignInfo();
  },
  mounted() {},
  computed: {},
  methods: {
    // 获取连续签到时间
    async getCenterSignInInfo() {
      const { data } = await getCenterSignInInfo();
      console.log("连续签到数据", data);
      if (data && data.length) {
        this.signInList = data;
        this.signInTime = this.signInList.filter((item) => item.signInStatus == 2).length;
        console.log("连续签到时间", this.signInTime);
      }
    },
    async getSignInfo() {
      let { data } = await getSignInfo();
      this.signInInfo = data;
    },
    // 获取连续签到图片
    getSignInImg(item, index) {
      let imgUrl = "";
      if (index < 6) {
        if (item.signInStatus != 2) {
          imgUrl = "https://oss.ienmore.com/resources/public/img/health/new/gold.png";
        } else {
          imgUrl = "https://oss.ienmore.com/frontUpload/partUpload/2024-08-21/4bb2d57f8be0c83cf63ef1cf1bc9a08d.png";
        }
      } else {
        if (item.signInStatus != 2) {
          imgUrl = "https://oss.ienmore.com/resources/public/img/health/new/chest.png";
        } else {
          imgUrl = "https://oss.ienmore.com/resources/public/img/health/new/open_chest.png";
        }
      }
      return imgUrl;
    },
    async handleSignIn() {
      if (!this.signInInfo.signInStatus) {
        let params = {
          platformId: this.$route.query.platformId || localStorage.getItem("platformId"),
        };
        let { data } = await userSignIn(params);
        this.$store.commit("changeUserSignInInfo", data);
        this.$store.commit("changeIsSignInStatus", true);
        this.getSignInfo();
        this.getCenterSignInInfo();
        this.$store.dispatch("getMemberPoints");
      }
    },
  },
};
</script>
<style scoped lang="scss">
.member-mission-center-signIn {
  background: #ffffff;
  border-radius: 6px;
  margin-top: 12px;
  padding: 0 12px;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  .signIn-top {
    padding-top: 15px;
    padding-bottom: 12px;
    img {
      height: 17px;
    }
    .signIn-top-text {
      font-weight: 400;
      font-size: 12px;
      color: #868ea8;
      line-height: 17px;
      white-space: nowrap;
      .signIn-top-text-num {
        color: #ff4a28;
      }
    }
  }
  .signIn-middle {
    width: 100%;
    .signIn-middle-item {
      padding-right: 4px;
      width: 46px;
      position: relative;
      .signIn-middle-item-content {
        height: 66px;
        background: #fff0e3;
        border-radius: 2px;
        .signIn-middle-item-top {
          height: 20px;
          background: #ffddb3;
          line-height: 20px;
          font-weight: 400;
          font-size: 12px;
          color: #b04227;
          border-radius: 2px 2px 0px 0px;
        }
        .signIn-middle-item-bottom {
          .signIn-middle-item-img {
            width: 30px;
          }
          .signIn-middle-item-img-last {
            width: 35px;
          }
          .signIn-middle-item-txt {
            font-weight: 400;
            font-size: 11px;
            color: #868ea8;
          }
        }
        .sign-in-for-7 {
          position: absolute;
          width: 42px;
          height: 14px;
          background: #ff4e26;
          border-radius: 8px 7px 7px 0px;
          left: 0;
          top: -11px;
          font-weight: 400;
          font-size: 10px;
          color: #ffffff;
        }
      }
      &:last-child {
        .signIn-middle-item-img {
         
          
        }
      }
    }
    .signIn-middle-item-active {
      opacity: 0.6;
    }
  }
  .signIn-bottom {
    width: 100%;
    .signIn-bottom-btn {
      height: 38px;
      background: linear-gradient(270deg, #ff4a28 0%, #ff7615 100%);
      box-shadow: inset 0px 1px 10px 0px rgba(255, 255, 255, 0.8);
      border-radius: 3px;
      text-align: center;
      line-height: 38px;
      font-weight: 500;
      font-size: 14px;
      color: #ffffff;
      margin: 15px 0;
    }
    .signIn-bottom-disable {
      background: #f5f6fa;
      color: #cccccc;
    }
  }
}
</style>
