<template>
  <div class="center-bottom-content-top flex-middle">
    <div class="center-content-bottom-left">{{ title }}</div>
    <div class="center-content-bottom-right">{{ desc }}</div>
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: "",
    },
    desc: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {},
};
</script>
<style scoped lang="scss">
.center-bottom-content-top {
  height: 22px;
  margin-bottom: 13px;
  .center-content-bottom-left {
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    line-height: 22px;
    white-space: nowrap;
    margin-right: 6px;
  }
  .center-content-bottom-right {
    font-weight: 400;
    font-size: 12px;
    color: #868ea8;
    white-space: nowrap;
  }
}
</style>
