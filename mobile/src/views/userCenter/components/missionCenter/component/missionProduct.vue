<template>
  <div class="middle-content-bottom flex-left">
    <img class="middle-content-bottom-img" :src="info.bannerImg" alt="" />
    <div class="middle-content-bottom-content unit flex-vertical">
      <div class="middle-content-bottom-title">{{ info.name }}</div>
      <div class="middle-content-bottom-text">
        <product-info :item="info"></product-info>
      </div>
      <div class="flex-right">
        <div class="middle-content-bottom-btn flex-middle flex-center" :class="info.status == 1 ? 'middle-content-bottom-btn-disabled' : ''" @click="goProduct">{{info.status == 1 ? '已查看' : '立即查看'}}</div>
      </div>
    </div>
  </div>
</template>
<script>
import ProductInfo from "@/components/newCommon/productInfo";
import { exhibitorClientUrl } from "@/config/env";
export default {
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  components: {
    ProductInfo,
  },
  computed: {},
  methods: {
    goProduct() {
      if (this.info.status != 1) {
        let url = `${exhibitorClientUrl}/productStore?productId=${this.info.id}&exhibitorId=${this.info.exhibitorId}&ruleCode=${this.info.ruleCode}`;
        window.location.href = url;
      }
    },
  },
};
</script>
<style scoped lang="scss">
.middle-content-bottom {
  .middle-content-bottom-img {
    width: 100px;
    height: 100px;
    margin-right: 10px;
    object-fit: contain;
  }
  .middle-content-bottom-content {
    min-width: 0px;
    .middle-content-bottom-title {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-weight: 500;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      margin-bottom: 5px;
    }
    .middle-content-bottom-text {
      margin-top: 2px;
    }
    .middle-content-bottom-btn {
      width: 72px;
      height: 30px;
      background: linear-gradient(135deg, #ffa400 0%, #ff6b00 100%);
      border-radius: 2px;
      font-weight: 500;
      font-size: 12px;
      color: #ffffff;
      line-height: 30px;
      margin-top: 12px;
    }
    .middle-content-bottom-btn-disabled {
      background: #eeeeee;
      color: #999999;
    }
  }
}
</style>
