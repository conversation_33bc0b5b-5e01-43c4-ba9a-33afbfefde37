<template>
  <div class="member-mission-center-card flex-vertical">
    <div class="member-mission-center-num unit-0 flex-middle">
      <div class="num-text flex-middle">
        <span class="num-text-num">{{ pointInfos.balance }}</span>
        <span class="num-text-desc">可用易币</span>
      </div>
      <div class="num-space unit"></div>
      <div class="num-desc" v-if="pointInfos.expiresPoints">
        <van-icon name="info-o" class="num-desc-icon" />
        <span class="num-desc-text">本月即将过期 {{ pointInfos.expiresPoints || 0 }} 易币</span>
      </div>
    </div>
    <div class="member-mission-center-tab unit flex-middle">
      <div class="member-mission-center-tab-item unit flex-middle flex-center" @click="handlePage('myCoin')">
        <img src="https://oss.ienmore.com/resources/public/img/health/new/gold.png" alt="" />
        <span>我的易币</span>
      </div>
      <div class="member-mission-center-tab-item-line"></div>
      <div class="member-mission-center-tab-item unit flex-middle flex-center" @click="handlePage('mall')">
        <img src="https://oss.ienmore.com/resources/public/img/health/new/mall.png" alt="" />
        <span>易币商城</span>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions } from "vuex";
import { getMallCanEnter } from "@/api/memberApi";
export default {
  data() {
    return {};
  },
  mounted() {
    this.getMemberPoints();
  },
  computed: {
    pointInfos() {
      // 查询积分信息
      return this.$store.state.pointInfos;
    },
  },
  methods: {
    ...mapActions(["getMemberPoints"]),
    async handlePage(path) {
      // 处理页面跳转逻辑
      const routeParams = {
        path: `/${path}`,
        query: {
          platformId: this.$route.query.platformId,
        },
      };
      // 如果是商城页面需要先检查是否可进入
      if (path === "mall") {
        const res = await getMallCanEnter();
        if (res.code !== "001") {
          return this.$toast(res.info);
        }

        if (!res.data) {
          return this.$toast("易币商城正在建设中");
        }

        this.$router.push(routeParams);

        return;
      }

      // 其他页面直接跳转
      this.$router.push(routeParams);
    },
  },
};
</script>
<style scoped lang="scss">
.member-mission-center-card {
  width: 100%;
  height: 110px;
  background: url("https://oss.ienmore.com/resources/public/img/health/new/quest_gold.png") no-repeat;
  background-size: 100%;
  padding: 20px 12px 0 13px;
  box-sizing: border-box;
  .member-mission-center-num {
    width: 100%;
    border-bottom: 1px solid #ffecdd;
    .num-text {
      .num-text-num {
        font-weight: 600;
        font-size: 32px;
        color: #333333;
        line-height: 45px;
      }
      .num-text-desc {
        font-weight: 400;
        font-size: 12px;
        color: #868ea8;
        line-height: 16px;
        margin-left: 6px;
        margin-top: 3px;
      }
    }
    .num-desc {
      .num-desc-icon {
        font-size: 13px;
        color: #ff4d4f;
        margin-right: 6px;
      }
      .num-desc-text {
        font-weight: 400;
        font-size: 12px;
        color: #ff4d4f;
      }
    }
  }
  .member-mission-center-tab {
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    line-height: 16px;
    margin-top: 4px;
    padding: 0 12px;
    width: 100%;
    box-sizing: border-box;
    .member-mission-center-tab-item {
      img {
        width: 20px;
        margin-right: 4px;
      }
      span {
        font-weight: 400;
        font-size: 13px;
        color: #666666;
        margin-top: 2px;
      }
    }
    .member-mission-center-tab-item-line {
      width: 0.5px;
      height: 18px;
      background: #ffecdd;
    }
  }
}
</style>
