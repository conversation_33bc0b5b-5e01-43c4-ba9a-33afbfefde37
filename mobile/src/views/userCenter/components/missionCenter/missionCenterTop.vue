<template>
  <div class="member-mission-center-top">
    <div class="member-mission-center-container">
      <center-top-card></center-top-card>
      <!--实名认证未实名、待审核、未通过提示--->
      <realnameAuthenTip marginTop="12px" marginBottom="12px"></realnameAuthenTip>
      <center-top-singIn></center-top-singIn>
    </div>
  </div>
</template>
<script>
import centerTopCard from "./component/centerTopCard.vue";
import centerTopSingIn from "./component/centerTopSignIn.vue";
import realnameAuthenTip from "../realnameAuthenTip.vue"
export default {
  data() {
    return {
     
    };
  },
  created() {
   
  },
  components: {
    centerTopCard,
    centerTopSingIn,
    realnameAuthenTip
  },
  mounted() {},
  computed: {},
  methods: {
    
  },
};
</script>
<style scoped lang="scss">
.member-mission-center-top {
  width: 100%;
  background: url("https://oss.ienmore.com/resources/public/img/health/new/task_title.png") no-repeat;
  background-size: 100%;
  .member-mission-center-container {
    width: 100%;
    padding: 0 15px;
    box-sizing: border-box;
    padding-top: 67px;
  }
}
</style>
