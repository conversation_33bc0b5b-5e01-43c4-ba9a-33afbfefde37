<template>
  <div class="mission-other-center-bottom-wrap" v-if="taskList.length">
    <div class="center-bottom-content">
      <mission-title title="其他每日任务" desc="完成任务可获得易币"></mission-title>
      <mission-item :item="item" v-for="(item, index) in taskList" :key="index"></mission-item>
    </div>
  </div>
</template>
<script>
import { getOtherTask } from "@/api/memberApi";
import MissionItem from "./component/missionItem";
import MissionTitle from "./component/missionTitle";
export default {
  data() {
    return {
      // 完成任务数据
      taskList: [],
    };
  },
  created() {
    this.getOtherTask();
  },
  components: {
    MissionItem,
    MissionTitle,
  },
  mounted() {},
  computed: {},
  methods: {
    async getOtherTask() {
      let params = {
        sysCode: "exhibitor",
      };
      const { data } = await getOtherTask(params);
      if (data && data.length) {
        this.taskList = data.map((item, index) => {
          return {
            ...item,
            btnName: this.getBtnName(item, index),
          };
        });
      }
    },
    getBtnName(item, index) {
      let name;
      switch (index) {
        case 0:
          if (item.status) {
            name = "继续发布";
          } else {
            name = "去发布";
          }
          break;
        case 1:
          if (item.status) {
            name = "继续询价";
          } else {
            name = "去询价";
          }
          break;
        case 2:
          if (item.status) {
            name = "继续搜索";
          } else {
            name = "去搜索";
          }
          break;
      }
      return name;
    },
  },
};
</script>
<style scoped lang="scss">
.mission-other-center-bottom-wrap {
  width: 100%;
  padding: 0px 15px 0px 15px;
  margin-top: 15px;
  margin-bottom: 12px;
  box-sizing: border-box;
  .center-bottom-content {
    width: 100%;
    background-color: white;
    padding: 15px 12px;
    box-sizing: border-box;
    border-radius: 6px;
  }
}
</style>
