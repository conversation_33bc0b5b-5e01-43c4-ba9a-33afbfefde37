<!-- 底部弹出的 长按关注公众号 组件 -->
<template>
  <div v-show="visiable" class="hidden-message">
    <div class="hidden-mask" @click="close"></div>
    <div class="hidden-card-outside" :class="!editShow ? 'select_adreess' : ''" :style="adressList.length == 0 && !editShow ? 'height: 47%;' : ''">
      <div class="hidden-card">
        <div class="hidden-message-title">
          <span>{{ editShow && editString == "add" ? "新建收货地址" : editShow && editString == "edit" ? "编辑收货地址" : "收货地址" }}</span>
          <img @click="close" src="@/assets/img/close1.png" alt="" />
        </div>
      </div>
      <div class="hidden-card body_bottom">
        <div class="select_body" v-if="!editShow">
          <div class="radio_text_body" v-for="(item, index) in adressList" :style="{ 'margin-top': index == 0 ? '15px' : '' }">
            <div class="" @click="endBtn(item)">
              <div v-if="selectForm.id != item.id" class="radio_class" :class="selectForm.id == item.id ? 'select_class' : ''"></div>
              <img v-if="selectForm.id == item.id" style="margin-right: 13px; width: 16px; height: 16px" src="@/assets/img/selectIcon.png" />
            </div>
            <div class="text_body" @click="endBtn(item)">
              <div class="text_body_top">
                <span style="margin-right: 15px">{{ item.name }}</span>
                <span>{{ item.mobile }}</span>
              </div>
              <div class="text_body_bot">{{ item.region.replace(/\//g, "-") }}-{{ item.address }}</div>
            </div>
            <div class="edit_img_body">
              <img src="@/assets/img/editIcon.png" alt="" @click="editBtn('edit', item)" />
            </div>
          </div>
        </div>
        <div class="form_body" v-if="editShow">
          <div class="show_form_body" v-for="(item, index) in formList">
            <div class="form_item_label" :class="editShow ? 'form_edit_item' : ''">{{ item.label }}</div>
            <cube-input
              :disabled="displedList.includes(item.mode)"
              :class="displedList.includes(item.mode) ? 'displaed' : ''"
              @blur="inpBtn(item.label)"
              :required="true"
              v-if="item.type == 'input'"
              v-model="formDate[item.mode]"
              :placeholder="'请输入'"
              class="form_inp_item"
            ></cube-input>
            <div class="region_body" v-if="item.type == 'direct'">
              <Region :placeholder="'请输入'" class="user-info-set-form" :regionText="formDate[item.mode]" :mallSeleType="true"></Region>
            </div>
            <textarea @blur="inpBtn(item.label)" v-if="item.type == 'inTwo'" v-model="formDate[item.mode]" placeholder="请输入" class="textrea_class form_inp_item"></textarea>
            <span class="red_color mar_inpt_item" v-if="item.required">必填</span>
            <span class="red_color mar_inpt_item" v-if="item.modelType">格式错误</span>
          </div>
        </div>
        <div v-if="adressList.length == 0 && !editShow" class="zan_wu_class">暂未创建地址</div>
        <div class="btn_body">
          <div class="btn_add_btn" @click="editBtn('add')" v-if="!editShow && editString == ''">
            {{ adressList.length ? "新增地址" : "新增收货地址" }}
          </div>
          <div v-if="editShow && editString == 'edit'" class="delete_body">
            <div class="delete_class" @click="deleteAdress(item)">删除收货地址</div>
            <div class="savee_class" @click="saveBtn(1)">保存</div>
          </div>
          <div class="btn_add_btn" v-if="editShow && editString == 'add'" @click="saveBtn(0)">保存地址</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getMemberDeliveryAddress, saveMemberAddress, deleteAdress, getOrderAddress, updateAdress } from "@/api/monetary";
import Region from "../components/region";
import { getProductCodeRel } from "@/api/monetary";
export default {
  name: "userPopoView",
  props: ["visiable", "orderForm"],
  data() {
    return {
      displedList: [],
      selectForm: "", // 选择的收货ID
      adressList: [], // 收货地址列表
      editString: "",
      editShow: false,
      formDate: {
        region: "",
        regionCode: "",
      },
      formList: [
        {
          label: "收件人姓名",
          type: "input",
          mode: "name",
        },
        {
          label: "手机号码",
          type: "input",
          mode: "mobile",
        },
        {
          label: "选择地区",
          type: "direct",
          mode: "region",
        },
        {
          label: "详情地址",
          type: "inTwo",
          mode: "address",
        },
      ],
    };
  },
  mounted() {
    // 如果是邮箱注册的账号则手机号可以修改，否则手机号不可修改
    if (this.getProductInfoInfo.customerType == 0) {
      this.displedList.push("mobile");
      this.$forceUpdate();
    }
    this.getMemberDeliveryAddress("created");
    this.$root.$on("changeRegion", ({ regionCode, region }) => {
      this.$set(this.formDate, "region", region);
      this.$set(this.formDate, "regionCode", regionCode.join(","));
      this.checkForm("选择地区");
    });
  },
  computed: {
    addressForm() {
      return this.$store.state.addressForm;
    },
    getProductInfoInfo() {
      return this.$store.state.getProductInfoInfo;
    },
    personProfile() {
      return this.$store.state.personal.personProfile;
    },
  },
  watch: {
    addressForm: {
      handler(val) {},
    },
    getProductInfoInfo: {
      handler(val) {},
    },
  },
  components: {
    Region,
  },
  methods: {
    // 校验手机号
    validatePhone() {
      const phoneRegex = /^1[3-9]\d{9}$/;
      return phoneRegex.test(this.formDate.mobile);
    },
    // 获取默认地址
    getOrderAddress() {
      getOrderAddress().then((res) => {
        this.formDate = res.data;
        if (res.data.addressId) {
          this.$set(this.formDate, "region", "");
          this.$set(this.formDate, "regionCode", "");
          this.$set(this.formDate, "address", "");
        }
        if (res.data.name) {
          this.displedList.push("name");
        }
      });
    },
    // 删除收货地址
    deleteAdress(val) {
      let params = {
        id: this.formDate.id,
      };
      deleteAdress(params).then((res) => {
        if (res.code == this.$successCode) {
          this.$toast("操作成功");
          this.editShow = false;
          this.getMemberDeliveryAddress("delete");
        }
      });
    },
    // 编辑修改地址
    editBtn(type, val) {
      this.editShow = true;
      this.editString = type;
      if (type == "edit") {
        this.formDate = val;
        if (val.name) {
          this.displedList.push("name");
        }
        this.$forceUpdate();
      } else {
        this.getOrderAddress();
      }
    },
    // 保存会员收货地址
    saveMemberAddress(type) {
      let params = {
        ...this.formDate,
      };
      var apiFun = type ? updateAdress : saveMemberAddress;
      apiFun(params).then((res) => {
        if (res.code == this.$successCode) {
          this.editShow = false;
          this.getMemberDeliveryAddress();
        } else {
          this.$toast(res.info);
        }
      });
    },
    // 获取收货地址
    getMemberDeliveryAddress(type) {
      getMemberDeliveryAddress().then((res) => {
        this.adressList = res.data;
        this.editString = "";
        // 初始化
        if (this.adressList.length) {
          if (type == "created") {
            if (this.addressForm.id) {
              var seleId = this.adressList.findIndex((item) => item.id == this.addressForm.id);
              if (seleId != -1) {
                this.selectForm = this.adressList[seleId];
              } else {
                this.selectForm = this.adressList[0];
              }
            } else {
              this.selectForm = this.adressList[0];
            }
          }
          // 如果是删除则默认把第一条选中为订单地址信息
          if (type == "delete") {
            if (this.adressList.length && this.adressList.findIndex((item) => item.id == this.selectForm.id) == -1) {
              this.selectForm = this.adressList[0];
            }
          }
        }
      });
    },
    // 保存收货地址
    saveBtn(type) {
      if (!this.checkForm()) return;
      this.editString = "";
      this.saveMemberAddress(type);
      this.editShow = false;
    },
    // 选择按钮事件
    endBtn(item) {
      var desc = this.adressList.findIndex((demo) => demo.id == item.id);
      this.selectForm = item;
      this.$store.commit("adressFunForm", desc != -1 ? this.adressList[desc] : {});
    },
    // 校验收货地址是否补全
    checkForm(label) {
      let flag = true;
      this.formList.forEach((item, index) => {
        if (!this.formDate[item.mode]) {
          if (label && label == item.label) {
            this.$set(this.formList[index], "required", true);
          }
          if (!label) {
            this.$set(this.formList[index], "required", true);
          }
          if (item.mode == "mobile") {
            this.$set(this.formList[index], "modelType", false);
            if (!this.validatePhone()) {
              this.$set(this.formList[index], "modelType", true);
            }
          }
          flag = false;
        } else {
          if (label && label == item.label) {
            this.$set(this.formList[index], "required", false);
          }
          if (!label) {
            this.$set(this.formList[index], "required", false);
          }
          if (item.mode == "mobile") {
            this.$set(this.formList[index], "modelType", false);
            if (!this.validatePhone()) {
              this.$set(this.formList[index], "modelType", true);
              flag = false;
            }
          }
        }
      });
      return flag;
    },
    // 输入框失去焦点
    inpBtn(label) {
      this.checkForm(label);
    },
    // 时间处理方法
    momentFun(time) {
      return moment(time).format("YYYY-MM-DD HH:mm:ss");
    },
    // 关闭
    close() {
      var item = this.adressList.findIndex((item) => item.id == this.selectForm.id);
      // this.$emit("selectEmit",item != -1 ? this.adressList[item]:{});
      this.$store.commit("adressFunForm", item != -1 ? this.adressList[item] : {});
      this.$emit("changeUserPopShow");
    },
  },
};
</script>

<style scoped lang="scss">
$color: #004da1;
.hidden-message {
  position: fixed;
  width: 100%;
  bottom: 0px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: rgba($color: #000000, $alpha: 0.3);
  .hidden-mask {
    flex: 1;
  }
  .hidden-card-outside {
    // padding-top: 22px !important;
    width: 100%;
    background: #ffffff;
    // padding: 12px 0 0px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 200;
  }
  .select_adreess {
    min-height: 70%;
  }
  .hidden-card {
    width: 100%;
    float: left;
    max-height: calc(100vh - 120px);
    overflow: auto;
  }
  .second {
    padding: 0 20px;
    text-align: center;
    position: relative;
    float: left;
    width: 100%;
  }
  .secondBorder .QRcode::before {
    display: inline-block;
    width: 1px;
    height: 120px;
    background-color: #dedede;
    content: "";
    position: absolute;
    top: 0;
    left: -36px;
  }
}
.QRcode {
  position: relative;
  padding: 7px;
  margin: 0 auto 22px;
  width: 114px;
  height: 114px;
  box-sizing: border-box;
  position: relative;
  .info-image {
    width: 100px;
    height: 100px;
  }
  i {
    position: absolute;
    display: inline-block;
    width: 14px;
    height: 14px;
  }
  i:nth-of-type(1) {
    top: 0;
    left: 0;
    border-top: 0.5px solid #1464a1;
    border-left: 0.5px solid #1464a1;
  }
  i:nth-of-type(2) {
    top: 0;
    right: 0;
    border-top: 0.5px solid #1464a1;
    border-right: 0.5px solid #1464a1;
  }
  i:nth-of-type(3) {
    bottom: 0;
    left: 0;
    border-bottom: 0.5px solid #1464a1;
    border-left: 0.5px solid #1464a1;
  }
  i:nth-of-type(4) {
    bottom: 0;
    right: 0;
    border-bottom: 0.5px solid #1464a1;
    border-right: 0.5px solid #1464a1;
  }
}
.hidden-message-title {
  text-align: center;
  color: #333333;
  font-size: 14px;
  // margin-bottom: 17px;
  line-height: 20px;
  padding: 0 20px;
  float: left;
  width: 100%;
  // margin: 0 auto 22px;
  font-weight: bold;
  font-size: 16px;
  border-bottom: 1px solid #f5f5f5;
  // margin-bottom: 20px;
  height: 46px;
  padding-top: 12px;
  font-weight: 500;
  img {
    float: right;
    // margin-right: 15px;
  }
}
.bot_body {
  height: 160px;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
}
.blue_text {
  font-weight: 400;
  font-size: 14px;
  color: #097db4;
  margin-top: 85px;
}
.bot_text {
  margin-top: 10px;
  display: flex;
  p {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    margin-left: 6px;
  }
  .name_img {
    width: 24px;
    height: 24px;
    margin-top: -3px;
    .img {
      width: 14px;
      height: 14px;
    }
  }
}
.footer-submit-btn {
  width: 100%;
  box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.06);
  background: #fff;
  text-align: center;
  margin: 0;
  margin-top: 38px;
  height: 65px;
  button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 42px;
    background: var(--color-primary);
    font-size: 14px;
    opacity: 0.9;
  }
}
.img_body {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 160px;
  border: 1px solid rgba(9, 125, 180, 0.2);
  position: relative;
  padding: 5px;
  img {
    height: 100%;
  }
  i {
    position: absolute;
    top: 0px;
    right: 0px;
  }
  .delete_class {
    z-index: 999;
    position: absolute;
    top: 8px;
    right: 8px;
    height: 16px;
  }
}
.text_bot {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 64px;
  height: 23px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 12px;
}
.not_text {
  width: 345px;
  height: 28px;
  background: rgba(255, 77, 79, 0.1);
  border-radius: 2px;
  display: flex;
  align-items: center;
  color: #ff4d4f;
  font-size: 12px;
  padding-left: 10px;
  margin-top: 16px;
}
.body_bottom {
  height: 100%;
}
.ka_body {
  .title {
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    text-align: left;
    font-style: normal;
    margin-bottom: 10px;
    margin-top: 20px;
  }
  .ka_item {
    height: 32px;
    background-color: #f5f6fa;
    display: flex;
    align-items: center;
    padding: 0px 12px;
    justify-content: space-between;
    .text_right {
      display: flex;
      align-items: center;
      img {
        width: 13px;
        margin-left: 6px;
      }
      p {
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        text-align: center;
        font-style: normal;
      }
    }
    p {
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      text-align: left;
      font-style: normal;
    }
  }
}
.btn_body {
  box-shadow: 0px -1px 0px 0px #eeeeee;
  position: absolute;
  bottom: 0;
  padding: 12px 15px 11px 15px;
  width: 100%;
  height: 65px;
  background-color: #ffffff;
  font-size: 14px;
  .btn_add_btn {
    width: 345px;
    height: 42px;
    background: #097db4;
    border-radius: 3px;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .delete_body {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .delete_class {
      width: 48%;
      height: 42px;
      background: #ffffff;
      border-radius: 3px;
      border: 1px solid #d9d9d9;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      text-align: left;
      font-style: normal;
    }
    .savee_class {
      width: 48%;
      height: 42px;
      background: #097db4;
      border-radius: 3px;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.show_form_body {
  margin: 0px 15px;
  .form_item_label {
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    text-align: left;
    font-style: normal;
    margin-top: 20px;
    margin-bottom: 8px;
  }
  .form_item_text {
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    text-align: left;
    font-style: normal;
    line-height: 20px;
    pre {
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-all;
    }
  }
  .form_edit_item::before {
    content: "*";
    color: red;
    margin-right: 5px;
    left: 0;
    top: 0;
  }
  .form_inp_item {
    height: 38px;
    background: #f5f5f5;
    border-radius: 2px;
    width: 100%;
    max-width: 100%;
    color: #333333;
    ::v-deep .cube-input-field {
      height: 38px;
      color: #333333;
    }
  }
  .displaed {
    ::v-deep .cube-input-field {
      height: 38px;
      color: #999999;
    }
  }
  .region_body {
    width: 100%;
    height: 38px;
    background: #f5f5f5;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    ::v-deep .cubeic-select {
      margin-right: 12px;
      color: #000000;
      opacity: 0.3;
    }
  }
  .textrea_class {
    border: 0px solid;
    min-height: 58px;
    width: 100%;
    padding: 9px 12px;
    line-height: 20px;
    color: #333333;
    font-size: 14px;
  }
  .red_color {
    font-weight: 500;
    font-size: 14px;
    color: #ff4d4f;
    text-align: left;
    font-style: normal;
    margin-top: 5px;
  }
  .mar_inpt_item {
    margin-top: 10px;
  }
  .user-info-set-form {
    ::v-deep .placeholder-text {
      color: #333333;
    }
    ::v-deep .emptyClass {
      color: #999999 !important;
    }
    ::v-deep .placeholder-text {
      font-size: 14px;
    }
  }
  ::v-deep .cube-input_active::after {
    border-color: #fff !important;
  }
}
.radio_text_body {
  display: flex;
  align-items: center;
  // padding: 0px 15px;
  height: 74px;
  margin: 0px 15px;
  border-bottom: 1px solid #eeeeee;
  overflow: auto;
  .radio_class {
    width: 16px;
    height: 16px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    margin-right: 12px;
  }
  .text_body {
    width: 87%;
    .text_body_bot {
      margin-top: 8px;
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      white-space: normal; /* 确保文本可以折行 */
      overflow: hidden; /* 隐藏超出的内容 */
      display: -webkit-box; /* 使用弹性盒子模型 */
      -webkit-line-clamp: 1; /* 限制显示的行数为2行 */
      -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
      line-height: 20px; /* 行高 */
      text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
      width: fit-content;
      max-width: 96%;
    }
    .text_body_top {
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      text-align: left;
      font-style: normal;
    }
  }
  .edit_img_body {
    // margin-left: 20px;
  }
  .select_class {
  }
}
.zan_wu_class {
  width: 100%;
  display: flex;
  justify-content: center;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  // margin-top: 100px;
  height: calc(100% - 120px);
  align-items: center;
}
.form_body {
  height: calc(100% - 110px);
  overflow-y: scroll;
  margin-bottom: 65px;
}
.select_body {
    margin-bottom: 65px;
}
</style>
