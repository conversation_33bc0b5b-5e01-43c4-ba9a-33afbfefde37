<template>
  <div class="interested-wrap" :style="style">
    <div class="interested-title">{{ title }}</div>
    <div class="interested-content flex-middle flex-wrap">
      <div @click="handlerSelect(item)" :class="{ 'interested-item-active': interestedData.includes(item.value) }" class="interested-item flex-middle flex-center" v-for="(item, index) in sourceData">
        {{ item.label }}
        <img src="../../../assets/img/select.png" class="select-img" v-show="interestedData.includes(item.value)" />
      </div>
    </div>
  </div>
</template>
<script>
import { getIndusHangtryList } from '@/api/configurable/common'
export default {
  props: {
    // 获取感兴趣源数据
    getInterestedData: {
      type: Function,
      require: true,
    },
    // 获取用户喜欢的数据
    getUserInterestedData: {
      type: Function,
      require: true,
    },
    saveUserInfo: {
      type: Function,
      require: true,
    },
    // 标题
    title: {
      type: String,
      require: true,
    },
    style: {
      type: Object,
      default: {
        marginTop: '25px',
      },
    },
    type: {
      type: String,
    },
  },
  data() {
    return {
      sourceData: [],
      interestedData: [],
    }
  },
  created() {
    // 获取源数据
    this.getSourceData()
    // 获取用户数据
    this.getUserData()
    // 保存
    // this.saveUserInfoData()
  },
  mounted() {},
  computed: {},
  methods: {
    getSourceData() {
      console.log(localStorage.getItem('platformId'),"63行的数据信息");
      if (localStorage.getItem('platformId') != 1) {
        let arr = {
          3: {
            subscribe: "root_system_info_medical_live_channel_publish",
          },
          5: {
            subscribe: "root_system_info_car_live_channel_publish",
          },
        };
        this.getInterestedData(arr[Number(localStorage.getItem('platformId'))].subscribe).then((res) => {
          // console.log('原数据', res)
          if (res.data && res.data.length) {
            if (this.type == 'industry') {
              this.sourceData = res.data.map((item) => {
                return {
                  label: item.codeValueDesc,
                  value: item.codeValueId,
                }
              })
            } else {
              this.sourceData = res.data.map((item) => {
                return {
                  label: item.name,
                  value: item.id,
                }
              })
            }
          }
        })
      } else  {
        getIndusHangtryList().then(res => {
          if (res.data && res.data.length) {
            if (this.type == 'industry') {
              this.sourceData = res.data.map((item) => {
                return {
                  label: item.name,
                  value: item.codeValueId,
                }
              })
            } else {
              this.sourceData = res.data.map((item) => {
                return {
                  label: item.name,
                  value: item.id,
                }
              })
            }
          }
        })
      }
      
    },
    getUserData() {
      this.getUserInterestedData().then((res) => {
        if (res.data) {
          if (this.type == 'industry') {
            res.data.forEach((item) => {
              this.interestedData.push(item.channel)
            })
          } else {
            if (res.data.classify) {
              let classify = res.data.classify.split(',')
              classify.forEach((item) => {
                this.interestedData.push(Number(item))
              })
            }
          }
        }
      })
    },
    // 保存用户
    async saveUserInfoData() {
      let params
      if (this.type == 'industry') {
        params = {
          channels: this.interestedData,
          platformId: localStorage.getItem('platformId'),
        }
      } else {
        params = {
          classify: this.interestedData.join(','),
        }
      }
      let res  = await this.saveUserInfo(params)
      return res
    },
    handlerSelect(data) {
      if (this.interestedData.includes(data.value)) {
        this.interestedData = this.interestedData.filter((item) => {
          return item != data.value
        })
      } else {
        this.interestedData.push(data.value)
      }
    },
    // 获取感兴趣的数据
    getInterestedDataRef() {
      return  this.interestedData;
    }
  },
}
</script>
<style scoped lang="scss">
.interested-wrap {
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  .interested-title {
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #333333;
    line-height: 20px;
    margin-bottom: 5px;
  }
  .interested-content {
    margin-bottom: 20px;
    .interested-item {
      margin-top: 10px;
      margin-right: 10px;
      width: 105px;
      height: 36px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #eeeeee;
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      position: relative;
      &:nth-child(3n) {
        margin-right: 0 !important;
      }
    }
    .interested-item-active {
      background: #f2faff;
      color: var(--color-primary);
    }
    .select-img {
      width: 14px;
      height: 14px;
      position: absolute;
      right: 0;
      bottom: 0;
    }
  }
}
</style>
