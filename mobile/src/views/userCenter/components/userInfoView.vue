<template>
    <div class="body_title">
        <div class="enterprise-live-header">
            <div class="header-platform" @click="homePage">
                <img :src="platformInfo.platformIcon">{{ title }}
            </div>
            <div class="header-right">
                <span class="header-collect" @click="collectLiveRoom" v-if="!collectType">{{collectFlag == 0 ? '收藏':'取消收藏'}}</span>
                <span @click="toUserCenter">个人中心</span>
            </div>
        </div>
    </div>
</template>



<script>
import { getCollectFlag } from '@/api/medicine/enterpriseLiveRoom'
import CONSTANT from '@/config/config_constant';
import { collectLiveRoom, getPlatformInfo } from '@/api/medicine/enterpriseLiveRoom'
import moment from 'moment';
export default {
    props: ["collectType"],
    data(){
        return {
            collectFlag: 0,
            platformInfo: JSON.parse(localStorage.getItem(CONSTANT.USERINFO)), // 公众号信息
            title: "",
        }
    },
    watch: {
        
    },
    components: {
    },
    mounted(){
        this.getPlatformInfo();
        if (!this.collectType) {
            this.getCollectFlag();
        }
    },
    methods: {
        // 查询是否有收藏直播间
        getCollectFlag() {
            let params = {
                itemType: 'liveRoom',
                itemId: this.$route.params.id
            }
            getCollectFlag(params).then(res => {
                if (res.code == this.$successCode) {
                    this.collectFlag = res.data ? 1:0;
                }
            })
        },
        getPlatformInfo(){
            getPlatformInfo().then(res=>{
                this.title = res.data.title
            })
        },
        // 跳转首页
        homePage(){
            this.$router.push({ name: 'home', query: { targetTab: '首页', local: true } })
        },
        // 收藏 || 取消收藏
        collectLiveRoom() {
            let params = {
                itemType: 'liveRoom',
                itemId: this.$route.params.id,
                status: this.collectFlag == 0 ? 1:0
            }
            collectLiveRoom(params).then(res => {
                if (res.code == this.$successCode) {
                    this.$createToast({ txt: this.collectFlag==0?'收藏成功':'取消收藏成功', type: 'txt' }).show()
                    this.collectFlag = this.collectFlag==0?1:0;
                } else {
                    this.$createToast({ txt: res.info, type: 'error' }).show()
                }
            })
        },
        // 个人中心
        toUserCenter() {
            window.location.href = location.origin + `/info/#/home/<USER>/0/0?platformId=${this.$route.query.platformId}&domainType=${this.$route.query.domainType}`;
        },
    },
}
</script>



<style lang='scss' scoped>
.enterprise-live-header {
    line-height: 45px;
    display: flex;
    justify-content: space-between;
    padding: 0 15px;
    background-color: #FFFFFF;
    position: absolute;
    top: -1px;
    width: 100%;
    z-index: 1;
    .header-platform {
        font-size: 13px;
        color: #333;
        img {
            width: 27px;
            height: 27px;
            margin-right: 6px;
            vertical-align: middle;
            margin-top: -3px;
        }
    }
    .header-right {
        font-size: 12px;
        color: #666;
        span {
            display: inline-block;
        }
        .header-collect {
            margin-right: 9px;
            position: relative;
            &::after {
                content: '';
                position: absolute;
                right: -5px;
                top: 18px;
                width: 1px;
                height: 10px;
                background: #D8D8D8;
            }
        }
    }
}
.body_title {
    height: 57px;
}
</style>