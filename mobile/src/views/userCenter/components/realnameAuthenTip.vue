<!--实名认证未实名、待审核、未通过提示--->
<template>
  <div
    v-if="getBerPointInfo.auditFlag != 1 && currentPlatformId == 3"
    class="realname-authen-tip-container"
    @click="showAuthenDialog"
    :style="{ margin: `${marginTop} ${marginRight} ${marginBottom} ${marginLeft} ` }"
  >
    <div class="tip-content">
      <div class="tip-content-text">
        <img src="@/assets/img/notice.png" width="18" />
        <span>易币需实名认证后才可以使用兑换</span>
      </div>
      <img src="@/assets/img/audit-right-arrow.png" width="14" v-if="canShowDialog" />
    </div>
  </div>
</template>
<script>
import { mapActions } from "vuex";
import { getBioChinaInfo } from "@/api/memberApi";
export default {
  props: {
    canShowDialog: {
      //点击是否能显示实名认证审核弹框
      type: Boolean,
      default: true,
    },
    marginBottom: {
      //外边距底部
      type: String,
      default: "0px",
    },
    marginTop: {
      //外边距上部
      type: String,
      default: "0px",
    },
    marginLeft: {
      //外边距左部
      type: String,
      default: "0px",
    },
    marginRight: {
      //外边距右部
      type: String,
      default: "0px",
    },
  },
  data() {
    return {};
  },
  components: {},
  computed: {
    getBerPointInfo() {
      // 查询会员信息
      return this.$store.state.getBerPointInfo;
    },
  },
  mounted() {
    this.getMemberAuthStatus();
  },
  methods: {
    ...mapActions(["getMemberAuthStatus"]),
    showAuthenDialog() {
      if (!this.canShowDialog) return;
      getBioChinaInfo().then((res) => {
        if (!res.data) {
          //if (this.getBerPointInfo.auditFlag == 0) return this.$toast("实名认证审核中，如有问题请联系主办方");
          this.$store.commit("changeUserPopShow", true);
        } else {
          this.$store.commit("changeBioChinaShow", true);
          this.$store.commit("changeBioChinaImg", res.data);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.realname-authen-tip-container {
  .tip-content {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    background: #ffede0;
    border-radius: 4px;
    font-size: 12px;
    color: #ff6b00;
    justify-content: space-between;
    .tip-content-text {
      display: flex;
      align-items: center;
      span {
        margin-left: 6px;
      }
    }
  }
}
</style>
