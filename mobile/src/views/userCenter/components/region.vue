<template>
  <div class="region-input flex-middle" @click="regionSelect">
    <span :class="{ emptyClass: !regionValue.length }" class='placeholder-text'>{{ region }}</span>
    <span class="unit"></span>
    <i v-if="!mallSeleType" class="cubeic-arrow"></i>
    <i v-if="mallSeleType" class="cubeic-select" :class="isSelectType ? 'isSelectClass':''"></i>
  </div>
</template>
<script>
import { getClassify } from '@/api/medicine/homePage'
export default {
  props: ["mallSeleType"],
  data() {
    return {
      regionValue: [],
      region: '',
      regionList: [],
      addressPicker: null,
      isSelectType: false, // 上下图标状态
    }
  },
  watch: {
    $attrs(val) {
      if (val.value) {
        this.regionValue = val.value.split(',')
      }
      if (val.regionText) {
        this.region = val.regionText
      } else {
        this.region = val.placeholder
      }
    },
  },
  mounted() {
    this.getClassifyRegion()
    if (this.mallSeleType) {
      if (this.$attrs.regionText) {
        this.regionValue = this.$attrs.regionText.split(',')
      }
      if (this.$attrs.regionText) {
        this.region = this.$attrs.regionText
      } else {
        this.region = this.$attrs.placeholder
      }
    }
  },
  computed: {},
  methods: {
    regionSelect() {
      if (this.addressPicker) {
        this.isSelectType = true;
        this.$nextTick(()=>{
            document.querySelector('.user-info-set-form').style.pointerEvents = 'auto';
            this.addressPicker.show()
        })
      }else{
        this.$toast('请求数据中,请耐心等待')
        document.querySelector('.user-info-set-form').style.pointerEvents = 'none';
        setTimeout(()=>{
            this.regionSelect();
        },1000)
      }
    },
    selectHandle(selectedVal, selectedIndex, selectedText) {
      this.isSelectType = false;
      //   console.log(selectedVal, selectedIndex, selectedText)
      this.regionValue = selectedVal
      this.region = selectedText.join('/')
      this.$root.$emit('changeRegion', { regionCode: this.regionValue, region: this.region })
    },
    cancelHandle() {
      this.isSelectType = false;
    },
    getClassifyRegion() {
      getClassify('cascade/root_common_region').then((res) => {
        // console.log('地区',res)
        if (res.code == '001') {
          this.deepTree(res.data)
          this.regionList = res.data.children

          this.addressPicker = this.$createCascadePicker({
            data: this.regionList,
            onSelect: this.selectHandle,
            onCancel: this.cancelHandle,
          })
        }
      })
    },
    // 递归请求地区
    deepTree(data) {
      for (let i = 0; i < data.children.length; i++) {
        let item = data.children[i]
        item.text = item.codeValueDesc
        item.value = item.codeValueId
        if (item.children && item.children.length) {
          this.deepTree(item)
        } else {
          item.children = undefined
        }
      }
    },
  },
}
</script>
<style scoped lang="scss">
.region-input {
  width: 100%;
  height: 22px;
  line-height: 22px;
  margin: 8px 0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  .emptyClass {
    color: #999;
  }
  .cubeic-arrow {
    font-size: 14px !important;
  }
  .placeholder-text{
    padding-left: 8px;
    font-size: 15px;
  }
  .isSelectClass {
    transform: rotate(180deg);
  }
}
</style>
<style lang="scss">
  .cube-picker-wheel-item {
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
