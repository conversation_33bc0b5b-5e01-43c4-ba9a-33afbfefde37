<template>
    <div>
        <div class="top_box">
            <div class="top_text_body">
                <div class="top_item">
                    <div class="top_item_left">
                        <p class="top_item_left_number">{{ pointInfos.balance || 0 }}</p>
                        <img src="@/assets/img/mallBi.png" alt="">
                        <p class="top_item_left_text">可用易币</p>
                    </div>
                    <div class="bot_item_left">
                        <p>本期即将过期：{{ pointInfos.expiresPoints || 0 }}易币</p>
                    </div>
                </div>
                <div class="bot_item">
                    <div class="top_item_right">
                        <div @click="jumpMore" class="jump_box">
                            <p :style="Preview > -1 ? 'font-size: 11px;':'font-size: 14px;'">赚易币</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bot_text_body">
                <div class="bot_text_left" @click="jumpBtn">
                    <img src="@/assets/img/mingIcon.png" alt="">
                    <p>易币明细</p>
                </div>
                <div class="bot_text_right" @click="duiBtn">
                    <img src="@/assets/img/dui_icon.png" alt="">
                    <p>我的兑换</p>
                </div>
            </div>
        </div>
    </div>
</template>



<script>
import png from "../../../assets/img/backBg.png"
import moment from 'moment';
export default {
    data(){
        return {
            Preview: process.env.VUE_APP_CURRENTMODE.indexOf('Preview'),
        }
    },
    watch: {
        
    },
    computed: {
        pointInfos() {
          // 查询积分信息
          this.widtMode = ((this.$store.state.pointInfos.memberPoints || 0)/(this.$store.state.pointInfos.nextMemberPoints || this.$store.state.pointInfos.memberPoints)) * 100;
          return this.$store.state.pointInfos;
        },
    },
    mounted(){
    },
    methods: {
        // 跳转兑换历史
        duiBtn() {
            this.$router.push({
                path: '/mall/exchange',
            })
        },
        // 跳转赚更多
        jumpMore() {
            this.$router.push({
                name: 'missionCenter',
            })
        },
        // 跳转易币明细页
        jumpBtn() {
            this.$router.push({
                path: '/myCoin/info',
                query: {
                    platformId: this.$route.query.platformId
                }
            })
        },
    },
}
</script>



<style lang='scss' scoped>
.top_box {
    width: 100%;
    height: 132px;
    // background: #B7DDFF;
    padding: 0px 15px;
    // display: flex;
    background-image: url("../../../assets/img/backBg.png");
    background-size: 100% 100%;
    .top_item_left {
        display: flex;
        align-items: flex-end;
        .top_item_left_number {
            font-weight: bold;
            font-size: 28px;
            color: #26527B;
            text-align: left;
            font-style: normal;
            margin-right: 10px;
            line-height: 21px;
        }
        img {
            width: 14px;
            height: 14px;
            box-shadow: 0px 0px 0px 0px #F7A644;
        }
        .top_item_left_text {
            font-size: 12px;
            margin-left: 4px;
            font-weight: 400;
            font-size: 12px;
            color: #26527B;
            text-align: left;
            font-style: normal;
        }
    }
    .top_item_right {
        display: flex;
        justify-content: flex-end;
        .jump_box {
            width: 66px;
            height: 28px;
            background: linear-gradient( 270deg, #E9F5FE 0%, #FFFFFF 100%);
            border-radius: 14px;
            display: flex;
            justify-content: center;
            align-items: center;
            p {
                font-weight: 500;
                // font-size: 14px;
                color: #26527B;
                text-align: left;
                font-style: normal;
            }
        }
    }
    .top_item {
        width: 70%;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    .bot_item {
        display: flex;
        align-items: center;
        width: 30%;
        justify-content: flex-end;
    }
    .bot_item_left {
        margin-top: 15px;
        p {
            font-weight: 400;
            font-size: 12px;
            color: #26527B;
            text-align: left;
            font-style: normal;
        }
    }
    .top_text_body {
        display: flex;
        width: 100%;
        height: 96px;
    }
    .bot_text_body {
        width: 100%;
        height: 36px;
        display: flex;
        justify-content: center;
        align-items: center;
        .bot_text_left {
            width: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 400;
            font-size: 13px;
            color: #26527B;
            text-align: left;
            font-style: normal;
            display: flex;
            align-items: center;
        }
        .bot_text_right {
            width: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 400;
            font-size: 13px;
            color: #26527B;
            text-align: left;
            font-style: normal;
        }
        img {
            width: 14px;
            height: 14px;
            margin-right: 4px;
        }
        p {
            margin-top: 2px;
        }
    }
}
</style>