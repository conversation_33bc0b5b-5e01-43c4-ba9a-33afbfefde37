<template>
  <div class="region-input flex-middle" @click="regionSelect">
    <span :class="{ emptyClass: !regionValue.length }" class='placeholder-text'>{{ region }}</span>
    <span class="unit"></span>
    <i class="cubeic-arrow"></i>
  </div>
</template>
<script>
import { getClassify } from '@/api/medicine/homePage'
export default {
  data() {
    return {
      regionValue: [],
      region: '',
      regionList: [],
      addressPicker: null,
    }
  },
  watch: {
    $attrs(val) {
      if (val.value) {
        this.regionValue = val.value.split(',')
      }
      if (val.rankText) {
        this.region = val.rankText
      } else {
        this.region = val.placeholder
      }
    },
  },
  mounted() {
    this.getClassifyRegion()
  },
  computed: {},
  methods: {
    regionSelect() {
      if (this.addressPicker) {
        this.$nextTick(()=>{
            document.querySelector('.user-info-set-form').style.pointerEvents = 'auto';
            this.addressPicker.show()
        })
      }else{
        this.$toast('请求数据中,请耐心等待')
        document.querySelector('.user-info-set-form').style.pointerEvents = 'none';
        setTimeout(()=>{
            this.regionSelect();
        },1000)
      }
    },
    selectHandle(selectedVal, selectedIndex, selectedText) {
      this.regionValue = selectedVal
      this.region = selectedText.join('/')
      this.$root.$emit('selectRankNature', { rankCode: this.regionValue, rank: this.region })
    },
    cancelHandle() {},
    getClassifyRegion() {
      getClassify("root_system_info_medical_rank").then((res) => {
        if (res.code == '001') {
          this.deepTree(res.data)
          this.regionList = res.data

          this.addressPicker = this.$createCascadePicker({
            data: this.regionList,
            onSelect: this.selectHandle,
            onCancel: this.cancelHandle,
          })
        }
      })
    },
    // 递归请求地区
    deepTree(data) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i]
        item.text = item.codeValueDesc
        item.value = item.codeValueId
      }
    },
  },
}
</script>
<style scoped lang="scss">
.region-input {
  width: 100%;
  height: 22px;
  line-height: 22px;
  margin: 8px 0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  justify-content: space-between;
  .emptyClass {
    color: #999;
  }
  .cubeic-arrow {
    font-size: 14px !important;
  }
  .placeholder-text{
    padding-left: 8px;
    font-size: 15px;
  }
}
</style>
<style lang="scss">
  .cube-picker-wheel-item {
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
