<template>
    <div>
        <div class="top_sear_body" style="margin-bottom: 12px;" :id="topShow ? 'padd_id':''">
            <div class="top_sear_body_item" :id="topShow ? 'top_id':''">
                <div class="top_sear_item" @click="zongBtn(0)" :class="(seleZonForm.selectId != 0 || searType == 0) ? 'blue_class':''">
                    <p>{{ seleZonForm.selectType }}</p>
                    <img :class="searType != 0 ? 'tranf_class':''" v-if="seleZonForm.selectId != 0 || searType == 0" src="@/assets/img/shai_blue_icon.png" alt="">
                    <img v-else src="@/assets/img/bot_icon.png" alt="">
                </div>
                <div class="top_sear_item" @click="zongBtn(2)" :class="(seleZonForm.productClassifyIds.length || searType == 2) ? 'blue_class':''">
                    <p>{{ seleZonForm.productClassifyName[0] ? seleZonForm.productClassifyName[0]:"分类" }}<span v-if="seleZonForm.productClassifyIds.length > 1">...</span></p>
                    <img :class="searType != 2 ? 'tranf_class':''" v-if="seleZonForm.productClassifyIds.length || searType == 2" src="@/assets/img/shai_blue_icon.png" alt="">
                    <img v-else src="@/assets/img/bot_icon.png" alt="">
                </div>
                <div class="top_sear_item" @click="zongBtn(1)" :class="(seleZonForm.produId != null || searType == 1) ? 'blue_class':''">
                    <p>{{ seleZonForm.produName ? seleZonForm.produName : "全部" }}</p>
                    <img v-if="seleZonForm.produId != null || searType == 1" :class="searType != 1 ? 'tranf_class':''" src="@/assets/img/shai_blue_icon.png" alt="">
                    <img v-else src="@/assets/img/bot_icon.png" alt="">
                </div>
                <div class="top_sear_item" style="margin-right: 0px;" @click="jinBtn(3)" :class="jinLiShow ? 'blue_class':''">
                    <p>仅看我可兑</p>
                </div>
            </div>
            <div class="border_box"></div>
            <img class="sear_class" src="@/assets/img/yi_sear_icon.png" alt="" @click="searchBtn">
        </div>
    </div>
</template>



<script>
import moment from 'moment';
export default {
    props: ["seleZonForm","topShow","searType","showPopup","jinShow"],
    data(){
        return {
            jinLiShow: false,
            searType: null, // 筛选类型区分
            seleYuanZonForm: {
                selectType: "综合推荐",
                selectId: 0,
                produName: null,
                productClassifyIds: [],
                productClassifyName: [],
            },
            zongList: [
                {
                    selectType: "综合推荐",
                    selectId: 0,
                },
                {
                    selectType: "易币从低到高",
                    selectId: 1,
                },
                {
                    selectType: "易币从高到低",
                    selectId: 2,
                },
            ],
            allList: [
                {
                    selectType: "全部",
                    produId: null,
                },
                {
                    selectType: "实物商品",
                    produId: "physical",
                },
                {
                    selectType: "虚拟商品",
                    produId: "virtual",
                },
            ],
            prodList: [],
        }
    },
    computed: {
    },
    watch: {
        jinShow: {
            handler(val) {
                console.log(val,"83行的数据信息");
                this.jinLiShow = val;
            }
        },
    },
    components: {
    },
    mounted(){
        console.log(this.jinShow,"85行的数据信息");
        this.jinLiShow = this.jinShow;
    },
    methods: {
        // 点击进入搜索
        searchBtn() {
            this.$router.push({
                path: '/mall/search',
            });
        },
        // 筛选选择
        seleBtn(val,type) {
            switch (type) {
                case 0:
                    this.seleZonForm.selectType = val.selectType;
                    this.seleZonForm.selectId = val.selectId;
                    break;
                case 1:
                    this.seleZonForm.produName = val.selectType;
                    this.seleZonForm.produId = val.produId;
                    break;
                case 2:
                    let dex = this.seleZonForm.productClassifyIds.includes(val.id);
                    if (dex) {
                        this.seleZonForm.productClassifyIds.splice(this.seleZonForm.productClassifyIds.findIndex(item => item == val.id),1);
                        this.seleZonForm.productClassifyName.splice(this.seleZonForm.productClassifyName.findIndex(item => item == val.productClassify),1);
                    } else {
                        this.seleZonForm.productClassifyIds.push(val.id);
                        this.seleZonForm.productClassifyName.push(val.productClassify);
                        this.$forceUpdate();
                    }
                    break;
                default:
                    break;
            }
            
        },
        // 仅看我可兑
        jinBtn() {
            this.jinLiShow = !this.jinLiShow;
            if (!this.topShow) {
                this.$emit("jinBtn",false)
            } else {
                this.$emit("jinBtn",true,this.jinLiShow)
            }
        },
        // 综合推荐点击筛选
        zongBtn(index) {
            this.$emit("zongBtn",index);
        },
    },
}
</script>



<style lang='scss' scoped>
.top_sear_body {
    align-items: center;
    display: -webkit-box;
    .top_sear_item {
        padding: 4px 7px;
        width: fit-content;
        background-color: #FFFFFF;
        display: flex;
        align-items: center;
        margin-right: 8px;
        img {
            width: 15px;
            margin-left: 4px;
        }
        p {
            font-weight: 400;
            font-size: 13px;
            color: #333333;
            text-align: left;
            font-style: normal;
        }
    }
    .sear_class {
        height: 16px;
        margin-left: 8px;
    }
    .top_sear_body_item {
        display: -webkit-box;
        align-items: center;
        max-width: calc(100% - 50px);
        overflow-y: auto;
    }
}
.border_box {
    width: 1px;
    height: 16px;
    background: #DDDDDD;
    margin-left: 21px;
}
.popup_content {
  width: 100%;
  max-height: 50%; /* 限制最大高度 */
  background-color: #fff;
  box-sizing: border-box;
//   text-align: center;
  padding: 15px 0px;
  z-index: 336;
}
#top_id {
    display: -webkit-box;
    overflow-x: auto;
    .top_sear_item {
        background-color: #F5F6FA;
    }
}
.popup_title {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    text-align: left;
    font-style: normal;
    p {
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    img {
        width: 14px;
    }
}
#padd_id {
    padding: 0px 12px;
}
.btn_body {
    border-top: 1px solid #EEEEEE;
    padding: 12px 15px;
    display: flex;
    justify-content: space-between;
    .call_btn {
        width: 165px;
        height: 42px;
        background: #FFFFFF;
        border-radius: 3px;
        border: 1px solid #D9D9D9;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        text-align: left;
        font-style: normal;
    }
    .save_btn {
        width: 165px;
        height: 42px;
        background: #097DB4;
        border-radius: 3px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        text-align: left;
        font-style: normal;
    }
}
.blue_class {
    background-color: #E6F2F7 !important;
    color: #097DB4 !important;;
    p {
        color: #097DB4 !important;
    }
}
.blue_text {
    color: #097DB4 !important;
}
.tranf_class {
    transform: rotate(180deg);
}
.blue_tranform {
    transform: rotate(180deg);
}
</style>