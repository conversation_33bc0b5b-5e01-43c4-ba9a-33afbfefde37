<!-- 底部弹出的 长按关注公众号 组件 -->
<template>
  <div v-show="visiable" class="hidden-message" @touchmove.prevent>
    <div class="hidden-mask" @click="close"></div>
    <div class="hidden-card-outside">
      <div class="hidden-card">
        <div class="hidden-message-title">
          <span>卡券</span>
          <img @click="close" src="@/assets/img/close1.png" alt="" />
        </div>
      </div>
      <div class="hidden-card body_bottom">
        <div class="ka_body" v-for="(item, index) in kaList">
          <div class="title" :style="index == 0 ? 'margin-top: 0px;' : ''">卡券{{ index + 1 }}</div>
          <div class="border_radio">
            <div v-if="item.type == 'voucherCard'" class="ka_item">
              <p>卡号</p>
              <div class="text_right">
                <p>{{ item.code }}</p>
                <img @click="copyBtn(item.code)" src="@/assets/img/copy_icon.png" alt="" />
              </div>
            </div>
            <div v-if="item.type == 'voucherCard'" class="ka_item">
              <p>密码</p>
              <div class="text_right">
                <p>{{ item.secret }}</p>
                <img @click="copyBtn(item.secret)" src="@/assets/img/copy_icon.png" alt="" />
              </div>
            </div>
            <div v-if="item.type == 'discountCode'" class="ka_item">
              <p>优惠码</p>
              <div class="text_right">
                <p>{{ item.code }}</p>
                <img @click="copyBtn(item.code)" src="@/assets/img/copy_icon.png" alt="" />
              </div>
            </div>
            <div class="ka_item">
              <p>卡券有效期</p>
              <div class="text_right">
                <p>
                  {{ momentFun(item.beginTime) + " - " + momentFun(item.endTime) }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getProductCodeRel } from "@/api/monetary";
export default {
  name: "userPopoView",
  props: ["visiable", "orderForm"],
  data() {
    return {
      kaList: [], // 卡号密码数字列表信息
      createUrl: "",
      uploadIconUrl: "",
      remindList: "",
      filePoint: [],
    };
  },
  mounted() {
    this.getProductCodeRel();
  },
  computed: {},
  methods: {
    // 时间处理方法
    momentFun(time) {
      if (time) {
        return moment(time).format("YYYY-MM-DD HH:mm:ss");
      }
    },
    // 查询卡号优惠码信息接口
    getProductCodeRel() {
      let params = {
        orderId: this.orderForm.orderId,
        type: this.orderForm.type,
      };
      getProductCodeRel(params).then((res) => {
        this.kaList = res.data;
      });
    },
    // 关闭
    close() {
      this.$emit("changeUserPopShow");
    },
    // 复制点击事件
    copyBtn(code) {
      const input = document.createElement("input");
      document.body.appendChild(input);
      input.setAttribute("readonly", "readonly");
      input.setAttribute("value", code);
      input.select();
      input.setSelectionRange(0, code.length);
      try {
        document.execCommand("copy");
      } catch (err) {}
      document.body.removeChild(input);
      this.$createToast({ txt: "复制成功", type: "txt" }).show();
    },
  },
};
</script>

<style scoped lang="scss">
$color: #004da1;
.hidden-message {
  z-index: 999;
  position: fixed;
  width: 100%;
  bottom: 0px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: rgba($color: #000000, $alpha: 0.3);
  .hidden-mask {
    flex: 1;
  }
  .hidden-card-outside {
    // padding-top: 22px !important;
    width: 100%;
    background: #ffffff;
    // padding: 12px 0 0px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 200;
    padding-bottom: 20px;
  }
  .hidden-card {
    width: 100%;
    float: left;
    // padding: 0px 15px;
  }
  .second {
    padding: 0 20px;
    text-align: center;
    position: relative;
    float: left;
    width: 100%;
  }
  .secondBorder .QRcode::before {
    display: inline-block;
    width: 1px;
    height: 120px;
    background-color: #dedede;
    content: "";
    position: absolute;
    top: 0;
    left: -36px;
  }
}
.QRcode {
  position: relative;
  padding: 7px;
  margin: 0 auto 22px;
  width: 114px;
  height: 114px;
  box-sizing: border-box;
  position: relative;
  .info-image {
    width: 100px;
    height: 100px;
  }
  i {
    position: absolute;
    display: inline-block;
    width: 14px;
    height: 14px;
  }
  i:nth-of-type(1) {
    top: 0;
    left: 0;
    border-top: 0.5px solid #1464a1;
    border-left: 0.5px solid #1464a1;
  }
  i:nth-of-type(2) {
    top: 0;
    right: 0;
    border-top: 0.5px solid #1464a1;
    border-right: 0.5px solid #1464a1;
  }
  i:nth-of-type(3) {
    bottom: 0;
    left: 0;
    border-bottom: 0.5px solid #1464a1;
    border-left: 0.5px solid #1464a1;
  }
  i:nth-of-type(4) {
    bottom: 0;
    right: 0;
    border-bottom: 0.5px solid #1464a1;
    border-right: 0.5px solid #1464a1;
  }
}
.hidden-message-title {
  text-align: center;
  color: #333333;
  font-size: 14px;
  margin-bottom: 17px;
  line-height: 20px;
  padding: 0 20px;
  float: left;
  width: 100%;
  margin: 0 auto 22px;
  font-weight: bold;
  font-size: 16px;
  border-bottom: 1px solid #f5f5f5;
  margin-bottom: 20px;
  height: 46px;
  padding-top: 12px;
  img {
    float: right;
    // margin-right: 15px;
  }
}
.bot_body {
  height: 160px;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
}
.blue_text {
  font-weight: 400;
  font-size: 14px;
  color: #097db4;
  margin-top: 85px;
}
.bot_text {
  margin-top: 10px;
  display: flex;
  p {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    margin-left: 6px;
  }
  .name_img {
    width: 24px;
    height: 24px;
    margin-top: -3px;
    .img {
      width: 14px;
      height: 14px;
    }
  }
}
.footer-submit-btn {
  width: 100%;
  box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.06);
  background: #fff;
  text-align: center;
  margin: 0;
  margin-top: 38px;
  height: 65px;
  button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 42px;
    background: var(--color-primary);
    font-size: 14px;
    opacity: 0.9;
  }
}
.img_body {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 160px;
  border: 1px solid rgba(9, 125, 180, 0.2);
  position: relative;
  padding: 5px;
  img {
    height: 100%;
  }
  i {
    position: absolute;
    top: 0px;
    right: 0px;
  }
  .delete_class {
    z-index: 999;
    position: absolute;
    top: 8px;
    right: 8px;
    height: 16px;
  }
}
.text_bot {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 64px;
  height: 23px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 12px;
}
.not_text {
  width: 345px;
  height: 28px;
  background: rgba(255, 77, 79, 0.1);
  border-radius: 2px;
  display: flex;
  align-items: center;
  color: #ff4d4f;
  font-size: 12px;
  padding-left: 10px;
  margin-top: 16px;
}
.body_bottom {
  padding: 0px 15px;
}
.ka_body {
  .title {
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    text-align: left;
    font-style: normal;
    margin-bottom: 10px;
    margin-top: 20px;
  }
  .ka_item {
    height: 32px;
    background-color: #f5f6fa;
    display: flex;
    align-items: center;
    padding: 0px 12px;
    justify-content: space-between;
    .text_right {
      display: flex;
      align-items: center;
      img {
        width: 13px;
        margin-left: 6px;
      }
      p {
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        text-align: center;
        font-style: normal;
      }
    }
    p {
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      text-align: left;
      font-style: normal;
    }
  }
  .border_radio {
    border-radius: 4px;
  }
}
</style>
