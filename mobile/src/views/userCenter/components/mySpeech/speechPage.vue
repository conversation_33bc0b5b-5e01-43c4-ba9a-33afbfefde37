<template>
  <div class="mySpeech-container">
    <tabList needList="tab" class="navigate-bar" @changeItem="changeTab" v-show="true"></tabList>
    <div class="scroll-list">
      <cube-scroll ref="scroll" :data="speechList" :options="options" @pulling-up="getMySpeechListFn">
        <div v-if="speechList.length == 0" class="no-data-tips">暂无数据</div>
        <!-- 行家活动 -->
        <infoItemSearchZly v-if="systemStyle == 'zly'" v-for="(item, index) in speechList" :key="index" :info="item" :iflast="index == speechList.length - 1" :showTag="true" :closeExhibitor="true"></infoItemSearchZly>
        <!-- 易贸医疗 -->
        <speechInfoItem v-if="systemStyle == null" v-for="(item, index) in speechList" :key="index" :info="item" :showTag="true" :iflast="index == speechList.length - 1"></speechInfoItem>
      </cube-scroll>
    </div>
  </div>
</template>
<script>
import tabList from "../tabList";
import { getInfoByDynamicUrl } from "@/api/configurable/common";
import moment from "moment";
import infoItem from "@/components/newCommon/infoItem.vue";
import infoItemSearchZly from "@/components/newCommon/infoItemSearchZly.vue";
import speechInfoItem from "@/components/medicine/speechInfoItem.vue";

export default {
  name: "mySpeech",
  components: { tabList, infoItem, infoItemSearchZly, speechInfoItem },
  data() {
    return {
      tabActive: {}, // tab选中项
      options: {
        // 上拉加载设置
        pullUpLoad: {
          threshold: 30,
          visible: false,
          txt: {
            more: "上拉加载更多",
            noMore: "没有更多信息啦！",
          },
        },
      },
      speechList: [], // 活动列表
      currentPage: 0, // 当前页码
      total: 0, // 数据总条数
    };
  },
  created() {
    document.title = "我的发言";
  },
  methods: {
    moment,
    // 返回个人中心
    goPersonal() {
      this.$router.replace({ name: "userCenter", query: { platformId: localStorage.getItem("platformId") } });
    },
    // 切换 tab 选中项 重置 加载参数
    changeTab(tab) {
      this.tabActive = tab;
      this.currentPage = 0;
      this.speechList = [];
      this.total = 0;
      this.$refs.scroll.scrollTo(0, 0);
      this.getMySpeechListFn();
    },
    // 获取列表
    getMySpeechListFn() {
      let params = {};
      this.tabActive.menuParams.forEach((item) => {
        params[item.menuParamKey] = item.menuParamValue;
      });
      params.pageNum = this.currentPage + 1;
      getInfoByDynamicUrl(this.tabActive.apiUrl, params).then((res) => {
        if (res.code == this.$successCode) {
          if (res.data) {
            console.log(this.speechList);
            if (res.data.list && res.data.list.length) {
              this.speechList = this.speechList.concat(res.data.list);
              this.currentPage++;
            }
            this.total = res.data.total;
          }
          // 已加载完毕  没有更多信息啦！
          if (this.total == this.speechList.length) {
            this.$refs.scroll.forceUpdate();
          }
          this.speechList.forEach((item, index) => {
            if (item) {
              this.speechList[index].type = this.TYPE[6];
            }
          });
        }
      });
    },
    // 详情
    toDetails(item) {
      this.naviToDetails(this.$route.query.platformId, item.itemType, item);
    },
  },
};
</script>

<style lang="scss" scoped>
$primaryColor: #ff620d;
.mySpeech-container {
  background: #fff;
  .navigate-bar {
    font-size: 15px;
    font-weight: 400;
    color: #333;
    .cubeic-arrow {
      float: left;
      transform: rotateY(180deg);
      color: #999;
    }
  }
  .scroll-list {
    height: calc(100% - 50px);
    .no-data-tips {
      > img {
        width: 185px;
        height: 142px;
      }
      > div {
        margin-top: 20px;
      }
      margin-top: 170px;
      text-align: center;
      color: #999;
      display: flex;
      flex-direction: column;
      align-items: center;
      flex-wrap: wrap;
    }
    .collect-item {
      margin-bottom: 10px;
      line-height: 20px;
      width: 175px;
      height: 180px;
      background: #fff;
      border-radius: 4px;
      display: inline-block;
      margin-right: 5px;
      .collect-item-img {
        width: 175px;
        height: 105px;
        border-radius: 4px;
        margin-bottom: 5px;
      }
      .collect-item-title {
        line-height: 17px;
        color: #333;
        font-size: 12px;
        font-weight: 400;
        margin-bottom: 8px;
        padding: 0 5px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: break-word;
      }
      .collect-item-info {
        color: #999;
        font-size: 10px;
        padding: 0 5px;
        line-height: 20px;
        p {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          svg {
            vertical-align: middle;
            margin-top: -3px;
            margin-right: 5px;
          }
        }
      }
    }
  }
}
</style>
