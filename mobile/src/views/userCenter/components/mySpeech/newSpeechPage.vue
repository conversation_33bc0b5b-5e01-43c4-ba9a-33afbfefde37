<template>
  <div class="speech-wrap">
    <detail-page ref="detailPage" :hasSearch="true" :hasFilter="true" :optionFilter="optionFilter" @tabClick="tabClick" @searchKeyWord="searchKeyWord">
      <template #filterItem>
        <commonScroll ref="commonScroll" :getListData="getListData" :listParams="activeTab ? configurationHasEnded : configurationNotStarted" :conversion="conversion">
          <template #default="slotProps">
            <speechInfoItem :info="slotProps.item" :showTag="true"></speechInfoItem>
          </template>
        </commonScroll>
      </template>
    </detail-page>
  </div>
</template>
<script>
import DetailPage from "../detailPage";
import { getSpeechList } from "@/api/userCenter";
import commonScroll from "../detailPage/commonScroll";
import SpeechInfoItem from "@/components/medicine/speechInfoItem";
export default {
  data() {
    return {
      optionFilter: [
        { name: "未开始", hasFilter: false },
        { name: "已结束", hasFilter: false },
      ],
      configurationNotStarted: {
        status: 1,
        keyword: "",
      },
      configurationHasEnded: {
        status: 0,
        keyword: "",
      },
      // 当前激活
      activeTab: 0,
      getListData: getSpeechList,
    };
  },
  created() {},
  components: { DetailPage, commonScroll, SpeechInfoItem },
  mounted() {},
  computed: {},
  methods: {
    tabClick(index) {
      if (this.activeTab == index) return;
      this.activeTab = index;
    },
    searchKeyWord(keyword) {
      this.configurationNotStarted.keyword = keyword;
      this.configurationHasEnded.keyword = keyword;
      this.reload();
    },
    // 重新加载数据
    reload() {
      this.$nextTick(() => {
        this.$refs.detailPage.$children[1].$refs.tabs[this.activeTab].$children[0].getList(true);
      });
    },
    // 转换数据
    conversion(data) {
      data.forEach((item) => {
        this.$set(item, "type", this.TYPE[6]);
      });
    },
  },
};
</script>
<style scoped lang="scss">
.speech-wrap {
  width: 100%;
  height: 100%;
}
</style>
