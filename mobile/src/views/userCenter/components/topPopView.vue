<!-- 底部弹出的 长按关注公众号 组件 -->
<template>
    <div class="hidden-message">
        <div class="hidden-mask" @click="close"></div>
        <div class="hidden-card-outside">
			<div class="hidden-card body_bottom">
                <section class="card-body">
                    <div class="popup_content">
            		    <div class="top_sear_body" style="margin-bottom: 12px;" id="padd_id">
            		        <div class="top_sear_body_item" id="top_id">
            		            <div class="top_sear_item" @click="zongBtn(0)" :class="seleZonForm.selectId != 0 || seleZonForm.searType == 0 ? 'blue_class':''">
            		                <p>{{ seleZonForm.selectType }}</p>
            		                <img :class="seleZonForm.searType != 0 ? 'tranf_class':''" v-if="seleZonForm.selectId != 0 || seleZonForm.searType == 0" src="@/assets/img/shai_blue_icon.png" alt="">
            		                <img v-else src="@/assets/img/bot_icon.png" alt="">
            		            </div>
            		            <div class="top_sear_item" @click="zongBtn(2)" :class="seleZonForm.productClassifyIds&&seleZonForm.productClassifyIds.length || seleZonForm.searType == 2 ? 'blue_class':''">
            		                <p>{{ seleZonForm.productClassifyName&&seleZonForm.productClassifyName[0] ? seleZonForm.productClassifyName[0]:"分类" }}<span v-if="seleZonForm.productClassifyIds&&seleZonForm.productClassifyIds.length > 1">...</span></p>
            		                <img :class="seleZonForm.searType != 2 ? 'tranf_class':''" v-if="seleZonForm.productClassifyIds&&seleZonForm.productClassifyIds.length || seleZonForm.searType == 2" src="@/assets/img/shai_blue_icon.png" alt="">
            		                <img v-else src="@/assets/img/bot_icon.png" alt="">
            		            </div>
            		            <div class="top_sear_item" @click="zongBtn(1)" :class="seleZonForm.produId != null || seleZonForm.searType == 1 ? 'blue_class':''">
            		                <p>{{ seleZonForm.produName ? seleZonForm.produName : "全部" }}</p>
            		                <img v-if="seleZonForm.produId != null || seleZonForm.searType == 1" :class="seleZonForm.searType != 1 ? 'tranf_class':''" src="@/assets/img/shai_blue_icon.png" alt="">
            		                <img v-else src="@/assets/img/bot_icon.png" alt="">
            		            </div>
            		            <div class="top_sear_item" style="margin-right: 0px;" @click="jinBtn(3)" :class="seleZonForm.jinShow ? 'blue_class':''">
            		                <p>仅看我可兑</p>
            		            </div>
            		        </div>
            		        <div class="border_box"></div>
            		        <img class="sear_class" src="@/assets/img/yi_sear_icon.png" alt="" @click="searchBtn">
            		    </div>
            		    <div class="popup_title" id="padd_id">
            		        <p class="select_p" v-if="seleZonForm.searType == 0" v-for="item in zongList" :key="index" @click="seleBtn(item,0)">
            		            <span :class="seleZonForm.selectId == item.selectId ? 'blue_text':''">{{ item.selectType }}</span>
            		            <img v-if="seleZonForm.selectId == item.selectId" src="@/assets/img/yi_sel_icon.png" alt="">
            		        </p>
            		        <p class="select_p" v-if="seleZonForm.searType == 1" v-for="item in allList" :key="index" @click="seleBtn(item,1)">
            		            <span :class="seleZonForm.produId == item.produId ? 'blue_text':''">{{ item.selectType }}</span>
            		            <img v-if="seleZonForm.produId == item.produId" src="@/assets/img/yi_sel_icon.png" alt="">
            		        </p>
            		        <p class="select_p" v-if="seleZonForm.searType == 2" v-for="item in prodList" :key="index" @click="seleBtn(item,2)">
            		            <span :class="seleZonForm.productClassifyIds.includes(item.id) ? 'blue_text':''">{{ item.productClassify }}</span>
            		            <img v-if="seleZonForm.productClassifyIds.includes(item.id)" src="@/assets/img/yi_sel_icon.png" alt="">
            		        </p>
            		    </div>
            		    <div class="btn_body">
            		        <div class="call_btn" @click="calBtn">取消</div>
            		        <div class="save_btn" @click="searcherBtn">确定</div>
            		    </div>
            		</div>
                </section>
			</div>
        </div>
    </div>
</template>

<script>
import { getProductClassifyList } from "@/api/monetary";
export default {
    name: 'userPopoView',
	data() {
		return {
			prodList: [],
			allList: [
                {
                    selectType: "全部",
                    produId: null,
                },
                {
                    selectType: "实物商品",
                    produId: "physical",
                },
                {
                    selectType: "虚拟商品",
                    produId: "virtual",
                },
            ],
			zongList: [
                {
                    selectType: "综合推荐",
                    selectId: 0,
                },
                {
                    selectType: "易币从低到高",
                    selectId: 1,
                },
                {
                    selectType: "易币从高到低",
                    selectId: 2,
                },
            ],
			seleZonForm: {},
		}
	},
	computed: {
        showPopup() {
    	  return this.$store.state.showPopup;
    	},
    },
	watch: {
	},
	mounted() {
		this.seleZonForm = JSON.parse(JSON.stringify(this.$store.state.mallHomePopForm));
		if (this.seleZonForm.searType == 2) {
			this.getProductClassifyList();
		}
	},
    computed: {
    },
    methods: {
		searchBtn() {

		},
		jinBtn() {
			this.seleZonForm.jinShow = !this.seleZonForm.jinShow;
			this.searcherBtn();
		},
		// 确定搜索事件
        searcherBtn() {
            this.$store.commit("pageMallFun", {
                pageNum: 1,
                type: "save",
            });
            this.$store.commit("showPopoFun",false);
            this.$store.commit("mallHomePopFun",this.seleZonForm);
        },
		// 取消
        calBtn() {
            this.$store.commit("showPopoFun",false);
        },
		// 筛选选择
        seleBtn(val,type) {
            switch (type) {
                case 0:
                    this.seleZonForm.selectType = val.selectType;
                    this.seleZonForm.selectId = val.selectId;
                    break;
                case 1:
                    this.seleZonForm.produName = val.selectType;
                    this.seleZonForm.produId = val.produId;
                    break;
                case 2:
                    let dex = this.seleZonForm.productClassifyIds.includes(val.id);
                    if (dex) {
                        this.seleZonForm.productClassifyIds.splice(this.seleZonForm.productClassifyIds.findIndex(item => item == val.id),1);
                        this.seleZonForm.productClassifyName.splice(this.seleZonForm.productClassifyName.findIndex(item => item == val.productClassify),1);
                    } else {
                        this.seleZonForm.productClassifyIds.push(val.id);
                        this.seleZonForm.productClassifyName.push(val.productClassify);
                        this.$forceUpdate();
                    }
                    break;
                default:
                    break;
            }
        },
		// 查询全部分类
        getProductClassifyList() {
            let params = {
                sysCode: "system",
            }
            getProductClassifyList(params).then(res => {
                this.prodList = res.data;
            })
        },
		// 综合推荐点击筛选
        zongBtn(index) {
            if (this.seleZonForm.searType == index || this.seleZonForm.searType == null) this.$store.commit("showPopoFun",!this.$store.state.showPopup);
            if (index == 2) {
                this.getProductClassifyList();
            }
			this.$set(this.seleZonForm,"searType",index);
			this.$forceUpdate();
        },
        // 关闭弹窗事件
        close() {
            this.$emit("close");
        },
    },
}
</script>

<style scoped lang="scss">
$color: #004da1;
.hidden-message {
    z-index: 999;
    position: fixed;
    width: 100%;
    bottom: 0px;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: rgba($color: #000000, $alpha: 0.3);
    left: 0px;
	z-index: 9999999999999;
    .hidden-mask {
        // flex: 1;
		z-index: 99999999999999999;
    }
	.hidden-card-outside{
        // padding-top: 22px !important;
		width: 100%;
		background: #ffffff;
		// padding: 12px 0 0px;
        border-radius: 0px 0px 4px 4px;
		position: fixed;
		left:0;
		top:0;
		z-index: 200;
	}
    .hidden-card {
        width: 100%;
		float: left;
        // padding: 0px 15px;
    }
		.second{
			padding: 0 20px;
			text-align: center;
			position: relative;
			float: left;
			width: 100%;
		}
		.secondBorder .QRcode::before{
				display: inline-block;
				width: 1px;
				height: 120px;
				background-color: #DEDEDE;
				content: "";
				position: absolute;
				top: 0;
				left: -36px;
		}
}
.QRcode{
	position: relative;
	padding: 7px;
	margin: 0 auto 22px;
	width: 114px;
	height: 114px;
	box-sizing: border-box;
	position: relative;
	.info-image {
		width: 100px;
		height: 100px;
	}
	i{
		position: absolute;
		display: inline-block;
		width: 14px;
		height: 14px;
	}
	i:nth-of-type(1){
		top: 0;
		left: 0;
		border-top: 0.5px solid #1464A1;
		border-left: 0.5px solid #1464A1;
	}
	i:nth-of-type(2){
		top: 0;
		right: 0;
		border-top: 0.5px solid #1464A1;
		border-right: 0.5px solid #1464A1;
	}
	i:nth-of-type(3){
		bottom: 0;
		left: 0;
		border-bottom: 0.5px solid #1464A1;
		border-left: 0.5px solid #1464A1;
	}
	i:nth-of-type(4){
		bottom: 0;
		right: 0;
		border-bottom: 0.5px solid #1464A1;
		border-right: 0.5px solid #1464A1;
	}
}
.hidden-message-title {
	text-align: center;
	color: #333333;
	font-size: 14px;
	margin-bottom: 17px;
	line-height: 20px;
	padding: 0 20px;
	float: left;
	width: 100%;
	margin: 0 auto 22px;
    font-weight: bold;
    font-size: 16px;
    border-bottom: 1px solid #F5F5F5;
    margin-bottom: 20px;
    height: 46px;
    padding-top: 12px;
    img {
        float: right;
        // margin-right: 15px;
    }
}
</style>
<style lang='scss' scoped>
.top_sear_body {
    align-items: center;
    display: flex;
    .top_sear_item {
        padding: 0px 8px;
        width: fit-content;
        background-color: #FFFFFF;
        display: flex;
        align-items: center;
        margin-right: 8px;
        height: 26px;
        flex-shrink: 0;
        img {
            width: 12px;
            height: 12px;
            margin-left: 4px;
        }
        p {
            font-weight: 400;
            font-size: 13px;
            color: #333333;
            text-align: left;
            font-style: normal;
        }
    }
    .sear_class {
        height: 16px;
        margin-left: 8px;
    }
    .top_sear_body_item {
        display: flex;
        align-items: center;
        max-width: calc(100% - 50px);
        overflow-x: auto; /* 确保水平滚动 */
        touch-action: pan-x; /* 允许横向滚动 */
        -webkit-overflow-scrolling: touch; /* 在iOS上优化滚动 */
    }
}
.border_box {
    width: 1px;
    height: 16px;
    background: #DDDDDD;
    margin-left: 25px;
}
.popup_content {
    width: 100%;
    background-color: #fff;
    box-sizing: border-box;
    padding: 15px 0px;
    z-index: 336;
    padding-bottom: 0px;
    height: fit-content;
    z-index: 666666;
    position: fixed;
}
#top_id {
    display: -webkit-box;
    overflow-x: auto;
    .top_sear_item {
        background-color: #F5F6FA;
    }
}
.popup_title {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    text-align: left;
    font-style: normal;
    padding: 0px 15px !important;
    p {
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    img {
        width: 14px;
        height: 12.88px;
    }
}
#padd_id {
    padding: 0px 12px;
    .select_p {
        height: 42px;
    }
}
.btn_body {
    border-top: 1px solid #EEEEEE;
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    height: 65px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0px 15px;
    .call_btn {
        width: 165px;
        height: 42px;
        background: #FFFFFF;
        border-radius: 3px;
        border: 1px solid #D9D9D9;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        text-align: left;
        font-style: normal;
        margin-right: 15px;
    }
    .save_btn {
        width: 165px;
        height: 42px;
        background: #097DB4;
        border-radius: 3px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        text-align: left;
        font-style: normal;
    }
}
.blue_class {
    background-color: #E6F2F7 !important;
    color: #097DB4 !important;;
    p {
        color: #097DB4 !important;
    }
}
.blue_text {
    color: #097DB4 !important;
}
.tranf_class {
    transform: rotate(180deg);
}
.blue_tranform {
    transform: rotate(180deg);
}
</style>