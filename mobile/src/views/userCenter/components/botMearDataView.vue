<template>
  <div>
    <cube-scroll v-if="newList.length != 0 && scrollType" class="table_scroll" ref="classifyScroll" :data="newList" :options="options" @pulling-down="loadDownFun" @pulling-up="loadFun">
      <mallTableView v-if="newList.length" :newList="newList"></mallTableView>
    </cube-scroll>
    <mallTableView v-if="newList.length != 0 && !scrollType" :newList="newList" ref="mall_top_ref" class="mall_top_ref"></mallTableView>
    <div class="que_class" v-if="!newList.length">
      <img src="@/assets/img/queIcon.png" alt="" />
      <p>暂无数据</p>
    </div>
  </div>
</template>

<script>
import mallTableView from "../components/mallTableView.vue";
export default {
  props: ["scrollType"],
  data() {
    return {
      distanceFromTop: 0,
      scrollShow: false,
      options: {
        pullUpLoad: {
          threshold: 50,
          txt: {
            more: "",
            noMore: "没有更多信息啦！",
          },
        },
        pullDownRefresh: {
          threshold: 60,
          stopTime: 1000,
          txt: "更新成功",
        },
        scrollbar: false,
      },
      newList: [],
      forData: {},
    };
  },
  watch: {
    pageMallForm: {
      handler(val) {
        if (val.type == "save") {
          this.getScrollFun();
        }
      },
    },
    showPopup: {
      handler(val) {
        if (val) {
          // 如果有筛选条件则自动滚动到顶部
          if (this.mallHomePopForm.selectId != 0 || this.mallHomePopForm.productClassifyIds.length || this.mallHomePopForm.produId || this.mallHomePopForm.jinShow) {
            this.getScrollFun();
          }
        }
      },
      deep: true,
    },
    $route: {
      handler(val) {
        if (val.path != "/mall") return;
        this.scrollFun();
      },
      deep: true,
    },
    getProductInfoForm: {
      handler() {
        if (!this.isPreviewEnviroment) {
          if (this.pageMallForm.type == "search") {
            this.newList = this.newList.concat(this.getProductInfoForm.list);
          } else {
            this.newList = this.getProductInfoForm.list;
          }
          this.scrollShow = false;
        } else {
          this.getProductInfoForm.layoutInfoList.forEach((item, index) => {
            if (item.modelType == "topUp" && item.webPlatform == 2) {
              item.layoutContentRelList.forEach((demo, desc) => {
                item.layoutContentRelList[desc].logo = demo.logo ? demo.logo.split(",")[0] : [];
              });
              this.newList = this.getProductInfoForm.layoutInfoList[index].layoutContentRelList;
            }
          });
        }
      },
      deep: true,
    },
  },
  computed: {
    mallHomePopForm() {
      return this.$store.state.mallHomePopForm;
    },
    showPopup() {
      return this.$store.state.showPopup;
    },
    mallScrollNumber() {
      return this.$store.state.mallScrollNumber;
    },
    getProductInfoForm() {
      return this.$store.state.getProductInfoForm;
    },
    pageMallForm() {
      return this.$store.state.pageMallForm;
    },
  },
  components: {
    mallTableView,
  },
  mounted() {
    if (!this.scrollType) {
      this.scrollFun();
      this.popTitTopFun();
    }
  },
  beforeDestroy() {
    if (!this.scrollType) {
    }
  },
  methods: {
    // 自动滚动事件
    getScrollFun() {
      this.$store.commit("mallScrollFun", this.distanceFromTop - 90);
      this.scrollFun(this.distanceFromTop - 90);
    },
    // 距离顶部的距离
    popTitTopFun() {
      this.$nextTick(() => {
        setTimeout(() => {
          const element = document.querySelector(".mall_top_ref");
          if (element) {
            // 获取元素的位置信息
            const rect = element.getBoundingClientRect();
            // 获取元素距离顶部的距离
            this.distanceFromTop = rect.top;
          } else {
            this.popTitTopFun();
          }
        }, 300);
      });
    },
    // 滚动加载记录
    scrollFun(scro) {
      const scrollContainer = document.querySelector(".top_body_scroll");
      this.$nextTick(() => {
        setTimeout(() => {
          scrollContainer.scrollTop = scro ? scro : this.mallScrollNumber;
          this.$forceUpdate();
        }, 300);
      });
      if (scro) return;
      scrollContainer.addEventListener("scroll", (val) => {
        const container = scrollContainer;
        const windowHeight = container.clientHeight; // 容器可视高度
        const scrollHeight = container.scrollHeight; // 容器总高度
        const scrollTop = container.scrollTop; // 当前滚动高度
        this.$store.commit("mallScrollFun", scrollTop);
        if (this.scrollShow) return;
        // 判断是否滚动到底部
        if (scrollTop + windowHeight >= scrollHeight - 10) {
          // 使用一个小的容差值（例如10px）避免浮动误差
          this.scrollShow = true;
          if (this.getProductInfoForm.total > this.newList.length) {
            this.$store.commit("pageMallFun", {
              pageNum: this.pageMallForm.pageNum + 1,
              type: "search",
            });
          }
        }
      });
    },
    // 滚动触底事件
    loadFun() {
      setTimeout(() => {
        if (this.getProductInfoForm.total > this.newList.length) {
          this.$store.commit("pageMallFun", {
            pageNum: this.pageMallForm.pageNum + 1,
            type: "search",
          });
          this.$nextTick(() => {
            this.$refs.classifyScroll.refresh();
          });
        } else {
          this.$refs.classifyScroll.forceUpdate();
        }
      }, 1000);
    },
    loadDownFun() {
      this.newList = [];
      this.$store.commit("pageMallFun", {
        pageNum: 1,
        type: "rest",
      });
      // this.$nextTick(() => {
      this.$refs.classifyScroll.forceUpdate();
      // })
    },
  },
};
</script>

<style lang="scss" scoped>
.bot_recome_body {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .top_img_box {
    width: 170px;
    padding: 8px;
    background-color: #ffffff;
    margin-bottom: 10px;
    position: relative;
    .img_box_item {
      width: 171px;
      height: 170px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .posi_ab_class {
      position: absolute;
      top: 6px;
      right: 6px;
      width: 56px;
      height: 16px;
    }
  }
  .hui_color {
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    text-align: left;
    font-style: normal;
    margin-top: 8px;
    height: 40px; /* 两行的高度 */
    white-space: normal; /* 确保文本可以折行 */
    overflow: hidden; /* 隐藏超出的内容 */
    display: -webkit-box; /* 使用弹性盒子模型 */
    -webkit-line-clamp: 2; /* 限制显示的行数为2行 */
    -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
    line-height: 20px; /* 行高 */
    text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
  }
  .red_color {
    font-weight: 500;
    font-size: 16px;
    color: #ff4d4f;
    text-align: left;
    font-style: normal;
    margin-top: 8px;
    .price_font {
      font-weight: 500;
      font-size: 16px;
      color: #ff4d4f;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      span {
        font-size: 12px;
      }
    }
    .old_price_font {
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      line-height: 16px;
      text-align: left;
      font-style: normal;
      text-decoration-line: line-through;
      margin-left: 6px;
    }
  }
}
.que_class {
  width: 100%;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  // background-color: #FFFFFF;
  img {
    width: 120px;
  }
  p {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    text-align: left;
    font-style: normal;
    margin-top: 16px;
  }
}
.table_scroll {
  margin-top: 12px;
  max-height: 90vh;
  overflow-y: scroll;
  // ::v-deep .cube-scroll-list-wrapper {
  //     display: flex;
  //     flex-wrap: wrap;
  //     justify-content: space-between;
  // }
  // ::v-deep .cube-scroll-list-wrapper {
  //     min-height: fit-content !important;
  // }
}
</style>
