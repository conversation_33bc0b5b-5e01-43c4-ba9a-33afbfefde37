<template>
    <div class="userCenter-tab-list">
        <div :class="{'tab-bar': needList=='tab','tag-list': needList=='tag'}">
            <ul>
                <li v-for="item in list" :key="item.id" :class="{active: activeItem.id==item.id}" class="list-item" @click="changeItem(item)">{{item.name}}</li>
            </ul>
        </div>
    </div>
</template>
<script>
import { getOfficialMenu } from '@/api/configurable/home'

export default {
    name: "tabList",
    props: ['needList'],
    data () {
        return {
            list: null, // 列表
            activeItem: null, // 选中项 
        }
    },
    created () {
         getOfficialMenu({ pid: 0, platformId: localStorage.getItem('platformId') }).then( async res => {
            if (res.code != this.$successCode) { return }
            this.list = await this.getTargetObjectWhereAttrIs(res.data, 'menuKey', this.$route.name).children
            this.activeItem = this.list[0]             
            this.$emit('changeItem', this.activeItem);       
        })
    },
    mounted () {
    },
    methods: {
        // 切换 选中项
        changeItem (item) {
            this.activeItem = item;
            this.$emit('changeItem', this.activeItem);
        }
    }
}
</script>

<style lang="scss" scoped>
$primaryColor: var(--color-primary);
.userCenter-tab-list {
    .tab-bar {
        width: 100%;
        height: 45px;
        // border-top: 0.5px solid rgba(0,0,0,.1);
        border-bottom: 0.5px solid #eee;
        ul {
            white-space: nowrap;
            line-height: 45px;
            font-size: 14px;
            color: #999999;
            width: 100%;
            display: flex;
            justify-content: space-around;
            li {
                width: 100%;
                text-align: center;
                &.active {
                    font-weight: 500;
                    color: #333333;
                    position: relative;
                    &::after {
                        content: "";
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        margin: auto;
                        width: 16px;
                        height: 2px;
                        border-radius: 2px;
                        background-color: $primaryColor;
                    }
                }
            }
        }
    }
    .tag-list {
        padding: 15px;
        li {
            display: inline-block;
            padding: 0 10px;
            line-height: 24px;
            height: 24px;
            color: #666;
            font-size: 14px;
            border: 1px solid #eee;
            border-radius: 12px;
            margin-right: 10px;
            &.active {
                color: $primaryColor;
                border-color: $primaryColor;
            }
        }
    }
}
</style>

