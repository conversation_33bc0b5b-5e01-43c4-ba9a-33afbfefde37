<template>
  <div class="body_box" v-if="forData.status">
    <topSearchView></topSearchView>
    <botMearDataView></botMearDataView>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import topSearchView from "../components/topSearchView.vue";
import botMearDataView from "../components/botMearDataView.vue";
export default {
  props: ["type"],
  data() {
    return {
      oneStatus: false,
      forData: {},
      typeList: [
        { value: "all", label: "全部" },
        { value: 1, label: "仅看收费直播" },
        { value: 0, label: "仅看免费直播" },
      ],
    };
  },
  computed: {
    getLayoutInfoForm() {
      return this.$store.state.getLayoutInfoForm;
    },
    topUpStatus() {
      const { layoutInfoList = [] } = this.getLayoutInfoForm;
      const findLayoutInfo = () => layoutInfoList.find((item) => item.modelType === 'topUp') || {};
      return findLayoutInfo().status;
    },
  },
  watch: {
    getLayoutInfoForm: {
        handler() {
            this.getLayoutInfoForm.layoutInfoList.forEach(item=>{
                if (item.modelType == "topUp") {
                    this.forData = item;
                }
            })
        },
        deep: true,
    },
    getProductInfoForm: {
      handler() {
        this.$store.state.getProductInfoForm.layoutInfoList.forEach((item) => {
          this.newList = item.layoutContentRelList;
          this.forData = item;
          this.$forceUpdate();
        });
      },
      deep: true,
    },
  },
  components: {
    topSearchView,
    botMearDataView,
  },
  mounted() {
    this.getLayoutInfoList();
  },
  methods: {
    ...mapActions(['getLayoutInfoList']),
    seleBtn() {
      if (!this.oneStatus) {
        this.$refs.dropMenu.show();
      } else {
        this.$refs.dropMenu.hide();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.body_box {
  margin-top: 12px;
}
</style>
<style lang="scss">
// .list-drop-menu {
//   width: 200px;
//   position: absolute;
//   top: 33px;
//   right: 0;
//   background: #fff;
//   max-height: none;
//   border-radius: 4px;
//   padding: 0;
//   box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
//   .cube-tip-close {
//     display: none;
//   }
//   .cube-tip-angle {
//     top: 1px;
//     right: 12px !important;
//     &::before {
//       border-width: 0;
//       width: 9px;
//       height: 9px;
//       background: #fff;
//       transform: rotate(45deg);
//       box-shadow: 0 -8px 10px 0px rgba(0, 0, 0, 0.1);
//     }
//   }
//   .cube-tip-content {
//     line-height: 38px !important;
//     color: #999;
//     font-size: 14px;
//     z-index: 1;
//     white-space: nowrap;
//     & > div {
//       padding: 0 16px;
//       & + div {
//         border-top: 1px solid rgba(0, 0, 0, 0.06);
//       }
//       &.active {
//         color: #00629F;
//       }
//     }
//   }
// }
</style>
