<template>
    <div>
        <div class="top_sear_body" style="margin-bottom: 12px;">
            <div class="top_sear_body_item">
                <div class="top_sear_item" @click="zongBtn(0)" :class="seleYuanZonForm.selectId != 0 ? 'blue_class':''">
                    <p>{{ seleYuanZonForm.selectType }}</p>
                    <img class="blue_tranform" v-if="seleYuanZonForm.selectId != 0" src="@/assets/img/shai_blue_icon.png" alt="">
                    <img v-else src="@/assets/img/bot_icon.png" alt="">
                </div>
                <div class="top_sear_item" @click="zongBtn(2)" :class="seleYuanZonForm.productClassifyIds.length ? 'blue_class':''">
                    <p>{{ seleYuanZonForm.productClassifyName[0] ? seleYuanZonForm.productClassifyName[0]:"分类" }}<span v-if="seleYuanZonForm.productClassifyIds.length > 1">...</span></p>
                    <img class="blue_tranform" v-if="seleYuanZonForm.productClassifyIds.length" src="@/assets/img/shai_blue_icon.png" alt="">
                    <img v-else src="@/assets/img/bot_icon.png" alt="">
                </div>
                <div class="top_sear_item" @click="zongBtn(1)" :class="seleYuanZonForm.produId != null ? 'blue_class':''">
                    <p>{{ seleYuanZonForm.produName ? seleYuanZonForm.produName : "全部" }}</p>
                    <img class="blue_tranform" v-if="seleYuanZonForm.produId != null" src="@/assets/img/shai_blue_icon.png" alt="">
                    <img v-else src="@/assets/img/bot_icon.png" alt="">
                </div>
                <div class="top_sear_item" style="margin-right: 0px;" @click="jinBtn(3)" :class="seleYuanZonForm.jinShow ? 'blue_class':''">
                    <p>仅看我可兑</p>
                </div>
            </div>
            <div class="border_box"></div>
            <img class="sear_class" src="@/assets/img/yi_sear_icon.png" alt="" @click="searchBtn">
        </div>
        
    </div>
</template>



<script>
import { getProductClassifyList } from "@/api/monetary";
import { mapActions } from 'vuex';
import topPopView from "../components/topPopView.vue";
import moment from 'moment';
export default {
    data(){
        return {
            seleZonForm: {
                selectType: "综合推荐",
                selectId: 0,
                produName: null,
                productClassifyIds: [],
                productClassifyName: [],
            },
            seleYuanZonForm: {
                selectType: "综合推荐",
                selectId: 0,
                produName: null,
                productClassifyIds: [],
                productClassifyName: [],
                searType: null,
                jinShow: false,
            },
            zongList: [
                {
                    selectType: "综合推荐",
                    selectId: 0,
                },
                {
                    selectType: "易币从低到高",
                    selectId: 1,
                },
                {
                    selectType: "易币从高到低",
                    selectId: 2,
                },
            ],
            allList: [
                {
                    selectType: "全部",
                    produId: null,
                },
                {
                    selectType: "实物商品",
                    produId: "physical",
                },
                {
                    selectType: "虚拟商品",
                    produId: "virtual",
                },
            ],
            prodList: [],
        }
    },
    computed: {
        mallHomePopForm() {
            return this.$store.state.mallHomePopForm;
        },
        pointInfos() {
            return this.$store.state.pointInfos;
        },
        pageMallForm() {
            return this.$store.state.pageMallForm;
        }
    },
    watch: {
        mallHomePopForm: {
            handler(val) {
                this.seleYuanZonForm = val;
            },
            deep: true
        },
        pageMallForm: {
            handler() {
                if (this.pageMallForm.type != "rest") {
                    this.saveBtn();
                }
            },
            deep: true
        },
    },
    components: {
        topPopView,
    },
    mounted(){
        this.$store.commit("pageMallFun", {
            pageNum: 1,
            type: "rest",
        });
        this.saveBtn();
        if (!this.isPreviewEnviroment) {
            this.getMemberPoints();
        }
    },
    methods: {
        // 确定搜索事件
        searcherBtn() {
            this.$store.commit("pageMallFun", {
                pageNum: 1,
                type: "rest",
            });
            this.$store.commit("showPopoFun",false);
            this.saveBtn();
        },
        // 查询全部分类
        getProductClassifyList() {
            let params = {
                sysCode: "system",
            }
            getProductClassifyList(params).then(res => {
                this.prodList = res.data;
            })
        },
        // 重置
        reset() {
            let demo = {
                pageNum: 1,
                pageSize: 10,
                selectType: 0,
                productClassifyIds: [],
            }
            this.getProductInfoList(demo);
        },
        ...mapActions(['getLayoutInfoList', 'getProductInfoList','getMemberPoints']),
        // 搜索确定事件
        saveBtn() {
            let demo = {
                pageNum: this.pageMallForm.pageNum,
                pageSize: 10,
                selectType: this.seleYuanZonForm.selectId,
                productType: this.seleYuanZonForm.produId,
                balance: this.seleYuanZonForm.jinShow ? this.pointInfos.balance:null,
                productClassifyIds: this.seleYuanZonForm.productClassifyIds,
            }
            this.getProductInfoList(demo);
        },
        // 点击进入搜索
        searchBtn() {
            this.$router.push({
                path: '/mall/search',
            });
            this.$store.commit("showPopoFun",false)
        },
        // 仅看我可兑
        jinBtn() {
            this.seleYuanZonForm.jinShow = !this.seleYuanZonForm.jinShow
            this.$store.commit("mallHomePopFun",this.seleYuanZonForm);
            this.searcherBtn();
        },
        // 综合推荐点击筛选
        zongBtn(index) {
            this.$store.commit("showPopoFun",true);
            if (index == 2) {
                this.getProductClassifyList();
            }
            this.$store.commit("mallHomePopFun",{...this.seleYuanZonForm,searType:index});
        },
    },
}
</script>



<style lang='scss' scoped>
.top_sear_body {
    align-items: center;
    display: flex;
    .top_sear_item {
        padding: 0px 8px;
        width: fit-content;
        background-color: #FFFFFF;
        display: flex;
        align-items: center;
        margin-right: 8px;
        height: 26px;
        flex-shrink: 0;
        img {
            width: 12px;
            height: 12px;
            margin-left: 4px;
        }
        p {
            font-weight: 400;
            font-size: 13px;
            color: #333333;
            text-align: left;
            font-style: normal;
        }
    }
    .sear_class {
        height: 16px;
        margin-left: 8px;
    }
    .top_sear_body_item {
        display: flex;
        align-items: center;
        max-width: calc(100% - 50px);
        overflow-x: auto; /* 确保水平滚动 */
        touch-action: pan-x; /* 允许横向滚动 */
        -webkit-overflow-scrolling: touch; /* 在iOS上优化滚动 */
    }
}
.border_box {
    width: 1px;
    height: 16px;
    background: #DDDDDD;
    margin-left: 25px;
}
.popup_content {
    width: 100%;
    background-color: #fff;
    box-sizing: border-box;
    padding: 15px 0px;
    z-index: 336;
    padding-bottom: 0px;
    height: fit-content;
    z-index: 666666;
    position: fixed;
}
#top_id {
    display: -webkit-box;
    overflow-x: auto;
    .top_sear_item {
        background-color: #F5F6FA;
    }
}
.popup_title {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    text-align: left;
    font-style: normal;
    padding: 0px 15px !important;
    p {
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    img {
        width: 14px;
        height: 12.88px;
    }
}
#padd_id {
    padding: 0px 12px;
    .select_p {
        height: 42px;
    }
}
.btn_body {
    border-top: 1px solid #EEEEEE;
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    height: 65px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0px 15px;
    .call_btn {
        width: 165px;
        height: 42px;
        background: #FFFFFF;
        border-radius: 3px;
        border: 1px solid #D9D9D9;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        text-align: left;
        font-style: normal;
        margin-right: 15px;
    }
    .save_btn {
        width: 165px;
        height: 42px;
        background: #097DB4;
        border-radius: 3px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        text-align: left;
        font-style: normal;
    }
}
.blue_class {
    background-color: #E6F2F7 !important;
    color: #097DB4 !important;;
    p {
        color: #097DB4 !important;
    }
}
.blue_text {
    color: #097DB4 !important;
}
.tranf_class {
    transform: rotate(180deg);
}
.blue_tranform {
    transform: rotate(180deg);
}
</style>