<template>
  <div :class="instructionsShow||showCommon||showUl?'cover':'appilyBillItem'" @click="showUl=false">
     <div  :class="instructionsShow||showCommon||showUl?'appilyBill-info-cover':'appilyBill-info'">
      <span style="color:#333;font-weight:500">开票金额&nbsp;<span style="color:red;font-weight:600">￥{{priceFixed(orderInfo.payPrice)}}</span></span><br/><br/>
      <span style="color:#666;font-weight:400">订单编号&nbsp;{{orderInfo.orderCode}}</span>
     </div>
     <div  :class="instructionsShow||showCommon||showUl?'appilyBill-form-cover':'appilyBill-form'"> 
        <cube-form :model="form" ref="form">
          <div class="form-title">
            <span style="color:#333;font-weight:600">填写开票信息<span class="billInstructions" @click="instructions">开票须知></span></span>
            <span class="vxBillInfo" @click="wxAppily">微信开票信息<img src="../../assets/img/vxBillinfo.png" alt=""></span>
          </div>
        <cube-form-group>
          <cube-form-item :field="fields[8]"></cube-form-item>
           <cube-form-item class="invoiceType">
            <cube-radio-group v-model="form.invoiceType" :options="options" :horizontal="true" @input="changeInvoiceType"/>
            <cube-radio-group v-model="form.invoiceForm" :options="inioceOption" class="invoiceForm"></cube-radio-group>
           </cube-form-item>
          <div class="companyName border-bottom-1px">
            <cube-form-item :field="fields[0]" ></cube-form-item>
            <span class="notNull" v-if="companyNameShow">必填</span>
            <img src="@/assets/img/clear.png" alt="" v-if="this.form.companyName" @click="clear" class="cubeic-wrong">
            <!-- <i class="cubeic-wrong" v-if="this.form.companyName" @click="clear"></i> -->
            <span class="textTip" v-if="commonInviconList.length" @click="commomInvicon">选择我常用的开票信息</span>
          </div>
           <!-- <span class="textTip" v-if="commonInviconList.length" @click="commomInvicon">选择我常用的开票信息</span> -->
          <cube-form-item :field="fields[1]"></cube-form-item>
          <span class="notNull" v-if="taxpayerNumberShow">必填</span>
          <cube-form-item :field="fields[2]"></cube-form-item>
          <span class="notNull" v-if="invoiceContentShow">必填</span>
          <cube-form-item :field="fields[3]"></cube-form-item>
          <cube-form-item :field="fields[4]"></cube-form-item>
          <cube-form-item :field="fields[5]"></cube-form-item>
          <cube-form-item :field="fields[6]"></cube-form-item>
        </cube-form-group>
         </cube-form>
     </div>
     <div :class="instructionsShow||showCommon||showUl?'appilyBill-form-cover':'appilyBill-form'">
      <div class="form-title">
       <span style="color:#333;font-weight:600">发票寄送信息</span>
      </div>
      <cube-form :model="form" ref="form">
        <cube-form-item :field="fields[7]"></cube-form-item>
        <span class="notNull" v-if="recipientEmailShow">必填</span>
        <span class="notNull" v-if="emailText">请输入正确的邮箱</span>
      </cube-form>
     </div>
     <cube-checkbox v-model="checked" shape='square'>保存为我的常用开票信息</cube-checkbox>
    <cube-button type="submit" @click="submit">保存</cube-button>
    <div class="instructions" v-show="instructionsShow">
      <div class="instructions-title">
        <span>开票须知</span>
        <i class="cubeic-close" style="float:right" @click="close"></i>
      </div>
      <div class="instructions-content">
        <span :class="objectId==3?'instructions-content-tip3':'instructions-content-tip1'"><span>注明</span></span>&nbsp;
        <span class="instructions-content-text">开票信息一旦提交将无法修改，请务必详细检查</span>
      </div>
      <div class="instructions-content">
        <span :class="objectId==3?'instructions-content-tip3':'instructions-content-tip1'">注明</span>&nbsp;
        <span class="instructions-content-text">可选发票类型和发票内容均与主办方设置有关，如有特殊需求，请直接向主办方咨询</span>
      </div>
    </div>
    <div class="commonInvoice" v-show="showCommon">
      <div class="commonInvoice-title">
        <span>选择我的常用开票信息</span>
      <i class="cubeic-close" style="float:right" @click="close"></i>
      </div>
      <div>
        <ul>
          <li v-for="(item,index) in commonInviconList" :key="index" class="commonInvoice-item" @click="changeCommonInvoice(item)" :class="{active:commonInviconId==item.id}">{{item.companyName}} <i v-if="commonInviconId==item.id" class="cubeic-ok" style="float:right"></i></li>
        </ul>
      </div>
    </div>
    <div class="tab-div" v-show="showUl">
       <div class="companyName-title">
        <span>查询公司列表</span>
      <i class="cubeic-close" style="float:right" @click="close"></i>
      </div>
          <ul class="appily-tab-item" v-if="companyNameList.length">
              <li v-for="item in companyNameList" :key="item.KeyNo" class="commonInvoice-item" :class="{active: companyNameKeyNo==item.KeyNo}" @click="changeCompanyName(item)">{{item.Name}}</li>
          </ul>
          <span v-else class="companyNameListEmpty">
            <img src="@/assets/img/no_data_icon.png" alt="">
            <span class="companyNameListEmpty-text">暂无数据</span>
          </span>
     </div>
  </div>
</template>
<script>
import { getOrderById,searchInvoice,saveRequestByUser,getInvoiceContent,getCommonUseInvoiceList,getCreditCodeInfoByMember } from '@/api/userCenter';
import { updateLocale } from 'moment';
export default {
  name: "appilyBill",
  data() {
    return {
      orderId: this.$route.query.orderId,
      itemType: this.$route.query.itemType,
      orderInfo:{},
      showUl:false,
      companyNameList:[],
      companyNameKeyNo:'',
      instructionsShow:false,
      invoiceContents:[],//发票内容
      checked:true,
      value:'',
      picker:false,
      InvoiceList:false,//是否又保存过发票
      commonInviconList:[],
      commonInviconId:'',//选择的常用发票的id
      showCommon:false,
      objectId:'',
      invoiceContentShow:false,
      taxpayerNumberShow:false,
      companyNameShow:false,
      recipientEmailShow:false,
      emailText:false,
      form:{
        invoiceForm:'electron',//发票媒介
        invoiceType:'',
         companyName:'',
         taxpayerNumber:'',
         companyAddress:'',
         bankName:'',
         companyPhone:'',
         bankNumber:'',
         invoiceSubject:'',
         invoiceTitleId:'',
         invoiceContent:'',
         recipientEmail:''
      },
      invoiceInfo: {
          invoiceTitle: '', // 发票抬头
          taxpayerNumber: '', // 纳税人识别号
          companyAddress: '', // 注册地址
          companyPhone: '', // 电话号码
          bankName: '', // 开户银行
          bankNumber: '' // 银行账号
            }, // 发票信息
      options: [
        // {
        //   value:'ordinary',
        //   label:'增值税普通发票'
        // },
        // {
        //   value:'special',
        //   label:'增值税专用发票'
        // }
      ],
      inioceOption:[{value:'electron',label:'电子'}],
 fields: [
        {
          type: 'input',
          modelKey: 'companyName',
          label: '开票抬头',
          props: {
            placeholder: '请输入'
          },
          rules: {
            required: true
          },
          events:{
            'blur':()=>{
              this.blur('companyName')
            },
            'input':()=>{
              this.init()
            }
          }
        },
        {
          type: 'input',
          modelKey: 'taxpayerNumber',
          label: '税号',
          props: {
            placeholder: '请输入'
          },
          rules: {
            required: true
          },
           events:{
            'blur':()=>{
              this.blur('taxpayerNumber')
            }
          }
        },
        {
          type: 'input',
          modelKey: 'invoiceContent',
          label: '发票内容',
          props: {
            placeholder: '请选择',
            // model:'',
          },
          
          rules: {
            required: true
          },
           events:{
            'blur':()=>{
              this.blur('invoiceContent')
            }
          }
        },
        {
          type: 'input',
          modelKey: 'companyAddress',
          label: '注册地址',
          props: {
            placeholder: '请输入'
          },
          rules: {
            required: false
          }
        },
        {
          type: 'input',
          modelKey: 'companyPhone',
          label: '注册电话',
          props: {
            placeholder: '请输入'
          },
          rules: {
            required: false
          }
        },
        {
          type: 'input',
          modelKey: 'bankName',
          label: '开户银行',
          props: {
            placeholder: '请输入'
          },
          rules: {
            required: false
          }
        },
        {
          type: 'input',
          modelKey: 'bankNumber',
          label: '开户账号',
          props: {
            placeholder: '请输入'
          },
          rules: {
            required: false
          }
        },
        {
          type: 'input',
          modelKey: 'recipientEmail',
          label: '收件人邮箱',
          props: {
            placeholder: '请输入'
          },
          rules: {
            required: true
          },
          events:{
            'blur':()=>{
              this.blur('recipientEmail')
            }
          }
        },
        {
          type: 'input',
          modelKey: 'invoiceSubject',
          label: '开票方',
          props: {
            placeholder: '请输入',
             readonly:true
          },
          rules: {
            required: false,
          }
        },
       
      
      ]
    }
  },
  created(){
  },
  mounted(){
    this.getOrderDetails()
    let button=window.document.createElement('div')
    // let text=document.createTextNode('查询')
    // button.append(text)
    let select=window.document.createElement('div')
    button.innerHTML="<cube-button class='btn' style='background:var(--color-primary);border:none;color:#fff;display:inline-block;width:42px;height:20px;line-height:20px;text-align:center;border-radius:3px' id='btn'>查询</cube-button>"
    select.innerHTML='<i class="cubeic-select"></i>'
   document.getElementsByClassName('cube-input')[1].append(button)
   document.getElementsByClassName('cube-input')[3].append(select)
   let that=this
   document.querySelector('.btn').onclick=function(e){
    e.stopPropagation()
    that.search()
   }
   document.getElementsByClassName('cube-input')[3].onclick=function(){
    that.showPicker()
    document.activeElement.blur();
   }
   this.objectId=this.$route.query.platformId
  },
  computed:{
  },
  methods:{
    init(){
      if(this.companyNameKeyNo==''){
      }else{
      this.form.taxpayerNumber=''
      this.form.bankNumber=''
      this.form.bankName=''
      this.form.companyPhone=''
      this.form.companyAddress=''
      if(this.form.companyName==''){
        this.companyNameKeyNo=''
      }
      }  
    },
    // 获取订单详情
    getOrderDetails() {
      let params = [
        'orderId=' + this.orderId,
        'itemType=' + this.itemType
      ]
      getOrderById(params.join('&')).then( res => {
        if (res.code == this.$successCode) {
          this.orderInfo = res.data;
             this.getInvoiceContent()//获取开票内容
            this.getCommonUseInvoiceList()
        }
      })
    },
    // 查询抬头
    search(){
      this.showUl=true
  
      let params={
         searchKey:this.form.companyName,
         pageIndex:1,
         moduleType:this.orderInfo.itemType,
         moduleId:this.orderInfo.itemId,
         sysCode:this.orderInfo.sysCode,
         businessId:this.orderInfo.businessId,
         businessType:this.orderInfo.businessType,
         channel:this.orderInfo.channel
      }
      searchInvoice({params}).then(res=>{
         this.companyNameList=res.data.Result
      })
    },
     changeCompanyName(item){
      this.form.companyName=item.Name
      this.companyNameKeyNo=item.KeyNo
      this.showUl=false
      let params={
        keyWord:item.Name,
        precision:false,
         sysCode:this.orderInfo.sysCode,
        moduleType:this.orderInfo.itemType,
        moduleId:this.orderInfo.itemId,
        businessType:this.orderInfo.businessType,
        platformId:this.orderInfo.platformId,
        channel:this.orderInfo.channel,
        bizId:this.orderInfo.bizId,
        businessId:this.orderInfo.businessId,
      }
      getCreditCodeInfoByMember(params).then(res=>{
        this.form.bankNumber=res.data.Result.BankAccount
        this.form.bankName=res.data.Result.Bank
        this.form.companyPhone=res.data.Result.Tel
        this.form.companyAddress=res.data.Result.Address
        this.form.taxpayerNumber=res.data.Result.CreditCode
      })
      this.companyNameShow=false
      this.taxpayerNumberShow=false
    },
    // 提交开票
    submit(){
      if(this.form.companyName==''){
        this.companyNameShow=true
      } else{
        this.companyNameShow=false
      }
      if(this.form.taxpayerNumber==''){
        this.taxpayerNumberShow=true
      }else{
        this.taxpayerNumberShow=false
      }
       if(this.form.invoiceContent==''){
        this.invoiceContentShow=true
      }else{
        this.invoiceContentShow=false
      }
      if(this.form.recipientEmail==''){
        this.recipientEmailShow=true
      }else{
        this.recipientEmailShow=false
      }
    let id={
     'orderId':this.orderInfo.orderId
    }
    let idList=[]
    idList.push(id)
    let params={
        // ...paramsModule,
        recipientName:'',
        recipientEmail:this.form.recipientEmail,
        recipientCompany:'',
        recipientAddress:'',
        recipientPhone:'',
        bizId:this.orderInfo.bizId,
        platformId:this.orderInfo.platformId,
        channel:this.orderInfo.channel,
        businessId:this.orderInfo.businessId,
        sysCode:this.orderInfo.sysCode,
        moduleType:this.orderInfo.itemType,
        moduleId:this.orderInfo.itemId,
        businessType:this.orderInfo.businessType,
        invoiceType:this.form.invoiceType,//发票类型
        invoiceForm:this.form.invoiceForm,//发票媒介
        bankNumber:this.form.bankNumber,//开户账号
        bankName:this.form.bankName,//开户银行
        companyPhone:this.form.companyPhone,//注册电话
        companyAddress:this.form.companyAddress,//注册地址
        taxpayerNumber:this.form.taxpayerNumber,//税号
        companyName:this.form.companyName,//发票抬头
        invoiceContentId:this.form.invoiceContentId,
        invoiceTitleId:this.form.invoiceTitleId,
        invoiceSubject:this.form.invoiceSubject,
        rels:idList,
        saveAsCommon:this.checked,
        mergeType:'alone',
        languageType:JSON.parse(localStorage.getItem('dataActionData')).languageType
    }
    this.$refs.form.validate(valid=>{
        if(valid){
            saveRequestByUser(params).then(res=>{
               if(res.code=='001'){
                 history.back(-1)
              }
            })
        }
    })
    },
    // 开票须知
    instructions(){
      this.instructionsShow=!this.instructionsShow
    },
    // 关闭开票须知弹框
    close(){
      this.instructionsShow=false
      this.showCommon=false
    },
    // 获取开票内容
    getInvoiceContent(){
       let orderIds=[]
      orderIds.push(this.orderInfo.orderId)
      let params={
        moduleType:this.orderInfo.itemType,
         moduleId:this.orderInfo.itemId,
         sysCode:this.orderInfo.sysCode,
         businessId:this.orderInfo.businessId,
         businessType:this.orderInfo.businessType,
         channel:this.orderInfo.channel,
          bizId:this.orderInfo.bizId,
        platformId:this.orderInfo.platformId,
        orderIds:orderIds,
        token:localStorage.getItem('proToken')
      }
      getInvoiceContent(params).then(res=>{
       if(res.data){
         res.data.forEach(item=>{
          this.form.invoiceTitleId=item.id
          this.form.invoiceSubject=item.companyName
          item.invoiceTypes.forEach(i=>{
            let invoiceType={value:'',lable:''}
            invoiceType.value=i.invoiceType
            invoiceType.label=i.invoiceTypeText
            this.options.push(invoiceType)
          })
          this.form.invoiceType=this.options[0].value
          this.$nextTick(()=>{
            document.querySelectorAll('.cube-radio .cube-radio-label')[0].classList.add('change')
          })
          item.invoiceContents.forEach(obj=>{
            this.invoiceContents.push(obj)
          })
        })
         this.fields[2].props.options=this.invoiceContents
       }else{
          this.$createToast({ txt: res.info, type: 'error' }).show()
          setTimeout(()=>{
            history.back(-1)
          },2000)
       }
      
      })
    },
    // 微信开票信息
    wxAppily(){
      wx.ready(() => {
                wx.invoke('chooseInvoiceTitle', {
                    "scene": "1"
                }, (res) => {
                    let wxInvoice = JSON.parse(res.choose_invoice_title_info);
                    this.form.companyName=wxInvoice.title?wxInvoice.title:''
                    this.form.taxpayerNumber=wxInvoice.taxNumber?wxInvoice.taxNumber:''
                    this.form.companyAddress=wxInvoice.companyAddress?wxInvoice.companyAddress:''
                    this.form.companyPhone=wxInvoice.telephone?wxInvoice.telephone:''
                    this.form.bankName=wxInvoice.bankName?wxInvoice.bankName:''
                    this.form.bankNumber=wxInvoice.bankAccount?wxInvoice.bankAccount:''
                    if(this.form.companyName){
                        this.companyNameShow=false
                      }else{
                        this.companyNameShow=true
                      }
                      if(this.form.taxpayerNumber){
                        this.taxpayerNumberShow=false
                      }else{
                        this.taxpayerNumberShow=true
                      }
                })
            })
    },
    // 发票内容选择框
    showPicker(){
     if (!this.picker) {
        this.picker = this.$createPicker({
          title: '发票内容',
          data: [this.invoiceContents],
           alias: {
            value: 'id',
            text: 'invoiceContent'
          },
          valueChange:this.change,
          onSelect: this.selectHandle,
        })
      }
      this.picker.show()
    },
    selectHandle(selectedVal,selectedIndex,selectText){
      this.form.invoiceContentId=selectedVal.toString()
      this.form.invoiceContent=selectText.toString()
      this.invoiceContentShow=false
    },
    getCommonUseInvoiceList(){
      let params={
         bizId:this.orderInfo.bizId,
        platformId:this.orderInfo.platformId,
        channel:this.orderInfo.channel,
        businessId:this.orderInfo.businessId,
        sysCode:this.orderInfo.sysCode,
        moduleType:this.orderInfo.itemType,
        moduleId:this.orderInfo.itemId,
        businessType:this.orderInfo.businessType,
      }
      getCommonUseInvoiceList(params).then(res=>{
        this.commonInviconList=res.data
      })
    },
    commomInvicon(){
      this.showCommon=!this.showCommon
    },
    // 选择常用发票
    changeCommonInvoice(item){
    this.commonInviconId=item.id
    this.showCommon=false
    this.form.companyName=item.companyName,
    this.form.taxpayerNumber=item.taxpayerNumber
    this.form.companyAddress=item.companyAddress,
    this.form.bankName=item.bankName,
    this.form.companyPhone=item.companyPhone,
    this.form.bankNumber=item.bankName
    if(this.form.companyName){
      this.companyNameShow=false
    }else{
      this.companyNameShow=true
    }
    if(this.form.taxpayerNumber){
      this.taxpayerNumberShow=false
    }else{
      this.taxpayerNumberShow=true
    }
    },
    blur(type){
      console.log(type);
       if(type=='companyName'){
          if(this.form.companyName==''){
              this.companyNameShow=true
             }else{
              this.companyNameShow=false
             }
       }else if(type=='taxpayerNumber'){
         if(this.form.taxpayerNumber==''){
            this.taxpayerNumberShow=true
          }else{
            this.taxpayerNumberShow=false
          }
       }else if(type=='invoiceContent'){
          if(this.form.invoiceContent==''){
            this.invoiceContentShow=true
          }else{
            this.invoiceContentShow=false
          }
       }else{
        if(this.form.recipientEmail==''){
          this.recipientEmailShow=true
        }else{
          let reg=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          if(!reg.test(this.form.recipientEmail)){
            this.emailText=true
          }else{
            this.emailText=false
          }
           this.recipientEmailShow=false
        }
       }
    },
    clear(){
      this.form.companyName=''
      this.form.taxpayerNumber=''
      this.form.companyAddress=''
      this.form.companyPhone=''
      this.form.bankName=''
      this.form.bankNumber=''
    },
    priceFixed(value){
      return parseFloat(value).toFixed(2)
    },
    changeInvoiceType(){
      let dom1 =document.querySelectorAll('.cube-radio .cube-radio-label')[1]
      if(dom1)
           dom1.classList.remove('change')
      if(this.form.invoiceType=='ordinary'){
          let dom0 =document.querySelectorAll('.cube-radio .cube-radio-label')[0]
          if(dom0) dom0.classList.add('change')
      }else{
        let dom0 =document.querySelectorAll('.cube-radio .cube-radio-label')[0]
          if(dom0) dom0.classList.remove('change')
        let dom1 =document.querySelectorAll('.cube-radio .cube-radio-label')[1]
          if(dom1) dom1.classList.add('change')
      }
    
    }
  },
}
</script>
<style scoped lang='scss'>
$primaryColor: var(--color-primary);
*{
  font-size: 14px;
}
.appilyBillItem{
  overflow: auto;
  background: #F5F6FA;
}
.appilyBill-info,
.appilyBill-form,
.appilyBill-info-cover,
.appilyBill-form-cover{
  width: 94%;
  height: auto;
  background: #fff;
  border: 1px solid #fff;
  margin: 10px;
  padding: 10px;
  position: relative;
}
.appilyBill-info-cover,.appilyBill-form-cover{
z-index: -10 !important;
}
.appilyBill-form-cover{
  padding: 0;
}
.appilyBill-form{
  padding: 0;
}
.form-title{
  border-bottom: 1px solid #EEEEEE;
  height: 41px;
  line-height: 41px;
  padding-left: 10px;
}
.billInstructions{
  margin-left: 6px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
}
.vxBillInfo{
  float: right;
  color: #00C800;
  font-size: 12px;
  padding-right: 6px;
  img{
   margin-left: 6px;
   vertical-align: middle;
  }
}
/deep/.border-right-1px:after{
  border-right: none !important;
}
.cube-btn{
  width: 88px;
  height: 32px;
  line-height: 0;
  margin: 0 auto;
  background: $primaryColor;
}
.textTip{
  margin-left: 8px;
  // margin-top: 8px;
  color: $primaryColor;
  position: absolute;
  top: 40px;
}
.btn{
      display: inline-block;
    width: 42px;
    height: 20px;
    text-align: center;
    line-height: 20px;
}
.appily-tab-item{
    // z-index: 99999;
    height: 300px;
    display: flex;
    flex-direction: column;
    width: 350px;
    margin-top: 10px;
    overflow: auto;
    li{
        height: 20px;
        line-height: 20px;
        text-align: center;
        // margin-right: 10px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
         &.active {
            color: $primaryColor;
            // border-bottom: 3px solid $primaryColor;
                }
    }
 }
.instructions,.commonInvoice,.tab-div{
  position: absolute;
  bottom: 0;
  // top: 149px;
  width: 100%;
  height: 198px;
  background: #fff;
  border-radius: 12px 12px 0 0;
  padding: 10px;
  // z-index: 99991;
  transform: translate();
}
.instructions{
  height: 205px;
}
.tab-div{
  height: auto;
  top: 353px;
  right: 0;
  overflow: hidden;
}
.instructions-title{
  height: 52px;
  line-height: 52px;
  border-bottom:1px solid #eee ;
  span{
    font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 600;
   color: #333333;
  }
}
.instructions-content{
  // display: flex;
  height: 52px;
  line-height: 52px;
  // border-bottom:1px solid #eee ;
  .instructions-content-tip3, .instructions-content-tip1{
    // display: inline-block;
    float: left;
    width: 58px;
    height: 19px;
    line-height: 19px;
    border-radius: 2px;
    margin-top: 18px;
    text-align: center;
    color: $primaryColor;
    background: rgba(20, 100, 161, 0.1)
    // opacity: 0.1;
  }
  .instructions-content-tip1{
    background: rgba(255, 107, 0, 0.1);
  }
  .instructions-content-text{
    float: right;
    width: 280px;
    font-size: 14px;
    line-height: 19px;
    margin-top: 14px;
    margin-right: 10px;
    color: #333333;
  }
}
.cover {
    background: rgba(0, 0, 0, 0.5);
    width: 100%; /*宽度设置为100%，这样才能使隐藏背景层覆盖原页面*/
    height: 100%;
    filter: alpha(opacity=100); /*设置透明度为100%*/
    opacity: 1; /*非IE浏览器下设置透明度为100%*/
    display: block;
    overflow: hidden;

}
.commonInvoice{
  height: 337px;
  padding: 0 15px;
  .commonInvoice-title{
    height: 52px;
    line-height: 52px;
    border-bottom: 1px solid #eee;
   span{
     font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #333333;
   }

  }
}
.companyName-title{
    height: 52px;
    line-height: 52px;
    border-bottom: 1px solid #eee;
   span{
     font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #333333;
   }
}
.commonInvoice-item{
 margin-top: 20px;
 font-size: 14px;
font-family: PingFangSC-Medium, PingFang SC;
// font-weight: 500;
  &.active{
    color: $primaryColor;
  }
}
.notNull{
  margin-left: 20px;
  color: red;
  position: absolute;
}
.companyNameListEmpty{
  display: inline-block;
  width: 150px;
  height: 130px;
  // text-align: center;
  margin-top: 52px;
  margin-left: 106px;
  .companyNameListEmpty-text{
    margin-left: 48px;
    margin-top: 22px;
    color: #999;
    display: inline-block;
  }
}
</style>
<style  lang='scss'>
.border-bottom-1px:after{
  width: 199% !important;
}
.change{
  color: #FF620D;
}
.cube-checkbox_checked .cube-checkbox-ui i{
  color: var(--color-primary);
}
.cube-toast-tip{
  max-height: 200px;
}
.appilyBillItem .cover{
  .cube-validator-msg{
  display: none !important;
}
}
.cubeic-wrong{
  position: absolute;
  top:22px;
  left: 273px;
  width: 15px;
}
.companyName{
  // border-bottom: 1px solid #eee;
  .cube-input input{
    padding-right: 25px;
  }
  .cube-form-item{
    min-height: 60px;
  }
}
.cube-input-field{
  color: #333 !important;
}
.cube-radio-wrap{
  justify-content: start !important;
  -webkit-justify-content:start !important;
}
.appilyBillItem .cover{
  .cube-radio-group{
    padding-bottom: 0 !important;
    padding-left: 0 !important;
  }
}
  .cube-checkbox-input{
    &:checked{
      width: 200px;
    }
  }
  .cube-checkbox-ui:before{
    transform: scale(1.4) !important;
  }
 .cube-validator-msg{
  display: none !important;
}
.cube-radio-group[data-horz=true] .cube-radio{
  padding-left: 0 !important;
}
.cube-radio-group{
  padding-left: 0 !important;
}
.invoiceType{
  height: 65px;
  padding-top: 10px;
  .invoiceForm{
    margin-top: -20px;
  }
}
</style>

