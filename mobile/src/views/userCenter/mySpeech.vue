<template>
  <div class="my-speech-wrap">
    <!-- 老版发言 -->
    <speech-page v-if="currentPlatformId != 3"></speech-page>
    <!-- 新版发言 -->
    <new-speech-page v-else></new-speech-page>
  </div>
</template>
<script>
import SpeechPage from './components/mySpeech/speechPage';
import NewSpeechPage from './components/mySpeech/newSpeechPage';
export default {
  data() {
    return {

    }
  },
  components: {
    SpeechPage,
    NewSpeechPage
  },
  mounted(){

  },
  computed:{

  },
  methods:{

  },
}
</script>
<style scoped lang="scss">
.my-speech-wrap {
  width: 100%;
  height: 100%;
  background: #fff;
}

</style>
