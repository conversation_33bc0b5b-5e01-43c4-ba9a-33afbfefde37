<template>
  <div class="body top_body_scroll" :style="isPreviewEnviroment ? 'pointer-events: none;':'max-height: 100%;'">
    <userInfoView :collectType="true"></userInfoView>
    <!--实名认证未实名、待审核、未通过提示--->
    <realnameAuthenTip marginBottom="12px" marginLeft="12px" marginRight="12px"></realnameAuthenTip>
    <div class="conent_class">
          <topMallView></topMallView>
          <mallScrollImg></mallScrollImg>
          <memRecoreView class="memRecoreView" :title="'最新上架'" :newList="newList" :type="'new'"></memRecoreView>
          <memRecoreView class="memRecoreView" :title="'易享推荐'" :type="'recommend'"></memRecoreView>
          <botAllMallView :type="'topUp'"></botAllMallView>
    </div>
    <div class="PreviewEnviroment" v-if="isPreviewEnviroment"></div>
  </div>
</template>
<script>
import userInfoView from "../../views/userCenter/components/userInfoView.vue";
import { mapActions } from 'vuex';
import memRecoreView from "../../views/userCenter/components/memRecoreView.vue";
import botAllMallView from "../../views/userCenter/components/botAllMallView.vue";
import mallScrollImg from "../../views/userCenter/components/mallScrollImg.vue";
import topMallView from "../../views/userCenter/components/topMallView.vue";
import realnameAuthenTip from "./components/realnameAuthenTip.vue";
import { getPersonApi } from '@/api/memberApi.js'
export default {
  name: "mallBody",
  data() {
    return {
      options: {
          pullUpLoad: {
              threshold: 50,
              txt: {
                  more: '',
                  noMore: '没有更多信息啦！'
              }
          },
          pullDownRefresh: {
              threshold: 60,
              stopTime: 1000,
              txt: '更新成功'
          },
          scrollbar: false,
      },
      title: "",
    };
  },
  computed: {
      showPopup() {
        return this.$store.state.showPopup;
      },
      pageMallForm() {
        return this.$store.state.pageMallForm;
      },
      getProductInfoForm() {
        return this.$store.state.getProductInfoForm;
      },
      getLayoutInfoForm() {
        return this.$store.state.getLayoutInfoForm;
      },
  },
  watch: {
      getProductInfoForm: {
          handler() {
          },
          deep: true
      },
  },
  components: {
    mallScrollImg,
    memRecoreView,
    botAllMallView,
    topMallView,
    userInfoView,
    realnameAuthenTip
  },
  mounted() {
    
    let that = this;
    window.addEventListener("message", (ev) => {
      this.getLayoutInfoList();
    });
    this.getPersonApi();
  },
  methods: {
     // 获取微信分享设置信息
      getPersonApi() {
        let params = {
          platformId: this.platformId,
        }
        getPersonApi(params).then(res=>{
          let that = this;
          this.wxShareConfig(res.data,'pageDataAction', {
              that,
              clickEle: 'mallShare',
              currentPage: 'mall',
              targetPage: '',
              businessId: '',
              queryParam: '',
              businessType: 'mall',
              businessSeq: null
            })
        })
      },
      loadDownFun() {
          this.$store.commit("pageMallFun", {
              pageNum: 1,
              type: "rest",
          });
          this.$nextTick(() => {
              this.$refs.classifyScroll.forceUpdate();
          })
      },
      // 滚动触底事件
      loadFun() {
          setTimeout(() => {
              if (this.getProductInfoForm.total > this.newList.length) {
                  this.$store.commit("pageMallFun", {
                      pageNum: this.pageMallForm.pageNum + 1,
                      type: "search",
                  });
                  this.$nextTick(() => {
                      this.$refs.classifyScroll.refresh();
                  })
              } else {
                  this.$refs.classifyScroll.forceUpdate();
              }
              
          }, 1000);
      },
      ...mapActions(['getLayoutInfoList']),
  },
};
</script>
<style scoped lang="scss">
.top_box {
  display: flex;
  align-items: center;
  padding: 0px 15px;
}
.left_one {
  background-color: #0162a0;
  width: fit-content;
  padding: 10px 10px;
  color: #FFFFFF;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .strong {
    font-weight: bold;
    font-size: 16px;
  }
  p {
    margin-bottom: 10px;
    font-size: 10px;
  }
}
.left_two {
  background-color: #fc6431;
  color: #FFFFFF;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 5px;
  margin-right: 5px;
  height: fit-content;
}
.memRecoreView {
  margin-top: 10px;
}
.body {
  padding-bottom: 20px;
  // padding: 0px 12px;
  background-color: #F5F6FA;
  height: auto !important;
  background-color: #F5F6FA;
}

.conent_class {
  padding: 0px 12px;
  overflow: auto;
}
.PreviewEnviroment {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #ff000000;
  top: 0;
}
.table_scroll {
    margin-top: 12px;
    max-height: calc(100vh - 57px);
    overflow-y: scroll;
}
.scroll_class {
  ::v-deep .cube-scroll-content {
    transform: none !important;
  }
}
.top_body_scroll {
  overflow: auto;
}
</style>
<style lang="scss">
.appContainer {
  background-color: #F5F6FA;
}
</style>
