<template>
  <div class="user-info-set-container">
    <div class="user-info-set-main">
      <div class="header">
        <img :src="require(`@/assets/${platformId}/userInfoSet_header_bg.png`)" />
        <div class="header-content">
          <div class="header-content-title">完善{{ platformName }}会员信息</div>
          <span :style="userInfo.platformId == '3' ? 'opacity: 0.5;':''">
            请完善以下个人信息，获取专属{{ platformName }}名片及行业内容推荐，开启您的{{ platformName }}之旅
          </span>
        </div>
        <div class="header-avator">
          <img class="avator-img" :src="userInfo.headImg ? userInfo.headImg : imgPlatChange()" />
          <cube-upload class="avator-upload" :max="1" :multiple="false" :action="uploadUrl" @files-added="addUpload" @file-success="changeAvator" />
          <cube-loading v-show="uploadLoadingShow"></cube-loading>
          <img class="avator-refresh" :src="require(`@/assets/${platformId}/userInfoSet_refresh.png`)" />
          <div class="icon_posit" :class="memberInfo.auditFlag == 0 || memberInfo.auditFlag == 3 || memberInfo.auditFlag == 2 ? 'red_color':'black_color'" v-if="platformId == 3">
            <svg class="svg-icon" aria-hidden="true">
              <use v-if="memberInfo.auditFlag == 1" v-bind:xlink:href="'#icon-info_1'"></use>
              <use v-else v-bind:xlink:href="'#icon-leftbar_info1'"></use>
            </svg>
            <div class="text">{{ memMethoed("one") }}</div>
          </div>
        </div>
      </div>
      <!--实名认证未实名、待审核、未通过提示--->
      <realnameAuthenTip marginTop="50px" marginBottom="12px" marginLeft="15px" marginRight="15px"></realnameAuthenTip>
      <cube-form ref="cubeForm" :model="userInfo" :schema="customerType == 0 ? schemaPhone : schemaEmail" :options="options" class="user-info-set-form" :class="{'mt0': getBerPointInfo.auditFlag != 1}" @submit="submitHandler" @validate="validateHandler">
        <cube-form-item :field="item" :key="index"  v-for="(item,index) in customerType == 0 ? schemaPhone.fields : schemaEmail.fields">
          <template slot="label" v-if="item.modelKey == 'company' && settingForm.serviceStatus && getBerPointInfo.authFlag == 1">
            <div class="name_body">
              <span class="name_color">{{ item.label }}</span>
              <svg class="name_img" aria-hidden="true" v-if="platformId == 3" @click="changeName">
                <use v-bind:xlink:href="'#icon-tongyong_tishi'"></use>
              </svg>
            </div>
          </template>
          <cube-input v-model="userInfo[item.modelKey]" v-if="item.type == 'input' && item.modelKey == 'memberName' || item.modelKey == 'company' || item.modelKey == 'position'" :disabled="displedMethos(item.modelKey)">
              <template v-if="item.modelKey == 'memberName' && platformId == 3" slot="append">
                <div class="inp_icon" @click="popBtn">
                  <div class="img_class" v-if="uploadIconUrl">
                    <img v-if="uploadIconUrl" :src="uploadIconUrl" />
                  </div>
                  <div class="text">{{ memMethoed("two") }}</div>
                  <svg class="svg-icon" aria-hidden="true">
                    <use v-bind:xlink:href="'#icon-Right'"></use>
                  </svg>
                </div>
              </template>
          </cube-input>
          <!-- <cube-select v-model="userInfo[item.modelKey]" v-if="item.type == 'select'" :options="item.props.options"></cube-select> -->
        </cube-form-item>
      </cube-form>
      <!-- 感兴趣的领域 -->
      <interested type="industry" ref="industry" :style="style" :getInterestedData="getClassify" :getUserInterestedData="getUserIndustryEnmore" title="选择您感兴趣的领域" :saveUserInfo="putmemberAndDomains"></interested>
      <!-- 感兴趣的品类 -->
      <interested v-if="platformId == 3" type="classify" ref="classify" :getInterestedData="getSelProduct" :getUserInterestedData="getUsersBasicInfo" title="选择您感兴趣的品类" :saveUserInfo="postUsersSave"></interested>
    </div>
    <div class="footer-submit-btn">
      <cube-button @click="toSubmit">保存</cube-button>
    </div>
    <!-- 小易联系客服弹窗 -->
    <van-dialog @close="closeBtn" v-model:show="codeShow" class="vat_dia_body">
      <companyCodeView v-if="codeShow" :settingForm="settingForm" @closeEmit="closeEmit" class="companyCodeView"></companyCodeView>
    </van-dialog>
  </div>
</template>

<script>
import userPopoView from "../../components/common/userPopoView.vue";
import { mapActions } from 'vuex';
import companyCodeView from "../../components/common/companyCodeView";
import { getUserInfo, updateUserInfo, getUserIndustryEnmore, getSelProduct, getUsersBasicInfo, postUsersSave } from '@/api/userCenter'
import { saveMemberAuth, getPersonApi } from '@/api/memberApi.js'
import { uploadUrl } from '@/config/env'
import Region from './components/region'
import cascader from './components/cascader'
import selectDer from './components/selectDer'
import BusinessCard from './components/businessCard'
import Interested from './components/interested'
import realnameAuthenTip from './components/realnameAuthenTip.vue'
import { getClassify } from '@/api/medicine/homePage'
import { putmemberAndDomains } from '@/api/domin'
export default {
  name: 'userInfoSet',
  data() {
    return {
      filePoint: [],
      settingForm: {}, // 实名认证客服配置信息
      codeShow: false,
      uploadIconUrl: "", // 认证url
      createUrl: "", // 初始url
      platformId: '',
      userInfo: {}, // 用户信息
      schemaPhone: {
        // form 列表
        fields: [
          {
            type: 'input',
            modelKey: 'memberName',
            label: '真实姓名',
            props: { placeholder: '请输入您的真实姓名', clearable: true },
            rules: { notWhitespace: true,required:true,max:50 },
            trigger: 'blur',
          },
          {
            type: 'input',
            modelKey: 'company',
            label: '公司名称',
            props: { placeholder: '请输入公司名称', clearable: true },
            rules: { notWhitespace: true,required:true,max:100 },
            trigger: 'blur',
          },
          {
            type: 'input',
            modelKey: 'position',
            label: '职位',
            props: { placeholder: '请填写您当前的职位', clearable: true },
            rules: { notWhitespace: true,required:true,max:100 },
            trigger: 'blur',
          },
          {
            type: 'input',
            modelKey: 'mobile',
            label: '手机号',
            props: { placeholder: '请填写有效的手机号', clearable: true, disabled: true },
            rules: { required:true,pattern:/^[1-9][0-9]*$/ },
            trigger: 'blur',
          },
          {
            type: 'input',
            modelKey: 'email',
            label: '邮箱',
            props: { placeholder: '请填写有效的邮箱地址', clearable: true },
            rules: {required:true,pattern:/^[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*@[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*\.[a-zA-Z]{2,}$/},
            trigger: 'blur',
          },
          {
            component: cascader,
            modelKey: 'enterpriseNatureCode',
            label: '公司类型',
            props: { placeholder: '请输入', clearable: true, enterpriseNatureText: "" },
            rules: { required: false },
            trigger: 'blur',
          },
          {
            component: selectDer,
            modelKey: 'functionCode',
            label: '职能',
            props: { placeholder: '请选择', clearable: true, text: "", type: "functionCode", apiText: "root_system_info_medical_function" },
            rules: { required: false },
            trigger: 'blur',
          },
          {
            component: selectDer,
            modelKey: 'rankCode',
            label: '职级',
            props: { placeholder: '请选择', clearable: true, text: "", type: "rankCode", apiText: "root_system_info_medical_rank" },
            rules: { required: false },
            trigger: 'blur',
          },
          {
            type: 'input',
            modelKey: 'oph',
            label: '固定电话',
            props: { placeholder: '请输入', clearable: true },
            rules: { required: false },
            trigger: 'blur',
          },
          {
            component: Region,
            modelKey: 'regionCode',
            label: '所在地区',
            props: { placeholder: '请输入', clearable: true, regionText: '' },
            rules: { required: false },
          },
          // {
          //   component: BusinessCard,
          //   modelKey: 'businessCard',
          //   label: '个人名片',
          //   rules: { required: false },
          // },
        ],
      },
      schemaEmail: {
        // form 列表
        fields: [
          {
            type: 'input',
            modelKey: 'memberName',
            label: '真实姓名',
            props: { placeholder: '请输入您的真实姓名', clearable: true },
            rules: { notWhitespace: true,required:true,max:50 },
            trigger: 'blur',
          },
          {
            type: 'input',
            modelKey: 'company',
            label: '公司名称',
            props: { placeholder: '请输入公司名称', clearable: true },
            rules: { notWhitespace: true,required:true,max:100 },
            trigger: 'blur',
          },
          {
            type: 'input',
            modelKey: 'position',
            label: '职位',
            props: { placeholder: '请填写您当前的职位', clearable: true },
            rules: { notWhitespace: true ,required:true,max:100},
            trigger: 'blur',
          },
          {
            type: 'input',
            modelKey: 'mobile',
            label: '手机号',
            props: { placeholder: '请填写有效的手机号', clearable: true },
            rules: {required:true,pattern:/^[1-9][0-9]*$/},
            trigger: 'blur',
          },
          {
            type: 'input',
            modelKey: 'email',
            label: '邮箱',
            props: { placeholder: '请填写有效的邮箱地址', clearable: true, disabled: true },
            rules: { required: true,pattern:/^[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*@[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*\.[a-zA-Z]{2,}$/ },
            trigger: 'blur',
          },
          {
            component: cascader,
            modelKey: 'enterpriseNatureCode',
            label: '公司类型',
            props: { placeholder: '请输入', clearable: true, enterpriseNatureText: "" },
            rules: { required: false },
            trigger: 'blur',
          },
          {
            component: selectDer,
            modelKey: 'functionCode',
            label: '职能',
            props: { placeholder: '请选择', clearable: true, text: "", type: "functionCode", apiText: "root_system_info_medical_function" },
            rules: { required: false },
            trigger: 'blur',
          },
          {
            component: selectDer,
            modelKey: 'rankCode',
            label: '职级',
            props: { placeholder: '请选择', clearable: true, text: "", type: "rankCode", apiText: "root_system_info_medical_rank" },
            rules: { required: false },
            trigger: 'blur',
          },
          {
            type: 'input',
            modelKey: 'oph',
            label: '固定电话',
            props: { placeholder: '请输入', clearable: true },
            rules: { required: false },
            trigger: 'blur',
          },
          {
            component: Region,
            modelKey: 'regionCode',
            label: '所在地区',
            props: { placeholder: '请选择', clearable: true, regionText: '' },
            rules: { required: false },
          },
          // {
          //   component: BusinessCard,
          //   modelKey: 'businessCard',
          //   label: '个人名片',
          //   rules: { required: false },
          // },
        ],
      },
      maxVal:['memberName','company','position','functionCode'],
      options: {
        scrollToInvalidField: true, // form 验证 标识
      },
      uploadUrl, // 图片上传路径
      uploadLoadingShow: false, // 上传图片loading 显示状态
      platformName: '行家',
      customerType: 0,
      // 感兴趣的领域接口
      getClassify,
      getUserIndustryEnmore,
      putmemberAndDomains,
      // 感兴趣的品类接口
      getSelProduct,
      getUsersBasicInfo,
      postUsersSave,
      style: {
        marginTop: '5px',
      },
    }
  },
  watch: {
    getBerPointInfo: {
      handler(val) {
        this.$forceUpdate();
      },
      deep: true, // 深度监听对象的变化
      immediate: true // 立即执行一次
    }
  },
  components: {
    Interested,
    companyCodeView,
    userPopoView,
    realnameAuthenTip
  },
  computed: {
    memberInfo() {
      // 查询会员信息
      return this.$store.state.memberInfo;
    },
    getBerPointInfo() {
      // 获取个人认证信息
      this.createUrl = this.$store.state.getBerPointInfo.businessUrl;
      this.uploadIconUrl = this.$store.state.getBerPointInfo.businessUrl;
      return this.$store.state.getBerPointInfo;
    },
  },
  created() {
    // 获取公司类型
    // this.getClassifyCompany('root_system_exhibitor_shop_types',5)
    // 获取职能类型
    // this.getClassifyCompany('root_system_info_medical_function',"functionCode")
    // 获取职级类型
    // this.getClassifyCompany('root_system_info_medical_rank',"rankCode")
     this.platformId = localStorage.getItem('platformId')
    // 这里需要根据平台添加不平的字段显示
    if( this.platformId != '3'){
      this.schemaPhone.fields.splice(5,this.schemaPhone.fields.length - 1);
      this.schemaEmail.fields.splice(5,this.schemaEmail.fields.length - 1);
    }
  },
  mounted() {
    this.getPersonApi();
    this.getMemberLevelInfo();
    if(this.platformId == 3){
      this.platformName = '医疗';
    }else if(this.platformId == 5){
      this.platformName = '汽车';
    }else{
      this.platformName = '行家';
    }
    this.getUserInfo()
    const h = document.body.scrollHeight
    window.onresize = function () {
      if (document.body.scrollHeight < h - 10) {
        setTimeout(() => {
          document.activeElement.scrollIntoView({ behavior: 'smooth' })
        })
      } else {
        document.body.scrollTop = document.body.scrollHeight
      }
    }

    this.$root.$on('changeUpload', (img) => {
      this.userInfo.businessCard = img
    })

    this.$root.$on('changeRegion', ({ regionCode, region }) => {
      this.userInfo.regionCode = regionCode.join(',')
      this.userInfo.region = region
      this.regionCodeHandler("regionCode","region","regionText");
    })
    // 企业类型更改
    this.$root.$on('changeEnterpriseNature', ({ enterpriseNatureCode, enterpriseNature }) => {
      this.userInfo.enterpriseNatureCode = enterpriseNatureCode.join(',')
      this.userInfo.enterpriseNature = enterpriseNature
      this.regionCodeHandler("enterpriseNatureCode","enterpriseNature","enterpriseNatureText");
    })
    // 下拉选择事件
    this.$root.$on('selectEnterNature', ({ listCode, listText, code, text }) => {
      this.userInfo[code] = listCode.join(',')
      this.userInfo[text] = listText
      this.regionCodeHandler(code,text,"text");
    })
  },
  methods: {
    ...mapActions(['getMemberLevelInfo', 'getMemberAuthStatus']),
    // 点击打开提交审核弹出
    popBtn() {
      if (this.getBerPointInfo.auditFlag == 0) return this.$toast("实名认证审核中，如有问题请联系主办方");
      this.$store.commit("changeUserPopShow", true);
    },
    // 获取字段的索引值
    getFieldIndex(data, key) {
      return data.findIndex((item) => {
        return item.modelKey == key;
      });
    },
    // 地区的字段进行赋值
    regionCodeHandler(type,text,colum) {
      const regionPhoneIndex = this.getFieldIndex(this.schemaPhone.fields, type);
      const regionEmailIndex = this.getFieldIndex(this.schemaEmail.fields, type);
      if (regionPhoneIndex != -1) { 
        this.schemaPhone.fields[regionPhoneIndex].props[colum] = this.userInfo[text];
      }
      if (regionEmailIndex != -1) {
        this.schemaEmail.fields[regionEmailIndex].props[colum] = this.userInfo[text];
      }
    },
    // 默认头像展示根据平台不同展示不同的图片
    imgPlatChange() {
      console.log(this.platformId,"365行的数据信息");
      if (this.platformId == 3) {
        return require('@/assets/img/new_head_img.png')
      } else {
        return require('@/assets/img/head_pic_no.png')
      }
    },
    // 关闭
    closeEmit() {
      this.codeShow = false;
    },
    // 处理输入框编辑事件
    displedMethos(field) {
      // 如果已经实名认证过并且是公司职位姓名字段、并且是医药平台则禁止编辑
      if (this.getBerPointInfo.authFlag && (field == 'memberName' || field == 'company' || field == 'position') && this.platformId == 3) {
        return true
      }
    },
    // 获取实名客服配置信息
    getPersonApi() {
      let params = {
        platformId: this.platformId,
      }
      getPersonApi(params).then(res=>{
        this.settingForm = res.data;
      })
    },
    // 姓名点击事件
    changeName() {
      this.codeShow = true;
    },
    // 认证状态校验
    memMethoed(val) {
      switch (0) {
        case 0:
          if (this.getBerPointInfo.auditFlag == 3) {
            return val == "one" ? "未实名":"去认证"
          }
          if (this.getBerPointInfo.auditFlag == 0) {
            return "待审核"
          }
          if (this.getBerPointInfo.auditFlag == 2) {
            return val == "one" ? "待重新提交":"重新提交"
          }
          if (this.getBerPointInfo.auditFlag == 1) {
            return val == "one" ? "已实名":"再次认证"
          }
          break;
        case 1:

        default:
          break;
      }
    },
    // 获取 个人信息
    getUserInfo() {
      getUserInfo().then((res) => {
        if (res.code == this.$successCode) {
          this.userInfo = res.data ? res.data : {}
          this.$store.commit('changePersonPro',res.data?res.data:{})
          this.getMemberAuthStatus();
          // 处理空选项
          this.handlerEmptyVal()
          this.regionCodeHandler("regionCode","region","regionText");
          this.regionCodeHandler("enterpriseNatureCode","enterpriseNature","enterpriseNatureText");
          this.regionCodeHandler("functionCode","function","text");
          this.regionCodeHandler("rankCode","rank","text");
          this.customerType = res.data.customerType
          //   this.$root.$emit('upload', this.userInfo.businessCard)
          let isIOS = !!window.navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
          if (this.userInfo.customerType == 0) {
            // 手机号登录，不可修改手机号
            // this.schemaPhone.fields[3].props.disabled = true;
            if (!isIOS) {
              document.getElementsByClassName('cube-input')[this.getFieldIndex(this.schemaEmail.fields,"mobile")].getElementsByTagName('input')[0].style.color = '#ccc'
            }
          } else {
            // 邮箱登录，不可修改邮箱
            // this.schemaEmail.fields[4].props.disabled = true;
            if (!isIOS) {
              document.getElementsByClassName('cube-input')[this.getFieldIndex(this.schemaEmail.fields,"email")].getElementsByTagName('input')[0].style.color = '#ccc'
            }
          }
        }
      })
    },
    handlerEmptyVal(){
      this.maxVal.forEach((key)=>{
          if(this.userInfo[key] === null){
              this.userInfo[key] = '' ;
          }
      })
    },
    // 去提交
    toSubmit() {
      this.$refs.cubeForm.submit()
    },
    // 提交表单
    submitHandler(e) {
      e.preventDefault()
      // if (this.userInfo.enterpriseNatureCode) {
      //   if (!this.customerType) {
      //   } else {
      //   }
      // }
      if (this.platformId == 3) {
        this.userInfo.authFlag = null;
        this.userInfo.auditFlag = null;
        if(!this.$refs.industry.getInterestedDataRef().length){
            this.$toast('请选择您感兴趣的领域');
            return;
        }
        Promise.all([updateUserInfo(this.userInfo), this.$refs.industry.saveUserInfoData(), this.$refs.classify.saveUserInfoData()]).then((res) => {
          let flag = res.every((item) => {
            return item.code == '001'
          })
          if (flag) {
            this.$toast('保存成功');
            this.$router.go('-1')
          }
        })
      } else {
        if(!this.$refs.industry.getInterestedDataRef().length){
            this.$toast('请选择您感兴趣的领域');
            return;
        }
        Promise.all([updateUserInfo(this.userInfo), this.$refs.industry.saveUserInfoData()]).then((res) => {
          let flag = res.every((item) => {
            return item.code == '001'
          })
          if (flag) {
            this.$toast('保存成功');
            this.$router.go('-1')
          }
        })
      }
    },
    // 认证身份名片上传成功
    iconUploadBtn(res) {
      this.uploadIconUrl = res.response.data;
      this.$refs.uploadPoint[0].removeFile(this.filePoint[0])
    },
    // 修改头像
    changeAvator(res) {
      if (res.response.code == this.$successCode) {
        this.$set(this.userInfo, 'headImg', res.response.data)
        this.$createToast({ txt: '上传成功', type: 'correct' }).show()
      } else {
        this.$createToast({ txt: '上传失败，请重试', type: 'error' }).show()
      }
      this.uploadLoadingShow = false
    },
    // 选择完文件后触发
    addUpload() {
      this.uploadLoadingShow = true
    },
    validateHandler(result) {},
    getClassifyCompany(key,type) {
      getClassify(key).then((res) => {
        this.schemaPhone.fields[this.getFieldIndex(this.schemaPhone.fields,type)].props.options = res.data.map((item) => {
          return {
            value: item.codeValueId,
            text: item.codeValueDesc,
          }
        })
        this.schemaEmail.fields[this.getFieldIndex(this.schemaEmail.fields,type)].props.options = res.data.map((item) => {
          return {
            value: item.codeValueId,
            text: item.codeValueDesc,
          }
        })
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.svg-icon {
  width: 14px;
  height: 14px;
}
.cube-form-item {
  border-bottom: solid 1px #f5f5f5 !important;
  border-color: #f5f5f5 !important;
}
.border-bottom-1px::after {
  border-bottom: 1px solid #fff;
  left: 0;
  bottom: 0;
  width: 100%;
  -webkit-transform-origin: 0 bottom;
  transform-origin: 0 bottom;
}
.user-info-set-container.container {
  height: 100%;
  overflow-y: auto;
  .user-info-set-main {
    height: calc(100% - 70px);
    overflow-y: auto;
    .header {
      position: relative;
      img {
        width: 100%;
      }
      .header-content {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        padding: 20px 15px 0 15px;
        color: #fff;
        font-size: 13px;
        line-height: 16px;
        .header-content-title {
          font-size: 20px;
          color: #fff;
          font-weight: 400;
          margin-bottom: 15px;
        }
      }
      .header-avator {
        position: absolute;
        bottom: -35px;
        left: 0;
        width: 100%;
        text-align: center;
        .avator-img {
          width: 70px;
          height: 70px;
          border-radius: 50%;
          border: 2px solid #fff;
          background: #fff;
        }
        .avator-upload {
          display: inline-block;
          width: 70px;
          height: 70px;
          margin-left: -70px;
          opacity: 0;
        }
        .cube-loading {
          display: inline-block;
          width: 70px;
          height: 70px;
          margin-left: -70px;
          background: #000;
          opacity: 0.8;
          filter: alpha(opacity=80);
          border-radius: 50%;
          padding: 20px;
          .cube-loading-spinners {
            width: 30px;
            height: 30px;
            .cube-loading-spinner {
              color: #fff !important;
            }
          }
        }
        .avator-refresh {
          width: 22px;
          height: 22px;
          border-radius: 50%;
          margin-left: -22px;
          margin-bottom: 4px;
        }
      }
    }
    .user-info-set-form {
      margin-top: 60px;
      padding-left: 16px;
      &.mt0{
        margin-top: 0;
      }
      .cube-form-item {
        padding-left: 0;
        font-size: 13px;
        display: block;
        margin-bottom: 18px;
        border-color: #eee;
        .cube-form-label {
          color: #666;
          width: 100%;
        }
        &.cube-form-item_required {
          .cube-form-label::before {
            color: #ff2626;
            margin: 0;
            width: 8px;
          }
        }
        input {
          font-size: 16px;
          padding: 9px 8px;
          color: #222;
        }
        .cube-input-clear {
          // padding: 6px;
          // background: #f5f5f5;
          // border-radius: 20px;
          margin-top: -20px;
          i {
            font-size: 12px !important;
            padding: 11px;
            &::before {
              content: '\E60D';
              color: #999;
              margin-left: 1px;
            }
          }
        }
      }
    }
  }
  .footer-submit-btn {
    width: 100%;
    box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.06);
    background: #fff;
    text-align: center;
    margin: 0;
    margin-top: 5px;
    padding: 9px 15px;
    height: 65px;
    button {
      width: 345px;
      height: 48px;
      background: var(--color-primary);
      font-size: 18px;
    }
  }
}
.icon_posit {
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 7px;
  height: fit-content;
  left: 61%;
  height: 16px;
  .text {
    margin-left: 3px;
    vertical-align: middle;
  }
}
.red_color {
  font-weight: 400;
  font-size: 10px;
  text-align: left;
  font-style: normal;
  background: rgb(255,237,236);
  border-radius: 2px;
  padding: 3px 4px;
  .text {
    color: #FF4D4F;
    margin-left: 3px;
  }
}
.black_color {
  font-weight: 400;
  font-size: 10px;
  color: #333333;
  text-align: left;
  font-style: normal;
  background: #DCE6FC;
  height: 16px;
  padding: 3px 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  .text {
    color: #333333;
    margin-right: 3px;
  }
}
.inp_icon {
  display: flex;
  align-items: center;
  .text {
    background: #E8E8E9;
    border-radius: 2px;
    margin: 0 auto;
    text-align: center;
    padding: 1px 4px;
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    font-style: normal;
    margin-right: 5px;
  }
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  .img_class {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #EEEEEE;
    padding: 2px 5px;
    width: 36px;
    height: 36px;
    margin-right: 8px;
  }
}
.name_color::before {
  color: #ff2626;
  content: '*'; /* 设置伪元素的内容为 * */
  margin-right: 5px; /* 设置与内容的间距 */
}
::v-deep .name_body {
  display: flex !important;
  align-items: center;
  .name_img {
    width: 14px;
    height: 14px;
    margin-left: 3px;
  }
}
.companyCodeView {
  width: 100% !important;
}
</style>
<style lang="scss">
.user-info-set-container{
.cube-form_standard .cube-select{
  padding-left: 8px;
  font-size: 15px;
 }
 .cube-input input[disabled]{
  color: #ccc!important;
  -webkit-text-fill-color: #ccc;
  -webkit-opacity: 1;
  opacity: 1;
 }
}
  
.vat_dia_body {
  border-radius: 10px;
  .van-dialog__footer {
    display: none !important;
  }
}
</style>
