<template>
  <div class="myInquiry">
      <div class="myInquiry-top">
         <div class="top-search">
           <cube-input v-model="keyWord" placeholder="请输入关键词" @input="searchKeyWord">
            <template #prepend>
                <i class="cubeic-search" style="color:#999"></i>
            </template>
           </cube-input>
         </div>
         <div class="top-tab">
            <span class="tab-item" @click="changeTab(item)" :class="{active:activeTab==item.id}" v-for="item in tab" :key="item.id">{{item.title}}</span>
         </div>
      </div>
      <div class="myInquiry-content">
        <cube-scroll ref="scroll" :options='options' :data='inquiryForm' @pulling-up="scrollToEnd">
            <div>
                <div @click="goDetail(item)" class="inquiryItem" v-if="inquiryForm.length" v-for="item in inquiryForm" :key="item.id">
                    <div class="inquiryItem-top">
                        <span class="text">询价时间：{{handleTime(item.inquiryTime)}}</span>
                        <span :class="'status'+item.status">{{item.status==2?'已处理':'待处理'}}</span>
                    </div>
                    <div class="inquiryItem-bottom">
                        <div class="bottom-left">
                            <img v-if="item.productId!==''&&item.productId!==null" :src="item.picUrl?item.picUrl:picUrl" alt="">
                            <img v-else :src="item.logoUrl?item.logoUrl:busUrl" alt="">
                        </div>
                        <div class="bottom-right">
                            <div class="picName">
                                <span class="from" v-if="item.productId!==''&&item.productId!==null">{{item.types}}</span>
                                <span class="picName-text">{{item.productId!==''&&item.productId!==null?item.productName:item.busName}}</span>
                            </div>
                            <div class="picInfo" v-if="item.productId!==''&&item.productId!==null">
                                <span>货号：{{item.numbers}}</span>
                                <span>规格：{{item.specsName}}</span>
                                <span>品牌：{{item.brandName}}</span>
                            </div>
                            <div v-else style="height:40px">
 
                            </div>
                            <div class="shopInfo">
                                <span class="shopName">
                                    <img :src="item.logoUrl?item.logoUrl:busUrl" alt="">
                                    {{item.busName}}
                                </span>
                                    <span class="expectedContactType">
                                        <!-- <i style="margin-right:5px vertical-align:middle" :class="item.expectedContactType==1?'cubeic-phone':'cubeic-email'"></i> -->
                                        <img v-if="item.expectedContactType==1" src="../../assets/img/phone.png" alt="">
                                        <img v-else src="../../assets/img/email.png" alt="">
                                        {{item.expectedContactType==1?'电话沟通':'邮件沟通'}}
                                    </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </cube-scroll>
      </div>
  </div>
</template>
<script>
import {getUserList} from '../../api/userCenter'
export default {
  name: "",
  data() {
    return {
      keyWord:'',
      tab:[{id:0,title:'全部'},
           {id:2,title:'已处理'},
           {id:1,title:'待处理'}],
      activeTab:0,
      inquiryForm:[],
      page:{
        pageNum:1,
        pageSize:10,
        total:0
      },
      pullup:true,
      options: { // 上拉加载设置
                pullUpLoad: {
                    threshold: 30,
                    visible: false,
                    txt: {
                        more: '上拉加载更多',
                        noMore: '没有更多信息啦！'
                    }
                }
            },
     busUrl:'https://oss.ienmore.com/resources/public/img/exhibitor/inquiry/%E5%85%AC%E5%8F%B8%E9%BB%98%E8%AE%A4%E5%9B%BE.png',
     picUrl:'https://oss.ienmore.com/resources/public/img/exhibitor/inquiry/%E4%BA%A7%E5%93%81%E9%BB%98%E8%AE%A4%E5%9B%BE.png'
    }
  },
  mounted(){
   this.getUserList()
  },
  computed:{

  },
  methods:{
    // 切换tab
    changeTab(item){
        this.keyWord=''
        this.page.pageNum=1
        this.activeTab=item.id
        this.inquiryForm=[]
        this.getUserList()
    },
    // 获取询价表
    getUserList(){
        console.log(this.activeTab==0?null:this.tabActive);
     let params={
       pageNum:this.page.pageNum,
       pageSize:this.page.pageSize,
       status:this.activeTab==0?null:this.activeTab,
       keyword:this.keyWord
     }
     getUserList(params).then(res=>{
        if(res.code=='001'){
            this.inquiryForm=this.inquiryForm.concat(res.data.list)||[]
            this.page.total=res.data.total
        }else{
        this.$toast(res.info);
        }
     })
    },
    // 处理时间
    handleTime(val){
        return moment(val).format('yyyy-MM-DD HH:mm')
    },
    goDetail(item){
        console.log(item,'pp');
      sessionStorage.setItem('myInquiryDetail',JSON.stringify(item))
      this.$router.push({ name: 'myInquiryDetail', params: { id: item.id }})
    },
    // 加载更多
    scrollToEnd(){
          setTimeout(() => {

    }, 1000)
        console.log('ppp');
        if(this.page.total>this.inquiryForm.length){
            this.page.pageNum++
            this.getUserList()
        }
    },
    searchKeyWord(){
        this.page.pageNum=1
        this.page.pageSize=10
        this.inquiryForm=[]
        this.getUserList()
    }
  },
}
</script>
<style scoped lang='scss'>
 .myInquiry{
   background: #F5F6FA;
   .myInquiry-top{
    height: 90px;
    background: #fff;
    .top-search{
        width: 375px;
        height: 48px;
        background: #FFFFFF;
        padding: 10px 15px;
        .cube-input{
           width: 345px;
            height: 34px;
            background: #F5F6FA;
            border-radius: 2px;
            padding-left: 15px;
        }
    }
    .top-tab{
        height: 42px;
        .tab-item{
            display: inline-block;
            width: 125px;
            height: 42px;
            line-height: 42px;
            text-align: center;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            &.active{
                color: #097DB4;
            }
        }
    }
   }
   .myInquiry-content{
    width: 100%;
    padding-left: 15px;
    padding-top: 15px;
    height: 100%;
    padding-bottom: 20px;
    // overflow: auto;
    .inquiryItem{
        width: 345px;
        // height: 202px;
        background: #FFFFFF;
        border-radius: 6px;
        margin-bottom: 12px;
        .inquiryItem-top{
            width: 345px;
            height: 36px;
            background: #FFFFFF;
            box-shadow: 0px 1px 0px 0px rgba(239,239,239,0.5);
            border-radius: 6px 6px 0px 0px;
            line-height: 36px;
            padding-left: 10px;
            .text{
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #666666;
            }
            .status1,.status2{
                display: inline-block;
                width: 48px;
                height: 22px;
                background:#BFBFBF;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #FFFFFF;
                float: right;
                text-align: center;
                line-height: 22px;
                margin: 7px 9px;
            }
            .status1{
                background: #FF4D4F;
            }
        }
        .inquiryItem-bottom{
            width: 100%;
            // height: 166px;
            padding-top: 15px;
            display: flex;
            padding-bottom: 15px;
            .bottom-left{
               min-width: 70px;
                height: 100%;
                padding-left: 10px;
                img{
                    width: 60px;
                    height: 60px;
                }

            }
            .bottom-right{
                padding-left: 15px;
                padding-right: 23px;
                .picName{
                    display: flex;
                    line-height: 20px;
                    .from{
                        display: inline-block;
                        min-width: 36px;
                        max-width: 36px;
                        height: 20px;
                        background: #31B3FE;
                        border-radius: 2px;
                        line-height: 20px;
                        color: #fff;
                        font-size: 12px;
                        text-align: center;
                        margin-right: 6px;
                    }
                    .picName-text{
                    width: 200px;
                   height: 40px;
                    overflow:hidden;
                    text-overflow: ellipsis;
                    -webkit-line-clamp: 2;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    color: #333;
                    font-size: 14px;
                    line-height: 20px;
                    }

                }
                .picInfo{
                    margin-top: 6px;
                    span{
                        display: block;
                        margin-bottom: 6px;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 12px;
                        color: #666666;
                         width: 265px;
                         height: 12px;
                        overflow:hidden;
                        text-overflow: ellipsis;
                        -webkit-line-clamp: 1;
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                    }
                }
                .shopInfo{
                    display: flex;
                     img{
                            width: 20px;
                            height: 20px;
                            display: inline-block;
                            vertical-align: middle;
                        }
                    .shopName{
                        display: inline-block;
                         width: 170px;
                         height: 20px;
                        overflow:hidden;
                        text-overflow: ellipsis;
                        -webkit-line-clamp: 1;
                        display: -webkit-inline-box;
                        -webkit-box-orient: vertical;
                        color: #333;
                        font-size: 12px;
                    }
                    .expectedContactType{
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 12px;
                        color: #097DB4;
                        line-height: 16px;
                        img{
                            width: 14px;
                            height: 14px;
                            vertical-align: middle;
                        }
                    }
                }
            }
        }
    }
   }
 }
</style>
