<template>
  <div class="my-appointment-wrap flex-vertical">
    <div class="my-appointment-header unit-0" v-if="banner && banner.logo">
      <img :src="banner.logo" alt="" @click="goToBanner"/>
    </div>
    <div class="my-appointment-content unit">
      <detail-page ref="detailPage" :hasSearch="false" :hasFilter="true" :optionFilter="optionFilter" @tabClick="tabClick">
        <template #filterItem>
          <commonScroll
            :noImg="noImg"
            :showNoText="false"
            ref="commonScroll"
            :getListData="getListData"
            :listParams="activeTab ? configurationHasEnded : configurationNotStarted"
            @handleNoData="handleNoData"
          >
            <template #default="slotProps">
              <appointmentItem :item="slotProps.item" :status="activeTab" ></appointmentItem>
            </template>
            <template #noData v-if="hasSourceList[activeTab]">
              <div class="no-data">
                <img src="https://oss.ienmore.com/frontUpload/partUpload/2025-03-12/fcb370f1f966064b99007466db0b3133.png" alt="" />
              </div>
            </template>
          </commonScroll>
        </template>
      </detail-page>
    </div>
  </div>
</template>
<script>
import DetailPage from "./components/detailPage";
import { getMyAppointmentList, getMyAppointmentBanner } from "@/api/userCenter";
import commonScroll from "./components/detailPage/commonScroll";
import appointmentItem from "./components/appointmentItem";
export default {
  components: {
    DetailPage,
    commonScroll,
    appointmentItem,
  },
  data() {
    return {
      optionFilter: [
        { name: "未开始", hasFilter: false },
        { name: "已结束", hasFilter: false },
      ],
      configurationNotStarted: {
        status: 1,
        keyword: "",
      },
      configurationHasEnded: {
        status: 0,
        keyword: "",
      },
      // 当前激活
      activeTab: 0,
      getListData: getMyAppointmentList,
      // 修改为对象，分别存储每个tab的状态
      hasSourceList: {
        0: false,
        1: false
      },
      banner: {},
    };
  },
  created() {
    this.getMyAppointmentBanner();
  },
  mounted() {},
  computed: {},
  methods: {
    tabClick(index) {
      if (this.activeTab == index) return;
      this.activeTab = index;
    },
    handleNoData(flag) {
      // 更新对应tab的状态
      this.$set(this.hasSourceList, this.activeTab, flag);
    },
    async getMyAppointmentBanner() {
      const res = await getMyAppointmentBanner();
      if (res.code == "001" && res.data) {
        this.banner = res.data.items[0];
      } else if (res.code != "001") {
        this.$toast(res.info);
      }
    },
    goToBanner() {
      if (this.banner.url) {
        window.location.href = this.banner.url;
      }
    },
  },
};
</script>
<style scoped lang="scss">
$avatarRatio: 6.25;
.my-appointment-wrap {
  width: 100%;
  height: 100%;
  .my-appointment-header {
    width: 100%;
    height: calc(100vw / #{$avatarRatio});
    img {
      width: 100%;
      height: 100%;
    }
  }
  :deep(.no-data) {
    margin-top: 75px;
    text-align: center;
    img {
      width: 170px;
      height: 163px;
    }
  }
}
</style>
