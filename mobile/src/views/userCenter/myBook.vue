<template>
    <div class="scroll-list-wrap">
        <div class="searchHeadWrap">
            <div class='flex-left unit-0'>
                <div class="searchLeft unit flex-middle">
                    <div class="center_search">
                        <img src="@/assets/img/search.png" alt="">
                        <iframe id="rfFrame" name="rfFrame" src="about:blank" style="display:none;"></iframe>
                        <form action="#" class="form_search" @submit.prevent="search" target="rfFrame">
                            <div class="form-search-div">
                                <input type="text" v-model="pageData.name" autofocus="autofocus" placeholder="请输入搜索关键字" id="searchInput" />
                                <div v-show="pageData.name" class="clear-button" @click="clearData">
                                    <svg width="8px" height="8px" viewBox="0 0 15 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <g id="画板备份-7" transform="translate(-572.000000, -176.000000)" fill-rule="nonzero">
                                                <g id="删除" transform="translate(570.000000, 174.000000)">
                                                    <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="20" height="19"></rect>
                                                    <polygon id="路径" fill="#999999" points="17.8947368 15.9285714 11.1278196 9.5 17.8947368 3.07142858 16.7669173 2 10 8.42857142 3.23308272 2 2.10526316 3.07142858 8.87218044 9.5 2.10526316 15.9285714 3.23308272 17 10 10.5714286 16.7669173 17"></polygon>
                                                </g>
                                            </g>
                                        </g>
                                    </svg>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <cube-scroll class="list-scroll" ref="scroll" :data="List" :options="options" @pulling-down="onPullingDown" @pulling-up="onPullingUp">
            <my-book-item v-for="item,index in List" :info="item" :key="index" v-show="List.length"></my-book-item>
            <div v-if="List.length == 0" class="no-content">
                <img src="@/assets/img/no_data_icon.png" alt="">
                暂无内容
            </div>
        </cube-scroll>

    </div>
</template>
<script>
import { getMyBook } from '@/api/configurable/common.js';
import myBookItem from '@/components/medicine/myBookitem.vue';
export default {
    components: {
        myBookItem
    },
    props: [],
    data () {
        return {
            List: [],
            options: {
                pullUpLoad: {
                    threshold: 50,
                    stopTime: 1000,
                    txt: {
                        more: '',
                        noMore: '没有更多信息啦！'
                    }
                },
                pullDownRefresh: {
                    threshold: 60,
                    stopTime: 1000,
                    txt: '更新成功'
                },
                scrollbar: false
            },
            pageData: {
                pageNum: 1,
                pageSize: 10,
                name: ""
            }
        };
    },
    computed: {

    },
    watch: {

    },
    created () {

    },
    mounted () {
        this.getInfoBook();
    },
    methods: {
        search () {
            this.pageData.pageNum = 1
            this.List = [];
            this.getInfoBook();
        },
        getInfoBook (mode) {
            let platformId = this.$route.query.platformId || localStorage.getItem('platformId') || 3;
            let params = Object.assign({ platformId }, this.pageData);
            getMyBook(params).then(res => {
                if (res.code == '001') {
                    if (mode == 'next') {
                        this.List = this.List.concat(res.data.list)
                    } else {
                        if (res.data.list && res.data.list.length) {
                            this.List = res.data.list;
                        }
                    }
                    if (res.data.list.length == 0) {
                        this.pageData.pageNum--
                        this.$refs.scroll.forceUpdate();
                    }
                }else{
                    if(res.info){
                        this.$toast(res.info);
                    }
                }
            })
        },
        onPullingDown () {
            this.pageData.pageNum = 1
            this.getInfoBook();
        },
        onPullingUp () {
            this.pageData.pageNum++
            setTimeout(() => {
                this.getInfoBook('next');
            }, 300);
        },
        clearData () {
            this.pageData.name = '';
            this.pageData.pageNum = 1
            this.getInfoBook();
        }
    }
}
</script>
<style lang='scss' scoped>
.scroll-list-wrap {
    background-color: #fff;
}

.filter-button {
    height: 20px;
    font-size: 14px;
    color: #999999;
    line-height: 20px;
    margin-left: 14px;
    display: flex;
    align-items: center;
    width: fit-content;
    flex-wrap: nowrap;
    > i {
        transition-duration: 300ms;
        transform: scale(0.4);
        margin: -4px;
    }
}
.rotate {
    transform: scale(0.4) rotate(180deg) !important;
}
.filter {
    width: calc(100vw - 20px);
    background-color: #ffffff;
    bottom: 0;
    transform: translateY(99%);
    position: absolute;
    > span {
        display: inline-block;
        margin: 10px 0 0 0;
        height: 20px;
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        line-height: 20px;
    }
}
.filter-item-list {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    > div {
        position: relative;
        height: 32px;
        background: #f4f4f4;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 400;
        color: #444444;
        line-height: 20px;
        padding: 0 25px;
        margin-right: 15px;
        margin-top: 10px;
    }
}
.filter-button-group {
    margin-top: 18px;
    margin-left: -15px;
    margin-right: -15px;
    display: flex;
    > div {
        width: 50%;
        height: 40px;
        line-height: 40px;
        text-align: center;
    }

    :nth-child(1) {
        background: #ffebdd;
        color: #ff6b00;
    }
    :nth-child(2) {
        background: #ff6b00;
        color: #ffffff;
    }
}
.filter-cover {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
    height: 100vh;
    width: 110vw;
    margin-left: -15px;
}
.filter-item-selected {
    background: #fff7f2 !important;
    color: #ff6b00 !important;
    position: relative;
}
.selected-icon {
    position: absolute;
    right: 0;
    bottom: -2.5px;
}
</style>

<style  scoped lang="scss">
.searchHeadWrap {
    z-index: 2;
    width: 100%;
    padding: 3px 12px 3px 12px;
    position: sticky;
    background-color: #fff;
}
.searchLeft {
    width: 45px;
    height: 46px;
}

.searchRight {
    height: 46px;
    box-sizing: border-box;
}
.center_search {
    height: 35px;
    width: 100%;
    display: flex;
    align-items: center;
    background: #f3f3f3;
    border-radius: 4px;
}
.center_search img {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    vertical-align: middle;
    margin-left: 13px;
}
#searchInput {
    outline: none;
    background: transparent;
    border: none;
    font-size: 16px;
    line-height: 20px;
    width: 90%;
    padding-left: 2px;
}
#searchInput::placeholder {
    font-size: 16px;
    color: #999;
}

.form_search {
    width: 100%;
}
.form-search-div {
    width: 100%;
    display: flex;
}
.search-button {
    margin-left: 8px;
    height: 35px;
    width: 60px;
    line-height: 35px;
    text-align: center;
    color: #333;
    border-radius: 15px;
    background: #f5f5f5;
    border-radius: 4px;
}
.item-list {
    border-top: 10px solid #f5f5f5;
    background-color: #fff;
}
.no-content {
    // text-align: center;
    // margin-top: 30px;
    // color: #999;
    > img {
        width: 185px;
        height: 142px;
    }
    > div {
        margin-top: 20px;
    }
    margin-top: 120px;
    text-align: center;
    color: #999;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-wrap: wrap;
}
.list-scroll {
    height: calc(100% - 46px);
    overflow: hidden;
    background-color: #fff;
}
</style>
