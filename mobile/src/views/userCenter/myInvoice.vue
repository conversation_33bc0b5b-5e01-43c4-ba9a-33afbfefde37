<template>
    <div class="myInvoice-container">
        <div class="myInvoice-tip clear-fix">
            为了顺利开票，请您准确填写以下信息
            <cube-button :inline="true" :outline="true" @click="getWXInvoice">获取微信发票抬头</cube-button>
        </div>
        <cube-form :model="invoiceInfo" :schema="schema" :options="options" class="myInvoice-form" @submit="submitHandler"></cube-form>
    </div>
</template>

<script>
import { getInvoiceInfo, updateInvoiceInfo } from '@/api/userCenter'
export default {
    name: 'myInvoice',
    data () {
        return {
            options: {
                scrollToInvalidField: true, // form 验证 标识
            },
            invoiceInfo: {
                companyName: '', // 发票抬头
                taxpayerNumber: '', // 纳税人识别号
                companyAddress: '', // 注册地址
                companyPhone: '', // 电话号码
                bankName: '', // 开户银行
                bankNumber: '' // 银行账号
            }, // 发票信息
            schema: { // form 列表
                fields: [
                    {
                        type: 'input', modelKey: 'companyName', label: '发票抬头', props: { placeholder: '请输入发票抬头(公司名称)' },
                        rules: { required: true, notWhitespace: true }, trigger: 'blur'
                    },
                    {
                        type: 'input', modelKey: 'taxpayerNumber', label: '纳税人识别号', props: { placeholder: '15-20位' },
                        rules: { required: true, min: 15, max: 20 }, trigger: 'blur'
                    },
                    { type: 'input', modelKey: 'companyAddress', label: '注册地址', props: { placeholder: '单位地址信息' } },
                    {
                        type: 'input', modelKey: 'companyPhone', label: '电话号码', props: { placeholder: '电话号码' },
                        rules: { pattern: /^[0-9\-\s]+$/, trigger: 'blur' }
                    },
                    { type: 'input', modelKey: 'bankName', label: '开户银行', props: { placeholder: '开户银行' } },
                    { type: 'input', modelKey: 'bankNumber', label: '银行账号', props: { placeholder: '银行账号' } },
                    { type: 'submit', label: '保存' }
                ]
            }
        }
    },
    created () {
        document.title = "我的开票信息"
    },
    mounted () {
        this.getInvoiceInfo()
    },
    methods: {
        // 保存 提交
        submitHandler (e) {
            e.preventDefault();
            let params = {}
            this.schema.fields.forEach(item => {
                if (item.modelKey) {
                    params[item.modelKey] = this.invoiceInfo[item.modelKey]
                }
            })
            updateInvoiceInfo(params).then(res => {
                if (res.code == this.$successCode) {
                    const toast = this.$createToast({ txt: '保存成功', type: 'correct' })
                    toast.show();
                    this.getInvoiceInfo()
                }
            })
        },
        // 获取我的 发票信息
        getInvoiceInfo () {
            getInvoiceInfo().then(res => {
                if (res.code == this.$successCode) {
                    if (res.data) this.invoiceInfo = res.data;
                }
            })
        },
        // 获取微信 发票抬头
        getWXInvoice () {
            wx.ready(() => {
                wx.invoke('chooseInvoiceTitle', {
                    "scene": "1"
                }, (res) => {
                    console.log(res);
                    let wxInvoice = JSON.parse(res.choose_invoice_title_info);
                    this.invoiceInfo = {
                        companyName: wxInvoice.title || '', // 发票抬头
                        taxpayerNumber: wxInvoice.taxNumber || '', // 纳税人识别号
                        companyAddress: wxInvoice.companyAddress || '', // 注册地址
                        companyPhone: wxInvoice.telephone || '', // 电话号码
                        bankName: wxInvoice.bankName || '', // 开户银行
                        bankNumber: wxInvoice.bankAccount || '', // 银行账号
                    }
                })
            })
        }
    }
}
</script>

<style lang="scss">
.myInvoice-container.container {
    height: 100%;
    overflow-y: auto;
    .myInvoice-tip {
        font-size: 12px;
        padding: 10px;
        line-height: 30px;
        border-bottom: 10px solid #eee;
        .cube-btn {
            float: right;
        }
    }
    .myInvoice-form {
        .cube-form-label {
            width: 120px;
            font-size: 14px;
        }
        .cube-btn {
            background-color: var(--color-primary);
            margin-top: 30px;
        }
        .cube-validator-msg {
            &::before {
                font-size: 14px;
            }
            .cube-validator-msg-def {
                font-size: 12px;
            }
        }
    }
}
</style>
