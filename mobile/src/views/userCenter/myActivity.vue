<template>
    <div class="myActivity-container">
        <!-- <div class="navigate-bar">
      <i class="cubeic-arrow" @click="goPersonal"></i>
      我的活动
    </div> -->
        <tabList needList="tab" @changeItem="changeTab"></tabList>
        <div class="scroll-list">
            <cube-scroll ref="scroll" :data="activityList" :options="options" @pulling-up="getMyActivityListFn">
                <div v-if="activityList.length==0" class="no-data-tips">
                    <img src="@/assets/img/no_data_icon.png" alt="">
                    <div>暂无活动</div>
                </div>
                <infoItemSearchZly v-for="(item,index) in activityList" :key='index' :info='item' :iflast='index == activityList.length-1' :showTag='true' :closeExhibitor="true"></infoItemSearchZly>
                <!-- <infoItem v-if="systemStyle == null" v-for="(item,index) in activityList" :key='index' :info='item' :showTag='true' :iflast='index == activityList.length-1'></infoItem> -->
            </cube-scroll>
        </div>
    </div>
</template>
<script>
import tabList from './components/tabList';
import infoItem from '@/components/newCommon/infoItem.vue'
import infoItemSearchZly from '@/components/newCommon/infoItemSearchZly.vue'

import { getInfoByDynamicUrl } from '@/api/configurable/common'
import moment from 'moment'
export default {
    name: "myActivity",
    components: { tabList, infoItem, infoItemSearchZly },
    data () {
        return {
            tabActive: {}, // tab选中项
            options: { // 上拉加载设置
                pullUpLoad: {
                    threshold: 30,
                    visible: false,
                    txt: {
                        more: '上拉加载更多',
                        noMore: '没有更多信息啦！'
                    }
                }
            },
            activityList: [], // 活动列表
            currentPage: 0, // 当前页码
            total: 0, // 数据总条数
            tags: ['热门推荐', '精选推荐', '推荐啊这是'],
            ITEM_TYPES: {
                ACTIVITY: 0,
                COURSE: 1,
                LETURER: 2,
                AUDIO: 3,
                THINK_TANK: 4,
                INFO: 5,
                LIVE: 6,
                TOPIC: 7,
                DIY: 8,
                SERIESLIVE: 9
            },
        }
    },
    created () {
        document.title = "我的活动"
    },
    methods: {
        moment,
        // 返回个人中心
        goPersonal () {
            this.$router.replace({ name: 'userCenter', query: { platformId: localStorage.getItem('platformId') } });
        },
        // 切换 tab 选中项 重置 加载参数
        changeTab (tab) {
            console.log('changeTab');

            this.tabActive = tab;
            this.currentPage = 0;
            this.activityList = [];
            this.total = 0;
            this.$refs.scroll.scrollTo(0, 0);
            this.getMyActivityListFn();
        },
        // 获取列表
        getMyActivityListFn () {
            let params = {};
            this.tabActive.menuParams.forEach(item => {
                params[item.menuParamKey] = item.menuParamValue
            })
            params.pageNum = this.currentPage + 1;
            getInfoByDynamicUrl(this.tabActive.apiUrl, params).then(res => {
                if (res.code == this.$successCode) {
                    if (res.data) {
                        console.log(this.activityList);
                        this.activityList = this.activityList.concat(res.data.list);
                        console.log(this.activityList);
                        this.total = res.data.total
                        this.currentPage++
                    }
                    // 已加载完毕  没有更多信息啦！
                    if (this.total == this.activityList.length) {
                        this.$refs.scroll.forceUpdate();
                    }
                    this.activityList.forEach((item, index) => {
                        this.activityList[index].id = item.itemId
                        this.activityList[index].type = item.itemType
                        this.activityList[index].conferenceType = item.itemTag
                    })
                }
            })
        },
        // 详情
        toDetails (item) {
            window.location.href = item.itemUrl;
        }
    }
}
</script>

<style lang="scss" scoped>
$primaryColor: #ff620d;
.myActivity-container {
    .navigate-bar {
        text-align: center;
        font-size: 18px;
        font-weight: 400;
        line-height: 50px;
        color: #222;
        padding: 0 10px;
        border-bottom: 1px solid #f7f7f7;
        .cubeic-arrow {
            float: left;
            transform: rotateY(180deg);
            color: #999;
        }
    }
    .scroll-list {
        height: calc(100% - 40px);
        padding-top: 10px;
        .no-data-tips {
            > img {
                width: 185px;
                height: 142px;
            }
            > div {
                margin-top: 20px;
            }
            margin-top: 170px;
            text-align: center;
            color: #999;
            display: flex;
            flex-direction: column;
            align-items: center;
            flex-wrap: wrap;
        }
        .activity-item {
            margin-bottom: 15px;
            padding: 0 15px 10px;
            border-bottom: 0.5px solid #e5e5e5;
            .activity-item-img {
                float: left;
                position: relative;
                img {
                    width: 122px;
                    height: 73px;
                    border-radius: 4px;
                }
                .activity-item-img-tags {
                    position: absolute;
                    top: 0;
                    left: 0;
                    height: 18px;
                    line-height: 18px;
                    padding: 0 4px;
                    border-radius: 4px;
                    opacity: 0.8;
                    color: #fff;
                    background: $primaryColor;
                    span {
                        font-size: 10px;
                    }
                }
            }
            .activity-item-right {
                margin-left: 132px;
                line-height: 18px;
                .activity-item-right-title {
                    color: #333;
                    font-size: 13px;
                    font-weight: 400;
                    line-height: 16px;
                    height: 34px;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -webkit-box-orient: vertical;
                    margin-bottom: 8px;
                    padding-top: 2px;
                }
                .activity-item-right-tags {
                    height: 14px;
                    margin-bottom: 8px;
                    display: flex;
                    flex-direction: row;
                    li {
                        padding: 0 5px;
                        background: #ffecde;
                        margin-right: 15px;
                        border-radius: 2px;
                        color: #ff620d;
                        line-height: 14px;
                        height: 14px;
                        max-width: 50px;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        span {
                            font-size: 10px;
                        }
                        svg {
                            vertical-align: middle;
                            margin-top: -3px;
                            margin-right: 5px;
                        }
                    }
                }
                .item-right-bottom-info {
                    color: #999;
                    font-size: 10px;
                    line-height: 14px;
                    p {
                        line-height: 16px;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        svg {
                            vertical-align: middle;
                            margin-top: -3px;
                            margin-right: 5px;
                        }
                        &.sponsor-info {
                            color: #333;
                            line-height: 26px;
                            img {
                                width: 20px;
                                height: 20px;
                                box-shadow: 0px 0px 0px 0px
                                    rgba(153, 153, 153, 1);
                                border-radius: 50%;
                                vertical-align: middle;
                                margin-right: 10px;
                            }
                        }
                    }
                }
                .activity-item-right-date {
                    display: flex;
                    flex-direction: row;
                    li {
                        width: 50%;
                    }
                }
            }
        }
    }
}
</style>

