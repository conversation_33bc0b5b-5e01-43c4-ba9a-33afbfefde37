<template>
    <div class='userInfoList'>
        <div class="tip clear-fix topBorder">
            <span style="float:left;padding-left:15px;font-weight: 400;color: #333333;">报名信息</span>
            <span style="float:right;padding-right:15px;color:var(--color-primary)" v-if="memberStatus == 0" @click='editSwitch'>{{onlySee ? '编辑' : '取消编辑'}}</span>
        </div>
        <div class="bodyInfo" v-if='onlySee'>
            <div v-for="(item,index) in formDataContent" :key="index">
                <div class='infoName'>{{item.fieldName}}</div>
                <div style="height:8px"></div>
                <div class='infoTextMoreLine' v-if="item.fieldType==2 || item.fieldType==4">{{item.text || '空'}}</div>
                <div class='infoText' v-else>{{item.text || '空'}}</div>
            </div>
        </div>

        <scroll ref="betterScroll" class="scroll-wraps" :data="formData" v-if="!onlySee">
            <div class="course-content-wrap">
                <div class="editUserGuest">
                    <liveForm  @changeEditStatus='changeEditStatus' @checkForm="checkForm" @extendTextError="extendTextError" @sortFormData="sortFormData" @changeCustomerType="changeCustomerType" @showPopupFun="showPopupFun" :customerFormType="customerType" :isForEdit="isForEdit" :isEdit="true" ref="ieventForm" :uploadUrl="uploadUrl" :language="isZhLange" :formId="formId" :formData="formData" @submitFun="submitFun" :content='formDataContent' :isSignInfo='isSignInfo' />
                    <div class="submitFooterSave">
                        <cube-button type="submit" @click='submit'>保存报名信息</cube-button>
                    </div>
                </div>

            </div>
        </scroll>
    </div>
</template>
  
  <script>
import moment from 'moment'
import scroll from '@/components/common/scroll';
import { liveForm } from 'enmore_common_mobile';
import { uploadUrl } from '@/config/env';
import {
    getPersonalInfo,
    getFormFields,
    getformId,
    isPlatformForm,
    editSignMemberInfo,
    checkNewFormInfo
} from '@/api/userInfo';

export default {
    name: 'userInfoList',
    data () {
        return {
            options: {
                scrollbar: true
            },
            optionsForm: {
                scrollToInvalidField: true, // form 验证 标识
            },
            member: {},//报名人员信息
            onlySee: true,//查看/编辑
            guest: false,//嘉宾
            formDataContent: [],//表单填写的数据

            // 以下是 表单的相关数据
            formData: [],//表单
            customerType: 0,
            isForEdit: false,
            isEdit: '',
            uploadUrl,
            localange: '',//应用于表单编辑是否中文
            formId: 0,//***关键的表单id---门票获得 */
            selectTicket: '',
            formFilesId: 0,// 表单字段id
            doMain: false, // 判断是否需要更改domainType
            discountInputValue: '',//优惠码相关 应该没用
            content: [],

            // 嘉宾表单--对应下边表单验证
            // 如果是嘉宾-给赋值
            guestForm: {},
            memberStatus: 0,//正常或屏蔽
            isZhLange: 0,
            isSignInfo: true,
            temporyformData: [],//临时存储，用于切换刷新
        }
    },
    components: {
        scroll,
        liveForm
    },
    computed: {
    },
    mounted () {
        this.getPersonalList();
        setTimeout(() => {
            this.getformId();
        }, 300);
    },
    created () {
        document.title = "报名"
    },
    watch: {
        guestForm (newValue, oldValue) {
            this.guestForm = newValue
        }
    },
    methods: {
        //获取当前表单信息
        getPersonalList () {
            let params = {
                businessId: 0,
                bizId: this.$route.query.bizId,
                moduleId: this.$route.query.moduleId,
                sysCode: 'info',
                moduleType: this.$route.query.moduleType,
                businessType: this.$route.query.businessType,
            }
            getPersonalInfo(params).then(res => {
                if (res.code == this.$successCode) {
                    this.formDataContent = this.extendTextAndSortArray(res.data.formContents) || [];
                    this.customerType = res.data.member.customerType || 0;
                    this.isZhLange = res.data.member.languageType || 0;
                    if ((!res.data.member.guestClassify.includes('audience')) || res.data.member.moduleType !== res.data.member.sourceModuleType) {
                        this.guest = true;
                    }
                    this.guestForm = res.data.member;
                    this.formFilesId = res.data.member.id;
                    this.memberStatus = res.data.member.memberStatus;
                }
                else {
                    console.log(res.info)
                }
            })
        },
        extendTextAndSortArray (contentform) {
            contentform.forEach((e, eINdex) => {
                if (e.fieldType == 3 || e.fieldType == 4) {
                    let jsonExtendText = JSON.parse(e.extendText);
                    let textArr = e.textEn.split(',').map((val, indexindex) => {
                        if (jsonExtendText[val]) {
                            const index = e.textEn.split(",").indexOf(val); // 获取当前元素在数组中的下标
                            return `${e.text.split(",")[index]}(${jsonExtendText[val]})`; // 修改为根据下标从 text 中取值
                        } else {
                            return `${e.text.split(",")[indexindex]}`;
                        }
                    });
                    // e.text = textArr.join(',');
                    e.text = textArr.join('\n');
                }
                // 时间格式化
                else if (e.fieldType == 6) {
                    e.text = moment(contentform[eINdex].text).format('YYYY-MM-DD HH:mm:ss');
                }
                else if (e.fieldType == 7) {
                    e.text = moment(contentform[eINdex].text).format('YYYY-MM-DD');
                }
            })
            // 定义排序顺序和相应的字段名
            const sortOrder = ['姓名', '公司', '职位', '手机', '邮箱']
            const fieldName = ['姓名', '公司', '职位', '手机', '邮箱']
            let sortedArr = contentform.sort((a, b) => {
                // 先判断是否是前五个需要排序的字段
                let indexA = sortOrder.indexOf(a.fieldName)
                let indexB = sortOrder.indexOf(b.fieldName)
                if (indexA !== -1 && indexB !== -1) {
                    return indexA - indexB // 如果都是前五个需要排序的字段，则根据排序顺序排序
                } else if (indexA === -1 && indexB === -1) {
                    return a.sortNum - b.sortNum // 如果都不是需要排序的字段，则按sortNum排序
                } else {
                    return indexA === -1 ? 1 : -1 // 如果只有一个是需要排序的字段，则将该字段排在前面
                }
            })
            sortedArr = sortedArr.filter((item) => {
                return item.text !== ''
            })
            return sortedArr
        },
        //获取门票列表
        getformId () {
            let params = {
                businessId: 0,
                businessType: this.$route.query.businessType,
                formBusinessType: this.$route.query.businessType,
                formType: "ticket",
                moduleId: this.$route.query.moduleId,
                moduleType: this.$route.query.moduleType,
                productType: "ticket",
                sysCode: "info",
                signType: 'ticket',
            }
            getformId(params).then((res) => {
                if (res.code == this.$successCode) {
                    this.selectTicket = res.data.list[0];
                    let formList = res.data.list[0].formList || [];
                    //表单编辑无需匹配英文表单
                    // if (formList.length > 0) {
                    //     this.formId = formList.find(
                    //         (item) => item.languageType == this.isZhLange
                    //     ).id;
                    // }
                    this.formId = formList.find(
                        (item) => item.languageType == 0
                    ).id;
                    // this.getSignTitleForm(this.formId);//编辑的时候才走
                } else {
                    this.$toast(res.info);
                }
            });
        },
        getSignTitleForm (formId) {
            getFormFields({ formId }).then(res => {
                if (res.code == "001") {
                    this.$nextTick(() => {
                        this.formData = this.$refs.ieventForm.formatFormData(res.data);
                        // this.formData = res.data;
                        if (this.guest) {
                            const requiredModels = ['name', 'company', 'position', 'mobilephone', 'email'];
                            const filteredData = []; // 创建一个空数组用来保存过滤后的结果                           
                            // 根据过滤条件进行过滤并添加新对象
                            requiredModels.forEach(model => {
                                const existingObject = this.formData.find(obj => obj.model === model);
                                if (existingObject) {
                                    filteredData.push(existingObject);
                                } else {
                                    filteredData.push({ model: model, value: '' }); // 如果表单没有的话,默认添加上述五个
                                }
                            });
                            this.formData = filteredData;
                        }
                        setTimeout(() => {
                            this.$refs.ieventForm.setFormInfo(res.data);
                        }, 400)
                        setTimeout(() => {
                            this.$refs.ieventForm.changeEditStatus();
                        }, 600)
                    })
                }
            })
        },

        editSwitch () {
            this.onlySee = !this.onlySee;
            if (!this.onlySee) {
                this.$nextTick(() => {
                    this.getSignTitleForm(this.formId)
                    this.$refs.betterScroll.scrollTo(0, 0);
                })
            }
        },

        // 传入表单方法
        // 扩展项滚动
        extendTextError () {
            let el = document.querySelector('.text-danger');
            this.$nextTick(() => {
                this.$refs.betterScroll.scrollToElement(el.parentNode.parentNode.parentNode.parentNode, 300)
            })
        },
        // 检测并滚动 --表单验证成功不走此方法，验证不成功，屏幕回到报错位置
        checkForm () {
            this.$nextTick(() => {
                let el = document.querySelectorAll('.editUserGuest .cube-validator-msg-def');
                for (let i = 0; i < el.length; i++) {
                    if (el[i].innerHTML) {
                        this.$refs.betterScroll.scrollToElement(el[i].parentNode.parentNode.parentNode, 100);
                        return;
                    }
                }
            })

        },
        // 表单进行排序
        sortFormData (data) {
            let editStatus = data.filter((i) => {
                return !i.editStatus;
            })
            this.formData.forEach((item, index) => {
                editStatus.forEach((subitem) => {
                    if (subitem.model == item.model) {
                        this.$set(item, 'del', true)
                    }
                })
            })
            this.formData = this.formData.filter((item) => {
                return !item.del
            })
            this.formData = [...editStatus, ...this.formData];
        },
        //客户类型
        changeCustomerType (payload) {
            this.customerType = payload;
        },
        changeEditStatus(property){
            this.formData = this.formData.map((item)=>{
                item.editStatus = true;
                return item;
            })
        },
        //目前未知
        showPopupFun (info) {
            this.$toast(info);
        },

        //普通人员提交表单 
        submitSimple (e) {
            // 我普通人员
            let params = {
                member: {
                    // languageType: this.isZhLange ? 0 : 1,
                    formContents: e.data,
                    // customerType: this.customerType,
                    productId: this.selectTicket.id,
                    signTypeId: this.selectTicket.signTypeId,
                    skuId: this.selectTicket.defaultSkuId,
                    signInfoId: this.formFilesId,//报名人员id
                    signType: 'ticket'
                },
                signAway: 'MOBILE',
                channel: "MOBILE",
                platformId: sessionStorage.getItem('platformId'),
                itemInfo: {
                    businessId: 0,
                    businessType: this.$route.query.businessType,
                    moduleId: this.$route.query.moduleId,
                    moduleType: this.$route.query.moduleType,
                    sysCode: "info",
                    sourceModuleId: this.$route.query.moduleId,
                    sourceModuleType: this.$route.query.moduleType,
                },
            }
            params.member.languageType = this.getLanguageType(e.data)
            params.member.customerType = this.getCustomerType(e.data)
            editSignMemberInfo(params).then(res => {
                if (res.code == this.$successCode) {
                    this.getPersonalList();
                    this.onlySee = true;
                }
                else {
                    this.$message.warning(res.info);
                }
            })

        },
        // 嘉宾 普通人员中转
        submitFun (payload) {
            if (this.guest) {
                this.submitGuest(payload)
            }
            else {
                this.submitSimple(payload)
            }
        },
        // 嘉宾的提交验证
        submitGuest (e) {
            console.log('我嘉宾', e);
            // 我嘉宾
            let paramsData = {
                memberSignInfo: {
                    ...this.guestForm
                },
                signAway: 'MOBILE',
                channel: "MOBILE",
                platformId: sessionStorage.getItem('platformId'),
                order: null,
                itemInfo: {
                    businessId: 0,
                    businessType: this.$route.query.businessType,
                    moduleId: this.$route.query.moduleId,
                    moduleType: this.$route.query.moduleType,
                    sysCode: "info",
                    sourceModuleId: this.$route.query.moduleId,
                    sourceModuleType: this.$route.query.moduleType,
                },
            }
            paramsData.memberSignInfo = this.paramsDataSet(paramsData.memberSignInfo, e.data)

            editSignMemberInfo(paramsData).then(res => {
                if (res.code == this.$successCode) {
                    this.getPersonalList();
                    this.onlySee = true;
                }
                else {
                    this.$message.warning(res.info);
                }
            })
        },
        paramsDataSet (memberSignInfo, area) {
            const findValue = (model) => {
                const foundItem = area.find(item => item.model === model);
                return foundItem ? foundItem.value : '';
            };
            memberSignInfo.name = findValue('name');
            memberSignInfo.company = findValue('company');
            memberSignInfo.position = findValue('position');
            memberSignInfo.mobilephone = findValue('mobilephone');
            memberSignInfo.email = findValue('email');

            memberSignInfo.iddAreaCode = area.find(item => item.model == 'mobilephone').extendText;

            memberSignInfo.customerType = this.getCustomerType(area);
            memberSignInfo.languageType = this.getLanguageType(area);
            return memberSignInfo
        },
        // 点击提交按钮，进行表单验证
        submit () {
            this.$refs.ieventForm.submit();
        },
        // 语言类型
        getLanguageType (val) {
            let lantype
            val.forEach(e => {
                if (e.model == 'mobilephone')
                    lantype = e.extendText
            })
            if (lantype == '+86' || lantype == '+852' || lantype == '+853' || lantype == '+886') {
                return 0 // this.languageType=0
            }
            else {
                return 1  // this.languageType= 1
            }
        },
        // 客户类型
        getCustomerType (val) {
            let Custype
            val.forEach(e => {
                if (e.model == 'mobilephone') Custype = e.extendText
            })
            if (Custype == '+86') {
                return 0 //this.customType = 0
            }
            else {
                return 2 //this.customType = 2
            }
        },
    },
}
  </script>
  
  <style lang='scss' scoped>
.userInfoList {
    padding-bottom: 60px;
    // padding:15px 0 80px;
    width: 100%;
    height: 100%;
    overflow: hidden;
    // box-sizing: border-box;
    .scroll-wraps {
        height: 100%;
        overflow: hidden;
        .editUserGuest {
            margin-top: 1px;
            // overflow:hidden;
        }
        .submitFooterSave {
            text-align: -webkit-center;
            .cube-btn {
                background-color: var(--color-primary);
                color: #ffffff;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                width: 150px;
                height: 40px;
                line-height: 5px;
            }
        }
    }
    .tip {
        width: 100%;
        height: 42px;
        line-height: 42px;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        box-shadow: 0px 1px 0px 0px #eeeeee !important;
        border-radius: 6px 6px 0px 0px;
    }
    .bodyInfo {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        padding-left: 15px;
        height: 100%;
        overflow: scroll;
        .infoName {
            color: #999999;
            margin-top: 20px;
        }
        .infoText {
            color: #333333;
        }
        .infoTextMoreLine {
            color: #333333;
            white-space: pre-wrap;
        }
    }
    :deep(.scroll-wraps) {
        .signNewFormTitle {
            display: none;
        }
        .signNewFormWrap {
            padding-top: 0px;
            padding-left: 15px;
            .left:not(:empty) {
                margin-left: 3px !important;
            }
            .left:empty {
            }
            .formItemcheckbox {
                .cube-input-field {
                    background-color: #fff;
                }
                .cube-checkbox-group {
                    padding-bottom: 10px;
                }
            }
        }
    }
}
</style>