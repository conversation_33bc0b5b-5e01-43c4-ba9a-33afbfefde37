<template>
  <div class="body" ref="body_ref">
    <div class="top_img_body" v-if="getProductInfoInfo && getProductInfoInfo.logo">
      <cube-slide :auto-play="false" :data="getProductInfoInfo.logo" allowVertical ref="slideImage" :slides-per-view="3" @change="chaBtn">
        <cube-slide-item class="swiperItem_content" :key="index" v-for="(item, index) in getProductInfoInfo.logo">
          <div class="img_box">
            <img :src="item" alt="" />
          </div>
        </cube-slide-item>
      </cube-slide>
      <div class="bot_page">
        <p>{{ indNum + 1 }}/{{ getProductInfoInfo.logo.length }}</p>
      </div>
    </div>
    <div class="middle_top">
      <div class="middle_body">
        <p>{{ getProductInfoInfo.productPriceCoin }}<span>易币</span></p>
        <p class="hua_text_item" v-if="getProductInfoInfo.dashingCoinStatus">{{ getProductInfoInfo.dashingDescribeCoin }}易币</p>
      </div>
    </div>
    <div class="middle_text">
      <div class="middle_text_img" ref="pro_ref" :class="!titType ? 'hidden_class' : ''">
        <img v-if="getProductInfoInfo.productType == '实物商品'" src="@/assets/img/shi_icon.png" alt="" />
        <img v-if="getProductInfoInfo.productType == '虚拟商品'" src="@/assets/img/xu_icon.png" alt="" />
        <p class="tit_class">{{ getProductInfoInfo.productName }}</p>
      </div>
      <p class="zhan_p" @click="titType = !titType" v-if="titShow">
        {{ titType ? "收起" : "展开" }}
        <i class="cubeic-select" :class="titType ? 'play_class' : ''"></i>
      </p>
      <p class="hui_text" ref="mark_ref" v-if="getProductInfoInfo.mark" :class="!markType ? 'hidden_class' : ''">{{ getProductInfoInfo.mark }}</p>
      <p class="zhan_p" @click="markType = !markType" v-if="markShow">
        {{ markType ? "收起" : "展开" }}
        <i class="cubeic-select" :class="markType ? 'play_class' : ''"></i>
      </p>
    </div>
    <div class="pei_body" v-if="getProductInfoInfo.productType == '实物商品'">
      <div class="pei_top_text">
        <p class="pei_one_text">配送说明</p>
        <p class="pei_two_text">快递配送</p>
      </div>
      <div class="text_bot">由于目前所有快递紧张，不保证时效，希望理解。同时每个人只能填写注册实名认证信息为收件人，不可更改他人</div>
    </div>
    <div class="bot_info_class" v-if="getProductInfoInfo.description && getProductInfoInfo.description.length">
      <div class="bot_title_item">产品详情介绍</div>
      <div>
        <div v-for="(item, index) in getProductInfoInfo.description">
          <div v-if="item.type == 'text'" class="every text_class" v-html="item.text"></div>
          <div v-if="item.type == 'img'" class="every image">
            <img :src="item.url" alt="" />
          </div>
        </div>
      </div>
    </div>
    <div class="bot_btn_class">
      <div class="bot_btn" @click="jumpDealts" :style="getProductInfoInfo.leftCount == 0 ? 'background-color: #c3c8ca;' : 'background: #097DB4;'">
        {{ getProductInfoInfo.leftCount == 0 ? "已售罄" : "立即兑换" }}
      </div>
    </div>
    <img
      class="fan_btn_class"
      src="@/assets/img/fan.png"
      alt=""
      @click="fanBtn"
      :style="`left: ${touchEndX}px;top: ${touchEndY}px;`"
      @touchstart="touchstart"
      @touchend="handleTouchEnd"
      @touchmove="handleTouchMove"
    />
    <mallRecomeDia v-if="mallRecomeType" @mallEmitSave="mallEmitSave"></mallRecomeDia>
  </div>
</template>

<script>
import mallRecomeDia from "@/components/common/mallRecomeDia";
import { getMemberPoints, getMemberAuthStatus,getPersonApi } from "@/api/memberApi";
import { beforeExchange } from "@/api/monetary";
import { mapActions } from "vuex";
import pn from "../../assets/img/jin_icon.png";
import moment from "moment";
export default {
  data() {
    return {
      markType: false,
      markShow: false,
      touchEndX: 0,
      touchEndY: 0,
      titType: false,
      titShow: false,
      mallRecomeType: false, // 立即实名认证的弹窗
      indNum: 0, // 下标
    };
  },
  computed: {
    personProfile() {
      return this.$store.state.personal.personProfile;
    },
    getProductInfoInfo() {
      return this.$store.state.getProductInfoInfo;
    },
    getBerPointInfo() {
      return this.$store.state.getBerPointInfo;
    },
  },
  watch: {
    "getProductInfoInfo.mark": {
      handler(val) {
        // 判断标题是否超出两行
        if (val) {
          this.markLineChange();
        }
      },
      deep: true,
    },
    "getProductInfoInfo.productName": {
      handler(val) {
        // 判断标题是否超出两行
        this.titleLineChange();
      },
      deep: true,
    },
    personProfile: {
      handler() {
        this.getMemberAuthStatus();
      },
      deep: true,
    },
  },
  components: {
    mallRecomeDia,
  },
  created() {
    this.EnterExposure();
    this.getProductInfoDetail(this.$route.params.id);
    this.$store.dispatch("getPerprofile");
  },
  mounted() {
      this.getPersonApi();
  },
  beforeDestroy() {
    this.$store.commit("getProductInfoDetail", {});
  },
  methods: {
    ...mapActions(["getMemberAuthStatus"]),
      // 获取微信分享设置信息
      getPersonApi() {
        let params = {
          platformId: this.platformId,
        }
        getPersonApi(params).then(res=>{
            let that = this;
            this.wxShareConfig(res.data,'pageDataAction', {
                that,
                clickEle: 'mallGoodsShare',
                currentPage: 'mallGoods',
                targetPage: '',
                businessId: this.$route.params.id,
                queryParam: '',
                businessType: 'mallGoods',
                businessSeq: null
              })
        })
      },
    // 判断描述是否需要展示
    markLineChange() {
      this.$nextTick(() => {
        if (!this.$refs.mark_ref) return this.markLineChange();
        const text = this.$refs.mark_ref;
        const height = text.scrollHeight;
        const clientHeight = text.clientHeight;
        if (height > clientHeight) {
          this.markShow = true;
        }
      });
    },
    // 计算返回球开始的位置
    EnterExposure() {
      this.$nextTick(() => {
        if (!this.$refs.body_ref) return this.EnterExposure();
        setTimeout(() => {
          this.touchEndX = this.$refs.body_ref.clientWidth - 56;
          this.touchEndY = this.$refs.body_ref.clientHeight - 375;
        }, 0);
      });
    },
    handleTouchMove(event) {
      event.preventDefault(); // 阻止默认的滚动行为
      // 获取触摸移动时的坐标
      this.touchEndX = event.touches[0].pageX - 20;
      this.touchEndY = event.touches[0].pageY - 20;

      // 可以在这里执行其他操作，例如移动元素
    },
    handleTouchEnd(event) {},
    // 拖动事件操作
    touchstart() {},
    // 判断标题是否超出两行
    titleLineChange() {
      this.$nextTick(() => {
        if (!this.$refs.pro_ref) return this.titleLineChange();
        let text = this.$refs.pro_ref;
        let height = text.offsetHeight;
        if (height > 75) {
          this.titShow = true;
        }
      });
    },
    // 实名弹窗返回数据事件
    mallEmitSave() {
      this.mallRecomeType = false;
    },
    // 返回事件点击
    fanBtn() {
      this.$router.push({
        path: "/mall",
      });
    },
    ...mapActions(["getProductInfoDetail"]),
    // 变换
    chaBtn(index) {
      this.indNum = index;
    },
    // 返回商城
    jumpBtn() {
      this.$router.push({
        path: "/mall",
      });
    },
    // 跳转订单详情页
    async jumpDealts(order) {
      if (this.getProductInfoInfo.leftCount == 0) return;
      var demo = await beforeExchange({ productId: Number(this.$route.params.id) });
      if (demo.code == this.$successCode) {
        this.$router.push({
          path: `/mall/order/${this.$route.params.id}`,
        });
      } else if (demo.code == "002" && demo.data == 2) {
        // 如果没有认证过并且已经提交过认证则给出待审核的提示语
       // if (this.getBerPointInfo.auditFlag == 0 && this.getBerPointInfo.authFlag == 0) return this.$toast("实名认证审核中，如有问题请联系主办方");
        this.mallRecomeType = true;
      } else {
        return this.$toast(demo.info);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .cube-slide-group {
  // width: 100% !important;
}
.img_box {
  // width: 100%;
  position: relative;
  background-color: #ffffff;
  height: 375px;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    // width: 100%;
    max-height: 100%;
  }
}
.swiperItem_content {
  // width: 100% !important;
}
.bot_page {
  width: 36px;
  height: 20px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  position: absolute;
  bottom: 12px;
  right: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  p {
    font-size: 10px;
    color: #eeeeee;
    text-align: left;
    font-style: normal;
    opacity: 0.6;
  }
}
.top_img_body {
  position: relative;
  ::v-deep .cube-slide-dots {
    display: none;
  }
}
.middle_body {
  width: 351px;
  height: 60px;
  // background: linear-gradient( 180deg, #FF886F 0%, #FF503C 100%);
  border-radius: 4px;
  padding: 10px 12px 12px 12px;
  display: flex;
  align-items: center;
  background-image: url("../../assets/img/jin_icon.png");
  background-size: 100% 100%;
  margin-bottom: 0px;
  p {
    font-weight: 600;
    font-size: 20px;
    color: #ffffff;
    text-align: left;
    font-style: normal;
    span {
      // color: #EEEEEE;
      font-size: 12px;
      // opacity: 0.6;
    }
  }
}
.middle_text {
  padding: 10px 10px 12px 10px;
  background-color: #ffffff;
  margin: 0px 12px;
  border-radius: 0px 0px 4px 4px;
  .middle_text_img {
    line-height: 22px;
    img {
      width: 56px;
      height: 16px;
      margin-right: 6px;
      display: inline;
      margin-bottom: -2px;
    }
    p {
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      text-align: left;
      font-style: normal;
      display: inline;
    }
    .tit_class {
      display: inline;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      text-align: left;
      font-style: normal;
      line-height: 22px;
    }
  }
  .hui_text {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    text-align: left;
    font-style: normal;
    margin-top: 8px;
    line-height: 21px;
  }
}
.body {
  background-color: #f5f6fa;
  padding-bottom: 70px;
  min-height: 100%;
  height: auto !important;
}
.middle_top {
  background-color: #ffffff;
  margin: 12px;
  margin-bottom: 0px;
}
.pei_body {
  padding: 0px 10px 0px 10px;
  background-color: #ffffff;
  margin: 12px;
  height: 96px;
  border-radius: 4px;
  .pei_top_text {
    display: flex;
    height: 44px;
    border-bottom: 1px solid #eeeeee;
    align-items: center;
    .pei_one_text {
      font-weight: 400;
      font-size: 13px;
      color: #999999;
      text-align: left;
      font-style: normal;
    }
    .pei_two_text {
      font-weight: 400;
      font-size: 13px;
      color: #333333;
      text-align: left;
      font-style: normal;
      margin-left: 15px;
    }
  }
  .text_bot {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    text-align: left;
    font-style: normal;
    margin-top: 10px;
    line-height: 16px;
  }
}
.bot_info_class {
  background-color: #ffffff;
  padding: 12px;
  margin: 12px;
  // padding-bottom: 75px;
  border-radius: 4px;
  .bot_title_item {
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    text-align: left;
    font-style: normal;
    margin-bottom: 12px;
  }
}
.bot_btn {
  width: 345px;
  height: 42px;
  // background: #097DB4;
  border-radius: 3px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  text-align: left;
  font-style: normal;
}
.bot_btn_class {
  background-color: #ffffff;
  width: 100%;
  padding: 12px 15px 11px 15px;
  position: fixed;
  bottom: 0px;
}
.hua_text_item {
  font-weight: 400;
  font-size: 13px !important;
  color: #eeeeee;
  text-align: left;
  font-style: normal;
  text-decoration-line: line-through;
  opacity: 0.6;
  margin-left: 12px;
  margin-top: 5px;
}
.fan_btn_class {
  width: 44px;
  height: 44px;
  position: fixed;
  right: 12px;
  top: 58.6%;
}
.every {
  margin-bottom: 15px;
  font-size: 14px;
  color: #666666;
}
.text_class {
  line-height: 20px;
}
.zhan_p {
  margin-left: 6px;
  color: #097db4 !important;
  margin-left: 0px;
  margin-top: 4px;
  font-size: 12px;
}
.hidden_class {
  white-space: normal; /* 确保文本可以折行 */
  overflow: hidden; /* 隐藏超出的内容 */
  display: -webkit-box; /* 使用弹性盒子模型 */
  -webkit-line-clamp: 4; /* 限制显示的行数为2行 */
  -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
  line-height: 20px; /* 行高 */
  text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
}
.cubeic-select {
  font-size: 12px !important;
}
.play_class {
  transform: rotate(180deg);
}
</style>
