<template>
  <div class="my-activity-wrap">
    <detail-page ref="detailPage" :hasSearch="true" :hasFilter="true" :optionFilter="optionFilter" @tabClick="tabClick" @searchKeyWord="searchKeyWord">
      <template #filterItem>
        <commonScroll ref="commonScroll" :getListData="getListData" :listParams="activeTab ? configurationHasEnded : configurationNotStarted" :conversion="conversion">
          <template #default="slotProps">
            <infoItemSearchZly :iflast="true" :info="slotProps.item" :showTag="true" :closeExhibitor="true"></infoItemSearchZly>
          </template>
        </commonScroll>
      </template>
    </detail-page>
  </div>
</template>
<script>
import DetailPage from "./components/detailPage";
import { getMyActivityList } from "@/api/userCenter";
import infoItemSearchZly from "@/components/newCommon/infoItemSearchZly";
import commonScroll from "./components/detailPage/commonScroll";
export default {
  data() {
    return {
      optionFilter: [
        { name: "未开始", hasFilter: false },
        { name: "已结束", hasFilter: false },
      ],
      configurationNotStarted: {
        activityPosition: 1,
        typeStringList: "activity",
        keyword: "",
      },
      configurationHasEnded: {
        activityPosition: 0,
        typeStringList: "activity",
        keyword: "",
      },
      // 当前激活
      activeTab: 0,
      getListData: getMyActivityList,
    };
  },
  created() {},
  components: { DetailPage, infoItemSearchZly, commonScroll },
  mounted() {},
  computed: {},
  methods: {
    tabClick(index) {
      if (this.activeTab == index) return;
      this.activeTab = index;
    },
    searchKeyWord(keyword) {
      this.configurationNotStarted.keyword = keyword;
      this.configurationHasEnded.keyword = keyword;
      this.reload();
    },
    // 重新加载数据
    reload() {
      this.$nextTick(() => {
        this.$refs.detailPage.$children[1].$refs.tabs[this.activeTab].$children[0].getList(true);
        //this.$refs.commonScroll[this.activeTab].getList(true);
      });
    },
    // 转换数据
    conversion(data) {
      data.forEach((item) => {
        this.$set(item, "id", item.itemId);
        this.$set(item, "type", item.itemType);
        this.$set(item, "conferenceType", item.itemTag);
      });
    },
  },
};
</script>
<style scoped lang="scss">
.my-activity-wrap {
  width: 100%;
  height: 100%;
  background: #fff;
}
</style>
