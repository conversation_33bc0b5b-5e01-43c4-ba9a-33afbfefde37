<template>
  <div class="my-inquiry-wrap">
    <detail-page ref="detailPage" :hasSearch="true" :hasFilter="true" @onSubSelect="onSubSelect" :optionFilter="optionFilter" @tabClick="tabClick" @searchKeyWord="searchKeyWord">
      <template #filterItem>
        <commonScroll v-if="activeTab == 0" :getListData="getListBuyData" :listParams="purchaseConfiguration">
          <template #default="slotProps">
            <purchaseInfoItem :info="slotProps.item"></purchaseInfoItem>
          </template>
        </commonScroll>
        <commonScroll v-if="activeTab == 1" :getListData="getListInquiryData" :listParams="inquiryConfiguration">
          <template #default="slotProps">
            <inquiryInfoItem :item="slotProps.item"></inquiryInfoItem>
          </template>
        </commonScroll>
      </template>
    </detail-page>
  </div>
</template>
<script>
import DetailPage from "./components/detailPage";
import CommonScroll from "./components/detailPage/commonScroll";
import { getMyBuyList, getUserList, getPublicParams } from "@/api/userCenter";
import PurchaseInfoItem from "@/components/newCommon/purchaseInfoItem";
import InquiryInfoItem from "@/components/newCommon/inquiryInfoItem";
import CONSTANT from "@/config/config_constant";
export default {
  data() {
    return {
      optionFilter: [
        {
          name: "我的求购",
          hasFilter: false,
          subFilter: [
            {
              name: "处理状态",
              hasFilter: true,
              option: [
                { text: "全部", value: "", select: true },
                { text: "待处理", value: 0, select: false },
                { text: "已处理", value: 1, select: false },
                { text: "废弃", value: 2, select: false },
              ],
            },
            {
              name: "求购有效期",
              hasFilter: true,
              option: [],
            },
            {
              name: "按时间降序",
              hasFilter: false,
              autoChange: true, // 自动切换
              value: "desc",
            },
          ],
        },
        {
          name: "我的询价",
          hasFilter: false,
          subFilter: [
            {
              name: "全部",
              hasFilter: false,
              value: "",
            },
            {
              name: "处理",
              hasFilter: false,
              value: 2,
            },
            {
              name: "待处理",
              hasFilter: false,
              value: 1,
            },
          ],
        },
      ],
      searchOption: {},
      sourceList: [],
      // 当前激活
      activeTab: 0,
      // 求购配置
      purchaseConfiguration: {
        keyword: "",
        processingStatus: "",
        effective: "",
        sort: "asc",
      },
      // 询价配置
      inquiryConfiguration: {
        keyword: "",
        status: "",
      },
      // 我的求购的接口
      getListBuyData: getMyBuyList,
      // 我的询价的接口
      getListInquiryData: getUserList,
    };
  },
  created() {
    // 获取求购有效期
    this.getInquiryEffective();
  },
  components: { DetailPage, CommonScroll, PurchaseInfoItem, InquiryInfoItem },
  mounted() {},
  computed: {},
  methods: {
    tabClick(index) {
      if (this.activeTab == index) return;
      this.activeTab = index;
    },
    searchKeyWord(keyword) {
      this.purchaseConfiguration.keyword = keyword;
      this.inquiryConfiguration.keyword = keyword;
      this.reload();
    },
    reload() {
      this.$nextTick(() => {
        this.$refs.detailPage.$children[1].$refs.tabs[this.activeTab].$children[3].getList(true);
      });
    },
    // 获取求购有效期
    async getInquiryEffective() {
      let { data } = await getPublicParams(CONSTANT.inquiryEffective);
      if (data && data.children) {
        this.optionFilter[0].subFilter[1].option = data.children.map((item) => {
          return {
            text: item.codeValueDesc,
            value: item.codeId,
            select: false,
          };
        });
      }
      //console.log("data", data);
    },
    onSubSelect(item, index) {
      if (this.activeTab == 0) {
        switch (index) {
          case 0:
            this.purchaseConfiguration.processingStatus = item.value;
            // subFilter选中改变
            this.mergeData(item,index);
            break;
          case 1:
            this.purchaseConfiguration.effective = item.value;
            // subFilter选中改变
            this.mergeData(item,index);
            break;
          case 2:
            if (item.value == "desc") {
              this.purchaseConfiguration.sort = "asc";
            } else {
              this.purchaseConfiguration.sort = "desc";
            }
            this.optionFilter[0].subFilter[2].value = item.value == "desc" ? "asc" : "desc";
            break;
        }
      } else if (this.activeTab == 1) {
        this.inquiryConfiguration.status = item.value;
      }
      this.reload();
    },
    // 整合数据
    mergeData(item,index) {
      this.optionFilter[0].subFilter[index].option.forEach((filterItem) => {
        if (item.value === filterItem.value) {
          filterItem.select = true;
        } else {
          filterItem.select = false;
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.my-inquiry-wrap {
  width: 100%;
  height: 100%;
  :deep(.filter-content) {
    background: #f5f6fa !important;
  }
}
</style>
