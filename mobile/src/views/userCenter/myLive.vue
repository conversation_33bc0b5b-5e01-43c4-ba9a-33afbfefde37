<template>
  <div class="my-live-wrap">
    <detail-page ref="detailPage" :hasSearch="true" :hasFilter="true" :optionFilter="optionFilter" @tabClick="tabClick" @onSelect="onSelect" @searchKeyWord="searchKeyWord">
      <template #filterItem>
        <commonScroll v-for="(item, index) in optionFilter" ref="commonScroll" :key="index" v-if="activeTab == index"  :getListData="getListData" :listParams="index ? configurationHasEnded :configurationNotStarted" :conversion="conversion">
          <template #default="slotProps">
            <infoItemSearchZly :info="slotProps.item" :showTag="true" :closeExhibitor="true"></infoItemSearchZly>
          </template>
        </commonScroll>
      </template>
    </detail-page>
  </div>
</template>
<script>
import DetailPage from "./components/detailPage";
import { getMyActivityList } from "@/api/userCenter";
import infoItemSearchZly from "@/components/newCommon/infoItemSearchZly";
import commonScroll from "./components/detailPage/commonScroll";
export default {
  data() {
    return {
      optionFilter: [
        { name: "未开始", hasFilter: false},
        { name: "已结束", hasFilter: false },
        {
          name: "直播分类",
          hasFilter: true,
          disable: true,
          option: [
            { text: "全部直播", value: 0, select: true },
            { text: "单期直播", value: 1, select: false },
            { text: "系列直播", value: 2, select: false },
          ],
        },
      ],
      configurationNotStarted: {
        activityPosition: 1,
        typeStringList: "live,series",
        keyword: "",
      },
      configurationHasEnded: {
        activityPosition: 0,
        typeStringList: "live,series",
        keyword: "",
      },
      // 当前激活
      activeTab: 0,
      // 当前选择的下拉项
      currentSelect: 0,
      getListData: getMyActivityList,
    };
  },
  created() {},
  components: { DetailPage, infoItemSearchZly, commonScroll },
  mounted() {},
  computed: {},
  methods: {
    tabClick(index) {
      // console.log("得到索引值", arg);
      if (this.activeTab == index) return;
      this.activeTab = index;
    },
    onSelect(action) {
      if (action.value == this.currentSelect) return;
      switch (action.value) {
        case 0:
          this.configurationNotStarted.typeStringList = "live,series";
          // 已结束的也需要改动
          this.configurationHasEnded.typeStringList = "live,series";
          break;
        case 1:
          this.configurationNotStarted.typeStringList = "live";
          this.configurationHasEnded.typeStringList = "live";
          break;
        case 2:
          this.configurationNotStarted.typeStringList = "series";
          this.configurationHasEnded.typeStringList = "series";
          break;
      }
      // 选择
      this.optionFilter[2].option.forEach((item, index) => {
        if (item.value == action.value) {
          item.select = true;
        } else {
          item.select = false;
        }
      });
      this.currentSelect = action.value;
      this.reload();
    },
    searchKeyWord(keyword) {
      this.configurationNotStarted.keyword = keyword;
      this.configurationHasEnded.keyword = keyword;
      this.reload();
    },
    // 重新加载数据
    reload() {
      this.$nextTick(() => {
       this.$refs.commonScroll[this.activeTab].getList(true);
      });
    },
    conversion(data) {
      data.forEach((item) => {
        this.$set(item, "id", item.itemId);
        this.$set(item, "type", item.itemType);
        this.$set(item, "conferenceType", item.itemTag);
      });
    },
  },
};
</script>
<style scoped lang="scss">
.my-live-wrap {
  width: 100%;
  height: 100%;
  background: #fff;
}
</style>
