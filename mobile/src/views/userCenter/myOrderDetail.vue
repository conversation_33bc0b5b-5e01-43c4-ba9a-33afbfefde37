<template>
  <div class="myOrder-detail-container">
    <div class="goods-info clear-fix" @click="toGoodsInfo">
      <img :src="orderInfo.liveInfo?orderInfo.liveInfo.bannerImg:''" class="goods-info-img">
      <!-- <img src="orderInfo.bannerImg" class="goods-info-img"> -->
      <div class="goods-info-content">
        <div class="goods-info-title">{{orderInfo.liveInfo?orderInfo.liveInfo.name:''}}</div>
      </div>
    </div>
    <div class="order-info">
      <div class="order-info-title">订单详情</div>
      <ul class="order-info-content">
        <!-- <li><span>姓名：</span><div>{{orderInfo.name}}</div></li> -->
        <!-- <li><span>联系方式：</span><div>{{orderInfo.bookingMobilephone}}</div></li> -->
        <li><span>订单类型：</span><div>{{orderInfo.productName}}</div></li>
        <li><span>{{orderInfo.payTime?"实付":'需付'}}金额：</span><div>￥{{orderInfo.payTime?priceFixed(orderInfo.payPrice):priceFixed(orderInfo.discountPrice)}}</div></li>
        <li><span>支付状态：</span><div>{{orderInfo.orderStatusText}}</div></li>
        <li><span>订单编号：</span><div>{{orderInfo.orderCode}}</div></li>
        <li><span>下单时间：</span><div>{{moment(orderInfo.orderCreateTime).format('YYYY-MM-DD HH:mm:ss')}}</div></li>
      </ul>
    </div>
    <div class="footer-pay" >
      <cube-button v-if="orderInfo.orderStatus==4" @click="payOrder(orderInfo)">立即支付 <!-- {{thisCountDown}} --></cube-button>
    </div>
  </div>
</template>
<script>
import { getOrderById, postPay } from '@/api/userCenter';
import moment from 'moment';
export default {
  name: "myOrderDetail",
  data () {
    return {
      orderId: this.$route.query.orderId,
      itemType: this.$route.query.itemType,
      orderInfo: {},
      thisCountDown: '', // 倒计时
      itemTypes: {
        ACTIVITY:0,
        COURSE:1,
        LETURER:2,
        AUDIO:3,
        THINK_TANK:4,
        INFO:5,
        LIVE:6,
        TOPIC:7,
        DIY:8,
        SERIESLIVE: 9
      }
    }
  },
  mounted() {
    this.getOrderDetails()
  },
  methods: {
    moment,
    // 获取订单详情
    getOrderDetails() {
      let params = [
        'orderId=' + this.orderId,
        'itemType=' + this.itemType
      ]
      getOrderById(params.join('&')).then( res => {
        if (res.code == this.$successCode) {
          this.orderInfo = res.data;
          console.log(this.orderInfo,'bbb');
          // this.countDown(this.orderInfo.orderCreateTime);
        }
      })
    },
    // 倒计时
    countDown(orderCreateTime) {
      let endTime = new Date(orderCreateTime).getTime() + 30*60*1000;
      let time = '';
      setInterval(() => {
        let now = new Date().getTime();
        let thisMinute = moment.duration(endTime - now)._data.minutes;
        let thisSecond = moment.duration(endTime - now)._data.seconds;
        this.thisCountDown = (thisMinute<10?'0'+thisMinute:thisMinute) + ':' + (thisSecond<10?'0'+thisSecond:thisSecond)
      }, 1000);
    },
    // 立即支付
    payOrder(orderInfo) {
      console.log('支付',orderInfo);
       let params = {
            businessId: 0,
            businessType: orderInfo.itemType+"Sign",
            orderIdList:[this.orderId],
            sysCode: "info",
            moduleId: orderInfo.itemId,
            moduleType: orderInfo.itemType,
            sourceModuleId: orderInfo.itemId,
            sourceModuleType: orderInfo.itemType
      }
      postPay(params).then((res) => {
        let that = this;
        if (res.code == this.$successCode) {
          let payBody = res.data;
          //这里需要进行支付
          wx.chooseWXPay({
            timestamp: payBody.timeStamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
            nonceStr: payBody.nonceStr, // 支付签名随机串，不长于 32 位
            package: payBody.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
            signType: payBody.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
            paySign: payBody.paySign, // 支付签名
            success: function (res) {
              // 支付成功后的回调函数
              // this.$toast('支付成功');
              // 重新加载 订单列表
              that.getOrderDetails()
            },
            error: function () {
              that.$createToast({ txt: '支付失败', type: 'error'}).show()
            },
            cancel: function () {
              // console.log('取消支付')
            },
            complete: function () {
                
            }
          });
        } else {
          this.$createToast({ txt: res.info, type: 'error'}).show()
        }
      })
    },
    // 跳转商品详情 页面
    toGoodsInfo() {
      let thisHref = '';
      if (this.itemType == this.TYPE[6]) {
        // 直播
        if (this.orderInfo.liveInfo.liveType != this.TYPE[2]) {
            thisHref = location.origin + '/mobile/#/zh-cn/liveDetail?liveId=' + this.orderInfo.itemId + "&platformId=" + localStorage.getItem('platformId')+`&bizId=${this.orderInfo.liveInfo.bizId}&domainType=1`;
        } else {
            thisHref = location.origin  + '/mobile/#/ieventLive?liveId=' + this.orderInfo.itemId + "&platformId=" + localStorage.getItem('platformId')+`&bizId=${this.orderInfo.liveInfo.bizId}&domainType=1`;
        }
      } else if(this.itemType == this.TYPE[9]) {
        // 系列直播
        thisHref = location.origin + '/mobile/#/zh-cn/seriesLive/' + this.orderInfo.itemId + '?platformId=' + localStorage.getItem('platformId')+`&bizId=${this.orderInfo.liveInfo.bizId}&domainType=1`;
      }
      window.location.href = thisHref;
    },
     priceFixed(value){
      return parseFloat(value).toFixed(2)
    }
  }
}
</script>

<style lang="scss">
$primaryColor: var(--color-primary);
.myOrder-detail-container {
  .goods-info {
    padding: 10px;
    .goods-info-img {
      width: 150px;
      height: 90px;
      border-radius: 5px;
      border: 1px solid #F7F7F7;
      float: left;
    }
    .goods-info-content {
      margin-left: 160px;
      .goods-info-title {
        font-size: 16px;
        line-height: 22px;
        color: #222;
      }
    }
  }
  .order-info {
    border-top: 15px solid #F7F7F7;
    height: calc(100% - 110px);
    .order-info-title {
      padding: 15px;
      font-size: 16px;
      color: #666;
      border-bottom: 1px solid #f7f7f7;
    }
    .order-info-content {
      padding: 15px;
      padding-bottom: 60px;
      height: calc(100% - 50px);
      overflow: auto;

      li {
        margin-bottom: 15px;
        line-height: 18px;
        span {
          font-weight: 400;
          color: #222;
          float: left;
        }
        div {
          margin-left: 80px;
          color: #666;
        }
      }
    }
  }
  .footer-pay {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    .cube-btn {
      background: $primaryColor;
    }
  }
}
</style>

