<template>
  <div class="myInquiryDetail">
    <div class="myInquiryDetail-top">
        <div class="inquiryItem-top">
           <div class="status">{{inquiryFormDetail.status==2?'已处理':'待处理'}}</div>
           <div class="time">询价时间：{{handleTime(inquiryFormDetail.inquiryTime)}}</div>
        </div>
        <div class="inquiryItem-bottom">
            <div class="bottom-left">
                <img v-if='inquiryFormDetail.productId!==null&&inquiryFormDetail.productId!==""' :src="inquiryFormDetail.picUrl?inquiryFormDetail.picUrl:picUrl" alt="">
                <img v-else :src="inquiryFormDetail.logoUrl?inquiryFormDetail.logoUrl:busUrl" alt="">
            </div>
            <div class="bottom-right">
                <div class="picName">
                    <span class="from" v-if='inquiryFormDetail.productId!==null&&inquiryFormDetail.productId!==""'>{{inquiryFormDetail.types}}</span>
                    <span class="picName-text" v-if='inquiryFormDetail.productId!==null&&inquiryFormDetail.productId!==""'>{{inquiryFormDetail.productName}}</span>
                    <span class="picName-text" v-else>{{inquiryFormDetail.busName}}</span>
                </div>
                <div class="picInfo" v-if='inquiryFormDetail.productId!==null&&inquiryFormDetail.productId!=""'>
                    <span>货号：{{inquiryFormDetail.numbers}}</span>
                    <span>规格：{{inquiryFormDetail.specsName}}</span>
                    <span>品牌：{{inquiryFormDetail.brandName}}</span>
                </div>
            </div>
        </div>
    </div>
    <div class="myInquiryDetail-bottom">
        <div class="company">
            <div class="company-left">
                <img :src="inquiryFormDetail.logoUrl?inquiryFormDetail.logoUrl:busUrl" alt="">
            </div>
            <div class="company-right">
                <span style="color:#333;font-size:14px">{{inquiryFormDetail.shopName}}</span>
                <span class="company-name">公司名：{{inquiryFormDetail.busName}}</span>
                <span class="audit">
                    <img style="width:10px;height:11px" src="../../assets/img/businessLicense.png" alt="">
                    {{inquiryFormDetail.auditStatus==2?'营业执照已审核':'营业执照未审核'}}
                </span>
            </div>
        </div>
        <div class="userNeed">
          <span><span style="color:#666">询价需求:</span> <span class="demandName" style="color:#333;margin-left:6px"> {{inquiryFormDetail.demandName}}</span></span>
          <span style="display:block;margin-top:12px"><span style="color:#666">沟通方式: </span>
          <span class="content">
            <i  :class="inquiryFormDetail.expectedContactType==1?'cubeic-phone':'cubeic-email'"></i>
            {{inquiryFormDetail.expectedContactName}}</span>
          </span>
        </div>
        <div class="message">
            <span class="message-title" style="color:#666">我的留言： </span>
            <span class="message-content">{{inquiryFormDetail.messages}}</span>
        </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "myInquiryDetail",
  data() {
    return {
     inquiryFormDetail:{},
     busUrl:'https://oss.ienmore.com/resources/public/img/exhibitor/inquiry/%E5%85%AC%E5%8F%B8%E9%BB%98%E8%AE%A4%E5%9B%BE.png',
     picUrl:'https://oss.ienmore.com/resources/public/img/exhibitor/inquiry/%E4%BA%A7%E5%93%81%E9%BB%98%E8%AE%A4%E5%9B%BE.png'
    }
  },
  mounted(){
   this.inquiryFormDetail=JSON.parse(sessionStorage.getItem('myInquiryDetail'))
  },
  computed:{
 
  },
  methods:{
  // 处理时间
    handleTime(val){
        return moment(val).format('yyyy-MM-DD HH:mm')
    },
  },
}
</script>
<style scoped lang='scss'>
.myInquiryDetail{
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    .myInquiryDetail-top{
        width: 100%;
        height: 252px;
        background: url('../../assets/img/inquiryBg.png');
        background-repeat: no-repeat;
        padding-left: 15px;
        .inquiryItem-top{
            height: 88px;
            padding-top: 22px;
            .status{
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 18px;
                color: #FFFFFF;
                line-height: 25px;
            }
            .time{
                margin-top: 9px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #FFFFFF;
                line-height: 16px;
            }

        }
         .inquiryItem-bottom{
            // width: 100%;
            // height: 138px;
            padding-top: 15px;
            display: flex;
            padding-bottom: 15px;
            background: #fff;
            border-radius: 4px;
            width: 345px;
            .bottom-left{
               min-width: 70px;
                height: 100%;
                padding-left: 10px;
                img{
                    width: 60px;
                    height: 60px;
                }

            }
            .bottom-right{
                padding-left: 15px;
                padding-right: 23px;
                .picName{
                    display: flex;
                    line-height: 20px;
                    .from{
                        display: inline-block;
                        min-width: 36px;
                        max-width: 36px;
                        height: 20px;
                        background: #31B3FE;
                        border-radius: 2px;
                        line-height: 20px;
                        color: #fff;
                        font-size: 12px;
                        text-align: center;
                        margin-right: 6px;
                    }
                    .picName-text{
                    width: 200px;
                    height: 40px;
                    overflow:hidden;
                    text-overflow: ellipsis;
                    -webkit-line-clamp: 2;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    color: #333;
                    font-size: 14px;
                    line-height: 20px;
                    }

                }
                .picInfo{
                    margin-top: 6px;
                    span{
                        display: block;
                        margin-bottom: 6px;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 12px;
                        color: #666666;
                         width: 265px;
                         height: 12px;
                        overflow:hidden;
                        text-overflow: ellipsis;
                        -webkit-line-clamp: 1;
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                    }
                }
                .shopInfo{
                    display: flex;
                     img{
                            width: 20px;
                            height: 20px;
                            display: inline-block;
                            vertical-align: middle;
                        }
                    .shopName{
                        display: inline-block;
                         width: 140px;
                         height: 20px;
                        overflow:hidden;
                        text-overflow: ellipsis;
                        -webkit-line-clamp: 1;
                        display: -webkit-inline-box;
                        -webkit-box-orient: vertical;
                    }
                }
            }
        }
    }
    .myInquiryDetail-bottom{
        width: 100%;
        height: 100%;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        background: #F5F6FA;
        margin-top: -10px;
        padding-top: 10px;
        padding-left: 15px;
        .company{
            width: 345px;
            padding: 12px 10px;
            background: #fff;
            .company-left{
                width: 80px;
                height: 80px;
                background: #FFFFFF;
                border-radius: 2px;
                border: 1px solid #F4F4F4;
                display: inline-block;
                vertical-align: top;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
            .company-right{
                display: inline-block;
                height: 80px;
                width: 240px;
                padding-left: 10px;
                span{
                    display: block;
                    margin-bottom: 6px;
                }
                .company-name{
                    // width: 200px;
                    height: 40px;
                    display: block;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 12px;
                    color: #666666;
                }
                .audit{
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 12px;
                    color: #666666;
                }

            }
        }
        .userNeed{
            width: 345px;
            background: #FFFFFF;
            border-radius: 4px;
            margin-top: 12px;
            padding: 12px 10px;
            font-size: 13px;
            .content{
                // color: var(--color-primary);
                font-size: 13px;
                color: #097DB4;
                margin-left: 6px;
                i{
                    display: inline-block;
                    width: 12px;
                    height: 12px;
                    vertical-align: sub;
                    margin-right: 6px;
                }
            }
            .demandName{
                display: inline-block;
                width: 80%;
                vertical-align: top;
            }
        }
        .message{
            margin-top: 12px;
            width: 345px;
            min-height: 163px;
            background: #FFFFFF;
            border-radius: 4px;
            padding: 12px 10px;
            font-size: 13px;
            display: flex;
            .message-title{
                display: inline-block;
                width: 65px;
                padding-top: 4px;
            }
            .message-content{
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 13px;
                color: #333333;
                line-height: 20px;
                text-align: justify;
                font-style: normal;
                width: 252px;
                display: inline-block;
                vertical-align: top;
            }
        }
    }
}
</style>
