<template>
    <div class="discount-wrap">
        <scroll ref="betterScroll" class="discount-scroll" :data="discountList" :pullup="pullup" @scrollToEnd="scrollToEnd">
            <div>
                <div v-if='discountList.length'  class="discount-scroll-item flex-left" v-for="item,index in discountList" :key="index" :class="item.sysCode == 'system' ? 'hei_body_class':''" :style="{'background': `url(${discountCodeBgc(item.useStatus,item.sysCode)})`,'border':item.useStatus==3?'0px solid #eee':''}">
                    <div class="discount-scroll-item-left unit">
                        <div :class="item.sysCode == 'system' ? 'hei_body_class content_left_item':''" class="content-left flex-vertical">
                            <div class="content-left-top unit-0 flex-middle">
                                <span>优惠码 {{ item.code }}</span>
                                <img src="../../assets/img/copy-icon.png" alt="" class="copy-icon" @click="copyCode(item.code)">
                            </div>
                            <div v-if="item.sysCode == 'info'" class="content-left-middle flex-left unit">
                                <img :src="item.itemBannerImg" alt="" @click="useDiscountCode(item.url)">
                                <div class="content-left-middle-text">
                                    <div class="text-title">适用范围：{{item.moduleTypeText}}（{{item.usedTimes}}/{{item.useTimesText}}）</div>
                                    <div class="text-sub-title">{{ item.itemName }}</div>
                                </div>
                            </div>
                            <div v-else>
                                <div class="text-title">适用范围：{{item.moduleTypeText}}</div>
                            </div>
                            <div class="content-left-bottom flex-middle unit-0">
                                <span class="effective-time">有效期：{{item.beginTime.replace(/-/g,'.')}}-{{ item.endTime.replace(/-/g,'.') }}</span>
                                <span class="aboutTo" v-if="expireDiffTime(item.endTime) && item.useStatus==1">即将到期</span>
                            </div>
                        </div>
                    </div>
                    <div class="discount-scroll-item-right unit-0" :class="item.sysCode == 'system' ? 'hei_body_class':''">
                        <div :class="item.sysCode == 'system' ? 'hei_body_class':''" class="content-right flex-middle flex-center flex-vertical">
                            <div :style="(item.discountRatioType=='ratio' && item.discountPrice == 0 && item.sysCode == 'system') ? 'font-size: 16px;margin-bottom: 6px;':''" :class="{'content-right-price':item.useStatus==1,'not-use':item.useStatus!=1}">{{item.discountRatioType=='price'?'¥'+item.discountPrice:(item.discountRatioType=='ratio' && item.discountPrice == 0 && item.sysCode == 'system') ? "免单":(item.discountPrice/10).toFixed(2) + '折'}}</div>
                            <div :style="item.useStatus==1 ? 'margin-bottom: 0px;background: rgba(255,107,0,0.1);color: #FF6B00;':''" v-if="item.sysCode == 'system'" v-show="item.discountRatioType != 'ratio' || item.discountPrice != 0" :class="{'content-right-desc':item.useStatus==1,'content-right-desc-no':item.useStatus!=1}">
                                <span>{{item.discountRatioType == 'price'?'立减':'立享'}}</span>
                            </div>
                            <div v-else :class="{'content-right-desc':item.useStatus==1,'content-right-desc-no':item.useStatus!=1}" style="margin-bottom: 0px;">
                                <span>{{item.discountRatioType == 'price'?'立减':'立享'}}</span>
                            </div>
                            <div style="margin-top: 6px;" :style="(item.sysCode == 'system' && item.useStatus==1) ? 'background: linear-gradient( 90deg, #FFA400 0%, #FF6B00 100%);':''" :class="{'content-right-btn':item.useStatus==1,'content-right-btn-disable':item.useStatus!=1}" @click.stop="useDiscountCode(item.url,item.sysCode)">
                                {{ item.useStatusText.replace('可','') }}
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="!discountList.length" class="no-data-tips">
                    <img src="@/assets/img/no_discount.png" alt="">
                    <div>暂无优惠码</div>
                </div>
            </div>
        </scroll>
    </div>
</template>
<script>
import scroll from '@/components/common/scroll';
import { getMyDiscount } from '@/api/userCenter';
export default {
    data () {
        return {
            discountList: [],
            effectiveBgc: require('@/assets/img/effective.png'),
            expireBgc: require('@/assets/img/yiGuoQiIcon.png'),
            alreadyUse: require('@/assets/img/yiUserIcon.png'),
            unarrivedTime: require('@/assets/img/weiDateIcon.png'),
            pingcanuse: require('@/assets/img/pingLiIcon.png'),
            pingExpired: require('@/assets/img/pingExpired.png'),
            useralready: require('@/assets/img/useralready.png'),
            notdateicon: require('@/assets/img/notdateicon.png'),
            pageData: {
                pageSize: 10,
                pageNum: 1,
                total: 0,
                platformId: this.$route.query.platformId || localStorage.getItem('platformId')
            },
            pullup: true,
            selectEl: null
        }
    },
    components: {
        scroll
    },
    mounted () {
        this.getMyDiscount();
    },
    computed: {

    },
    methods: {
        async getMyDiscount () {
            let res = await getMyDiscount(this.pageData);
            console.log('我的优惠码', res);
            if (res.code == '001') {
                this.discountList = this.discountList.concat(res.data.list) || [];
                this.pageData.total = res.data.total;
            } else {
               // this.$createToast({ txt: res.info, type: 'txt' }).show();
               this.$toast(res.info);
            }
        },
        scrollToEnd () {
            console.log('加载更多数据');
            if (this.discountList.length < this.pageData.total) {
                this.pageData.pageNum++;
                this.getMyDiscount();
            }
        },
        // 复制优惠码
        copyCode (code) {
            const input = document.createElement('input')
            document.body.appendChild(input)
            input.setAttribute('readonly', 'readonly')
            input.setAttribute('value', code)
            input.select()
            input.setSelectionRange(0, code.length)
            try {
                document.execCommand('copy')
            } catch (err) { }
            document.body.removeChild(input)
            this.$createToast({ txt: '复制成功', type: 'txt' }).show();
        },
        expireDiffTime (time) {
            let endTime = new Date(time).getTime() + 24 * 60 * 60 * 1000;
            let nowTime = Date.now();
            if (endTime - nowTime < 259200000 && endTime - nowTime > 0) { // 小于3天
                return true;
            } else {
                return false;
            }
        },
        discountCodeBgc (status,sysCode) {
            if (sysCode == "system") {
                let bgcName;
                switch (status) {
                    case 1:
                        bgcName = this.pingcanuse;
                        break;
                    case 2:
                        bgcName = this.notdateicon;
                        break;
                    case 3:
                        bgcName = this.useralready;
                        break;
                    case 4:
                        bgcName = this.pingExpired;
                        break;
                    default:
                        break;
                }
                return bgcName;
            } else {
                let bgcName;
                switch (status) {
                    case 1:
                        bgcName = this.effectiveBgc;
                        break;
                    case 2:
                        bgcName = this.unarrivedTime;
                        break;
                    case 3:
                        bgcName = this.alreadyUse;
                        break;
                    case 4:
                        bgcName = this.expireBgc;
                        break;
                }

                return bgcName;
            }
            
        },
        useDiscountCode (url,sysCode) {
            if (sysCode == "system") {
                this.$router.push({
                    path: "home/11/0/0",
                })
            } else {
                // 去跳转直播详情
                window.location.href = url;
            }
        }
    },
}
</script>
<style scoped lang="scss">
.discount-wrap {
    width: 100%;
    height: 100%;
    padding: 15px;
    box-sizing: border-box;
    background-color: #FFFFFF;
    .discount-scroll {
        height: 100%;
        overflow: hidden;
        .discount-scroll-item {
            width: 100%;
            height: 130px;
            // border: 1px solid rgba(255, 77, 79, 0.15);
            border-radius: 5px;
            margin-bottom: 12px;
            // background: url(../../assets/img/effective.png) ;
            background-size: 100% 100% !important;
            .discount-scroll-item-left {
                .content-left {
                    height: 130px;
                    // background: #fff;
                    padding: 16px 12px 14px 12px;
                    box-sizing: border-box;
                    .text-title {
                        color: #666666;
                        font-size: 12px;
                    }
                    .content-left-top {
                        font-size: 14px;
                        font-family: PingFangSC-Medium, PingFang SC;
                        font-weight: 500;
                        color: #333333;
                        line-height: 16px;
                        height: 16px;
                        margin-bottom: 13px;
                        i {
                            font-size: 12px;
                            color: #333;
                            margin-left: 8px;
                        }
                        img {
                            width: 12px;
                            height: 12px;
                            margin-left: 7px;
                        }
                    }
                    .content-left-middle {
                        // border-bottom: 1px dashed #f2f2f2;
                        img {
                            width: 48px;
                            height: 32px;
                            border-radius: 1px;
                            margin-right: 10px;
                        }
                        .content-left-middle-text {
                            font-size: 12px;
                            font-family: PingFangSC-Regular, PingFang SC;
                            font-weight: 400;
                            line-height: 14px;
                            .text-title {
                                color: #666666 !important;
                                margin-bottom: 6px;
                                margin-bottom: 6px;
                            }
                            .text-sub-title {
                                color: #666666;
                                width: 186px;
                                white-space: nowrap;
                                text-overflow: ellipsis;
                                overflow: hidden;
                            }
                        }
                    }
                    .content-left-bottom {
                        // height: 38px;
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        .effective-time {
                            color: #333333;
                        }
                        .aboutTo {
                            color: #ff4d4f;
                            margin-left: 6px;
                        }
                    }
                }
            }
            .discount-scroll-item-right {
                width: 80px;
                height: 130px;
                .content-right {
                    width: 100%;
                    height: 130px;
                    .content-right-price {
                        font-size: 18px;
                        font-family: PingFangSC-Medium, PingFang SC;
                        font-weight: 500;
                        color: #ff4d4f;
                    }
                    .not-use {
                        font-size: 18px;
                        font-family: PingFangSC-Medium, PingFang SC;
                        font-weight: 500;
                        color: #cccccc;
                    }
                    .content-right-desc {
                        width: 44px;
                        height: 14px;
                        background: rgba(255, 77, 79, 0.1);
                        border-radius: 7px;
                        font-size: 10px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #ff4d4f;
                        line-height: 14px;
                        text-align: center;
                        margin-bottom: 7px;
                        margin-top: 2px;
                        span {
                            transform: scale(0.9);
                            display: inline-block;
                        }
                    }
                    .content-right-desc-no {
                        width: 44px;
                        height: 14px;
                        background: #eee;
                        border-radius: 7px;
                        font-size: 10px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #ccc;
                        line-height: 14px;
                        text-align: center;
                        // margin-bottom: 7px;
                        margin-top: 2px;
                        span {
                            transform: scale(0.9);
                            display: inline-block;
                        }
                    }
                    .content-right-btn {
                        width: 56px;
                        height: 26px;
                        background: linear-gradient(
                            90deg,
                            #ff8487 0%,
                            #ff4d4f 100%
                        );
                        border-radius: 2px;
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #ffffff;
                        text-align: center;
                        line-height: 26px;
                    }
                    .content-right-btn-disable {
                        width: 56px;
                        height: 26px;
                        background: #eee;
                        border-radius: 2px;
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #999999;
                        text-align: center;
                        line-height: 26px;
                    }
                }
            }
        }
        .no-data-tips {
            > img {
                width: 180px;
                height: 143px;
            }
            > div {
                margin-top: 15px;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #666666;
            }
            margin-top: 130px;
            text-align: center;
            color: #999;
            display: flex;
            flex-direction: column;
            align-items: center;
            flex-wrap: wrap;
        }
    }
}
.hei_body_class {
    height: 94px !important;
}
.content_left_item {
    justify-content: space-between;
    padding: 24px 12px 14px 12px !important;
    .content-left-top {
        margin-bottom: 0px !important;
        margin-top: 4px;
    }
    .content-left-bottom {
        height: fit-content !important;
    }
}
</style>
