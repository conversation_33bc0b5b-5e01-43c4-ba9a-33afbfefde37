<template>
    <div class="myCollect-container">
        <tabList needList="tab" class="navigate-bar" @changeItem="changeTab" v-show="true"></tabList>
        <div v-show="tags.length>0" class="tag-list">
            <span v-for="item in tags" :class="computedActive(item)" @click="tagItemClick(item)">
                {{item.name}}
            </span>
        </div>
        <div class="scroll-list">
            <cube-scroll ref="scroll" :data="collectList" :options="options" @pulling-up="getMyCollectListFn">
                <div v-if="collectList.length==0" class="no-data-tips">
                    <img src="@/assets/img/no_data_icon.png" alt="">
                    <div>暂无收藏</div>
                </div>
                <infoItemSearchZly v-if="systemStyle == 'zly'" v-for="(item,index) in collectList" :key='index' :info='item' :iflast='index == collectList.length-1' :showTag='true' :closeExhibitor="closeExhibitor"></infoItemSearchZly>
                <infoItem v-if="systemStyle == null" v-for="(item,index) in collectList" :key='index' :info='item' :showTag='true' :iflast='index == collectList.length-1'></infoItem>
            </cube-scroll>
        </div>
    </div>
</template>
<script>
import tabList from './components/tabList';
import { getInfoByDynamicUrl } from '@/api/configurable/common'
import moment from 'moment'
import infoItem from '@/components/newCommon/infoItem.vue'
import infoItemSearchZly from '@/components/newCommon/infoItemSearchZly.vue'


export default {
    name: "myCollect",
    components: { tabList, infoItem, infoItemSearchZly },
    data () {
        return {
            tabActive: {}, // tab选中项
            options: { // 上拉加载设置
                pullUpLoad: {
                    threshold: 30,
                    visible: false,
                    txt: {
                        more: '上拉加载更多',
                        noMore: '没有更多信息啦！'
                    }
                }
            },
            collectList: [], // 活动列表
            currentPage: 0, // 当前页码
            total: 0, // 数据总条数
            tags: [],
            activeItem: null,
            closeExhibitor: true,
        }
    },
    created () {
        document.title = "我的收藏"
    },
    methods: {
        moment,
        // 返回个人中心
        goPersonal () {
            this.$router.replace({ name: 'userCenter', query: { platformId: localStorage.getItem('platformId') } });
        },
        // 切换 tab 选中项 重置 加载参数
        changeTab (tab) {
            console.log(tab);
            this.closeExhibitor = !(tab.name == "产品与服务")
            this.tags = tab.children
            if (this.tags.length > 0) {
                this.activeItem = this.tags[0]
            } else {
                this.activeItem = null
            }
            this.tabActive = tab;
            this.currentPage = 0;
            this.collectList = [];
            this.total = 0;
            this.$refs.scroll.scrollTo(0, 0);
            this.getMyCollectListFn();
        },
        // 获取列表
        getMyCollectListFn () {
            let params = {};
            this.tabActive.menuParams.forEach(item => {
                params[item.menuParamKey] = item.menuParamValue
            })
            if (this.activeItem) {
                this.activeItem.menuParams.forEach(item => {
                    params[item.menuParamKey] = item.menuParamValue
                })
            }
            params.pageNum = this.currentPage + 1;
            getInfoByDynamicUrl(this.tabActive.apiUrl, params).then(res => {
                if (res.code == this.$successCode) {
                    if (res.data) {
                        this.collectList = params.pageNum==1?res.data.list:this.collectList.concat(res.data.list);
                        this.total = res.data.total
                        this.currentPage++
                    }
                    // 已加载完毕  没有更多信息啦！
                    if (this.total == this.collectList.length) {
                        this.$refs.scroll.forceUpdate();
                    }
                    this.collectList.forEach((item, index) => {
                        this.collectList[index].id = item.itemId
                        this.collectList[index].type = item.itemType
                        this.collectList[index].conferenceType = item.itemTag
                    })
                }
            })
        },
        // 详情
        toDetails (item) {
            this.naviToDetails(this.$route.query.platformId, item.itemType, item)
        },
        computedActive (item) {
            if (this.activeItem.id == item.id) {
                return "active"
            } else {
                return ""
            }
        },
        tagItemClick (item) {
            this.activeItem = item
            this.currentPage = 0;
            this.collectList = [];
            this.total = 0;
            this.$refs.scroll.scrollTo(0, 0);
            this.getMyCollectListFn();
        }
    }
}
</script>

<style lang="scss" scoped>
$primaryColor: #ff620d;
.myCollect-container {
    background: #fff;
    .navigate-bar {
        font-size: 15px;
        font-weight: 400;
        color: #333;
        .cubeic-arrow {
            float: left;
            transform: rotateY(180deg);
            color: #999;
        }
    }
    .scroll-list {
        height: calc(100% - 50px);
        .no-data-tips {
            > img {
                width: 185px;
                height: 142px;
            }
            > div {
                margin-top: 20px;
            }
            margin-top: 170px;
            text-align: center;
            color: #999;
            display: flex;
            flex-direction: column;
            align-items: center;
            flex-wrap: wrap;
        }
        .collect-item {
            margin-bottom: 10px;
            line-height: 20px;
            width: 175px;
            height: 180px;
            background: #fff;
            border-radius: 4px;
            display: inline-block;
            margin-right: 5px;
            .collect-item-img {
                width: 175px;
                height: 105px;
                border-radius: 4px;
                margin-bottom: 5px;
            }
            .collect-item-title {
                line-height: 17px;
                color: #333;
                font-size: 12px;
                font-weight: 400;
                margin-bottom: 8px;
                padding: 0 5px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                word-break: break-word;
            }
            .collect-item-info {
                color: #999;
                font-size: 10px;
                padding: 0 5px;
                line-height: 20px;
                p {
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    svg {
                        vertical-align: middle;
                        margin-top: -3px;
                        margin-right: 5px;
                    }
                }
            }
        }
    }
}
.tag-list {
    border-bottom: 0.5px solid #e5e5e5;
    span {
        margin: 10px 0;
        margin-left: 15px;
        display: inline-block;
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 20px;
    }
    .active {
        color: var(--color-primary) !important;
    }
}
</style>

