/** 订单列表页面 主要包括 地址选择 商品信息 订单信息 订单备注 支付 */
<template>
  <div class="body">
    <div class="body_top">
      <div class="top_dizhi_body" v-if="getProductInfoInfo.productType == '实物商品'" @click="showBtn">
        <img src="@/assets/img/dizhiIcon.png" />
        <div class="top_dizhi_text">
          <div class="text_left top_dizhi_text_body" :style="!formDate.id ? 'align-items: center;' : ''">
            <img src="@/assets/img/dizhi_icon.png" />
            <p v-if="!formDate.id">添加收货地址</p>
            <div v-if="formDate.id" class="dizhi_name">
              <div class="dizhi_name_item">
                <p class="dizhi_name_left">{{ formDate.name }}</p>
                <p style="margin-left: 15px">{{ formDate.mobile }}</p>
              </div>
              <div class="dizhi_p">
                <span>{{ formDate.region.replace(/\//g, "-") }}-{{ formDate.address }}</span>
              </div>
            </div>
          </div>
          <div class="text_left tranform_class">
            <i class="cubeic-select"></i>
          </div>
        </div>
      </div>
      <div class="top_box_item">
        <img class="tit_img" :src="logo" />
        <div class="top_title">
          <div class="top_title_item">
            <img v-if="getProductInfoInfo.productType == '虚拟商品'" src="@/assets/img/xu_icon.png" />
            <img v-if="getProductInfoInfo.productType == '实物商品'" src="@/assets/img/shi_icon.png" />
            <span>{{ getProductInfoInfo.productName }}</span>
          </div>
          <div class="bottom_title_item">
            <p class="red_color">{{ priceNumber }}易币</p>
            <div class="cao_box">
              <p class="sheng_color" v-if="getProductInfoInfo.leftCount <= 5">剩余{{ getProductInfoInfo.leftCount }}份</p>
              <p class="text_num_one">数量</p>
              <img class="jian_class" v-if="number > 1" src="@/assets/img/lanJian.png" @click="jianBtn" />
              <img class="jian_class" v-else src="@/assets/img/huiJian.png" @click="jianBtn" />
              <p class="num_class">{{ number }}</p>
              <img
                class="jian_class"
                v-if="(getProductInfoInfo.unLimitBuyFlag && getProductInfoInfo.maxBuyCount <= number) || getProductInfoInfo.leftCount <= number"
                src="@/assets/img/huiJia.png"
                @click="addNumber"
              />
              <img class="jian_class" v-else src="@/assets/img/lanJia.png" @click="addNumber" />
            </div>
          </div>
        </div>
      </div>
      <div class="zhi_body">
        <div class="zhi_top">
          <p class="text_left">需支付易币</p>
          <p class="text_right">{{ priceNumber * number }}易币</p>
        </div>
        <div class="border_body"></div>
        <div class="zhi_bot">
          <span style="margin-right: 10px">共{{ number }}件</span>
          <span
            >合计：<span class="red_color">{{ priceNumber * number }}易币</span></span
          >
        </div>
      </div>
      <!-- 购买信息 -->
      <div class="remark_body" v-if="getProductInfoInfo.subProductType === 'custom'">
        <div class="remark_title">购买信息</div>
        <div class="buy-info-text" :class="{ 'buy-info-text-label': getProductInfoInfo.prompt.requiredFlag == 1 }">{{ getProductInfoInfo.prompt.label }}</div>
        <input v-if="editShow" v-model="formDate.buyValue" :placeholder="getProductInfoInfo.prompt.placeHolder" class="textrea_class_buy"></input>
      </div>
      <!-- 订单备注 -->
      <div class="remark_body">
        <div class="remark_title">订单备注</div>
        <div class="remark_info_text">备注信息</div>
        <textarea v-if="editShow" v-model="formDate.remark" placeholder="请输入" class="textrea_class form_inp_item input_class"></textarea>
      </div>
    </div>
    <div class="bot_btn_body">
      <div>
        <span class="text_left">总计：</span>
        <span class="red_color">{{ priceNumber * number }}易币</span>
      </div>
      <div class="qu_zhi_btn" @click="goToPay">去支付</div>
    </div>
  </div>
</template>

<script>
import { getOrderAddress } from "@/api/monetary";
import Region from "./components/region";
import { mapActions } from "vuex";
import { exchange } from "@/api/monetary";
import mallRecomeDia from "@/components/common/mallRecomeDia";
export default {
  data() {
    return {
      addAgressShow: false, // 添加地址状态
      logo: "",
      formDate: {
        region: "",
        buyValue: null, // 购买信息的value
      },
      editShow: true, // 编辑状态
      priceNumber: null,
      number: 1, // 数量
      name: "",
    };
  },
  computed: {
    addressForm() {
      return this.$store.state.addressForm;
    },
    getProductInfoInfo() {
      return this.$store.state.getProductInfoInfo;
    },
    isPhysicalProduct() {
      return this.getProductInfoInfo.productType === "实物商品";
    },
  },
  watch: {
    formDate: {
      handler(val) {
        this.$store.commit("adressFunForm", val);
      },
    },
    addressForm: {
      handler(val) {
        this.selectEmit(val);
      },
    },
    getProductInfoInfo: {
      handler(val) {
        this.logo = Array.isArray(val.logo) ? val.logo[0] : val.logo;
        this.priceNumber = val.productPriceCoin;
        this.formDate.id = val.id;
        if (this.isPhysicalProduct) {
          this.getOrderAddress();
        }
        this.$forceUpdate();
      },
    },
  },
  components: {
    mallRecomeDia,
    Region,
  },
  mounted() {
    this.$root.$on("changeRegion", ({ regionCode, region }) => {
      this.$set(this.formDate, "region", region);
      this.$set(this.formDate, "regionCode", regionCode.join(","));
    });
    this.getProductInfoDetail(this.$route.params.id);
  },
  methods: {
    showBtn() {
      this.$store.commit("adressFuntion", true);
    },
    // 筛选地址事件
    selectEmit(val) {
      this.formDate = val;
      this.$store.commit("adressFuntion", false);
    },
    getOrderAddress() {
      getOrderAddress().then((res) => {
        this.formDate = res.data;
        this.formDate.id = res.data.addressId;
        this.$set(this.formDate, "region", res.data.region);
        this.$set(this.formDate, "regionCode", res.data.regionCode);
      });
    },
    ...mapActions(["getProductInfoDetail"]),
    // 保存收货地址
    saveBtn() {
      if (!this.checkForm()) return;
      this.editShow = false;
    },
    // 编辑操作事件
    editBtn(val) {
      this.editShow = true;
      this.$set(this.formDate, "region", val.region);
    },
    // 去支付
    async goToPay() {
      // 检查实物商品是否填写地址
      if (this.isPhysicalProduct && !this.formDate.id) {
        this.$toast("请先填写并保存相关信息");
        return;
      }

      // 检查自定义商品是否填写信息
      if (this.getProductInfoInfo.subProductType === "custom" && this.getProductInfoInfo.prompt.requiredFlag === 1 && !this.formDate.buyValue) {
        this.$toast("请填写购买信息");
        return;
      }

      // 拼接购买信息
      const getBuyInfo = () => {
       
        if (this.getProductInfoInfo.subProductType === 'custom') {
          return {
            buyLabel: this.getProductInfoInfo.prompt.label,
            buyValue: this.formDate.buyValue
          };
        }

        return {
          buyLabel: null,
          buyValue: null
        };
      };

      const baseParams = {
        productId: Number(this.$route.params.id),
        cnt: this.number, // 商品数量
        remark: this.formDate.remark, // 订单备注
        addressId: this.formDate.id, // 地址ID,
        ...getBuyInfo()
      };

      // 根据商品类型添加额外参数
      const params = this.isPhysicalProduct ? { ...baseParams, ...this.formDate } : baseParams;

      // 提交订单
      try {
        const res = await exchange(params);

        if (res.code === "001") {
          // 订单提交成功,跳转确认页
          await this.navigateToConfirm();
        } else {
          this.$toast(res.info);
        }
      } catch (error) {
        this.$toast("提交订单失败,请重试");
      }
    },
    // 跳转确认页
    navigateToConfirm() {
      this.$router.replace({
        path: "/mall/orderConfirm",
        query: {
          type: this.getProductInfoInfo.productType,
          auditFlag: this.getProductInfoInfo.auditFlag,
        },
      });
    },
    // 减数量
    jianBtn() {
      if (this.number > 1) {
        this.number--;
      }
    },
    // 加数量
    addNumber() {
      if (this.getProductInfoInfo.unLimitBuyFlag && this.getProductInfoInfo.maxBuyCount <= this.number) return this.$toast(`此商品限购${this.getProductInfoInfo.maxBuyCount}件`);
      if (this.getProductInfoInfo.leftCount <= this.number) return this.$toast(`库存不足`);
      this.number++;
    },
  },
  beforeDestroy() {
    this.$store.commit("adressFuntion", false);
  },
};
</script>

<style lang="scss" scoped>
.body_top {
  padding: 15px 12px;
  // min-height: 100vh;
  background-color: #f5f6fa;
}
.top_box_item {
  padding: 12px;
  background-color: #ffffff;
  display: flex;
  height: 98px;
  border-radius: 4px;
  img {
    max-width: 100%;
    max-height: 100%;
  }
  .tit_img {
    border-radius: 4px;
    width: 74px;
    height: 74px;
    object-fit: contain;
  }
}
.top_title {
  margin-left: 12px;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .top_title_item {
    line-height: 20px;
    white-space: normal; /* 确保文本可以折行 */
    overflow: hidden; /* 隐藏超出的内容 */
    display: -webkit-box; /* 使用弹性盒子模型 */
    -webkit-line-clamp: 2; /* 限制显示的行数为2行 */
    -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
    line-height: 20px; /* 行高 */
    text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
    img {
      width: 56px;
      height: 16px;
      display: inline;
      margin-right: 5px;
      margin-bottom: -3px;
    }
    span {
      line-height: 20px;
      color: #333333;
      font-size: 14px;
      font-weight: 500;
    }
  }
  .bottom_title_item {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
    .cao_box {
      display: flex;
      align-items: center;
      .text_num_one {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        text-align: left;
        font-style: normal;
        margin-right: 8px;
      }
      .jian_class {
        width: 16px;
        height: 16px;
        margin-right: 8px;
        // margin-bottom: 2px;
      }
      .num_class {
        margin-right: 8px;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        text-align: left;
        font-style: normal;
      }
      #blue_border {
        border: 1px solid #097db4;
        color: #097db4;
      }
      .hui_color {
        color: #999999 !important;
        border: 1px solid #999999 !important;
      }
      .sheng_color {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        text-align: left;
        font-style: normal;
        margin-right: 10px;
      }
    }
  }
}
.red_color {
  font-weight: 500;
  font-size: 14px;
  color: #ff4d4f;
  text-align: left;
  font-style: normal;
}
.zhi_body {
  background-color: #ffffff;
  padding: 0px 12px;
  height: 92px;
  margin-top: 12px;
  padding-top: 6px;
  border-radius: 4px;
  .zhi_top {
    display: flex;
    justify-content: space-between;
    height: 36px;
    align-items: center;
    margin-bottom: 6px;
    .text_left {
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      text-align: left;
      font-style: normal;
    }
    .text_right {
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      text-align: left;
      font-style: normal;
    }
  }
  .zhi_bot {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 15px;
    span {
      font-weight: 400;
      font-size: 12px;
      color: #666666;
      text-align: center;
      font-style: normal;
      display: flex;
      align-items: center;
    }
    .red_color {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #ff4d4f;
      text-align: left;
      font-style: normal;
    }
  }
}
.remark_body {
  background-color: #ffffff;
  padding: 0px 12px 20px 12px;
  margin-top: 12px;
  border-radius: 4px;
  .remark_title {
    padding: 11px 0px;
    border-bottom: 1px solid #eeeeee;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    text-align: left;
    font-style: normal;
    height: 44px;
    display: flex;
    align-items: center;
  }
  .remark_info_text {
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    text-align: left;
    font-style: normal;
    margin-top: 15px;
  }
  .buy-info-text {
    font-weight: 400;
    font-size: 14px;
    color: #333;
    line-height: 20px;
    margin-top: 15px;
  }
  .buy-info-text-label {
    &::before {
      content: "*";
      color: #ff4d4f;
      margin-right: 5px;
      font-size: 16px;
    }
  }
  .input_class {
    width: 100%;
    background: #f5f5f5;
    margin-top: 8px;
    min-height: 58px;
    padding: 12px 12px;
    border-radius: 2px;
    color: #333333;
    white-space: pre-wrap;
    pre {
      white-space: pre-wrap; /* 保留空白符序列，但是正常地进行换行 */
      word-wrap: break-word; /* 长单词或 URL 地址换行到下一行 */
      word-break: break-all;
      line-height: 20px;
    }
  }
}
.bot_btn_body {
  width: 100%;
  height: 65px;
  display: flex;
  align-items: center;
  padding: 0px 12px;
  background-color: #ffffff;
  position: absolute;
  bottom: 0px;
  justify-content: space-between;
  position: fixed;
  .qu_zhi_btn {
    width: 140px;
    height: 42px;
    background: #097db4;
    border-radius: 3px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 400;
    font-size: 16px;
    color: #ffffff;
    text-align: left;
    font-style: normal;
  }
  .text_left {
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    text-align: left;
    font-style: normal;
  }
  .red_color {
    font-size: 16px;
  }
}
.body {
  background-color: #f5f6fa;
  overflow-y: auto;
  height: 100% !important;
}
.dizhi_class {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .blue_color {
    font-weight: 400;
    font-size: 14px;
    color: #097db4;
    text-align: left;
    font-style: normal;
  }
  .shou_title {
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    text-align: left;
    font-style: normal;
    line-height: 22px;
  }
}
.show_form_body {
  .form_item_label {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    text-align: left;
    font-style: normal;
    margin-top: 20px;
    margin-bottom: 8px;
  }
  .form_item_text {
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    text-align: left;
    font-style: normal;
    line-height: 20px;
    pre {
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-all;
    }
  }
  .form_edit_item::before {
    content: "*";
    color: red;
    margin-right: 5px;
    left: 0;
    top: 0;
  }
}
.form_inp_item {
  height: 38px;
  background: #f5f5f5;
  border-radius: 2px;
  width: 100%;
  max-width: 100%;
}
.textrea_class {
  border: 0px solid;
  min-height: 58px;
  width: 100%;
  padding: 9px 12px;
  line-height: 20px;
  color: #333333;
}
.textrea_class_buy {
  border: none;
  height: 38px;
  width: 100%;
  padding: 9px 12px;
  line-height: 20px;
  color: #333333;
  box-sizing: border-box !important;
  width: 100%;
  background: #f5f5f5;
  margin-top: 8px;
  border-radius: 2px;
  color: #333333;
}
.bot_form_btn {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 20px;
  div {
    width: 96px;
    height: 36px;
    background: #097db4;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    text-align: left;
    font-style: normal;
  }
}
.mar_inpt_item {
  margin-top: 3px;
  display: block;
  margin-top: 6px;
}
.region_body {
  width: 100%;
  width: 327px;
  height: 38px;
  background: #f5f5f5;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  ::v-deep .cubeic-select {
    margin-right: 12px;
    color: #000000;
    opacity: 0.3;
  }
}
.top_dizhi_body {
  height: 80px;
  width: 100%;
  background-color: #ffffff;
  margin-bottom: 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  border-radius: 4px;
  img {
    height: 3px;
  }
  .top_dizhi_text {
    height: 80px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0px 15px;
    .text_left {
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      line-height: 20px;
      text-align: left;
      font-style: normal;
    }
  }
  .tranform_class {
    transform: rotate(270deg);
    width: 14px;
    height: 14px;
    font-size: 14px;
    .cubeic-select {
      font-size: 14px !important;
    }
  }
}
.top_dizhi_text_body {
  display: flex;
  // align-items: center;
  width: 100%;
  img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }
  .dizhi_name {
    width: 100%;
    .dizhi_name_item {
      display: flex;
      p {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
      .dizhi_name_left {
        max-width: 46%;
        white-space: normal; /* 确保文本可以折行 */
        overflow: hidden; /* 隐藏超出的内容 */
        display: -webkit-box; /* 使用弹性盒子模型 */
        -webkit-line-clamp: 1; /* 限制显示的行数为2行 */
        -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
        line-height: 20px; /* 行高 */
        text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
      }
    }
    .dizhi_p {
      white-space: normal; /* 确保文本可以折行 */
      overflow: hidden; /* 隐藏超出的内容 */
      display: -webkit-box; /* 使用弹性盒子模型 */
      -webkit-line-clamp: 1; /* 限制显示的行数为2行 */
      -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
      line-height: 20px; /* 行高 */
      text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
      margin-top: 8px;
    }
  }
}
.border_body {
  height: 1px;
  width: 100%;
  background-color: #eeeeee;
}
</style>
