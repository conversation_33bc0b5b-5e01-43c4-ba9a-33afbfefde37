<template>
  <div class="body">
    <div class="top_serarch_body">
      <cube-input v-model="keyword" placeholder="请输入关键字" @input="searchKeyWord" class="input_width" clearable>
        <template #prepend>
          <img style="width: 15px; height: 15px" src="@/assets/img/search_top_icov.png" alt="" />
        </template>
      </cube-input>
      <div class="search_bot_text flex-left flex-nowrap">
        <div class="flex-middle unit search-item-wrap">
          <div class="text_item" @click="seleBtn(item.value)" v-for="(item, index) in orderStatuList" :key="index">
            <div :class="{ 'text_item_item': expressStatus == item.value }">{{ item.label }}</div>
          </div>
        </div>
        <div class="text_item img_width unit-0">
          <img @click="shaiBtn" src="@/assets/img/screenIcon.png" alt="" />
          <cube-tip ref="dropMenu" direction="top" offsetRight="17px" class="list-drop-menu">
            <div v-for="(item, index) in typeList" :key="index" :style="item.value == productType ? 'color: #00629F;' : ''" @click="productBtn(item.value)">{{ item.label }}</div>
          </cube-tip>
        </div>
      </div>
    </div>
    <cube-scroll v-if="tableList.length != 0" class="table_scroll" ref="classifyScroll" :data="tableList" :options="options" @pulling-down="loadDownFun" @pulling-up="loadFun">
      <div v-for="(item, index) in tableList" class="box_body">
        <div class="dealit_body">
          <div class="top_title">
            <div class="top_tit_item">
              <img v-if="item.productType == '实物商品'" src="@/assets/img/shi_icon.png" alt="" />
              <img v-else src="@/assets/img/xu_icon.png" alt="" />
              <div class="ding_text">订单号：{{ item.orderCode }}</div>
            </div>
            <div v-if="item.orderStatus == 3" class="not_color">审核不通过</div>
            <div v-else-if="item.orderStatus == 1" style="color: #999999">已取消</div>
            <div class="right_text" v-else :class="item.expressStatus ? '' : 'right_text_red'">{{ item.expressStatus ? "已发货" : "未发货" }}</div>
          </div>
          <div class="border_class"></div>
          <div class="shang_item" @click="goToMallInfo(item)">
            <div class="img_body">
              <img :src="item.logo" alt="" />
            </div>
            <div class="text_right">
              <div class="text_top">{{ item.productName }}</div>
              <div class="text_bottom">
                <span>{{ item.productPriceCoin }}易币</span>
                <span class="span_text">x{{ item.num }}</span>
              </div>
            </div>
          </div>
          <!--审核不通过或取消订单-->
          <div
            class="yello_bot"
            style="background: #F5F6FA; color: #666;"
            v-if="item.orderStatus == 1 || item.orderStatus == 3">
            <div>
              <img src="@/assets/img/gray_icon.png" alt="" />
              <p>
                {{ item.orderStatus == 3 ? "审核未通过，易币将自动退回" : "订单已取消，易币将自动退回" }}
              </p>
              <span class="view" @click="viewReason(item)">查看原因</span>
            </div>
          </div>
          <!-- 虚拟商品查看卡券密码或优惠码 -->
          <div class="ka_body" v-if="item.productType == '虚拟商品' && item.orderStatus != 3 && item.orderStatus != 2" @click="seeBtn(item)">
            <div class="text" v-if='(item.type == "voucherCard" || item.type == "discountCode") && item.expressStatus == 1'>{{ item.type == "voucherCard" ? "查看卡券密码" : "查看优惠码"}}</div>
            <div class="text" v-if="item.type == 'custom'">购买信息</div>
            <img src="@/assets/img/zhan_icon.png" alt="" />
          </div>
          <!-- 虚拟商品审核状态 -->
          <div
            class="yello_bot"
            style="background: rgba(255, 107, 0, 0.1); color: rgb(255, 107, 0);"
            v-if="item.productType == '虚拟商品' && item.orderStatus == 2 "
          >
            <div>
              <img v-if="item.orderStatus != 3" src="@/assets/img/cheng_icon.png" alt="" />
              <img v-else src="@/assets/img/red_icon.png" alt="" />
              <p>
                卡券需待后台审核通过后发放
              </p>
            </div>
          </div>
        </div>
        <div class="border_body">
          <div class="border_class"></div>
        </div>
        <div class="shi_qian_body" v-if="zhanShow == index">
          <div class="border_bot_class">
            实付：<span class="red_color" :style="item.orderStatus == 3 || item.orderStatus == 1 ? 'color: #999999;' : ''">{{ item.orderStatus == 3 || item.orderStatus == 1 ? 0 : item.coinPrice }}易币</span>
          </div>
        </div>
        <div class="margin_class" v-if="zhanShow == index">
          <div class="border_class"></div>
        </div>
        <div class="shou_body" v-if="zhanShow == index">
          <div class="shou_item" v-if="item.productType == '实物商品'">
            <p class="item_left">寄送信息</p>
            <p class="item_right" style="line-height: 16px">{{ formData.name }},{{ formData.mobile }}{{ formData.region }}{{ formData.address }}</p>
          </div>
          <div class="shou_item">
            <p class="item_left">订单提交时间</p>
            <p class="item_right">{{ momentFun(formData.createTime) }}</p>
          </div>
          <div class="shou_item">
            <p class="item_left">订单支付时间</p>
            <p class="item_right">{{ momentFun(formData.payTime) }}</p>
          </div>
          <div class="shou_item">
            <p class="item_left">订单支付方式</p>
            <p class="item_right">{{ formData.payType }}</p>
          </div>
          <div class="shou_item">
            <p class="item_left">订单备注</p>
            <p class="item_right">{{ formData.payRemarks }}</p>
          </div>
          <div class="shou_item" v-if="item.productType == '实物商品' && item.expressStatus">
            <p class="item_left">快递公司</p>
            <p class="item_right">{{ formData.expressCompany }}</p>
          </div>
          <div class="shou_item" v-if="item.productType == '实物商品' && item.expressStatus">
            <p class="item_left">发货时间</p>
            <p class="item_right">{{ momentFun(formData.deliveryTime) }}</p>
          </div>
          <div class="shou_item" v-if="item.productType == '实物商品' && item.expressStatus">
            <p class="item_left">快递单号</p>
            <p class="item_right">
              <span>{{ formData.expressNum }}</span>
              <img @click="copyBtn(formData.expressNum)" src="@/assets/img/copy_icon.png" alt="" />
            </p>
          </div>
        </div>
        <div class="zhan_class">
          <div class="zhan_left_item" @click="zhanBtn(index, item)">
            <p>{{ zhanShow != index ? "展开详情" : "收起详情" }}</p>
            <img :class="zhanShow != index ? '' : 'shou_icon'" src="@/assets/img/zhan_icon.png" alt="" />
          </div>
          <div class="zhan_right_item" v-if="zhanShow != index">
            <p>
              实付：<span style="red_color" :style="item.orderStatus == 3 || item.orderStatus == 1 ? 'color: #999999;' : ''">{{ item.orderStatus == 3 || item.orderStatus == 1 ? 0 : item.coinPrice }}易币</span>
            </p>
          </div>
        </div>
      </div>
    </cube-scroll>
    <div class="que_class" v-if="!tableList.length">
      <img src="@/assets/img/queIcon.png" alt="" />
      <p>暂无数据</p>
    </div>
    <kaInfoView :visiable="visiable" v-if="visiable" @changeUserPopShow="changeUserPopShow" :orderForm="orderForm"></kaInfoView>
    <!-- 购买信息 -->
    <van-action-sheet v-model="purchaseInfoShow" title="购买信息">
      <purchaseInfo :orderId="orderForm.orderId" v-if="purchaseInfoShow"></purchaseInfo>
    </van-action-sheet>
    <!--取消订单或审核不通过展示原因-->
    <van-dialog class="reason_dialog" v-model:show="cancelOrAuditFailShow" title="提示" width="260" confirmButtonText="知道了" confirm-button-color="#097DB4" message-align="center">
        <div v-if="formData.cancelReason" class="reason_content">{{ formData.cancelReason }}</div>
        <!-- <div class="reason_content">暂无数据</div> -->
    </van-dialog>
  </div>
</template>

<script>
import kaInfoView from "../userCenter/components/kaInfoView.vue";
import { getExchangeHistory, getOrderInfoDetail } from "@/api/monetary";
import purchaseInfo from "./components/purchaseInfo.vue";
import moment from "moment";
export default {
  data() {
    return {
      orderForm: {}, // 点击订单的的信息
      visiable: false, // 卡券查看预览底部弹窗状态
      pageForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      options: {
        pullUpLoad: {
          threshold: 50,
          txt: {
            more: "",
            noMore: "没有更多信息啦！",
          },
        },
        pullDownRefresh: {
          threshold: 60,
          stopTime: 1000,
          txt: "更新成功",
        },
        scrollbar: false,
      },
      productType: null, // 商品类型字段
      formData: {}, // 订单详情信息
      tableList: [], // 历史订单列表
      oneStatus: false,
      typeList: [
        { value: "", label: "全部" },
        { value: "physical", label: "实物商品" },
        { value: "virtual", label: "虚拟商品" },
      ],
      zhanShow: null,
      expressStatus: null,
      keyword: "",
      purchaseInfoShow: false, // 购买信息底部弹窗
      // 订单状态
      orderStatuList: [
        { value: null, label: "全部" },
        { value: 1, label: "已发货" },
        { value: 0, label: "未发货" },
        { value: 2, label: "已取消" },
        { value: 3, label: "审核不通过" },
      ],
      cancelOrAuditFailShow: false
    };
  },
  watch: {},
  components: {
    kaInfoView,
    purchaseInfo,
  },
  mounted() {
    this.getExchangeHistory();
  },
  methods: {
    // 关闭查看卡券信息事件
    changeUserPopShow() {
      this.visiable = false;
    },
    // 查看卡号优惠码信息事件
    seeBtn(val) {
      this.orderForm = val;
      if (val.type == "voucherCard" || val.type == "discountCode") {
        this.visiable = true;
      } else if (val.type == "custom") {
        this.purchaseInfoShow = true;
      }
    },
    // 搜索把页码标为1数据清空
    restFun() {
      this.pageForm.pageNum = 1;
      this.tableList = [];
    },
    loadFun() {
      setTimeout(() => {
        if (this.total > this.tableList.length) {
          this.pageForm.pageNum += 1;
          this.getExchangeHistory();
          this.$nextTick(() => {
            this.$refs.classifyScroll.refresh();
          });
        } else {
          this.$refs.classifyScroll.forceUpdate();
        }
      }, 1000);
    },
    loadDownFun() {
      this.restFun();
      this.getExchangeHistory();
      this.$refs.classifyScroll.forceUpdate();
    },
    // 商品类型筛选
    productBtn(val) {
      this.oneStatus = !this.oneStatus;
      this.restFun();
      this.productType = val;
      this.getExchangeHistory();
    },
    // 查询订单详情
    getOrderInfoDetail(item) {
      let params = {
        orderId: item.orderId,
      };
      getOrderInfoDetail(params).then((res) => {
        this.formData = res.data;
      });
    },
    // 时间处理方法
    momentFun(time) {
      return moment(time).format("YYYY-MM-DD HH:mm:ss");
    },
    // 订单历史查询
    getExchangeHistory() {
      let params = {
        ...this.pageForm,
        sysCode: "system",
        moduleType: "goods",
        expressStatus: this.expressStatus == 3 || this.expressStatus == 2 ? null : this.expressStatus, // 发货状态字段
        orderStatus: this.expressStatus == 3 ? this.expressStatus : this.expressStatus == 2 ? 1 : null, // 审核通过状态
        productType: this.productType, // 商品类型字段
        keyword: this.keyword,
      };
      getExchangeHistory(params).then((res) => {
        this.tableList = this.tableList.concat(res.data.list);
        this.tableList.forEach((item, index) => {
          this.tableList[index].logo = item.logo.split(",")[0];
        });
        this.total = res.data.total;
        this.zhanShow = null;
      });
    },
    // 复制点击事件
    copyBtn(code) {
      const input = document.createElement("input");
      document.body.appendChild(input);
      input.setAttribute("readonly", "readonly");
      input.setAttribute("value", code);
      input.select();
      input.setSelectionRange(0, code.length);
      try {
        document.execCommand("copy");
      } catch (err) {}
      document.body.removeChild(input);
      this.$createToast({ txt: "复制成功", type: "txt" }).show();
    },
    // 点击筛选icon
    shaiBtn() {
      if (!this.oneStatus) {
        this.$refs.dropMenu.show();
      } else {
        this.$refs.dropMenu.hide();
      }
      this.oneStatus = !this.oneStatus;
    },
    // 展开事件点击
    zhanBtn(index, item) {
      if (this.zhanShow == index) return (this.zhanShow = null);
      this.zhanShow = index;
      this.getOrderInfoDetail(item);
    },
    //查看原因
    viewReason(item){
        this.cancelOrAuditFailShow = true;
        this.getOrderInfoDetail(item);
    },
    // 切换tab
    seleBtn(status) {
      this.restFun();
      this.expressStatus = status;
      this.getExchangeHistory();
    },
    // 关键词输入事件
    searchKeyWord() {
      this.restFun();
      this.getExchangeHistory();
    },
    // 跳转商品详情
    goToMallInfo(item){
      this.$router.push({
        path: `/mall/goods/${item.productId}`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.body {
  // padding: 10px 15px;
  min-height: 100% !important;
  background-color: #f5f6fa;
  padding-bottom: 20px;
  height: auto;
}
.top_serarch_body {
  // padding: 12px 12px 0px 12px;
  padding-top: 12px;
  background-color: #ffffff;
  :deep(.input_width) {
    padding: 0px 15px;
    background-color: #f5f5f5;
    margin: 0px 12px;
    border-radius: 4px;
    ::v-deep .cube-input-field {
      height: 33px;
    }
    .cubeic-wrong{
      right: 14px;
      top: 10px;
    }
  }

  .search_bot_text {
    width: 100%;
    .search-item-wrap {
      min-width: 0px;
      overflow-y: auto;
      &::-webkit-scrollbar {
        display: none;
      }
    }

    .text_item {
      width: 80px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #666666;
      flex-shrink: 0;
      .text_item_item {
        height: 100%;
        border-bottom: 2.3px solid #097db4;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #097db4;
        font-weight: bold;
      }
    }
    img {
      width: 15px;
      height: 15px;
    }
    .img_width {
      width: 44px;
      position: relative;
      flex-shrink: 0;
    }
  }
  ::v-deep .cube-input_active::after {
    border-color: #fff !important;
  }
}
.dealit_body {
  // padding: 13px 12px;
  background-color: #ffffff;
  margin: 15px 0 0px 0;
  .top_title {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // border-bottom: 1px solid #EEEEEE;
    height: 42px;
    padding: 0px 12px;
    img {
      width: 56px;
      height: 16px;
      margin-right: 10px;
    }
    .right_text {
      font-weight: 400;
      font-size: 12px;
      color: #52c41a;
      text-align: left;
      font-style: normal;
      white-space: nowrap;
    }
    .ding_text {
      font-weight: 400;
      font-size: 12px;
      color: #333333;
      text-align: left;
      font-style: normal;
    }
    .top_tit_item {
      display: flex;
      align-items: center;
    }
    .right_text_red {
      color: #ff6b00;
    }
  }
  .shang_item {
    padding: 0px 12px;
    margin-top: 15px;
    display: flex;
    .img_body {
      width: 74px;
      height: 74px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      img {
        border-radius: 4px;
        width: 74px;
        height: 74px;
        object-fit: cover;
      }
    }
    .text_right {
      width: calc(100% - 86px);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .text_top {
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        margin-bottom: 14px;
        white-space: normal; /* 确保文本可以折行 */
        overflow: hidden; /* 隐藏超出的内容 */
        display: -webkit-box; /* 使用弹性盒子模型 */
        -webkit-line-clamp: 2; /* 限制显示的行数为2行 */
        -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
        line-height: 20px; /* 行高 */
        text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
      }
      .text_bottom {
        display: flex;
        justify-content: flex-end;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        text-align: left;
        font-style: normal;
        .span_text {
          font-weight: 400;
          font-size: 14px;
          color: #999999;
          text-align: left;
          font-style: normal;
          margin-left: 10px;
        }
      }
    }
  }
}
.red_color {
  font-weight: 400;
  font-size: 12px;
  color: #ff4d4f;
  text-align: center;
  font-style: normal;
}
.zhan_class {
  display: flex;
  justify-content: space-between;
  background: #ffffff;
  padding: 12px;
  padding-top: 0px;
  .zhan_left_item {
    display: flex;
    align-items: center;
    p {
      font-weight: 400;
      font-size: 12px;
      color: #666666;
      text-align: center;
      font-style: normal;
    }
    img {
      margin-left: 4px;
      width: 16px;
    }
  }
  .zhan_right_item {
    p {
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      text-align: center;
      font-style: normal;
    }
    span {
      color: #ff4d4f;
      font-size: 14px;
    }
  }
}
.border_class {
  width: 100%;
  height: 0.5px;
  background: #eeeeee;
}
.border_body {
  padding: 12px;
  background-color: #ffffff;
}
.shou_body {
  background-color: #ffffff;
  padding: 8px 12px;
  padding-top: 12px;
  .shou_item {
    display: flex;
    justify-content: space-between;
    min-height: 32px;
    padding-top: 8px;
  }
  .item_right {
    font-weight: 400;
    font-size: 12px;
    color: #333333;
    text-align: right;
    font-style: normal;
    flex-wrap: wrap;
    max-width: 73%;
    line-height: 16px;
    display: flex;
    align-items: flex-start;
    img {
      width: 13px;
      height: 13px;
      margin-left: 7px;
    }
  }
  .item_left {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    text-align: left;
    font-style: normal;
    line-height: 16px;
  }
}
.shou_icon {
  transform: rotate(180deg);
}
.yello_bot {
  width: 327px;
  height: 32px;
  border-radius: 4px;
  margin-top: 12px;
  display: flex;
  align-items: center;
  padding: 0px 12px;
  margin: 0px 12px;
  margin-top: 12px;
  p {
    font-weight: 400;
    font-size: 12px;
    text-align: left;
    margin-top: 1px;
  }
  img {
    width: 14px;
    height: 14px;
    margin-right: 2px;
  }
  div {
    display: flex;
    align-items: center;
  }
  .view{
    font-size: 12px;
    color: #1890FF;
    margin-left: 10px;
    margin-top: 0.03rem;
  }
}
.ka_item {
  width: 100%;
  height: 32px;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  .ka_left_text {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    text-align: left;
    font-style: normal;
  }
  .ka_right_text {
    img {
      width: 12px;
      height: 12px;
      margin-left: 7px;
      margin-bottom: -2px;
    }
    span {
      font-weight: 400;
      font-size: 12px;
      color: #333333;
      text-align: center;
      font-style: normal;
    }
  }
}
.ka_body {
  background-color: #f5f6fa;
  margin: 12px;
  margin-bottom: 0px;
  padding: 0px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;
  border-radius: 4px;
  .text {
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    text-align: left;
    font-style: normal;
  }
  img {
    transform: rotate(270deg);
    width: 16px;
  }
}
.table_scroll {
  max-height: 90vh;
  overflow-y: auto;
}
.que_class {
  width: 100%;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  // background-color: #FFFFFF;
  img {
    width: 120px;
  }
  p {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    text-align: left;
    font-style: normal;
    margin-top: 16px;
  }
}
.shi_qian_body {
  margin-top: 12px;
  background-color: #ffffff;
  text-align: right;
  font-weight: 400;
  color: #999999;
  font-style: normal;
  font-size: 12px;
  margin: 0px 12px;
  .red_color {
    color: #ff4d4f;
    font-size: 14px;
  }
  .border_bot_class {
    // border-bottom: 1px solid #EEEEEE;
    padding-bottom: 12px;
  }
}
.not_color {
  font-weight: 400;
  font-size: 12px;
  color: #ff4d4f;
  text-align: center;
  font-style: normal;
  white-space: nowrap; // 确保文字不换行
}
.box_body {
  margin: 0px 12px;
  background-color: #ffffff;
  border-radius: 6px;
}
.margin_class {
  margin: 0px 12px;
}
</style>
<style lang="scss">
.list-drop-menu {
  width: 120px;
  position: absolute;
  top: 33px;
  right: 7px;
  background: #fff;
  max-height: none;
  border-radius: 4px;
  padding: 0;
  box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
  .cube-tip-close {
    display: none;
  }
  .cube-tip-angle {
    top: 1px;
    right: 12px !important;
    &::before {
      border-width: 0;
      width: 9px;
      height: 9px;
      background: #fff;
      transform: rotate(45deg) !important;
      box-shadow: 0 -8px 10px 0px rgba(0, 0, 0, 0.1);
    }
  }
  .cube-tip-content {
    line-height: 38px !important;
    color: #999;
    font-size: 14px;
    z-index: 1;
    white-space: nowrap;
    width: 120px;
    & > div {
      padding: 0 16px;
      & + div {
        border-top: 1px solid rgba(0, 0, 0, 0.06);
      }
      &.active {
        color: #00629f;
      }
    }
    div {
      width: 120px;
    }
  }
}
.reason_dialog{
    border-radius: 12px;
    .reason_content{
        font-size: 14px;
        color: #666666;
        line-height: 20px;
        padding: 15px 24px;
        
    }
}
</style>
