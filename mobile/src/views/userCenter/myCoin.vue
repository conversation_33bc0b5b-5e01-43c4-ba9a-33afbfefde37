<template>
  <div>
    <div class="body" :id="scrllType == true ? 'scroll_class':''">
      <memberTitleView @scrollMeth="scrollMeth"></memberTitleView>
      <coinImfBgView v-if="memCoinSettingForm.mallStatus"></coinImfBgView>
      <settingIconView v-if="memCoinSettingForm.ruleStatus"></settingIconView>
    </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex';
import settingIconView from "@/components/common/settingIconView";
import memberTitleView from "@/components/common/memberTitleView";
import coinImfBgView from "@/components/common/coinImfBgView";
import { getMemberLevelSwitchStatus } from "@/api/memberApi";
export default {
  data() {
    return {
      scrllType: false,
    };
  },
  components: {
    memberTitleView,
    settingIconView,
    coinImfBgView,
  },
  mounted() {
    this.getMemberLevelSwitchStatus();
    this.getSettingApi();
  },
  computed: {
    memCoinSettingForm() {
      return this.$store.state.memCoinSettingForm
    }
  },
  methods: {
    ...mapActions(['getSettingApi']),
    scrollMeth(type) {
      this.scrllType = type;
    },
    // 查询会员等级开关是否打开
    getMemberLevelSwitchStatus() {
      getMemberLevelSwitchStatus().then(res=>{
        if (res.code == this.$successCode) {
          this.$store.commit('getMemberLevelSwitchStatus', res.data)
        }
      })
    },
  },
};
</script>
<style scoped>
.body {
  background: #F5F6FA;
}
.container {
  background: #F5F6FA !important;
}
#scroll_class {
  height: 100vh;
  overflow: hidden;
}
</style>
