<template>
  <div class="member-mission-center-wrap">
    <!-- 头部 -->
    <mission-center-top />
    <!-- 每日直播 -->
    <mission-center-live />
    <!-- 产品 -->
    <mission-center-product />
    <!-- 其他任务 -->
    <mission-center-other />
    <!-- 底部  -->
    <mission-center-new />
  </div>
</template>
<script>
import MissionCenterTop from "./components/missionCenter/missionCenterTop.vue";
import MissionCenterLive from "./components/missionCenter/missionCenterLive";
import MissionCenterNew from "./components/missionCenter/missionCenterNew";
import MissionCenterProduct from "./components/missionCenter/missionCenterProduct";
import MissionCenterOther from "./components/missionCenter/missionCenterOther";
export default {
  data() {
    return {};
  },
  created() {},
  components: {
    MissionCenterTop,
    MissionCenterLive,
    MissionCenterNew,
    MissionCenterProduct,
    MissionCenterOther,
  },
  mounted() {},
  computed: {},
  methods: {},
};
</script>
<style scoped lang="scss">
.member-mission-center-wrap {
  width: 100%;
  height: fit-content;
  background: #f5f6fa;
  overflow: auto;
}
</style>
