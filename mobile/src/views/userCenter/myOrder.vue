<template>
  <div class="myOrder-container" @touchmove="handleTouchMove">
    <div class="tab-bar">
      <ul class="tab-list">
        <li v-for="(tab, key) in tab" :key="key" :class="{ active: tabId == key }" class="tabKey" @click="change(key, 'tab')">{{ tab }}</li>
      </ul>
      <tabList v-show="false" needList="tag" @changeItem="changeTab"></tabList>
    </div>
    <div class="tab-div">
      <ul>
        <ul @click="showTab" class="tab-title" v-if="tabId == 0">
          <li class="tagActiveName">{{ tagActiveName }} <img src="@/assets/img/down.png" alt="" /></li>
        </ul>
        <div v-if="showUl" class="tabList">
          <ul class="tab-item">
            <li v-for="(tab, key) in tabList" :key="key" :class="{ activeTab: tabActiveId == tab.id }" @click="changeTab(tab.id, 'tab')">{{ tab.title }}</li>
          </ul>
        </div>
      </ul>
    </div>
    <div class="scroll-list">
      <cube-scroll ref="scroll" :data="orderList" :options="options" @pulling-up="getMyOrderListFn">
        <div v-if="orderList.length == 0" class="no-data-tips">
          <img src="@/assets/img/no_data_icon.png" alt="" />
          <div>暂无订单</div>
        </div>
        <cube-checkbox-group v-model="orderChecked">
          <div v-for="(order, index) in orderList" :key="order.orderId" class="order-item">
            <div
              :class="order.itemType == 'live' || order.itemType == 'series' ? 'order-item-header lineElipsis' : 'order-item-activity'"
              @click="order.itemType !== 'activity' && toOrderDetail(order)"
            >
              <!-- <span>{{order.businessTypeText}}</span> -->
              <span
                v-if="ids == 3"
                :class="
                  order.itemType == 'live'
                    ? 'user-center-myOrder-table-item-img-tag-live-blue'
                    : order.itemType == 'series'
                    ? 'user-center-myOrder-table-item-img-tag'
                    : 'user-center-myOrder-table-item-img-tag-activity'
                "
                style="margin-right: 6px"
                >{{ order.itemType == "live" ? "直播" : order.itemType == "series" ? "系列直播" : "线下" }}</span
              >
              <span
                v-else-if="ids == 5"
                :class="
                  order.itemType == 'live'
                    ? 'user-center-myOrder-table-item-img-tag-live-purple'
                    : order.itemType == 'series'
                    ? 'user-center-myOrder-table-item-img-tag'
                    : 'user-center-myOrder-table-item-img-tag-activity'
                "
                style="margin-right: 6px"
                >{{ order.itemType == "live" ? "直播" : order.itemType == "series" ? "系列直播" : "线下" }}</span
              >
              <span
                v-else
                :class="
                  order.itemType == 'live'
                    ? 'user-center-myOrder-table-item-img-tag-live'
                    : order.itemType == 'series'
                    ? 'user-center-myOrder-table-item-img-tag'
                    : 'user-center-myOrder-table-item-img-tag-activity'
                "
                style="margin-right: 6px"
                >{{ order.itemType == "live" ? "直播" : order.itemType == "series" ? "系列直播" : "线下" }}</span
              >
              <!-- <span :class="order.businessTypeText == '直播'?'user-center-myOrder-table-item-img-tag-live':'user-center-myOrder-table-item-img-tag'">{{order.businessTypeText}}</span> -->
              <!-- <p>下单时间：{{formatTime(order.orderCreateTime,'YYYY-MM-DD HH:mm')}}</p> -->
              <p v-if="order.itemType !== 'activity'" style="font-size: 13px">订单编号:{{ order.orderCode }}</p>
              <div v-else-if="order.itemType == 'activity'" style="color: #666">
                活动时间：<span>{{ formatTime(order.beginTime, "YYYY-MM-DD HH:mm") }}</span>
              </div>
              <div v-if="order.itemType !== 'activity'" :style="'color:' + computedStatusStyle(order.orderStatus)" class="orderStatusText">
                {{ orderStatusText(order.orderStatus, order.payPrice) }}
              </div>
              <div v-if="order.itemType == 'activity'">
                <img v-if="order.position" class="offlineItem-top-img" src="../../assets/img/location.png" alt="" /><span class="position">{{ order.position }}</span>
              </div>
            </div>
            <div class="order-item-content" @click="activityToPay(order)">
              <img :src="order.bannerImg" alt="" />
              <div>
                {{ order.liveName }}
              </div>
            </div>
            <div class="order-item-detail" v-if="order.itemType !== 'activity'">
              <span class="order-item-detail-text"
                >支付类型&nbsp;&nbsp;<span style="color: #333">{{ order.productName }}</span></span
              >
              <span class="order-item-detail-text"
                >报名时间&nbsp;&nbsp;<span style="color: #333">{{ formatTime(order.orderCreateTime, "YYYY-MM-DD HH:mm:ss") }}</span></span
              >
              <span class="order-item-detail-text" v-if="order.orderStatus !== 4"
                >{{ order.payTime ? "实际支付金额" : "需支付" }}&nbsp;&nbsp;<span style="color: #333">￥{{ order.payTime ? priceFixed(order.payPrice) : priceFixed(order.discountPrice) }}</span>
                <img v-if="order.discountNumber > 0 && order.discountType > 0" src="@/assets/img/haveDiscounts.png" alt="" class="user-center-myOrder-table-item-detail-img-top" />
                <img v-if="order.refundMoney > 0 && order.orderStatus == 1" src="@/assets/img/drawback.png" alt="" class="user-center-myOrder-table-item-detail-img-top" />
              </span>
              <span class="order-item-detail-text" v-if="(order.auditTime !== null && order.orderStatus > 3) || order.orderStatus == 3 || order.orderStatus == 2"
                >审核状态&nbsp;&nbsp;
                <span v-if="order.auditTime !== null && order.orderStatus > 3" style="color: #999999">审核通过</span>
                <span v-if="order.orderStatus == 3" style="color: #ff4d4f">审核未通过</span>
                <span v-if="order.orderStatus == 2" style="color: #333">待审核</span>
              </span>
              <span class="order-item-detail-text" v-if="order.orderStatus == 9 && order.totalPrice > 0"
                >支付时间&nbsp;&nbsp;<span style="color: #333">{{ formatTime(order.payTime, "YYYY-MM-DD HH:mm:ss") }}</span></span
              >
              <span class="order-item-detail-text" v-if="order.orderStatus == 9 && order.totalPrice > 0"
                >支付方式&nbsp;&nbsp;<span style="color: #333">{{ order.payTypeText }}</span></span
              >
            </div>
            <div class="order-item-num" v-if="order.itemType !== 'activity'">
              <div>
                <div v-if="order.orderStatus == 4" class="discountPrice-title">
                  需支付<span class="discountPrice">￥{{ priceFixed(order.discountPrice) }}</span>
                  <img v-if="order.discountNumber > 0 && order.discountType > 0" src="@/assets/img/haveDiscounts.png" alt="" class="user-center-myOrder-table-item-detail-img" />
                  <img v-if="order.refundMoney > 0 && order.orderStatus == 1" src="@/assets/img/drawback.png" alt="" class="user-center-myOrder-table-item-detail-img ml-img" />
                </div>
                <div class="flex-middle">
                  <span v-if="order.orderStatus == 4" class="cancel-order" @click="cancelOrder(order, index)">取消订单</span>
                  <span v-if="order.orderStatus == 4" class="toPay" @click="toPay($event, order)">去支付</span>
                  <!-- v-if="order.orderStatus==9&&order.invoiceFlag==1&&order.invoiceStatus==0" -->
                  <span v-if="order.orderStatus == 9 && order.invoiceFlag == 1 && (order.openInvoice == 0 || order.openInvoice == null)" class="appilyBill" @click="appilyBill(order)">申请开票</span>
                  <span v-if="order.openInvoice == 1" class="alappilyBill appilyBill">已提交开票</span>
                </div>
              </div>
            </div>
          </div>
        </cube-checkbox-group>
      </cube-scroll>
    </div>
    <!-- <div class="pay-footer">
      <cube-button :inline="true">批量支付</cube-button>
      <cube-button :inline="true" :outline="true">申请开票</cube-button>
    </div> -->
  </div>
</template>
<script>
import tabList from "./components/tabList";
import moment from "moment";
import { getInfoByDynamicUrl } from "@/api/configurable/common";
import { postPay, getInvoiceContent, getOrderById, cancelOrder } from "@/api/userCenter";
export default {
  name: "myOrder",
  components: { tabList },
  data() {
    return {
      tabList: [
        { id: 0, title: "全部" },
        { id: 19, title: "无需支付" },
        { id: 4, title: "未支付" },
        { id: 9, title: "已支付" },
        { id: 10, title: "流转中" },
        { id: 2, title: "待审核" },
        { id: 3, title: "已拒绝" },
        { id: 1, title: "已取消" },
      ],
      tab: {
        0: "直播",
        1: "线下",
      },
      tabActiveId: 0, // tab选中项 id
      tagActive: {}, // tag 选中项
      // tagActiveName:'全部',
      options: {
        // 上拉加载设置
        pullUpLoad: {
          threshold: 30,
          visible: false,
          txt: {
            more: "上拉加载更多",
            noMore: "没有更多信息啦！",
          },
        },
      },
      orderList: [], // 订单列表
      orderChecked: [], // 订单 选中项
      currentPage: 0, // 当前页码
      total: 0, // 订单总条数
      orderLoading: false,
      showUl: false,
      // tabId:0,
      tabId: 0, //直播线下
      itemType: "live", //直播or线下
      toPayShow: false,
      ids: localStorage.getItem("platformId"),
    };
  },
  created() {
    document.title = "我的订单";
  },
  computed: {
    tagActiveName() {
      if (this.tabActiveId == "0") {
        return "全部";
      } else if (this.tabActiveId == "4") {
        return "未支付";
      } else if (this.tabActiveId == "9") {
        return "已支付";
      } else if (this.tabActiveId == "2") {
        return "待审核";
      } else if (this.tabActiveId == "3") {
        return "已拒绝";
      } else if (this.tabActiveId == "1") {
        return "已取消";
      } else if (this.tabActiveId == "10") {
        return "流转中";
      } else if (this.tabActiveId == "19") {
        return "无需支付";
      }
    },
  },
  methods: {
    handleTouchMove() {
      // console.log(1111);
      this.showUl = false;
    },
    // 切换活动 直播/线下
    change(item, type) {
      this.tabId = item;
      this.showUl = false;
      if (item == 0) {
        this.itemType = "live";
      } else {
        this.itemType = "activity";
      }
      this.orderList = [];
      this.orderChecked = [];
      this.currentPage = 0;
      this.total = 0;
      this.$refs.scroll.scrollTo(0, 0);
      this.getMyOrderListFn();
    },
    showTab() {
      this.showUl = !this.showUl;
    },
    // 切换 tab/tag 选中项
    changeTab(item, type) {
      if (type == "tab") {
        this.tabActiveId = item;
      } else {
        this.tagActive = item;
      }
      this.orderList = [];
      this.orderChecked = [];
      this.currentPage = 0;
      this.total = 0;
      this.$refs.scroll.scrollTo(0, 0);
      this.getMyOrderListFn();
      this.showUl = false;
    },
    // 获取列表
    getMyOrderListFn() {
      if (this.orderLoading) {
        // 防止重复调用
        return;
      }
      this.orderLoading = true;
      let params = {};
      this.tagActive.menuParams.forEach((item) => {
        params[item.menuParamKey] = item.menuParamValue;
      });
      params.pageNum = this.currentPage + 1;
      params.orderStatus = this.tabActiveId == 0 ? "" : this.tabActiveId;
      params.itemType = this.itemType;
      params.platformId = localStorage.getItem("platformId");
      getInfoByDynamicUrl(this.tagActive.apiUrl, params).then((res) => {
        this.orderLoading = false;
        if (res.code == this.$successCode) {
          this.orderList = this.orderList.concat(res.data.list);
          this.total = res.data.total;
          this.currentPage++;
          // 已加载完毕  没有更多信息啦！
          if (this.total == this.orderList.length) {
            this.$refs.scroll.forceUpdate();
          }
        }
      });
    },
    // 跳转 订单详情 页
    toOrderDetail(order) {
      let itemType = order.itemType;
      // debugger;
      this.$router.push({ name: "myOrderDetail", query: { orderId: order.orderId, itemType } });
    },
    // 倒计时
    countDown(index, orderCreateTime) {
      let endTime = new Date(orderCreateTime).getTime() + 30 * 60 * 1000;
      let time = "";
      setInterval(() => {
        let now = new Date().getTime();
        let thisMinute = moment.duration(endTime - now)._data.minutes;
        let thisSecond = moment.duration(endTime - now)._data.seconds;
        this.orderList[index].countDown = (thisMinute < 10 ? "0" + thisMinute : thisMinute) + ":" + (thisSecond < 10 ? "0" + thisSecond : thisSecond);
        this.$set(this.orderList, index, this.orderList[index]);
      }, 1000);
    },
    // 立即支付
    payOrder(e, item) {
      // e.stopPropagation();
      let params = {
        businessId: item.businessId,
        businessType: item.businessType,
        orderIdList: [item.orderId],
        sysCode: item.sysCode,
        moduleId: item.itemId,
        moduleType: item.itemType,
        sourceModuleId: item.itemId,
        sourceModuleType: item.itemType,
      };
      postPay(params).then((res) => {
        let that = this;
        if (res.code == this.$successCode) {
          let payBody = res.data;
          //这里需要进行支付
          wx.chooseWXPay({
            timestamp: payBody.timeStamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
            nonceStr: payBody.nonceStr, // 支付签名随机串，不长于 32 位
            package: payBody.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
            signType: payBody.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
            paySign: payBody.paySign, // 支付签名
            success: function (res) {
              // 支付成功后的回调函数
              // this.$toast('支付成功');
              // 重新加载 订单列表
              that.changeTab(that.tabActiveId, "tab");
            },
            error: function () {
              that.$createToast({ txt: "支付失败", type: "error" }).show();
            },
            cancel: function () {
              // console.log('取消支付')
            },
            complete: function () {},
          });
        } else {
          this.$createToast({ txt: res.info, type: "error" }).show();
        }
      });
    },
    computedStatusStyle(status) {
      if (status == "4") {
        return "#FF4D4F";
      } else if (status == "3" || status == "2" || status == "1") {
        return "#999999";
      } else {
        return "#45D434";
      }
    },
    orderStatusText(order, payPrice) {
      if (order == "4") {
        return "未支付";
      } else if (order == "9" && payPrice !== 0) {
        return "已支付";
      } else if (order == "2") {
        return "待审核";
      } else if (order == "3") {
        return "已拒绝";
      } else if (order == "1") {
        return "已取消";
      } else if (order == "10") {
        return "流转中";
      } else if (order == "9" && payPrice == 0) {
        return "无需支付";
      }
    },
    computedStatusText(order) {
      let text = "";
      switch (order.orderStatus) {
        case 4:
          text = "需付金额";
          break;
        case 9:
          text = "实付金额";
          break;
        case 10:
          text = "实付金额";
          break;
        default:
          text = "实付金额";
      }
      return text;
    },
    // 去支付
    toPay(e, data) {
      let { itemType, itemId, bizId, domainType } = data;
      if (itemType == "series") {
        window.location.href = location.origin + `/mobile/#/zh-cn/seriesFormPage?itemId=${itemId}&platformId=${this.$route.query.platformId}&bizId=${bizId}&domainType=${domainType}`;
      } else {
        window.location.href = location.origin + `/mobile/#/zh-cn/liveFormPage?liveId=${itemId}&platformId=${this.$route.query.platformId}&bizId=${bizId}&domainType=${domainType}`;
      }

      // let params=[
      //     "orderId=" + order.id,
      //     'itemType=' + order.itemType
      // ]
      // getOrderById(params.join('&')).then(res=>{
      //     console.log(res.data);
      //     this.currentPage = 0;
      //     this.$set(order,'discountPrice',res.data.discountPrice)
      //     this.$set(order,'orderStatus',res.data.orderStatus)
      //     this.$set(order,'openInvoice',res.data.openInvoice)
      //     this.$set(order,'invoiceFlag',res.data.invoiceFlag)
      //     this.$set(order,'payTime',res.data.payTime)
      //     this.$set(order,'payTypeText',res.data.payTypeText)
      //     this.$set(order,'totalPrice',res.data.totalPrice)
      //     this.$set(order,'auditTime',res.data.auditTime)
      //     this.$set(order,'payPrice',res.data.payPrice)
      //     this.$set(order,'discountNumber',res.data.discountNumber)
      //     this.$set(order,'discountType',res.data.discountType)
      //     this.$set(order,'refundMoney',res.data.refundMoney)
      //     this.payOrder(e, res.data)
      // })
    },
    // 申请开票
    appilyBill(item) {
      this.getInvoiceContent(item);
      //   let itemType=item.itemType
      //   this.$router.push({name:'appilyBill',query:{orderId:item.orderId,itemType}})
    },
    activityToPay(item) {
      console.log(item);
      if (item.itemType == "activity") {
        window.location.href = item.orderUrlMobile;
      } else {
        this.toGoodsInfo(item);
      }
    },
    // 获取开票内容
    getInvoiceContent(item) {
      let orderIds = [];
      orderIds.push(item.orderId);
      let params = {
        bizId: item.bizId,
        platformId: item.platformId,
        channel: item.channel,
        businessId: item.businessId,
        orderIds: orderIds,
        sysCode: item.sysCode,
        moduleType: item.itemType,
        moduleId: item.itemId,
        businessType: item.businessType,
        token: localStorage.getItem("proToken"),
      };
      getInvoiceContent(params).then((res) => {
        if (res.data) {
          let itemType = item.itemType;
          this.$router.push({ name: "appilyBill", query: { orderId: item.orderId, itemType } });
        } else {
          this.$createToast({ txt: res.info, type: "txt" }).show();
          //   setTimeout(()=>{
          //     history.back(-1)
          //   },2000)
        }
      });
    },
    toGoodsInfo(item) {
      console.log(item);
      let thisHref = "";
      if (item.itemType == this.TYPE[6]) {
        // 直播
        if (item.liveType != this.TYPE[2]) {
          thisHref = location.origin + "/mobile/#/zh-cn/liveDetail?liveId=" + item.itemId + "&platformId=" + localStorage.getItem("platformId") + `&bizId=${item.bizId}&domainType=1`;
        } else {
          thisHref = location.origin + "/mobile/#/ieventLive?liveId=" + item.itemId + "&platformId=" + localStorage.getItem("platformId") + `&bizId=${item.bizId}&domainType=1`;
        }
      } else if (item.itemType == this.TYPE[9]) {
        // 系列直播
        thisHref = location.origin + "/mobile/#/zh-cn/seriesLive/" + item.itemId + "?platformId=" + localStorage.getItem("platformId") + `&bizId=${item.bizId}&domainType=1`;
      }
      window.location.href = thisHref;
    },
    priceFixed(value) {
      return parseFloat(value).toFixed(2);
    },
    cancelOrder(order, index) {
      const params = {
        orderId: order.id,
        cancelReason: "手动取消",
        cancelWay: "front_cancel_ticket",
      };
      cancelOrder(params).then((res) => {
        if (res.code == this.$successCode) {
          this.$toast("订单已取消");
          this.orderList = this.orderList.map((item, i) => {
            if (i == index) {
              item.orderStatus = 1;
            }
            return item;
          });
        } else {
          this.$toast(res.info);
        }
      });
    },
  },
};
</script>

<style lang="scss">
$primaryColor: var(--color-primary);
.myOrder-container.container {
  height: 100%;
  // overflow: auto;
  // overflow: hidden;
  // padding-bottom: 50px;
  .lineElipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .tab-bar {
    width: 100%;
    height: 48px;
    position: relative;
    .tab-list {
      line-height: 44px;
      font-size: 16px;
      color: #666;
      // display: flex;
      // justify-content: space-around;
      border-bottom: 1px solid #f7f7f7;
      li {
        display: inline-block;
        margin-left: 20px;
        color: #666;
        font-size: 14px;
        // &:nth-child(2){
        //     margin-left: 20px;
        // }
        &.active {
          color: $primaryColor;
          border-bottom: 3px solid $primaryColor;
          font-weight: 600;
        }
      }
    }
  }
  .scroll-list {
    height: 100%;
    .no-data-tips {
      > img {
        width: 185px;
        height: 142px;
      }
      > div {
        margin-top: 20px;
      }
      margin-top: 170px;
      text-align: center;
      color: #999;
      display: flex;
      flex-direction: column;
      align-items: center;
      flex-wrap: wrap;
    }
    .order-item {
      margin-top: 10px;
      margin-left: 15px;
      width: 345px;
      background: #ffffff;
      border-radius: 4px;
      font-size: 14px;
      .order-item-header {
        color: #666666;
        line-height: 40px;
        position: relative;
        border-bottom: 1px solid rgba($color: #eee, $alpha: 0.5);
        // display: flex;
        // justify-content: space-between;
        padding: 0 10px;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        span {
          display: inline-block;
          width: max-content;
        }
        p {
          display: inline-block;
        }
        div {
          display: inline-block;
          // margin-left: 10px;
        }
      }
      .order-item-content {
        padding: 10px;
        display: flex;
        align-items: center;
        // border-bottom: 1px solid rgba($color: #eee, $alpha: .5);
        > img {
          width: 86px;
          height: 51px;
          border-radius: 4px;
        }
        > div {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          margin-left: 10px;
          font-size: 14px;
          font-weight: 400;
          color: #333333;
          line-height: 20px;
        }
      }
      .order-item-num {
        > div {
          padding: 10px;
          display: flex;
          justify-content: space-between;
          color: #666;
        }
      }
    }
  }
  .pay-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 10px 0;
    background-color: #fff;
    display: flex;
    justify-content: space-around;
    z-index: 11;
    box-shadow: 0 -2px 5px #f7f7f7;
    .cube-btn {
      width: 40%;
      background-color: $primaryColor;
      &.cube-btn-outline {
        background: none;
      }
    }
  }
}
.position {
  display: inline-block;
  width: 75px;
  height: 25px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  -o-text-overflow: ellipsis;
}
.discountPrice-title {
  margin-top: 7px;
  font-size: 14px;
  color: #333;
}
.discountPrice {
  // margin-top: 7px;
  margin-right: 10px;
  color: #ff4d4f;
  font-size: 16px;
}
</style>
<style lang="scss">
$primaryColor: var(--color-primary);
.tab-div {
  position: absolute;
  right: 15px;
  top: 14px;
  // z-index: 999999;
  width: 85px;
}
.tab-title {
  position: absolute;
  right: 13px;
  top: 0px;
}
.tabList {
  margin-top: 20px;
  margin-left: 14px;
  position: absolute;
  z-index: 99999;
}
.tagActiveName {
  // margin-right:30px;
  font-size: 14px;
  color: #666;
  img {
    display: inline-block;
    width: 12px;
    height: 8px;
  }
}
.tab-item {
  z-index: 99999;
  height: 300px;
  display: flex;
  flex-direction: column;
  width: 70px;
  height: auto;
  background: #ffffff;
  margin-top: 10px;
  box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
  li {
    height: 35px;
    line-height: 35px;
    text-align: center;
    // margin-right: 10px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    border-bottom: 1px solid #eee;
    &.activeTab {
      color: $primaryColor;
    }
  }
}
.myOrder-container {
  position: relative;
  .scroll-list {
    background: #f1f2f3;
  }
  .cube-checkbox-group {
    background: #f1f2f3;
  }
}
.order-item-detail {
  padding-top: 15px;
  padding-bottom: 5px;
  padding-left: 10px;
  margin-left: 10px;
  width: 94%;
  height: auto;
  // background-color: ;
  background: #f9f9f9;
  border-radius: 2px;
}
.order-item-detail-text {
  margin-bottom: 10px;
  font-size: 13px;
  color: #999999;
  display: block;
}
.user-center-myOrder-table-item-detail-img-top {
  margin-left: 10px;
  vertical-align: middle;
  display: inline-block;
  width: 45px;
}
.user-center-myOrder-table-item-detail-img {
  vertical-align: middle;
  // margin-right: 10px;
  margin-left: -5px;
  display: inline-block;
  width: 45px;
}
.cancel-order {
  padding: 5px 13px;
  font-weight: 400;
  font-size: 14px;
  color: $primaryColor;
  line-height: 20px;
  border: 1px solid $primaryColor;
  border-radius: 2px;
  margin-right: -28px;
}
.toPay,
.appilyBill,
.alappilyBill {
  float: right;
  // display: inline-block;
  width: 82px;
  height: 32px;
  line-height: 32px;
  background: $primaryColor;
  border-radius: 2px;
  border: 1px solid $primaryColor;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  text-align: center;
  vertical-align: middle;
  margin-left: 40px;
}
.appilyBill {
  margin-left: 242px;
}
.appilyBill,
.alappilyBill {
  color: #333333;
  background: #fff;
  border: 1px solid #eee;
}
.user-center-myOrder-table-item-img-tag-live,
.user-center-myOrder-table-item-img-tag,
.user-center-myOrder-table-item-img-tag-activity,
.user-center-myOrder-table-item-img-tag-live-blue,
.user-center-myOrder-table-item-img-tag-live-purple {
  display: inline-block;
  width: auto;
  height: 20px;
  line-height: 20px;
  padding: 0 3px;
  background: linear-gradient(270deg, #ffa400 0%, #ff6b00 100%);
  border-radius: 1px;
  color: #fff;
  font-size: 12px;
  margin-top: 9px;
  text-align: center;
}
.user-center-myOrder-table-item-img-tag-live-blue {
  background: linear-gradient(270deg, #5c8cff 0%, #1464a1 100%);
}
.user-center-myOrder-table-item-img-tag-live-purple {
  background: linear-gradient(270deg, #a156e9 0%, #5a2e84 100%);
}
.user-center-myOrder-table-item-img-tag {
  background: linear-gradient(270deg, #58afff 0%, #1890ff 100%);
}
.user-center-myOrder-table-item-img-tag-activity {
  background: linear-gradient(270deg, #8ae438 0%, #52c41a 100%);
}
.my-title {
  height: 31px;
  line-height: 31px;
  background: #f5f6f6;
}
.my-title-img {
  p {
    padding-left: 10px;
  }
}
.my-title-p {
  padding-left: 10px;
}
.alappilyBill {
  background: #f5f5f5;
  border-radius: 2px;
  color: #666666;
}
.paid {
  color: #52c41a;
}
.unpaid {
  color: #ff4d4f;
}
.nopay {
  color: #999;
}
.toPayStyle {
  width: 200px;
  height: 200px;
  background: #fff;
  position: absolute;
  top: 100px;
  left: 20px;
}
.order-item-activity {
  line-height: 33px;
  padding: 0 10px;
  // display: flex;
  border-bottom: 1px solid rgba(238, 238, 238, 0.5);
  div {
    display: inline-block;
    // margin-left: 10px;
  }
}
.ml-img {
  margin-left: 0px;
}
.orderStatusText {
  float: right;
}
</style>
