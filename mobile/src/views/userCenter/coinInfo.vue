<template>
    <div class="body">
        <div class="back_body">
            <div class="top_title">
                <img src="../../assets/img/integral_1.png" alt="">
                <p class="top_tit_text">我的易币</p>
                <p class="top_tit_number">{{ pointInfos.balance || 0 }}</p>
            </div>
            <!--实名认证未实名、待审核、未通过提示--->
            <realnameAuthenTip marginTop="10px" marginBottom="-11px"></realnameAuthenTip>
            <div class="table_body">
                <div class="tab_top_btn">
                    <div class="text">
                        <div class="btn" @click="radioBtn(0)" :class="radioTabIndex == 0 ? 'border_btn':''">易币明细</div>
                    </div>
                    <div class="text">
                        <div class="btn" @click="radioBtn(1)" :class="radioTabIndex == 1 ? 'border_btn':''">本月即将过期易币</div>
                    </div>
                </div>
            </div>
            <cionDetailed ref="cionDetailed" class="cionDetailed" :radioTabIndex="radioTabIndex"></cionDetailed>
        </div>
    </div>
</template>



<script>
import { mapActions } from 'vuex';
import cionDetailed from "@/components/common/cionDetailed"
import realnameAuthenTip from "./components/realnameAuthenTip.vue"
import moment from 'moment';
export default {
    data(){
        return {
            radioTabIndex: 0,
        }
    },
    watch: {
        radioTabIndex(type) {
            this.$refs.cionDetailed.getPointPage(type);
        }
    },
    computed: {
        pointInfos() {
          // 查询积分信息
          return this.$store.state.pointInfos;
        },
    },
    components: {
        cionDetailed,
        realnameAuthenTip
    },
    mounted(){
        this.getMemberPoints();
    },
    methods: {
        ...mapActions(['getMemberPoints']),
        // 切换tab
        radioBtn(type) {
            this.radioTabIndex = type;
        }
    },
}
</script>



<style lang='scss' scoped>
.body {
    // background: linear-gradient( 180deg, #D1E5FE 0%, #F5F6FA 100%);
    z-index: 0;
}
.back_body {
    padding: 16px 15px;
    // background: linear-gradient( 180deg, #D1E5FE 0%, #F5F6FA 100%);
    background-image: url("../../assets/img/cion_bg.png");
    // background: linear-gradient( 180deg, #D1E5FE 0%, #F5F6FA 100%);
    background-size: 100% auto;
    background-repeat: no-repeat; /* 不重复显示背景图 */
    height: 91%;
    .top_title {
        display: flex;
        align-items: center;
        img {
            width: 15px;
            margin-right: 4px;
            margin-bottom: 3px;
        }
        .top_tit_text {
            font-weight: 500;
            font-size: 15px;
            color: #333333;
            text-align: left;
            font-style: normal;
            margin-right: 6px;
            line-height: 20px;
        }
        .top_tit_number {
            font-weight: bold;
            font-size: 24px;
            color: #097DB4;
            text-align: left;
            font-style: normal;
            font-style: normal;
        }
    }
}
.table_body {
    margin-top: 21px;
    background: #FFFFFF;
    border-radius: 10px 10px 0px 0px;
    .tab_top_btn {
        display: flex;
        .text {
            font-size: 15px;
            height: 42px;
            width: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            .btn {
                width: fit-content;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }
}
.border_btn {
    border-bottom: 2px solid #097DB4;
    font-weight: 500;
    font-size: 15px;
    color: #097DB4;
    text-align: left;
    font-style: normal;
}
.cionDetailed {
    padding: 12px;
}
.container {
  background: #F5F6FA !important;
}
</style>