<!--  搜索的头部-->
<template>
    <div class="searchHeadZly">
        <div class='searchHeadWrap  flex-left unit-0'>
            <div class="searchLeft unit flex-middle">
                <div class="center_search">
                    <img src="@/assets/img/search.png" alt="">
                    <iframe id="rfFrame" name="rfFrame" src="about:blank" style="display:none;"></iframe>
                    <form action="about:blank" class="form_search" @submit.prevent="searchFun" target="rfFrame">
                        <div class="form-search-div">
                            <input type="search" v-model="searchData" autofocus="autofocus" placeholder="搜索活动/课程" id="searchInput" @change="changeSearchDatas" />
                            <div v-show="searchData" class="clear-button" @click="clearData">
                                <svg width="8px" height="8px" viewBox="0 0 15 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <g id="画板备份-7" transform="translate(-572.000000, -176.000000)" fill-rule="nonzero">
                                            <g id="删除" transform="translate(570.000000, 174.000000)">
                                                <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="20" height="19"></rect>
                                                <polygon id="路径" fill="#999999" points="17.8947368 15.9285714 11.1278196 9.5 17.8947368 3.07142858 16.7669173 2 10 8.42857142 3.23308272 2 2.10526316 3.07142858 8.87218044 9.5 2.10526316 15.9285714 3.23308272 17 10 10.5714286 16.7669173 17"></polygon>
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="searchRight unit-0 flex-center flex-middle">
                <!-- <div v-show="true" @click="searchFun" class="search-button">搜索</div> -->
                <div @click="goHome" class="search-button">取消</div>
            </div>
            <cube-popup type="my-popup" :mask="false" content="<i style='padding:20px;background:rgba(0,0,0,.8);color:#fff'>搜索内容不能为空</i>" ref="myPopup" />
        </div>
        <div v-show="historySearch.length &&  !searchResult[0] && searchResult.length">
            <div class="title-row">
                历史搜索
            </div>
            <div class="marker-group-radio">
                <div class="marker-group-radio-item" v-for="(item, index) in historySearch" v-show="index < 3 || historyShowAll">
                    <div @click="historyClick(item)">
                        <svg width="12px" height="12px" viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
                            <title>编组</title>
                            <desc>Created with Sketch.</desc>
                            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <g id="1.5搜索页" transform="translate(-14.000000, -203.000000)" stroke="#999999">
                                    <g id="编组-2" transform="translate(15.000000, 204.000000)">
                                        <g id="编组">
                                            <circle id="椭圆形" cx="5" cy="5" r="5"></circle>
                                            <polyline id="路径-6" points="5 2 5 5.45769617 7.99832577 5.45769617"></polyline>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </svg>
                        <span style="margin-left:5px">{{sliceTen(item)}}</span>
                    </div>
                    <div @click="deleteHistoryItem(item)">
                        <svg width="9px" height="8px" viewBox="0 0 9 8" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
                            <title>编组 45</title>
                            <desc>Created with Sketch.</desc>
                            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round">
                                <g id="1.5搜索页" transform="translate(-344.000000, -205.000000)" stroke="#999999">
                                    <g id="编组-46" transform="translate(341.000000, 201.000000)">
                                        <g id="编组-45" transform="translate(7.500000, 8.000000) rotate(45.000000) translate(-7.500000, -8.000000) translate(2.000000, 3.000000)">
                                            <line x1="5.5" y1="0" x2="5.5" y2="10" id="路径-7" transform="translate(5.500000, 5.000000) rotate(90.000000) translate(-5.500000, -5.000000) "></line>
                                            <line x1="5.5" y1="0" x2="5.5" y2="10" id="路径-7备份"></line>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </svg>
                    </div>
                </div>
                <div v-if="historySearch.length>2 && !historyShowAll" class="clear-history" @click="historyShowAll = true">全部搜索记录</div>
                <div v-if="historySearch.length>2 && historyShowAll" class="clear-history" @click="deleteSearchHistory">清除搜索记录</div>
            </div>
        </div>
        <div v-show="tagList.length && !searchResult[0] && searchResult.length">
            <div class="title-row">推荐搜索</div>
            <div class="marker-group-square">
                <div v-for="(item ,index) in tagList" @click="hotTagClick(item)">
                    <span>{{formatTag(item,9)}}</span>
                    <span v-if="index < 4">
                        <svg width="12px" height="15px" viewBox="0 0 12 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
                            <title>编组备份 2</title>
                            <desc>Created with Sketch.</desc>
                            <defs>
                                <polygon id="path-1" points="0 0 12 0 12 15 0 15"></polygon>
                            </defs>
                            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <g id="1.5搜索页" transform="translate(-49.000000, -439.000000)">
                                    <g id="编组备份-9" transform="translate(49.000000, 439.000000)">
                                        <g id="编组备份-2">
                                            <mask id="mask-2" fill="white">
                                                <use xlink:href="#path-1"></use>
                                            </mask>
                                            <g id="Clip-2"></g>
                                            <path d="M4.389352,0 C4.43605191,0.369847903 4.41128522,0.745211175 4.35535027,1.11272565 C4.21420111,1.98500244 3.84868249,2.81050466 3.3513547,3.53376042 C2.91352993,4.17216436 2.37695322,4.73695933 1.7879047,5.23387285 C1.4123115,5.53000573 1.09811031,5.89932331 0.83081892,6.29621773 C0.443996751,6.87734668 0.184786019,7.54353959 0.0711320823,8.23402134 C-0.0451454445,8.92991239 -0.0155513447,9.64786492 0.135672406,10.3358011 C0.513364481,12.0534142 1.65641035,13.5770349 3.18375974,14.4190832 C3.43614914,14.5556946 3.69347088,14.6888059 3.96569462,14.7799156 C3.84175621,14.3028362 3.67877878,13.8362572 3.57551426,13.3534503 C3.43761835,12.7302136 3.3595403,12.0815214 3.4572428,11.4458751 C3.52881435,10.9724019 3.71697825,10.5109141 4.03306843,10.1508241 C4.07903373,10.1017161 4.11786287,10.0420017 4.17998949,10.0125156 C4.33047864,10.7163616 4.51224098,11.4167073 4.79107617,12.0806729 C5.11335801,12.8554761 5.56440567,13.5866867 6.17664673,14.1611336 C6.54269007,14.5075412 6.96739688,14.7878704 7.42232746,15 C7.6336839,14.9118602 7.83181745,14.7955071 8.02953122,14.6801086 C8.47806024,14.412189 8.9069648,14.1106468 9.31330648,13.7807853 C10.0096074,13.2117477 10.6451459,12.5559492 11.1331337,11.7937676 C11.6479871,10.9948877 11.9826523,10.062154 11.9986037,9.10301012 C12.0237902,8.10016758 11.7061259,7.11609851 11.216459,6.2529433 C10.7512439,5.4300927 10.1427808,4.69877601 9.4781729,4.03364375 C9.44196735,3.99620288 9.40639147,3.95664072 9.35874706,3.93394285 C9.32915296,4.07543327 9.31666467,4.21989351 9.29934898,4.36339916 C9.22683294,5.01028828 9.13857536,5.65908657 8.9611157,6.2863537 C8.83980088,6.70212766 8.68574365,7.12129569 8.40995183,7.45964235 C8.22199781,7.69213636 7.94179836,7.86162788 7.63830142,7.85770349 C7.83895361,6.54642456 7.87264051,5.19187119 7.57648963,3.89268365 C7.36104039,2.94997985 6.94829714,2.03909548 6.29890605,1.3204005 C5.7769165,0.735665344 5.10989487,0.293693388 4.389352,0" id="Fill-1" fill="#FF6B00" mask="url(#mask-2)"></path>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </svg>
                    </span>
                </div>
            </div>
        </div>
        <cube-scroll-nav-bar style="padding:0 15px;" v-if="searchResult[0] && searchResult.length || scrollNavBarShow" :current="currentLabel" :labels="TypeLabels" @change="changeHandler" />
        <cube-scroll v-if="searchResult[0] && searchResult.length" class="item-list" ref="scroll" :data="searchResult" :options="options" @pulling-down="onPullingDown" @pulling-up="onPullingUp">
            <template v-for="(item,index) in searchResult">
                <infoItemSearchZly :key='index' :info='item' :showTag='true' :iflast='index == searchResult.length-1'></infoItemSearchZly>
            </template>
        </cube-scroll>
        <div v-if="searchResult.length == 0" class="no-content">
            暂无内容
        </div>
    </div>
</template>

<script>
import { queryHotTagList, getRecommendWords } from '@/api/configurable/common'
import { getConferenceFront } from '@/api/configurable/home'
import infoItemSearchZly from '@/components/newCommon/infoItemSearchZly.vue'
import infoItem from '@/components/newCommon/infoItem.vue'


export default {
    data () {
        return {
            searchData: '',
            historySearch: [],
            searchResult: [null],
            tagList: [],
            options: {
                pullUpLoad: {
                    threshold: 50,
                    txt: {
                        more: '上拉加载',
                        noMore: '没有更多信息啦！'
                    }
                },
                pullDownRefresh: {
                    threshold: 60,
                    stopTime: 1000,
                    txt: '更新成功'
                },
                scrollbar: false,

            },
            pageData: {
                pageNum: 1,
                pageSize: 10,
            },
            historyShowAll: false,
            scrollNavBarShow: false,
            currentLabel: '全部',
            TypeLabels: [],
            labelsOption: [
            ],
            labelsOptionParams: {}
        };
    },
    components: {
        infoItemSearchZly,
        infoItem
    },
    created () {
        this.labelsOption = [
            { name: '全部' },
            { name: '直播', typeStringList: `${this.TYPE[6]},${this.TYPE[9]}`, liveStatus: '1' },
            { name: '回看', typeStringList: `${this.TYPE[6]},${this.TYPE[9]}`, liveStatus: '0' },
            { name: '线下', typeStringList: `${this.TYPE[0]}` },
        ]
        this.TypeLabels = []
        this.labelsOption.map(item => {
            this.TypeLabels.push(item.name)
        })

    },
    computed: {
    },
    watch: {
        searchData (val) {
            if (val === '') {
                this.clearSearch()
            }
        }
    },
    mounted () {
        this.getHistorySearch()
        this.getTags()
    },
    methods: {
        getTags () {
            getRecommendWords().then(res => {
                console.log(res);
                this.tagList = []
                if (res.data) {
                    res.data.map(item => {
                        this.tagList.push(item.keyword)
                    })
                }

            })
        },
        //  改变搜索的数据
        changeSearchDatas () {
            console.log(this.searchData);
        },
        //  清空搜索框
        clearSearch () {
            this.pageData.pageNum = 1
            this.searchResult = [null]
        },
        // 从缓存中获取搜索历史
        getHistorySearch () {
            if (localStorage.getItem('historySearch' + localStorage.getItem('platformId'))) {
                this.historySearch = JSON.parse(localStorage.getItem('historySearch' + localStorage.getItem('platformId')))
            } else {
                localStorage.setItem('historySearch' + localStorage.getItem('platformId'), '[]')
                this.historySearch = []
            }
        },
        // 清除搜索历史
        deleteSearchHistory () {
            this.$createDialog({
                type: 'confirm',
                title: '清除历史搜索',
                content: '确定清除历史搜索记录',
                confirmBtn: {
                    text: '确定',
                    active: true,
                    disabled: false,
                    href: 'javascript:;'
                },
                cancelBtn: {
                    text: '取消',
                    active: false,
                    disabled: false,
                    href: 'javascript:;'
                },
                onConfirm: () => {
                    this.historySearch = []
                    localStorage.setItem('historySearch' + localStorage.getItem('platformId'), JSON.stringify(this.historySearch))
                },
                onCancel: () => {
                }
            }).show()
        },
        deleteHistoryItem (item) {
            this.historySearch.splice(this.historySearch.indexOf(item), 1)
            localStorage.setItem('historySearch' + localStorage.getItem('platformId'), JSON.stringify(this.historySearch))
        },
        //点击搜索历史
        historyClick (item) {
            this.searchData = item
            this.searchFun()
        },
        //点击热门标签
        hotTagClick (item) {
            this.searchData = item
            this.searchFun()
        },

        //取字符串前十个字符
        sliceTen (item) {
            if (item.length <= 10) {
                return item
            } else {
                return item.substring(0, 9) + '...'
            }

        },
        //搜索
        searchFun (mode) {
            if (this.searchData) {
                let params = {}

                params.pageSize = this.pageData.pageSize
                if (mode != 'next') {
                    this.pageData.pageNum = 1
                }
                params.pageNum = this.pageData.pageNum
                params.searchName = this.searchData

                params = {
                    ...params,
                    ...this.$route.query,
                    ...this.labelsOptionParams
                }

                getConferenceFront(params).then(res => {
                    if (mode == 'next') {
                        if (res.data.list.length > 0) {
                            this.searchResult = this.searchResult.concat(res.data.list)
                        }
                    } else {
                        // this.searchResult = []
                        setTimeout(() => {
                            this.searchResult = res.data.list
                        }, 20);
                    }
                    if (res.data.list.length == 0) {
                        this.pageData.pageNum--
                        this.$refs.scroll.forceUpdate()
                    }
                })

                //将搜索数据加入缓存
                let tempArray = JSON.parse(JSON.stringify(this.historySearch))
                //判断是否在数组中
                if (tempArray.indexOf(this.searchData) == -1) {
                    tempArray.unshift(this.searchData)
                } else {
                    tempArray.splice(tempArray.indexOf(this.searchData), 1)
                    tempArray.unshift(this.searchData)
                }
                this.historySearch = tempArray

                if (tempArray.length > 10) {
                    tempArray.length = 10
                }
                localStorage.setItem('historySearch' + localStorage.getItem('platformId'), JSON.stringify(this.historySearch))

            } else {
                let component = this.$refs.myPopup;
                component.show()
                setTimeout(() => {
                    component.hide()
                }, 2000)
            }
        },
        onPullingDown () {
            this.pageData.pageNum = 1
            this.searchFun()
        },
        onPullingUp () {
            this.pageData.pageNum++
            setTimeout(() => {
                this.searchFun('next')
            }, 1);
        },
        changeHandler (val) {
            this.scrollNavBarShow = true;
            this.labelsOption.map(item => {
                if (item.name == val) {
                    this.labelsOptionParams = item
                }
            })
            this.currentLabel = val
            this.searchFun()
        },
        clearData () {
            this.searchData = '';
            this.currentLabel = '全部'
            this.labelsOptionParams = {}
            this.scrollNavBarShow = false;
        },
        goHome () {
            this.$router.push({ name: 'home', query: { targetTab: '首页', local: true } })
        }
    }
}

</script>
<style  scoped>
.searchHeadZly {
    background-color: #fff;
    height: 100%;
}
.searchHeadWrap {
    margin-top: 10px;
    margin-bottom: 5px;
    width: 100%;
    padding: 0 15px;
    background-color: #fff;
}
.searchLeft {
}
.iconArrow {
    width: 9.5px;
    height: 17px;
    background: url("../../../assets/img/leftArrow.png") no-repeat;
    background-size: 100%;
}
.searchRight {
    box-sizing: border-box;
}
.center_search {
    height: 36px;
    width: 100%;
    display: flex;
    align-items: center;
    background: #f1f2f3;
    border-radius: 4px;
}
.center_search img {
    width: 12px;
    height: 12px;
    margin-right: 9px;
    vertical-align: middle;
    margin-left: 13px;
}
#searchInput {
    outline: none;
    background: transparent;
    border: none;
    font-size: 12px;
    line-height: 22px;
    width: 90%;
    padding-left: 2px;
}
input::-webkit-search-cancel-button {
    display: none;
}
input[type="search"]::-ms-clear {
    display: none;
}

#searchInput::placeholder {
    font-size: 16px;
    line-height: 18px;
    color: #999999;
    transform: translateY(2px);
}
.form_search {
    width: 100%;
}
.form-search-div {
    width: 100%;
    display: flex;
    align-items: center;
    height: 22px;
}
.search-button {
    margin-left: 8px;
    height: 35px;
    width: 35px;
    line-height: 35px;
    text-align: right;
    font-size: 14px;
    font-weight: 400;
    color: rgba(153, 153, 153, 1);
}
.item-list {
    border-top: 1px solid #eee;
    background-color: #fff;
    flex: 1;
}
</style>

<style lang='scss'>
.searchHeadZly {
    display: flex;
    flex-direction: column;
    .module {
        overflow-y: auto;
    }
    .title-row {
        margin-top: 30px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(51, 51, 51, 1);
        line-height: 20px;
        padding: 0 15px;
        div {
            font-weight: 400;
        }
    }
    .marker-group-radio {
        padding: 0 12px;
        display: flex;
        flex-direction: column;
        .marker-group-radio-item {
            height: 49px;
            font-size: 14px;
            font-weight: 400;
            color: rgba(51, 51, 51, 1);
            line-height: 20px;
            display: flex;
            flex-direction: row;
            align-items: center;
            border-bottom: 0.5px solid #eee;
            justify-content: space-between;
            > :nth-child(1) {
                display: flex;
                align-items: center;
                justify-content: left;
                width: 300px;
            }
            :nth-child(2) {
                min-width: 15px;
                min-height: 48px;
                display: flex;
                align-items: center;
            }
        }
        .clear-history {
            height: 50px;
            font-size: 14px;
            font-weight: 400;
            color: rgba(153, 153, 153, 1);
            line-height: 20px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
        }
    }
    .marker-group-square {
        padding: 0 15px;
        display: flex;
        flex-wrap: wrap;
        > div {
            margin-top: 15px;
            width: 50%;
            display: flex;
            align-items: center;
            span {
                font-size: 14px;
                font-weight: 400;
                color: rgba(51, 51, 51, 1);
                line-height: 20px;
                margin-right: 6px;
            }
        }
    }
    .no-content {
        text-align: center;
        margin-top: 30px;
        color: #999;
    }
    .clear-button {
        border-radius: 50%;
        background-color: #fff;
        height: 20px;
        width: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        // width: 16px;
        // height: 2px;
        // background: rgba(255, 107, 0, 1);
        // border-radius: 2px;
    }
    .cube-scroll-content {
        width: 100%;
    }
    .cube-scroll-nav-bar-items {
        display: flex;
        flex-direction: row;
        justify-content: space-around;
    }
    .cube-scroll-wrapper {
        text-align: left;
    }
    .cube-scroll-nav-bar-item {
        padding: 0;
        padding-bottom: 10px;
        margin: 10px 20px 0px 0px;
        position: relative;
        font-size: 14px;
        font-weight: 500;
        color: rgba(153, 153, 153, 1);
        line-height: 20px;
    }
    .cube-scroll-nav-bar-item_active {
        position: relative;
        font-size: 14px;
        font-weight: 500;
        color: rgba(51, 51, 51, 1);
        line-height: 20px;
        &::after {
            content: "";
            position: absolute;
            left: 50%;
            bottom: 0;
            transform: translateX(-50%);
            background-color: #ff6b00;
            width: 16px;
            height: 2px;
            border-radius: 2px;
        }
    }
}
</style>
