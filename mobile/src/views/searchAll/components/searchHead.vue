<!--  搜索的头部-->
<template>
    <div class="searchHead">
        <div class='searchHeadWrap  flex-left unit-0'>
            <div class="searchLeft unit flex-middle">
                <div class="center_search">
                    <img src="@/assets/img/search.png" alt="">
                    <iframe id="rfFrame" name="rfFrame" src="about:blank" style="display:none;"></iframe>
                    <form action="#" class="form_search" @submit.prevent="search" target="rfFrame">
                        <div class="form-search-div">
                            <input type="text" v-model="searchData" autofocus="autofocus" placeholder="请输入搜索关键字" id="searchInput" @change="changeSearchDatas" />
                            <div v-show="searchData" class="clear-button" @click="searchData=''">
                                <svg width="8px" height="8px" viewBox="0 0 15 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <g id="画板备份-7" transform="translate(-572.000000, -176.000000)" fill-rule="nonzero">
                                            <g id="删除" transform="translate(570.000000, 174.000000)">
                                                <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="20" height="19"></rect>
                                                <polygon id="路径" fill="#999999" points="17.8947368 15.9285714 11.1278196 9.5 17.8947368 3.07142858 16.7669173 2 10 8.42857142 3.23308272 2 2.10526316 3.07142858 8.87218044 9.5 2.10526316 15.9285714 3.23308272 17 10 10.5714286 16.7669173 17"></polygon>
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="searchRight unit-0 flex-center flex-middle">
                <div @click="search" class="search-button">搜索</div>
            </div>
            <cube-popup type="my-popup" :mask="false" content="<i style='padding:20px;background:rgba(0,0,0,.8);color:#fff'>搜索内容不能为空</i>" ref="myPopup" />
        </div>
        <div v-show="historySearch.length &&  !searchResult[0] && searchResult.length">
            <div class="title-row">
                <div>搜索历史</div>
                <div @click="deleteSearchHistory">
                    <svg width="20px" height="20px" viewBox="0 0 27 27" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <g id="画板备份-6" transform="translate(-689.000000, -269.000000)" fill-rule="nonzero">
                                <g id="删除" transform="translate(678.000000, 258.000000)">
                                    <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="48" height="48"></rect>
                                    <path d="M31.4428571,38 L17.5571429,38 C16.0685268,38 14.8571429,36.6776136 14.8571429,35.0545455 L14.8571429,20.3272727 L16.7857143,20.3272727 L16.7857143,35.0545455 C16.7857143,35.5853409 17.1382812,36.0363636 17.5571429,36.0363636 L31.4428571,36.0363636 C31.8617187,36.0363636 32.2142857,35.5853409 32.2142857,35.0545455 L32.2142857,20.3272727 L34.1428571,20.3272727 L34.1428571,35.0545455 C34.1428571,36.6776136 32.9314732,38 31.4428571,38 L31.4428571,38 Z M20.6428571,20.3272727 L22.5714286,20.3272727 L22.5714286,33.0909091 L20.6428571,33.0909091 L20.6428571,20.3272727 Z M26.9107143,20.3272727 L28.8392857,20.3272727 L28.8392857,33.0909091 L26.9107143,33.0909091 L26.9107143,20.3272727 Z M11,15.9090909 L38,15.9090909 L38,17.8727273 L11,17.8727273 L11,15.9090909 Z M32.2142857,17.8727273 L16.7857143,17.8727273 L16.7857143,13.9454545 C16.7857143,12.3223864 18.0844866,11 19.6785714,11 L29.3214286,11 C30.9155134,11 32.2142857,12.3223864 32.2142857,13.9454545 L32.2142857,17.8727273 Z M18.7142857,15.9090909 L30.2857143,15.9090909 L30.2857143,13.9454545 C30.2857143,13.4054545 29.8517857,12.9636364 29.3214286,12.9636364 L19.6785714,12.9636364 C19.1482143,12.9636364 18.7142857,13.4054545 18.7142857,13.9454545 L18.7142857,15.9090909 Z" id="形状" fill="#FF6B00"></path>
                                </g>
                            </g>
                        </g>
                    </svg>
                </div>
            </div>
            <div class="marker-group-radio">
                <div @click="historyClick(item)" v-for="item in historySearch">{{sliceTen(item)}}</div>
            </div>
        </div>
        <div v-show="tagList.length && !searchResult[0] && searchResult.length">
            <div class="title-row">热门标签</div>
            <div class="marker-group-square">
                <div v-for="item in tagList" @click="hotTagClick(item.tagName)">{{formatTag(item.tagName)}}</div>
            </div>
        </div>
        <cube-scroll class="item-list" v-if="searchResult[0] && searchResult.length" ref="scroll" :data="searchResult" :options="options" @pulling-down="onPullingDown" @pulling-up="onPullingUp">
            <infoItem v-for="(item,index) in searchResult" :key='index' :info='item' :showTag='true' :iflast='index == searchResult.length-1'></infoItem>
        </cube-scroll>
        <div v-if="searchResult.length == 0" class="no-content">
            暂无内容
        </div>
    </div>
</template>

<script>
import { queryHotTagList } from '@/api/configurable/common'
import { getConferenceFront } from '@/api/configurable/home'
import infoItem from '@/components/newCommon/infoItem.vue'

export default {
    data () {
        return {
            searchData: '',
            historySearch: [],
            searchResult: [null],
            tagList: [],
            options: {
                pullUpLoad: {
                    threshold: 50,
                    txt: {
                        more: '上拉加载',
                        noMore: '没有更多信息啦！'
                    }
                },
                pullDownRefresh: {
                    threshold: 60,
                    stopTime: 1000,
                    txt: '更新成功'
                },
                scrollbar: false,

            },
            pageData: {
                pageNum: 1,
                pageSize: 10,
            },
        };
    },
    components: {
        infoItem
    },
    created () {
    },
    computed: {
    },
    watch: {
        searchData (val) {
            if (val === '') {
                this.clearSearch()
            }
        }
    },
    mounted () {
        this.getHistorySearch()
        this.getTags()
    },
    methods: {
        getTags () {
            queryHotTagList().then(res => {
                console.log(res);
                this.tagList = res.data.list
            })
        },
        //  改变搜索的数据
        changeSearchDatas () {
            console.log(this.searchData);
        },
        //  清空搜索框
        clearSearch () {
            this.pageData.pageNum = 1
            this.searchResult = [null]
        },
        // 从缓存中获取搜索历史
        getHistorySearch () {
            if (localStorage.getItem('historySearch' + localStorage.getItem('platformId'))) {
                this.historySearch = JSON.parse(localStorage.getItem('historySearch' + localStorage.getItem('platformId')))
            } else {
                localStorage.setItem('historySearch' + localStorage.getItem('platformId'), '[]')
                this.historySearch = []
            }
        },
        // 清除搜索历史
        deleteSearchHistory () {
            this.$createDialog({
                type: 'confirm',
                title: '删除搜索',
                content: '确定删除搜索历史记录',
                confirmBtn: {
                    text: '确定',
                    active: true,
                    disabled: false,
                    href: 'javascript:;'
                },
                cancelBtn: {
                    text: '取消',
                    active: false,
                    disabled: false,
                    href: 'javascript:;'
                },
                onConfirm: () => {
                    this.historySearch = []
                    localStorage.setItem('historySearch' + localStorage.getItem('platformId'), JSON.stringify(this.historySearch))
                },
                onCancel: () => {
                }
            }).show()
        },
        //点击搜索历史
        historyClick (item) {
            this.searchData = item
            this.search()
        },
        //点击热门标签
        hotTagClick (item) {
            this.searchData = item
            this.search()
        },

        //取字符串前十个字符
        sliceTen (item) {
            if (item.length <= 10) {
                return item
            } else {
                return item.substring(0, 9) + '...'
            }

        },
        //搜索
        search (mode) {
            if (this.searchData) {
                let params = {}

                params.pageSize = this.pageData.pageSize
                if (mode != 'next') {
                    this.pageData.pageNum = 1
                }
                params.pageNum = this.pageData.pageNum
                params.searchName = this.searchData

                params = Object.assign(params, this.$route.query)

                getConferenceFront(params).then(res => {
                    if (mode == 'next') {
                        this.searchResult = this.searchResult.concat(res.data.list)
                    } else {
                        this.searchResult = []
                        setTimeout(() => {
                            this.searchResult = res.data.list
                        }, 20);
                    }
                    if (res.data.list.length == 0) {
                        this.pageData.pageNum--
                        this.$forceUpdate()
                    }
                })

                //将搜索数据加入缓存
                let tempArray = JSON.parse(JSON.stringify(this.historySearch))
                //判断是否在数组中
                if (tempArray.indexOf(this.searchData) == -1) {
                    tempArray.unshift(this.searchData)
                } else {
                    tempArray.splice(tempArray.indexOf(this.searchData), 1)
                    tempArray.unshift(this.searchData)
                }
                this.historySearch = tempArray

                if (tempArray.length > 10) {
                    tempArray.length = 10
                }
                localStorage.setItem('historySearch' + localStorage.getItem('platformId'), JSON.stringify(this.historySearch))

            } else {
                let component = this.$refs.myPopup;
                component.show()
                setTimeout(() => {
                    component.hide()
                }, 2000)
            }
        },
        onPullingDown () {
            this.pageData.pageNum = 1
            this.search()
        },
        onPullingUp () {
            this.pageData.pageNum++
            setTimeout(() => {
                this.search('next')
            }, 300);
        },
    }
}

</script>
<style  scoped>
.searchHead {
    background-color: #fff;
    height: 100%;
}
.searchHeadWrap {
    width: 100%;
    height: 46px;
    padding: 0 12px;
    background-color: #fff;
}
.searchLeft {
    width: 45px;
    height: 46px;
}
.iconArrow {
    width: 9.5px;
    height: 17px;
    background: url("../../../assets/img/leftArrow.png") no-repeat;
    background-size: 100%;
}
.searchRight {
    height: 46px;
    box-sizing: border-box;
}
.center_search {
    height: 35px;
    width: 100%;
    display: flex;
    align-items: center;
    background: #f5f5f5;
    border-radius: 4px;
}
.center_search img {
    width: 12px;
    height: 12px;
    margin-right: 9px;
    vertical-align: middle;
    margin-left: 13px;
}
#searchInput {
    outline: none;
    background: transparent;
    border: none;
    font-size: 12px;
    line-height: 12px;
    width: 90%;
    padding-left: 2px;
}
#searchInput::placeholder {
    font-size: 12px;
}
.form_search {
    width: 100%;
}
.form-search-div {
    width: 100%;
    display: flex;
}
.search-button {
    margin-left: 8px;
    height: 35px;
    width: 60px;
    line-height: 35px;
    text-align: center;
    color: #333;
    border-radius: 15px;
    background: #f5f5f5;
    border-radius: 4px;
}
.item-list {
    border-top: 10px solid #f5f5f5;
    background-color: #fff;
}
</style>


<style lang='scss' scoped>
.module {
    overflow-y: auto;
}
.title-row {
    margin-top: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 5px 10px;
    font-size: 16px;
    font-weight: 400;
    div {
        font-weight: 400;
    }
}
.marker-group-radio {
    padding: 0 12px;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 50px;
    div {
        font-size: 12px;
        color: #666;
        height: 25px;
        line-height: 25px;
        text-align: center;
        padding: 0px 10px;
        margin: 5px 5px;
        background: #f5f5f5;
        border-radius: 4px;
    }
}
.marker-group-square {
    padding: 0 12px;
    display: flex;
    flex-wrap: wrap;
    div {
        font-size: 12px;
        color: #666;
        height: 25px;
        line-height: 25px;
        text-align: center;
        padding: 0px 10px;
        margin: 5px 5px;
        background: #f5f5f5;
        border-radius: 4px;
    }
}
.no-content {
    text-align: center;
    margin-top: 30px;
    color: #999;
}
.clear-button {
    border-radius: 50%;
    background-color: #fff;
    height: 20px;
    width: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>