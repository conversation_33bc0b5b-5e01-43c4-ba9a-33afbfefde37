<!-- 全局搜索界面 -->
<template>
    <div class='searchAllWrap flex-vertical'>
        <!-- 头部的搜索的输入框 -->
        <SearchHead v-if="!systemStyle"  />
        <searchHeadZly v-if="systemStyle == 'zly'"  />
    </div>
</template>
<script>
import SearchHead from './components/searchHead';
import searchHeadZly from './components/searchHeadZly';
export default {
    name: "searchAll",
    data () {

    },

    components: {
        SearchHead,
        searchHeadZly
    },

    computed: {
    },
    created () {
    },
    mounted () {
    },
    activated () {
    },

    methods: {},
}

</script>
<style  scoped>
.searchAllWrap {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    background: #fff;
    overflow: hidden;
}
</style>