<template>
    <div class="personProfileWrap">
        <!-- <div class="personProfileTop" >
           <i class="back" @click="goback"></i>
          个人资料
        </div> -->
        <div class="personProfileMiddle">
            <div class="personProfileImg flex-center flex-middle">
                <img v-if="personProfile.headImg" :src="personProfile.headImg" alt="">
                <img v-else src="../../assets/img/head_pic_no.png" alt="">
                <!-- <cube-upload ref="upload" style="opacity:0" class="uploadfile" :action="action" :simultaneous-uploads="1" @file-success="successLoad" @files-added="filesAdded" /> -->
            </div>
            <!-- <div class="personProfileText">点击头像修改</div> -->
        </div>
        <div class="personProfileBottom">
            <div class="personProfileItem flex-left">
                <div class="unit-1-2 personProfile_left">手机</div>
                <div class="unit-1-2 personProfile_right">{{personProfile.mobile}}</div>
            </div>
            <div class="personProfileItem flex-left" @click="showOpen(personProfile.memberName,'memberName')">
                <div class="unit-1-2 personProfile_left">昵称</div>
                <div class="unit-1-2 personProfile_right">{{personProfile.memberName}}</div>
            </div>
            <div class="personProfileItem flex-left" @click="showOpen(personProfile.company,'company')">
                <div class="unit-1-2 personProfile_left">公司</div>
                <div class="unit-1-2 personProfile_right">{{personProfile.company}}</div>
            </div>
            <div class="personProfileItem flex-left" @click="showOpen(personProfile.position,'position')">
                <div class="unit-1-2 personProfile_left">职位</div>
                <div class="unit-1-2 personProfile_right">{{personProfile.position}}</div>
            </div>
            <div class="personProfileItem flex-left" @click="showOpen(personProfile.email,'email')">
                <div class="unit-1-2 personProfile_left">邮箱</div>
                <div class="unit-1-2 personProfile_right">{{personProfile.email}}</div>
            </div>
            <!-- <div class="personProfileItem flex-left">
                <div class="unit-1-2 personProfile_left">个人认证</div>
                <div class="unit-1-2 personProfile_right">生鲜小白</div>
            </div> -->
        </div>
        <SetInfo v-if="showSet" />
    </div>
</template>

<script>
import SetInfo from '@/components/common/setInfo';
const $local = require('store');
import CONSTANT from '@/config/config_constant';
import { baseUrl } from '@/config/env';
export default {
    name: "personProfile",
    components: {
        SetInfo
    },
    data () {
        return {
            action: {
                target: baseUrl + '/member/upload',
                headers: {
                    Authorization: $local.get(CONSTANT.token)
                }
            }
        }
    },
    computed: {
        personProfile () {
            return this.$store.state.personal.personProfile;
        },
        showSet () {
            return this.$store.state.personal.showSet;
        }
    },
    created () {
        this.EnterExposure(44);
        this.$store.dispatch('getPerprofile');
    },
    beforeRouteLeave (to, from, next) {
        this.computTimeDiff(from.meta.id);
        next();
    },
    methods: {
        goback () {
            this.$router.back();
        },
        showOpen (data1, data2) {
            // this.showSet=true;
            this.$store.commit('changeShowSet', true);
            this.$store.commit('changeSetInfo', { content: data1, tip: data2 });
        },
        filesAdded (files) {
            let hasIgnore = false
            const maxSize = 3 * 1024 * 1024;
            for (let k in files) {
                const file = files[k]
                if (file.size > maxSize) {
                    file.ignore = true
                    hasIgnore = true
                }
            }
            hasIgnore && this.$createToast({
                type: 'warn',
                time: 1000,
                txt: '图片过大,不能超过3M'
            }).show()
        },
        successLoad (e) {
            //console.log(e.response)
            this.$store.commit('changeHeadImg', e.response.data);

        }
    }
}
</script>

<style scoped>
.personProfileWrap {
    width: 100%;
    overflow: hidden;
}
.personProfileTop {
    width: 100%;
    height: 45px;
    background: #004da1;
    position: relative;
    text-align: center;
    line-height: 45px;
    color: #fff;
    font-size: 17px;
}
.back {
    position: absolute;
    top: 11px;
    left: 16px;
    width: 17px;
    height: 22px;
    background: url("../../assets/img/back.png") no-repeat 0 0;
    background-size: 100% 100%;
}
.personProfileMiddle {
    height: 153px;
    background: url("../../assets/img/persoanBg.png") no-repeat;
    background-size: 100% 100%;
    position: relative;
    overflow: hidden;
}
.personProfileImg {
    width: 74px;
    height: 60px;
    margin: 0 auto;
    margin-top: 36px;
    position: relative;
}
.personProfileImg img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
}
.uploadfile {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    position: absolute;
    z-index: 10;
}
.personProfileText {
    width: 100%;
    height: 42px;
    line-height: 42px;
    text-align: center;
    color: #f0d8a4;
    font-size: 12px;
}
.personProfileBottom {
    width: 100%;
}
.personProfileItem {
    height: 45px;
    width: 100%;
    line-height: 45px;
    padding: 0 12px;
    border-bottom: 1px solid #e4e3e3;
}
.personProfile_left {
    color: #333;
    font-size: 14px;
}
.personProfile_right {
    color: #808080;
    font-size: 12px;
    text-align: right;
}
</style>


