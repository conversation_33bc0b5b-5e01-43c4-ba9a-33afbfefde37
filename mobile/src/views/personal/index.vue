<template>
    <div class="flex-vertical personWrap">
        <div class="unit-0 personTop">
            <div class="personTop_center flex-vertical flex-center">
                <div class="flex-vertical flex-center flex-middle personTop_center_img">
                    <img v-if="personProfile.headImg" class="avater" :src="personProfile.headImg" alt="">
                    <img v-else class="avater" src="../../assets/img/head_pic_no.png" alt="">
                </div>
                <p class="personal_username">{{personProfile.memberName}}</p>
            </div>
            <div class="setting" @click="personProfile">设置</div>
        </div>
        <div class="unit personBottom flex-vertical">
            <cube-tab-bar v-model="selectedLabelDefault" :data="tabs" @click="clickHandler" class="unit-0 person_tab">
            </cube-tab-bar>
            <div class="unit personBottomWrap" ref="personBottomWrap">
                <div class="personBottomContent">
                    <cube-scroll ref="personBottomScroll" :data="changeMyProject?myConcernData:myCollectData" :options="options" @pulling-down="onPullingDown" @pulling-up="onPullingUp" class="lecturerScr">
                        <div class="chartLists">
                            <chart-list v-for="collectItem,index in myCollectData" :key="index" :item="collectItem" @click.native="tz_collects(collectItem.id,collectItem.courseType)"></chart-list>
                        </div>
                    </cube-scroll>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import ChartList from '@/components/common/chartList';
export default {
    name: "personal",
    components: {
        ChartList,
    },
    data () {
        return {
            pageNumConcern: 1,
            pageNumCollect: 1,
            personalItem: [],
            changeMyProject: false,
            options: {
                pullUpLoad: {
                    threshold: 50,
                    txt: {
                        // more: '上拉加载',
                        noMore: '没有更多信息啦！'
                    }
                },
                pullDownRefresh: {
                    threshold: 60,
                    stopTime: 1000,
                    txt: '更新成功'
                },
                scrollbar: false
            },
            selectedLabelDefault: '我的收藏',
            tabs: [
                //     {
                //     label: '我的关注',
                // }, 
                {
                    label: '我的收藏',
                },]
        }
    },
    created () {
        // 重置页数
        this.EnterExposure(43);
        this.pageNumConcern = 1;
        this.pageNumCollect = 1;
        //  个人资料
        this.$store.dispatch('getPerprofile');
        //  我的关注的数据
        this.$store.dispatch('getMyConcern', { pageNum: this.pageNumConcern, pageSize: 5 });
        // // 我的收藏数据
        this.$store.dispatch('getMyCollects', { pageNum: this.pageNumCollect, pageSize: 5 });
    },
    beforeRouteLeave (to, from, next) {
        this.computTimeDiff(to.meta.id);
        next();
    },
    computed: {
        personProfile () {
            return this.$store.state.personal.personProfile;
        },
        myConcernData () {
            return this.$store.state.personal.myConcernData;
        },
        myCollectData () {
            return this.$store.state.personal.myCollectData;
        },
        // 关注的总页数
        myConcernTotal () {
            return this.$store.state.personal.myConcernDataTotal;
        },
        //  收藏的总页数
        myCollectTotal () {
            return this.$store.state.personal.myCollectDataTotal;
        }
    },
    methods: {
        clickHandler (tab) {
            if (tab == '我的关注') {
                this.changeMyProject = true;

            } else if (tab == '我的收藏') {
                this.changeMyProject = false;
            }
        },
        onPullingDown () {
            if (this.changeMyProject) {
                this.refreshData(this.pageNumConcern, 'getMyConcern');
            } else {
                this.refreshData(this.pageNumCollect, 'getMyCollects');
            }
        },
        //  上啦加载更多的数据
        onPullingUp () {
            if (this.changeMyProject) {
                this.moreLoadData(this.myConcernData, this.myConcernTotal, this.pageNumConcern, 'getMyConcern');
            } else {
                this.moreLoadData(this.myCollectData, this.myCollectTotal, this.pageNumCollect, 'getMyCollects');
            }
        },
        personProfile () {
            this.pageDataAction(44, 43, 44);
            this.$router.push('/personProfile');
        },
        //  加载更多的封装吧函数  
        moreLoadData (data1, data2, pageNum, funName) {
            this.personalItem = data1;
            setTimeout(() => {
                if (data1.length < data2) { //判断是不是加载完毕
                    pageNum++;
                    this.$store.dispatch(funName, { pageNum, pageSize: 5 });
                    this.$nextTick(() => {
                        this.$refs.personBottomScroll.refresh();
                    })
                }
                else {
                    this.$refs.personBottomScroll.forceUpdate(); //没有更多信息啦！
                }
            }, 1000);
        },
        refreshData (pageNum, funName) {
            pageNum = 1;
            this.$store.dispatch(funName, { pageNum, pageSize: 5 });
            this.$nextTick(() => {
                this.$refs.personBottomScroll.refresh();
            })
        },
        tz_lecturers (index, type) {
            console.log(index)
            this.pageDataAction(42, 43, 39, index);
            if (type) {
                this.naviToDetails(this.$route.query.platformId, 2, { index })
                // this.$router.push(`/lecturer/detail/${index}`);
            } else {
                console.warn('现在没有伙伴类型了不知道怎么改');
                this.$router.push(`/partnerDetail/${index}`);
            }

        },
        tz_collects (id, type) {
            this.pageDataAction(43, 43, 34, id);
            if (type) {
                //跳转音频课
                this.naviToDetails(this.$route.query.platformId, 1, { id, courseType: 1 })
            } else {
                //跳转视频课
                this.naviToDetails(this.$route.query.platformId, 1, { id, courseType: 0 })
            }

        }
    }
}
</script>

<style scoped>
.personWrap {
    width: 100%;
    height: 100%;
    position: relative;
}
.personTop {
    width: 100%;
    height: 183px;
    background: url("../../assets/img/persoanBg.png") no-repeat;
    background-size: 100% 100%;
    position: relative;
}
.personTop .personTop_logo {
    position: absolute;
    width: 65px;
    height: 38px;
    top: 15px;
    left: 20px;
}
.personTop_center {
    height: 100%;
    margin: 0 auto;
    padding-top: 26px;
    box-sizing: border-box;
}
.personBottom {
    width: 100%;
    overflow: hidden;
}
.personTop_center_img {
    width: 100%;
    width: 60px;
    height: 60px;
    border-radius: 50%;
}
.personTop_center_img > .avater {
    width: 60px;
    height: 60px;
    border-radius: 50%;
}
.personal_username {
    color: #fff;
    font-size: 15px;
    text-align: center;
    height: 31px;
    line-height: 31px;
    white-space: nowrap;
}
.alias {
    width: 67px;
    height: 22px;
    border: 1px solid #be9d75;
    border-radius: 22px;
    color: #fff;
    text-align: center;
    line-height: 22px;
    font-size: 11px;
}
.setting {
    width: 40px;
    height: 15px;
    position: absolute;
    top: 18px;
    right: 17px;
    color: #fff;
    background: url("../../assets/img/rightArrow.png") no-repeat 34px 2px;
    background-size: 6px 10px;
}
.person_tab {
    height: 40px;
    border-bottom: 1px solid #e4e3e3;
}
.personBottomWrap {
    overflow: hidden;
}
.personBottomContent {
    height: 100%;
    overflow: hidden;
}
.chartLists {
    padding: 0 12px;
    box-sizing: border-box;
}
</style>

