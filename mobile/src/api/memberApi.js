/**
 * 注意事项
 * GET方法传参的时候需要用大括号包起来
 */
import Axios from "@/plugins/http";
import { memberAutjApi, platformBizApi } from "../config/env";

const getMemberLevelInfo = (params) => {
  return Axios("memberAutjApi").get(`/front/memberLevel/getMemberLevelInfo`, { params });
};
const getMemberPoints = (params) => {
  return Axios("memberAutjApi").get(`/front/memberLevel/getMemberPoints`, { params });
};

// 查询会员等级开关状态
const getMemberLevelSwitchStatus = (params) => {
  return Axios("memberAutjApi").get(`/front/memberLevel/getMemberLevelSwitchStatus`, { params });
};

const getSettingApi = (params) => {
  return Axios("platformBizApi").get(`${platformBizApi}/officialMenuManager/coin/setting`, { params });
}; // 获取我的易币设置内容
const getMonthExpiresPointsList = (params) => {
  return Axios("memberAutjApi").get(`${memberAutjApi}/front/points/getMonthExpiresPointsList`, { params });
}; // 查询易币过期积分明细
const getPointPage = (params) => {
  return Axios("memberAutjApi").get(`${memberAutjApi}/front/points/page`, { params });
}; // 查询会员积分明细

// 获取用户的签到信息
const getSignInfo = (params) => Axios("memberAutjApi").get(`/front/signIn/home`, { params });

// 用户签到
const userSignIn = (params) => Axios("memberAutjApi").post(`/front/signIn?platformId=${params.platformId}`, params);

// 连续签到
const getCenterSignInInfo = (params) => Axios("memberAutjApi").get(`/front/signIn`, { params });

// 每日看直播
const getLiveTask = () => Axios(3).get(`/officialMenu/mission/daily`);

// 每日看产品
const getProductTask = (params) => Axios(3).get(`/officialMenu/mission/product`, { params });

// 其他任务
const getOtherTask = (params) => Axios("memberAutjApi").get(`/front/mission/daily`, { params });

// 新手任务
const getNewTask = (params) => Axios("memberAutjApi").get(`/front/mission/new`, { params });

const saveMemberAuth = (params) => Axios("memberAutjApi").post(`/memberAuthFront/saveMemberAuth`, params); // 提交认证信息
// 获取认证信息
const getMemberAuthStatus = (params) => Axios("memberAutjApi").get(`/memberAuthFront/getMemberAuthStatus`, { params });
const getPersonApi = (params) => Axios("platformBizApi").get(`${platformBizApi}/officialMenuManager/person/setting`, { params });


// 积分触发
const postTriggerPoints = (params) => Axios("memberAutjApi").post(`/front/points/trigger`, params);

// 获取易币商城是否可以进入
const getMallCanEnter = (params) => Axios(3).get(`/officialMenu/mall/status`, { params });


// 针对bioChina活动的获取实名认证信息
const getBioChinaInfo = (params) => Axios("memberAutjApi").get(`/memberAuthFront/getBusinessCard`, { params });

// 一键认证
const getOneKeyAuth = (params) => Axios("memberAutjApi").post(`/memberAuthFront/saveMemberActivityAuth`, params);


export {
  getMemberLevelInfo,
  getMemberPoints,
  userSignIn,
  getSignInfo,
  getCenterSignInInfo,
  getMemberLevelSwitchStatus,
  getSettingApi,
  getMonthExpiresPointsList,
  getPointPage,
  getLiveTask,
  getNewTask,
  saveMemberAuth,
  getMemberAuthStatus,
  getPersonApi,
  getOtherTask,
  getProductTask,
  postTriggerPoints,
  getMallCanEnter,
  getBioChinaInfo,
  getOneKeyAuth,
};
