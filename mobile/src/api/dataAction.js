
import Axios from '@/plugins/http';
//  点击页面按钮的埋点接口
const dataActionClick=(data)=>{return Axios(2).post(`/dataAction/click/${data.systemType}`,data)}
//   页面的曝光
const  dataActionExposure=(data)=>{return Axios(2).post(`/dataAction/pageStart/exposure/${data.systemType} `,data)}
//  页面的停留时间
const  dataActionStay=(data)=>{return Axios(2).post(`/dataAction/stay/${data.systemType}`,data)}
// 
const  getUserInfo = (params) => {
  return Axios(1).get(`/member/wx/${params.openId}`);
}
export {
  dataActionClick,
  dataActionExposure,
  dataActionStay,
  getUserInfo
}