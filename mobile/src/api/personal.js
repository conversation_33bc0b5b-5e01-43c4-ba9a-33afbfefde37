import Axios from '@/plugins/http'

//  获取个人资料的
const getPerPri=()=>{return Axios(0).get(`member/center.json`)}
// 我的关注
const getConcern=(params)=>{return Axios(0).get(`member/center/listLecturer.json`,{params})}
//  我的收藏
const getMyCollect=(params)=>{return Axios(0).get(`member/center/listCourse.json`,{params})}
// 我的设置
const mySetting=()=>{return Axios(1).get(`member/getUserInfo.json`)}
// 我的设置上传
const headPortraitUpload=(params)=>{return Axios(1).put(`member.json`,params)}

export {
  getPerPri,
  getConcern,
  getMyCollect,
  mySetting,
  headPortraitUpload
}