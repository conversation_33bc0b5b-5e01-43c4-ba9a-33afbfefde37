import Axios from '@/plugins/http';

//  音频更多的列表数据
const audioList = (params) => { return Axios(0).get(`/audio.json`, { params }) };

//  获取音频课程标签
const getAudioClassify = () => {return Axios(0).get(`common/getCodeValueByPid/root_system_info_industryActivity_channel_medicalMedicine.json `)};

//  音频列表的具体的数据
const getAudioMList = (params) => { return Axios(0).get(`/courseInfo/list.json`, { params }) };

//  音频课程简介
const getAudioAbstractList = (params) => { return Axios(0).get(`/courseInfo/${params.id}.json`) };

//  音频章节的数据
const getAudioChapterInfo = (params) => { return Axios(0).get(`/courseChapter/courseCascade/${params.courseId}.json`) };

//  是否加入学习
const courseLearn = (params) => { return Axios(0).put(`/courseJoinDetail/join/${params.courseId}.json?platformId=${params.platformId}`) };

const collectInfo = (params) => { return Axios(0).get(`/courseCollectDetail/${params.courseId}.json`) }//获取收藏信息

const collect = (params) => { return Axios(0).put(`/courseCollectDetail/collect/${params.courseId}.json?platformId=${params.platformId}`) }//收藏

const unCollect = (params) => { return Axios(0).put(`/courseCollectDetail/uncollect/${params.courseId}.json?platformId=${params.platformId}`) }//取消收藏

const goodInfo = (params) => { return Axios(0).get(`/coursePraiseDetail/${params.courseId}.json`) }//获取点赞信息

const good = (params) => { return Axios(0).put(`/coursePraiseDetail/praise/${params.courseId}.json?platformId=${params.platformId}`) }//点赞

const unGood = (params) => { return Axios(0).put(`/coursePraiseDetail/uncollect/${params.courseId}.json?platformId=${params.platformId}`) }//取消点赞

//当前整门课程的学习进度
const getCourseCurrentTime = (params) => { return Axios(0).get(`courseCurrentTime/${params.courseId}.json`) }

//  发送每门课程的进度
const postCourseCurrentTime = (params) => { return Axios(0).post(`courseCurrentTime.json`, params) }

//  发送课程的学习时长
const courseLearnTime = (params) => { return Axios(0).post(`courseLearn.json`, params) }


export {
  audioList,
  getAudioClassify,
  getAudioMList,
  getAudioAbstractList,
  getAudioChapterInfo,
  courseLearn,
  collectInfo,
  collect,
  unCollect,
  goodInfo,
  good,
  unGood,
  getCourseCurrentTime,
  postCourseCurrentTime,
  courseLearnTime,
}
