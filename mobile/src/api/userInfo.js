import Axios from '@/plugins/http'


// 新获取个人信息--用于查看表单
const getPersonalInfo = (params) => Axios('signOrderUrl').get(`signFront/getMemberSignDetailDto`, { params });

// 判断是否发布行家活动
const isPlatformForm = (params) => {
    return Axios(8).get(`/platformItemRel/info/${params.itemType}/${params.itemId}`);
}


//直播-获取formid(获取下边表单前准备)getTicketList
const getformId = (params) => { 
    return Axios('productUrl').get(`/ticketManager/getTicketInfoList.json`,{params})
}

//根据表单id查询表单内容
const getFormFields = (params) => {
    return Axios(8).get(`/formField/${params.formId}.json`, { params});
}


// 新版查询填写过的数据
const checkNewFormInfo = (params) => {
    return Axios('signOrderUrl').get(`/signFront/listLastContents`,{params});
};

//编辑表单
const editSignMemberInfo = (params) => { 
    return Axios('signOrderUrl').put(`/signFront/updateContentByMember`,params);
}
export {
    getPersonalInfo,
    getFormFields,
    isPlatformForm,
    getformId,
    checkNewFormInfo,
    editSignMemberInfo
}