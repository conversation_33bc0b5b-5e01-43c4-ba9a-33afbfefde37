import Axios from '@/plugins/http'
//获取首页配置信息
const getOfficialMenu=(params)=>{return Axios(3).get(`/officialMenu`,{params})}


const getOfficialMenuPreview=(params)=>{return Axios(3).get(`/officialMenu/preview`,{params})}

// 获取广告信息
const getAdvertisement = (params) => { return Axios(3).get(`/officialMenu/dialog`, { params }) }


//请求视图
const getConferenceFront=(params)=>{return Axios(3).get(`/conferenceFront`,{params})}


//增加标签的点击数
const addClickTagNumByRelIdy=(params)=>{return Axios(3).post(`/conferenceFront/addClickNum/${params.relId}`)}

//获取联系主办方信息
const getContactMessage=(params)=>{return Axios(3).get(`/conferenceFront/getContactMessage`,{params})}

//查询活动相关的直播列表
const getLiveInfoListByActivityId=(params)=>{return Axios(6).get(`liveInfo/listByActivityId`,{params})}

//返回formId
const getContectSponsorFormPageFormId=(params)=>{return Axios(3).get(`/conferenceFront/getFormId`,{params})}

//通过formId 查询 表单内容
const getFormById=(params)=>{return Axios(3).get(`/businessFormField/${params.formId}`)}

//获取填写过的表单
const getMemberAdvice=(params)=>{return Axios(3).get(`/conferenceFront/getMemberAdvice`,{params})}

//联系会务组填写表单
const setMemberAdvice=(params)=>{return Axios(3).post(`/conferenceFront/setMemberAdvice`,params)}

// 获取新表单的字段
let getFormFields = (params) => {
    return Axios(8).get(`/formField/${params.formId}.json`, {params});
}


export {
    getOfficialMenu,
    getConferenceFront,
    getLiveInfoListByActivityId,
    getContactMessage,
    addClickTagNumByRelIdy,
    getContectSponsorFormPageFormId,
    getFormById,
    getMemberAdvice,
    setMemberAdvice,
    getFormFields,
    getOfficialMenuPreview,
    getAdvertisement
}