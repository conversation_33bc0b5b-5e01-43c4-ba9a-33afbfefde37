import Axios from '@/plugins/http'
const getInfoByDynamicUrl=(url,params)=>{return Axios(4).get(url,{params})}

//请求搜索栏的标签
const queryHotTagList=(params)=>{return Axios(3).get(`/conferenceFront/queryHotTagList`,{params})}

//请求推荐搜索
const getRecommendWords = () =>{ return Axios(3).get(`/keyword/${localStorage.getItem('platformId')}.json`)}


// 我的预约活动 signOrderUrl
const getMyBook = (params) =>{ return Axios('signOrderUrl').get(`/book/pageList`,{params})};

// 行家关注领域
const getIndusHangtryList=(params)=>{return Axios("textLiveApi").get(`/officialMenu/industry`,{params})}

export {
    getInfoByDynamicUrl,
    queryHotTagList,
    getRecommendWords,
    getMyBook,
    getIndusHangtryList,
}