
import Axios from '@/plugins/http';

//  音频更多的列表数据
const audioList=(params)=>{return Axios(0).get(`/audio.json`,{params})}

//  音频点赞
const audioParise=(params)=>{return Axios(0).put(`audio/${params.id}.json?platformId=${params.platformId}`)}

//  章节课程的点赞
const courseChapterParise =  (params)=>{return Axios(0).put(`courseChapter/${params.id}.json?platformId=${params.platformId}`)}

//  音频更多的封面图片
const coverImg = ()=>{return Axios(0).get(`top/4.json`)}


export {
  audioList,
  audioParise,
  courseChapterParise,
  coverImg
}