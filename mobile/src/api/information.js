
/**
 * 注意事项
 * GET方法传参的时候需要用大括号包起来
 */
import Axios from '@/plugins/http'
const getList=(params)=>{return Axios(0).get(`/information.json`,{params})}
const getContent=(params)=>{return Axios(0).get(`/information/${params.id}.json`)}
// 获取资讯列表
const getInformationList=(params)=>{return Axios(3).get(`/conference/front/news/queryOfficialNewsList`,{params})}

// 获取资讯列表详情
const getInformationDetail=(params)=>{return Axios(3).get(`/conference/front/news/${params.id}`,{params})}

export {
    getList,
    getContent,
    getInformationList,
    getInformationDetail
}