// 医药系统  活动
import Axios from '@/plugins/http'


// 获取 活动首页 布局信息
let getActivityHomeList;
if (process.env.VUE_APP_CURRENTMODE.indexOf('Preview')>-1) { // 预览环境
  getActivityHomeList = (layoutId) => Axios(3).get(`/layout/selectActivity/${layoutId}`);
} else {
  getActivityHomeList = (layoutId) => Axios(3).get(`/front/layout/selectActivity/${layoutId}`);
}

// 获取 活动列表
const  getActivityList = (params) => Axios(3).get(`/conference/front/list/${params.platformId}`, {params})

// 获取活动列表 自定义活动 更多
const getActivityListByLayoutId = (params) => Axios(3).get(`/front/layout/activityList/${params.platformId}/${params.layoutId}`, {params})

// 线下活动
let selectLoadingActivityList
if (process.env.VUE_APP_CURRENTMODE.indexOf('Preview')>-1) { // 预览环境
  selectLoadingActivityList = (params) => Axios(3).get(`/layout/selectLoadingActivityList/${params.layoutId}`, {params})
} else {
  selectLoadingActivityList = (params) => Axios(3).get(`/front/layout/selectLoadingActivityList/${params.layoutId}`, {params})
}

export {
  getActivityHomeList,
  getActivityListByLayoutId,
  getActivityList,
	selectLoadingActivityList,
}