// 企业直播间
import Axios from '@/plugins/http'
import {exhibitorUrl} from '@/config/env';

// 获取企业直播间 信息
export const getLiveRoomInfo = (params) => Axios(3).get(`/enterpriseLiveRoom/front/${params.relId}`, {params})

// 获取 企业直播间 关联 直播
export const getLiveList = (params) => Axios(3).get(`/enterpriseLiveRoom/front/getItemInfoDtoByLiveRoom/${params.relId}`, {params})

// 查询 是否已收藏 企业直播间
export const getCollectFlag = (params) => Axios(3).post(`/enterpriseLiveRoom/front/memberCollectFlag`, params)

// 收藏 、 取消收藏 企业直播间
export const collectLiveRoom = (params) => Axios(3).post(`/enterpriseLiveRoom/front/collect/${params.status}`, params)

// 获取企业直播间  分享设置
export const getLiveRoomShareConfig = (params) => Axios(3).get(`/shareSettings/front`, {params})

// 查询企业直播间列表
export const getLiveRoomList = (params) => Axios(3).get(`layout/home/<USER>/liveRoom/getListRelAll`, { params })

export const  getPlatformInfo = (params) => {return Axios(3).get(`/website`, {params});
}
// 商家详情
export const  selExhibitorDetails = (params) => {return Axios().post(`${exhibitorUrl}/pcExhibitorApi/selExhibitorDetails`, params);}
export const  selExhibitorDetailsRePro = (params) => {return Axios().post(`${exhibitorUrl}/pcExhibitorApi/selExhibitorDetailsRePro`, params);}