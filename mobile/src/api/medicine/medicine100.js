import Axios from '@/plugins/http'
//创始理事单位，理事单位
const getCompanylist=(params, preview)=>{return Axios(3).get(`/bio/${preview}company/list/${params.id}`,{params})}
//年度主席团，会员单位
const getGuestlist=(params, preview)=>{return Axios(3).get(`/bio/${preview}/guest/list/${params.layoutId}`,{params})}

const getapplylist = (params) => Axios(3).post(`/bio/front/apply`,params)
export {
  getCompanylist,
  getGuestlist,
  getapplylist
  }