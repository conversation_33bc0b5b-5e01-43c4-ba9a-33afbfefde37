
/**
 * 注意事项
 * GET方法传参的时候需要用大括号包起来
 */
import Axios from '@/plugins/http'


const getList=(params)=>{return Axios(0).get(`/activityInfo.json`,{params})}
const getContent=(params)=>{return Axios(0).get(`/activityInfo/${params.id}.json`)}
const isFollow=(params)=>{return Axios(0).get(`/activityInfo/isFollow.json`,{params})} //是否关注
const follow=(params)=>{return Axios(0).put(`/activityInfo/follow/${params.activityId}.json`)} //关注
const cancelFollow=(params)=>{return Axios(0).put(`/activityInfo/cancelFollow/${params.activityId}.json`)}//取消关注

export {
    getList,
    getContent,
    isFollow,
    follow,
    cancelFollow
}