// 医药系统  活动
import Axios from '@/plugins/http'
import { memberAutjApi, platformBizApi, orderUrl } from "../config/env";


// 查询商城配置信息
let getLayoutInfoList;
if (process.env.VUE_APP_CURRENTMODE.indexOf('Preview')>-1) { // 预览环境
    getLayoutInfoList = (params) => Axios("memberAutjApi").get(`/layoutInfo/getLayoutInfoList`, { params });
} else {
    getLayoutInfoList = (params) => Axios("memberAutjApi").post(`/front/layoutInfo/getLayoutInfoList`, params);
}

let getProductInfoList;
if (process.env.VUE_APP_CURRENTMODE.indexOf('Preview')>-1) { // 预览环境
    getProductInfoList = (params) => Axios("memberAutjApi").get(`/layoutInfo/getLayoutInfoList`, params);
} else {
    getProductInfoList = (params) => Axios("memberAutjApi").post(`/productInfo/getProductInfoList?pageNum=${params.pageNum}&pageSize=${params.pageSize}`, params);
}

const getProductClassifyList = (params) => Axios("memberAutjApi").post(`/productInfo/getProductClassifyList`, params);

const getProductInfoDetail = (params) => Axios("memberAutjApi").get(`/productInfo/getProductInfoDetail`, { params });

const exchange = (params) => Axios("memberAutjApi").post(`/productInfo/exchange`, params);

const getExchangeHistory = (params) => Axios("memberAutjApi").get(`/productInfo/getExchangeHistory`, { params });

const getOrderInfoDetail = (params) => Axios("memberAutjApi").get(`/productInfo/getOrderInfoDetail`, { params });

const getOrderAddress = (params) => Axios(7).get(`/order/getOrderAddress`, { params });

const getProductCodeRel = (params) => Axios("memberAutjApi").get(`/productInfo/getProductCodeRel`, { params });

// 获取购买信息
const getCustomInfo = (params) => Axios("memberAutjApi").get(`/productInfo/getCustom`, { params });

const beforeExchange = (params) => Axios("memberAutjApi").post(`/productInfo/beforeExchange`, params);

const getMemberDeliveryAddress = (params) => Axios(1).get(`/member/address/getMemberDeliveryAddress`, params); // 获取收货地址

const saveMemberAddress = (params) => Axios(1).post(`/member/address/save`, params); // 保存会员收货地址

const deleteAdress = (params) => Axios(1).delete(`/member/address/delete`, { params }); // 删除收货地址

const updateAdress = (params) => Axios(1).post(`/member/address/update`, params); // 更新会员收货地址

const getMallPointsSwitchStatus = (params) => Axios('memberAutjApi').get(`/front/layoutInfo/getMallPointsSwitchStatus`, params); // 获取易币商城是否开启状态接口

export {
    getLayoutInfoList,
    getProductInfoList,
    getProductClassifyList,
    getProductInfoDetail,
    exchange,
    getExchangeHistory,
    getOrderInfoDetail,
    getOrderAddress,
    getProductCodeRel,
    beforeExchange,
    getMemberDeliveryAddress,
    saveMemberAddress,
    deleteAdress,
    updateAdress,
    getCustomInfo,
    getMallPointsSwitchStatus,
}