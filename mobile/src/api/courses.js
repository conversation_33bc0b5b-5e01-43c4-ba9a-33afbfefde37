import Axios from '@/plugins/http'


const getCourseClassify = () => {return Axios(0).get(`/common/getCodeValueByPid/root_system_info_industryActivity_channel_medicalMedicine.json`) }//课程分类

const getSwiperList = (params) => { return Axios(0).get(`/top/2.json`, { params }) }
const getCoursesList = (params) => { return Axios(0).get(`/courseInfo/list.json`, { params }) }//课程列表
const getCourseInfo = (params) => { return Axios(0).get(`/courseInfo/${params.id}.json`) }//课程简介
const getChapterList = (params) => { return Axios(0).get(`/courseChapter/courseCascade/${params.courseId}.json`) }//章节列表

const getPPT = (params) => { return Axios(0).get(`/courseChapterFileRel/listByCourseId/${params.chapterId}/0.json`) }//资料
const getVideo = (params) => { return Axios(0).get(`/courseChapterFileRel/listByCourseId/${params.chapterId}/1.json`) }//视频
const getFile = (params) => {
    return Axios(0).all([getPPT(params), getVideo(params)]);
}

const getLecturer = (params) => { return Axios(0).get(`/lecturer/listByCourseId/${params.bizId}/${params.courseId}.json`) }//获取讲师

const collectInfo = (params) => { return Axios(0).get(`/courseCollectDetail/${params.courseId}.json`) }//获取收藏信息
const collect = (params) => { return Axios(0).put(`/courseCollectDetail/collect/${params.courseId}.json?platformId=${params.platformId}`) }//收藏
const unCollect = (params) => { return Axios(0).put(`/courseCollectDetail/uncollect/${params.courseId}.json?platformId=${params.platformId}`) }//取消收藏

const goodInfo = (params) => { return Axios(0).get(`/coursePraiseDetail/${params.courseId}.json`) }//获取点赞信息
const good = (params) => { return Axios(0).put(`/coursePraiseDetail/praise/${params.courseId}.json?platformId=${params.platformId}`) }//点赞
const unGood = (params) => { return Axios(0).put(`/coursePraiseDetail/uncollect/${params.courseId}.json?platformId=${params.platformId}`) }//取消点赞

//  是否加入学习
const courseLearn = (params) => { return Axios(0).put(`/courseJoinDetail/join/${params.courseId}.json?platformId=${params.platformId}`) };

//当前整门课程的学习进度
const getCourseCurrentTime = (params) => { return Axios(0).get(`courseCurrentTime/${params.courseId}.json`) }

//  发送每门课程的进度
const postCourseCurrentTime = (params) => { return Axios(0).post(`courseCurrentTime.json`, params) }

//  发送课程的学习时长
const courseLearnTime = (params) => { return Axios(0).post(`courseLearn.json`, params) }

//  评价列表
const getCourseSpeak = (params) => { return Axios(0).get(`/courseSpeak/list/${params.courseId}.json`, { params }) }

//  初次评价和追加评价
const getCourseSpeakId = (params) => { return Axios(0).get(`/courseSpeak/${params.id}.json`) }

//  添加评价
const getCourseSpeakAdd = (params) => { return Axios(0).post(`/courseSpeak.json`, params) }


export {
    getCourseClassify,
    getSwiperList,
    getCoursesList,
    getCourseInfo,
    getChapterList,
    getFile,
    getLecturer,
    collectInfo,
    collect,
    unCollect,
    goodInfo,
    good,
    unGood,
    courseLearn,
    getCourseCurrentTime,
    postCourseCurrentTime,
    courseLearnTime,
    getCourseSpeak,
    getCourseSpeakId,
    getCourseSpeakAdd
}