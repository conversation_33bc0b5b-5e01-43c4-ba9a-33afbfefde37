

import Axios from '../plugins/http';
const getCaptcha = (params) => {
    return Axios(1).get(`/captcha`,{params});
}
const  validateCaptcha = (params) => {
    return Axios(1).post(`/captcha`,params);
}
// 自动登陆
const  autoLogin = () => {
    return Axios(1).get(`/member/refreshToken/pro`);
}
// 获取渠道码
const getInvitationCode = (params) => {
    return Axios('signOrderUrl').get(`/invitationCode/front/getInvitationCode`, {params})
}
// 关联渠道码与注册人的关系
const invitationCodeMemberRel = (params) => {
    return Axios('signOrderUrl').post(`/invitationCodeMemberRel/save/${params.code}`, params)
}

// 渠道码是否有效
const validateInvitationCode = (params) => {
    return Axios('signOrderUrl').get(`/invitationCode/front/validate/${params.invitationCodeId}`, {params})
}
export {
    getCaptcha,
    validateCaptcha,
    autoLogin,
    getInvitationCode,
    invitationCodeMemberRel,
    validateInvitationCode
}