import Axios from '@/plugins/http'

const getSwiperList=(params)=>{return Axios(0).get(`/top/0.json`,{params})}
// const getCourseList=(params)=>{return Axios(0).get(`/home/<USER>
const getCoursesList = (params)=>{return Axios(0).get(`/courseInfo/list.json`,{params})}
const getMarqueeList=(params)=>{return Axios(0).get(`/audio.json`,{params})}
const getActivityList=(params)=>{return Axios(0).get(`/home/<USER>
const getlecturerListDatas=(params)=>{return Axios(0).get(`/home/<USER>
//关注和取消关注
const collectIsConcel=(params)=>{return Axios(0).get(`lecturer/collect/${params.lecturerId}/${params.symbol}.json`)}
const searchList=(params)=>{return Axios(0).get(`/home/<USER>/${params.bizId}/${params.content}.json`)}
//精选智库
const getThinkTankList = (params) => { return Axios(0).get(`thinkTank.json`, { params }) } // thinkTank.json  get请求  pageNum  pageSize 

//  搜索
const searchAllModel =  (params) => { return Axios(0).get(`/home/<USER>
// 关于精选直播
const getLiveCourses =  (params) => { return Axios(0).get(`/live`, { params }) };

export {
  getSwiperList,
  getMarqueeList,
  getActivityList,
  getlecturerListDatas,
  collectIsConcel,
  searchList,
  getThinkTankList,
  getCoursesList,
  searchAllModel,
  getLiveCourses
}