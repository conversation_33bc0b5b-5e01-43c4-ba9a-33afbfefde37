import Axios from '@/plugins/http'

// 查询 是否已收藏
const checkCollect = (params) => Axios(3).post(`/conferenceFront/memberCollectFlag`, params);


const checkLiveList = (params) => Axios(3).get(`/conferenceFront/getListByActivityId`, {params});


const checkContect = (params) => Axios(3).get(`/conferenceFront/getContactOrganizerByActivityId`, {params});


// 收藏
const collectActivity = (params, status) => Axios(3).post(`/conferenceFront/memberCollect/${status}`, params);

// 获取活动分享设置
const getActivityShareConfig = (params) => Axios(3).get(`/conference/front/share/${params.itemId}`, { params });

// 查询活动详情 （获取conferenceUrl 用于活动内页跳转iframe显示）
const getActivityUrl = (params) => Axios(3).get(`/conference/front/${params.activityId}`, {params});

export {
  checkCollect,
  collectActivity,
  getActivityShareConfig,
  checkLiveList,
  checkContect,
  getActivityUrl
}