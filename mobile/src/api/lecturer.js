import Axios from '@/plugins/http'

const getLecturerList=(params)=>{return Axios(0).get(`lecturer/list.json`,{params})}
const collectIsConcel=(params)=>{return Axios(0).get(`lecturer/collect/${params.lecturerId}/${params.symbol}.json`)}
const lecturerDetail=(params)=>{return Axios(0).get(`lecturer/${params.id}.json`)}
const ListCourses=(params)=>{return Axios(0).get(`lecturer/listCourses.json`,{params})}   

export {
  getLecturerList,
  collectIsConcel,
  lecturerDetail,
  ListCourses
}