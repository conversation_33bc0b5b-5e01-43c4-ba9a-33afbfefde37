/**
 * 注意事项
 * GET方法传参的时候需要用大括号包起来
 */
import Axios from '@/plugins/http'
import {exhibitorUrl} from '@/config/env';

// 获取个人信息
const getUserInfo = () => Axios(1).get(`member/getUserInfo.json`);

// 修改 个人信息
const updateUserInfo = (params) => Axios(1).put(`member.json`, params)

// 获取 我的发票信息
const getInvoiceInfo = () => Axios(3).get(`/front/memberInvoice/getInvoice`);

// 新增修改 我的发票信息
const updateInvoiceInfo = (params) => Axios(3).put(`/front/memberInvoice`, params);

// 获取 订单详情
const getOrderById = (params) => Axios(5).get(`/sign/getOrderById?${params}`);

// 订单支付
// const postPay = (params) => Axios(7).post(`/pay/wechat/${params.orderId}`);
let postPay = (params) => {
  let {
    businessId,
    businessType
  } = params;
  return Axios(7).post(`/pay/wechatMobileStartPay?businessId=${businessId}&businessType=${businessType}`, params);
}


// 获取 我的活动列表/直播  
const getMyActivityList = (params) => Axios(3).get(`/conferenceFront/listMyActivity`,{params});

// 获取 我的收藏列表 
const getMyCollectList = (params) => Axios(3).get(`/member/center/memberCollect`,{params});

// 新版我的收藏
const getMyCollectListNew = (params) => Axios(3).get(`/member/center/memberCollectNew`,{params});

// 获取 我的订单信息 
const getMyOrderList = (params) => Axios(5).get(`/sign/getMyOrders?${params}`);

// 查询发票抬头
const searchInvoice = (params) => Axios(11).get(`/qichacha/member/listCompanyNameByMember`, params)
// 电子票
const clickElectronicTicket = (params) => Axios('signOrderUrl').get(`/signManager/getByCode/${params.code}`)
// 提交发票
const saveRequestByUser = (params) => Axios('signOrderUrl').post(`/invoiceRequest/front/saveRequestByMember`, params)

// 我的优惠码
const getMyDiscount = (params) => Axios('productUrl').get(`/discountCode/front/list`, {
  params
});

// 获取支付url
const getPayUrl = (params) => Axios(7).post(`/pay/webWechatStartPay`, params);

// 获取支付状态
const getOrderStatus = (params) => Axios(7).get(`/order/getOrderStatus/${params.orderId}`, {
  params
})
// 获取发票内容
const getInvoiceContent = (params) => Axios('signOrderUrl').post(`/productInvoiceTitleRel/front/getProductInvoiceSubjectByOrderIds`, params)
// 查看是否有常用开票信息
const getCommonUseInvoiceList = (params) => Axios('signOrderUrl').get('memberInvoice/front/getCommonUseInvoiceList', {
  params
})
// 获取抬头
const getCreditCodeInfoByMember = (params) => Axios(11).get('/qichacha/member/getCreditCodeInfoByMember', {
  params
})

// 退出登录
const quitLogin = (params) => Axios(1).get('member/quit')

// 获取 用户 已关注领域  （易贸医疗）
const getUserIndustryEnmore = () => Axios(3).get('/conferenceFront/getDomain')

// 感兴趣的品类

// 获取感兴趣的品类
const getSelProduct = (params) => Axios(3).post(`${exhibitorUrl}/pcProductApi/selProductTypes`,params)

// 获取用户的感兴趣的品类
const getUsersBasicInfo = (params) => Axios(3).post(`${exhibitorUrl}/pcUsersApi/getUsersBasicInfo`,params)

// 保存用户感兴趣品类
const postUsersSave = (params) => Axios(3).post(`${exhibitorUrl}/pcUsersApi/usersSave`,params)
// myInquiryForm
// 获取询价单
const getUserList=(params)=>Axios(3).get(`${exhibitorUrl}/exhibitorProductInquiry/getUserList`,{params})

// 我的求购
const getMyBuyList=(params)=>Axios(3).post(`${exhibitorUrl}/exhibitorBuyingLeads/list`,params)

// 获取公共参数
const getPublicParams = (params) => Axios(9).get(`/common/codevalue/cascade/${params}`);


// 获取发言数据
const getSpeechList = (params) => Axios(5).get(`/inviteGuest/list`,{params});

// 取消订单
const cancelOrder = (params) => Axios('productUrl').put(`/productSkuFront/cancelSkuOrdersByMember`,params);

// 我的约见的banner
const getMyAppointmentBanner = (params) => Axios(3).get(`/officialMenu/personal/advert/appointment`,{params});

// 我的约见列表
const getMyAppointmentList = (params) => Axios(3).get(`/conferenceFront/listMyAppointment`,{params});


export {
  getUserInfo,
  updateUserInfo,
  getMyActivityList,
  getMyCollectList,
  getInvoiceInfo,
  updateInvoiceInfo,
  getMyOrderList,
  getOrderById,
  searchInvoice,
  clickElectronicTicket,
  saveRequestByUser,
  postPay,
  getMyDiscount,
  getPayUrl,
  getOrderStatus,
  getInvoiceContent,
  getCommonUseInvoiceList,
  getCreditCodeInfoByMember,
  quitLogin,
  getUserIndustryEnmore,
  getSelProduct,
  getUsersBasicInfo,
  postUsersSave,
  getUserList,
  getMyBuyList,
  getPublicParams,
  getMyCollectListNew,
  getSpeechList,
  cancelOrder,
  getMyAppointmentBanner,
  getMyAppointmentList
}