<template>
  <div class="code-dialog flex-vertical flex-center">
    <van-icon name="cross" class="close-btn" @click="closeBtn" />
    <div class="code-text">{{ settingForm.serviceTips ||  "如需修改请联系小易" }}</div>
    <img class="code-img" :src="settingForm.serviceQrcode" alt="" v-if="settingForm.serviceQrcode" />
    <!-- 二维码 -->
    <div class="code-img" v-else></div>
    <div class="code-desc">保存二维码图片，微信扫码添加</div>
  </div>
</template>
<script>
import QRCode from "qrcodejs2";
export default {
  props: ["settingForm"],
  data() {
    return {};
  },
  watch: {},
  components: {},
  mounted() {
    // 生成二维码
    if (this.settingForm.qrcodeUrl) {
      this.generateCode();
    }
  },
  methods: {
    closeBtn() {
      this.$emit("closeEmit");
    },
    // 生成二维码
    generateCode() {
      this.$nextTick(() => {
        const code = document.querySelector(".code-img");
        if (code) {
          new QRCode(code, {
            text: this.settingForm.qrcodeUrl,
            width: 150,
            height: 150,
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.code-dialog {
  width: 260px;
  height: 261px;
  background: #ffffff;
  border-radius: 10px;
  padding: 15px;
  box-sizing: border-box;
  position: relative;
  .close-btn {
    position: absolute;
    right: 15px;
    top: 15px;
  }
  .code-text {
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
    margin-bottom: 12px;
    margin-top: 11px;
  }
  .code-img {
    width: 150px;
    height: 150px;
    object-fit: contain;
    margin-bottom: 10px;
  }
  .code-desc {
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 17px;
  }
}
</style>
