<template>
    <div class="cooperation flex-center flex-middle flex-vertical">
        <div class="cooperation-content flex-vertical flex-center flex-middle">
            <div class="cooperation-title">{{cooperationData.shareDesc}} </div>
            <div class="cooperation-code flex-middle flex-center">
                <img :src="cooperationData.shareImgUrl" alt="">
            </div>
        </div>
        <div class="cooperationBottom flex-center flex-middle">
            <img src="../../assets/img/popup_icon_cross.png" alt="" @click="close">
        </div>
    </div>
</template>
<script>
import {getHomeShare} from '@/api/medicine/homePage';
export default {
    data () {
        return {
            cooperationData:{}
        }
    },
    mounted () {
        this.getHomeShare();
    },
    computed: {

    },
    methods: {
        close () {
            this.$emit('close');
        },
        async getHomeShare(){
            let {data} = await getHomeShare({type:'contact '});
            console.log('问卷数据',data);
            if(data && data.length){
                this.cooperationData = data[0];
            }
        }
    },
}
</script>
<style scoped lang="scss">
.cooperation {
    position: absolute;
    z-index: 999;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    .cooperation-content {
        width: 260px;
        height: 318px;
        border-radius: 4px;
        background: url(../../assets/img/hezuo-bg.png) no-repeat;
        background-size: cover;
        .cooperation-title {
            font-size: 15px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #002b45;
            line-height: 21px;
            width: 158px;
            text-align: center;
        }
        .cooperation-code {
            margin-top: 20px;
            width: 170px;
            height: 170px;
            background: #ffffff;
            border-radius: 6px;
            img {
                width: 156px;
                height: 156px;
            }
        }
    }
    .cooperationBottom {
        width: 100%;
        height: 32px;
        margin-top: 25px;
        img {
            width: 32px;
            height: 32px;
        }
    }
}
</style>
