<!-- 转发后优惠码提示弹窗 -->
<template>
  <div class="dicountCode-wrap flex-middle flex-center">
    <div class="dicountCode-content flex-vertical">
      <div class="dicountCode-content-top unit-0">
        <div class="top-text flex-middle flex-center">您确定要退出登录吗？</div>
      </div>
      <div class="dicountCode-content-bottom unit flex-middle">
        <div class="btn-left unit flex-middle flex-center" @click="cancel">取消</div>
        <div class="btn-right unit flex-middle flex-center" @click="quit">退出登录</div>
      </div>
    </div>
  </div>
</template>
<script>
import { quitLogin } from "@/api/userCenter";
import CONSTANT from "@/config/config_constant";
import clearAllCookie from "@/utils/delCookie";
import getCookie from '@/utils/getCookie';
export default {
  name: "discountDialog",
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {
    cancel() {
      this.$emit("close");
      location.reload();
    },
    quit() {
      quitLogin().then((res) => {
        if (res.code == "001") {
          this.$toast("成功退出登录");
          if(getCookie('invitationCodeId')){
              clearAllCookie('invitationCodeId');
          }
          localStorage.removeItem(CONSTANT.token);
          clearAllCookie(CONSTANT.Authorization);
          this.$store.commit("changeLoginStatus", false);
          // 清空个人中心数据
          this.$store.commit("clearnPersonPro");
          this.cancel();
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.dicountCode-wrap {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 5000;
  background: rgba(0, 0, 0, 0.5);
  width: 100vw;
  height: 100vh;
  .dicountCode-content {
    width: 280px;
    height: 132px;
    background: #ffffff;
    border-radius: 8px;
    .dicountCode-content-top {
      width: 100%;
      height: 82px;
      border-bottom: 1px solid #eeeeee;
      .top-text {
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #222;
        text-align: center;
        width: 100%;
        height: 100%;
      }
    }
    .dicountCode-content-bottom {
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999;
      .btn-left {
        height: 100%;
        border-right: 1px solid #eeeeee;
      }
      .btn-right {
        height: 100%;
        color: var(--color-primary);
      }
    }
  }
}
</style>
