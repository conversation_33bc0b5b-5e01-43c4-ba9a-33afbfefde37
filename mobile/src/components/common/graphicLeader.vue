<!-- 图文组件 -->
<template>
  <div class="graphic-leader-wrap flex-middle">
    <img :src="require(`@/assets/img/no.${index + 1}.png`)" alt="" class="leader-img-number" />
    <img :src="item.coverImg" alt="" class="leader-img-content" />
    <span class="leader-text">{{ item.name }}</span>
  </div>
</template>
<script>
export default {
  name: "graphicLeader",
  props: {
    index: {
      type: Number,
      default: 0,
    },
    item: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      title: "图文组件",
    };
  },
  mounted() {},
  computed: {},
  methods: {},
};
</script>
<style scoped lang="scss">
.graphic-leader-wrap {
  margin-bottom: 10px;
  .leader-img-number {
    width: 14px;
    margin-right: 6px;
  }
  .leader-img-content {
    width: 70px;
    height: 42px;
    object-fit: contain;
    margin-right: 6px;
  }
  .leader-text {
    font-size: 12px;
    color: #333;
    height: 42px;
    line-height: 19px;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}
</style>
