<template>
  <div>
    <div class="sign-in-dialog-wrap flex-middle flex-center">
      <div class="sign-in-dialog-content flex-vertical flex-center" :class="animationClass" data-wow-duration="0.5s" data-wow-delay="0.3s">
        <p class="title_item">提醒</p>
        <div class="sign-in-dialog-text text_row">
          <p>必须实名认证的客户才可以参与易币兑换，请先做实名认证申请</p>
        </div>
        <div class="sign-in-dialog-btn" @click="saveBtn">立即去实名</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getBioChinaInfo } from "@/api/memberApi";
export default {
  data() {
    return {
      dialogVisible: true,
    };
  },
  watch: {},
  components: {},
  mounted() {},
  methods: {
    // 去实名
    async saveBtn() {
      try {
        const res = await getBioChinaInfo();
        if (!res.data) {
          this.$store.commit("changeUserPopShow", true);
        } else {
          this.$store.commit("changeBioChinaShow", true);
          this.$store.commit("changeBioChinaImg", res.data);
        }
      } catch (error) {
        console.error('获取实名认证信息失败', error);
      } finally {
        this.$emit("mallEmitSave");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.text_row {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  text-align: center;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  text-align: right;
  font-style: normal;
  padding: 0px 24px;
  p {
    text-align: center;
  }
}
.sign-in-dialog-wrap {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  background: rgba(0, 0, 0, 0.6);
  width: 100vw;
  height: 100vh;
  .sign-in-dialog-content {
    padding: 20px 0px;
    width: 260px;
    background: #ffffff;
    border-radius: 10px;
    padding-bottom: 0px;
    .sign-in-dialog-img-title {
      width: 235px;
      height: auto;
      margin-top: -15px;
    }
    .sign-in-dialog-img-gold {
      width: 150px;
      height: auto;
      margin-top: -20px;
    }
    .sign-in-dialog-text {
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      .sign-in-dialog-text-desc {
        color: #666666;
      }
      .sign-in-dialog-text-count {
        color: #ff4a28;
      }
    }
    .sign-in-dialog-btn {
      width: fit-content;
      height: 30px;
      padding: 0px 10px;
      border-radius: 5px;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 15px;
      border-top: 1px solid #eeeeee;
      font-weight: 400;
      font-size: 16px;
      color: #ff6b00;
      text-align: right;
      font-style: normal;
      padding: 12px 0px;
      height: 46px;
      width: 100%;
    }
  }
}
.title_item {
  font-weight: 500;
  font-size: 16px;
  color: #333333;
  text-align: right;
  font-style: normal;
  margin-bottom: 15px;
}
</style>
