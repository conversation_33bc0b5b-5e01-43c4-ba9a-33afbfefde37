<template>
    <div class="body">
        <div class="title_top">{{ memCoinSettingForm.ruleName }}</div>
        <div class="member_body" v-if="memCoinSettingForm.ruleDesc && memCoinSettingForm.ruleDesc.length > 0">
            <div v-for="(item,index) in memCoinSettingForm.ruleDesc">
                <div v-if="item.type == 'text'" class="every text" v-html="item.text">
                </div>
                <div v-if="item.type == 'img'" class="every image">
                    <img :src="item.url" alt=""/>
                </div>
            </div>
        </div>
    </div>
</template>



<script>
import moment from 'moment';
export default {
    data(){
        return {
        }
    },
    watch: {
        
    },
    components: {
    },
    computed: {
        memCoinSettingForm() {
            return this.$store.state.memCoinSettingForm
        }
    },
    mounted(){
    },
    methods: {
        
    },
}
</script>



<style lang='scss' scoped>
.body {
    margin: 0px 16px;
    background: #FFFFFF !important;
    padding: 12px;
    margin-top: 12px;
    border-radius: 6px;
}
.title_top {
    // margin-top: 17px;
    font-weight: 500;
    font-size: 15px;
    color: #333333;
    text-align: left;
    font-style: normal;
}
.member_body {
    // max-height: 400px;
    overflow-y: auto;
    margin-top: 12px;
    .text {
        line-height: 20px;
    }
}
.every {
    margin-bottom: 15px;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    text-align: left;
    font-style: normal;
}
.image {
    width: 100%;
    img {
        width: 100%;
    }
}
</style>