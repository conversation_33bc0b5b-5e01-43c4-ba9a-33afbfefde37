<template>
    <div class="setInfoWrap">
       <div class="lecturerDetailsTop" >
        <i class="back" @click="close"></i>
             <span @click="makeSure">确定</span> 
      </div>
        <div class="inputContent">
           <input type="text" v-model="inputInfo">
        </div>
       
    </div>
</template>

<script>
/**
 * 个人中心里面的设置页面
 */

export default {
  name:'setInfo',
  data(){
    return {
       inputInfo:''
    }
  },
  computed:{
    setInfo(){
      return this.$store.state.personal.setInfo;
    },
  },
  mounted(){
    this.inputInfo=this.setInfo.content;
  },
  methods:{
    close(){
      this.$store.commit('changeShowSet',false);
    },
    makeSure(){
      if (this.setInfo.tip == 'email') {
        let reg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
        if (this.inputInfo && !reg.test(this.inputInfo)) {
          this.$createToast({
            txt: '邮箱格式不正确',
            type: 'warn'
          }).show();
          return;
        }
      }
      this.$store.commit('changeShowSet',false);
      this.$store.commit('changeMemberSave',{content:this.inputInfo,key:this.setInfo.tip});
    }
  }
}
</script>

<style scoped>
  .setInfoWrap{
    width: 100%;
    height: 100%;
    background: #eee;
    position: fixed;
    left:0;
    top:0;
    z-index: 10;
  }
  .inputContent{
    height: 60px;
    padding: 5px 10px;
    box-sizing: border-box;
    margin-top: 10px;
    width: 100%;
  }
  .inputContent input{
    width: 100%;
    background: #fff;
    height: 30px;
  }
  .lecturerDetailsTop{
    width: 100%;
    height: 45px;
    background: #004da1;
    position: relative;
    text-align: right;
    line-height: 45px;
    color: #fff;
    font-size: 17px;
    padding-right: 20px;
    box-sizing: border-box;
  }
  .back{
    position: absolute;
    top: 11px;
    left: 16px;
    width: 17px;
    height: 22px;
    background: url("../../assets/img/back.png") no-repeat 0 0;
    background-size: 100% 100%;
    }
</style>


