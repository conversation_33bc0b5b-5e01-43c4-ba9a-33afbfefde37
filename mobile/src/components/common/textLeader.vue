<template>
  <div class="text-leader-wrap flex-middle">
    <span class="leader-text-number">{{ index + 1 }}</span>
    <span class="leader-text-content">{{ item.name }}</span>
  </div>
</template>
<script>
export default {
  name: "textLeader",
  props: {
    index: {
      type: Number,
    },
    item: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {},
};
</script>
<style scoped lang="scss">
.text-leader-wrap {
  margin-bottom: 10px;
  .leader-text-number {
    font-size: 16px;
    color: #999;
    width: 20px;
    height: 16px;
    text-align: left;
    margin-right: 4px;
  }
  .leader-text-content {
    font-size: 12px;
    color: #333;
    height: 19px;
    line-height: 19px;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
