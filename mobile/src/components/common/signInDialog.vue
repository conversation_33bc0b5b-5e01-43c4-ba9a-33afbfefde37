<template>
  <div class="sign-in-dialog-wrap flex-middle flex-center">
    <div class="sign-in-dialog-content flex-vertical flex-center" :class="animationClass" data-wow-duration="0.5s" data-wow-delay="0.3s">
      <img src="https://oss.ienmore.com/resources/public/img/health/new/signin_tip.png" alt="" class="sign-in-dialog-img-title" />
      <img src="https://oss.ienmore.com/resources/public/img/health/new/signin_success.png" alt="" class="sign-in-dialog-img-gold" />
      <div class="sign-in-dialog-text">
        <span class="sign-in-dialog-text-desc">已签到 易币</span>
        <span class="sign-in-dialog-text-count">+{{ userSignInInfo.points }}</span>
      </div>
      <div class="sign-in-dialog-btn" @click="closeDialog">知道了</div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      animationClass: "wow zoomIn",
    };
  },
  inject: ["updateWowDom"],
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.updateWowDom();
    });
  },
  computed: {
    userSignInInfo() {
      return this.$store.state.userSignInInfo;
    }
  },
  methods: {
    closeDialog() {
      this.animationClass = "wow zoomOut";
      setTimeout(() => {
        this.$store.commit("changeIsSignInStatus", false);
      }, 800);
    },
  },
};
</script>
<style scoped lang="scss">
.sign-in-dialog-wrap {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  background: rgba(0, 0, 0, 0.6);
  width: 100vw;
  height: 100vh;
  .sign-in-dialog-content {
    width: 260px;
    height: 255px;
    background: #ffffff;
    border-radius: 10px;
    .sign-in-dialog-img-title {
      width: 235px;
      height: auto;
      margin-top: -15px;
    }
    .sign-in-dialog-img-gold {
      width: 150px;
      height: auto;
      margin-top: -20px;
    }
    .sign-in-dialog-text {
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      .sign-in-dialog-text-desc {
        color: #666666;
      }
      .sign-in-dialog-text-count {
        color: #ff4a28;
      }
    }
    .sign-in-dialog-btn {
      width: 200px;
      height: 38px;
      background: linear-gradient(270deg, #ff4a28 0%, #ff7615 100%);
      box-shadow: inset 0px 1px 10px 0px rgba(255, 255, 255, 0.8);
      border-radius: 3px;
      margin-top: 15px;
      text-align: center;
      line-height: 38px;
      font-weight: 500;
      font-size: 14px;
      color: #ffffff;
    }
  }
}
</style>
