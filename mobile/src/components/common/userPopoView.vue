<!-- 底部弹出的 长按关注公众号 组件 -->
<template>
    <div v-show="visiable" class="hidden-message" @touchmove.prevent>
        <div class="hidden-mask" @click="close"></div>
        <div class="hidden-card-outside">
			<div class="hidden-card">
				<div class="hidden-message-title">
                    <span>实名认证</span>
                    <img @click="close" src="../../assets/img/close1.png" alt="">
				</div>
			</div>
			<div class="hidden-card body_bottom">
                <!--实名认证未实名、待审核、未通过提示--->
                <realnameAuthenTip :canShowDialog="false" marginBottom="10px"></realnameAuthenTip>
                <cube-upload
                  v-model="filePoint"
                  ref="uploadPoint"
                  :multiple="false"
                  :max="1"
                  :action="uploadUrl"
                  @file-success="iconUploadBtn">
                  <cube-upload-btn>
                    <div class="bot_body" v-if="!uploadIconUrl">
                    </div>
                    <div v-else class="img_body">
                        <img @click.stop.prevent="deleBtn" class="delete_class" src="@/assets/img/delePng.png" alt="">
                        <img :src="uploadIconUrl" alt="">
                        <p class="text_bot">{{ getBerPointInfo.auditFlag == 1 ? "再次认证":"重新上传" }}</p>
                    </div>
                  </cube-upload-btn>
                </cube-upload> 
                <div class="not_text" v-if="getBerPointInfo.auditFlag == 2">
                    审核失败需重新上传名片或工牌 <span class="view" @click="viewReason(item)">查看原因</span>
                </div>
                <div class="bot_text">
                    <svg class="name_img" aria-hidden="true">
                      <use v-bind:xlink:href="'#icon-tongyong_tishi'" class="img"></use>
                    </svg>
                    <p>请上传名片、工牌等可以证明自己在职信息的截图，以便后台工作人员进行审核，支持jpg、png格式，图片大小不超过3M</p>
                </div>
                <div class="footer-submit-btn">
                  <cube-button @click="toSubmit">提交审核</cube-button>
                </div>
			</div>
        </div>
         <!--审核不通过原因-->
        <van-dialog class="reason_dialog" v-model:show="auditFailShow" title="审核失败原因" width="260" confirmButtonText="知道了" confirm-button-color="#097DB4" message-align="center">
            <div v-if="getBerPointInfo.auditFailReason" class="reason_content">{{ getBerPointInfo.auditFailReason }}</div>
        </van-dialog>
    </div>
</template>

<script>
import { uploadUrl } from '@/config/env'
import { mapActions } from 'vuex';
import { saveMemberAuth } from '@/api/memberApi.js'
import realnameAuthenTip from '@/views/userCenter/components/realnameAuthenTip.vue';
export default {
    name: 'userPopoView',
    props: [ 'visiable'],
	data() {
		return {
            createUrl: "",
            uploadIconUrl: "",
			remindList: '',
            filePoint: [],
            uploadUrl, // 图片上传路径
            auditFailShow: false
		}
	},
    components:{
        realnameAuthenTip
    },
	mounted() {
		this.getMemberAuthStatus()
	},
    computed: {
        getBerPointInfo() {
            // 获取个人认证信息
            this.createUrl = this.$store.state.getBerPointInfo.businessUrl;
            this.uploadIconUrl = this.$store.state.getBerPointInfo.businessUrl;
            return this.$store.state.getBerPointInfo;
        },
        // 个人信息
        personProfile() {
            return this.$store.state.personal.personProfile;
        },
    },
    methods: {
        ...mapActions(['getMemberLevelInfo', 'getMemberAuthStatus']),
        //查看原因
        viewReason(){
            this.auditFailShow = true;
        },
        // 清除图片
        deleBtn() {
            this.uploadIconUrl = ''; // 清空上传的图片URL
        },
        // 提交审核
        toSubmit() {
            if (!this.uploadIconUrl) return this.$toast("请上传名片或工牌");
            if (this.getBerPointInfo.auditFlag == 2 && this.uploadIconUrl == this.createUrl) return this.$toast("请重新上传图片再提交审核");
            if (this.getBerPointInfo.auditFlag == 1 && this.uploadIconUrl == this.createUrl) {
                this.$toast("请上传新的名片或工牌");
                return 
            }
            let params = {
                businessUrl: this.uploadIconUrl, // 名片或者工牌
                memberName: this.personProfile.memberName, // 会员名称
                company: this.personProfile.company, // 公司名称
                memberId: this.personProfile.id, // 会员Id
                platformId: this.personProfile.platformId, // 平台Id
                position: this.personProfile.position, // 职位
            }
            saveMemberAuth(params).then((res) => {
                this.$store.commit("changeUserPopShow", false);
                setTimeout(() => {
                    this.$toast("提交成功，请等待主办方审核");
                }, 500);
                this.getMemberAuthStatus();
            })
        },
        // 认证身份名片上传成功
        iconUploadBtn(res) {
          this.uploadIconUrl = res.response.data;
          this.$refs.uploadPoint.removeFile(this.filePoint[0])
        },
        // 关闭
        close() {
            this.$store.commit("changeUserPopShow", false);
        },
    },
}
</script>

<style scoped lang="scss">
$color: #004da1;
.hidden-message {
    z-index: 999;
    position: fixed;
    width: 100%;
    // bottom: 0px;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: rgba($color: #000000, $alpha: 0.3);
    .hidden-mask {
        flex: 1;
    }
	.hidden-card-outside{
        // padding-top: 22px !important;
		width: 100%;
		background: #ffffff;
		// padding: 12px 0 0px;
		border-top-left-radius: 10px;
		border-top-right-radius: 10px;
		position: fixed;
		left:0;
		bottom:0;
		z-index: 200;
	}
    .hidden-card {
        width: 100%;
		float: left;
        // padding: 0px 15px;
    }
		.second{
			padding: 0 20px;
			text-align: center;
			position: relative;
			float: left;
			width: 100%;
		}
		.secondBorder .QRcode::before{
				display: inline-block;
				width: 1px;
				height: 120px;
				background-color: #DEDEDE;
				content: "";
				position: absolute;
				top: 0;
				left: -36px;
		}
}
.QRcode{
	position: relative;
	padding: 7px;
	margin: 0 auto 22px;
	width: 114px;
	height: 114px;
	box-sizing: border-box;
	position: relative;
	.info-image {
		width: 100px;
		height: 100px;
	}
	i{
		position: absolute;
		display: inline-block;
		width: 14px;
		height: 14px;
	}
	i:nth-of-type(1){
		top: 0;
		left: 0;
		border-top: 0.5px solid #1464A1;
		border-left: 0.5px solid #1464A1;
	}
	i:nth-of-type(2){
		top: 0;
		right: 0;
		border-top: 0.5px solid #1464A1;
		border-right: 0.5px solid #1464A1;
	}
	i:nth-of-type(3){
		bottom: 0;
		left: 0;
		border-bottom: 0.5px solid #1464A1;
		border-left: 0.5px solid #1464A1;
	}
	i:nth-of-type(4){
		bottom: 0;
		right: 0;
		border-bottom: 0.5px solid #1464A1;
		border-right: 0.5px solid #1464A1;
	}
}
.hidden-message-title {
	text-align: center;
	color: #333333;
	font-size: 14px;
	margin-bottom: 17px;
	line-height: 20px;
	padding: 0 20px;
	float: left;
	width: 100%;
	margin: 0 auto 22px;
    font-weight: bold;
    font-size: 16px;
    border-bottom: 1px solid #F5F5F5;
    margin-bottom: 20px;
    height: 46px;
    padding-top: 12px;
    img {
        float: right;
        // margin-right: 15px;
    }
}
.bot_body {
    background-image: url("../../assets/img/upBg.png");
    height: 160px;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
}
.blue_text {
    font-weight: 400;
    font-size: 14px;
    color: #097DB4;
    margin-top: 85px;
}
.bot_text {
    margin-top: 10px;
    display: flex;
    p {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        margin-left: 6px;
    }
    .name_img {
        width: 24px;
        height: 24px;
        margin-top: -3px;
        .img {
            width: 14px;
            height: 14px;
        }
    }
}
.footer-submit-btn {
  width: 100%;
  box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.06);
  background: #fff;
  text-align: center;
  margin: 0;
  margin-top: 38px;
  height: 65px;
  button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 42px;
    background: var(--color-primary);
    font-size: 14px;
    opacity: 0.9;
  }
}
.img_body {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 160px;
    border: 1px solid rgba(9,125,180,0.2);
    position: relative;
    padding: 5px;
    img {
        height: 100%;
    }
    i {
        position: absolute;
        top: 0px;
        right: 0px;
    }
    .delete_class {
        z-index: 999;
        position: absolute;
        top: 8px;
        right: 8px;
        height: 16px;
    }
}
.text_bot {
    position: absolute;
    bottom: 8px;
    right: 8px;
    width: 64px;
    height: 23px;
    background: rgba(0,0,0,0.5);
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    font-size: 12px;
}
.not_text {
    width: 345px;
    height: 28px;
    background: rgba(255,77,79,0.1);
    border-radius: 2px;
    display: flex;
    align-items: center;
    color: #FF4D4F;
    font-size: 12px;
    padding-left: 10px;
    margin-top: 16px;
    .view{
        color: #1890FF;
        margin-left: 10px;
    }
}
.body_bottom {
    padding: 0px 15px;
}
.reason_dialog{
    border-radius: 12px;
    .reason_content{
        font-size: 14px;
        color: #666666;
        line-height: 20px;
        padding: 15px 24px;
        text-align: center;
    }
}
</style>