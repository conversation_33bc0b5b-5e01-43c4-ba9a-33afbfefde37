<!-- 公共新闻资讯组件 -->
<template>
  <div class="information-rowOne flex-left" @click="handleClick(item)">
    <div class="information-left unit-0">
      <img :src="item.coverUrl" alt="" class="information-left-img" />
    </div>
    <div class="information-right unit">
      <div class="information-right-title">{{ item.title }}</div>
      <div class="information-right-desc">{{ item.oneSentenceIndex }}</div>
      <div class="information-right-time">{{ formatTime(item.publishTime) }}</div>
    </div>
  </div>
</template>
<script>
export default {
  name: "CommonInformation",
  props: {
    item: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {
    handleClick(itemData) {
      if (!itemData.type) {
        this.$router.push({
          path: "/moduleDetail/news/" + itemData.id,
        });
      } else {
        window.open(itemData.content);
      }
    },
  },
};
</script>
<style scoped lang="scss">
.information-rowOne {
  width: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-sizing: border-box;
  margin-bottom: 10px;
  padding: 10px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
  .information-left {
    width: 100px;
    margin-right: 10px;
    height: 66px;
    flex-shrink: 0;
    .information-left-img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      border-radius: 4px;
    }
  }
  .information-right {
    min-width: 0;
    .information-right-title {
      font-weight: 500;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      //2行省略
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      margin-bottom: 6px;
      height: 40px;
    }
    .information-right-desc {
      font-weight: 400;
      font-size: 12px;
      color: #666666;
      line-height: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 8px;
    }
    .information-right-time {
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      line-height: 16px;
    }
  }
}
</style>
