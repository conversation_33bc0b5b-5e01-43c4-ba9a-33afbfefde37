<template>
    <div class="body">
        <div class="top_text">
            <svg class="svg-icon" aria-hidden="true">
              <use v-bind:xlink:href="'#icon-tongyong_tishi'"></use>
            </svg>
            <p>历史个人易币明细并未导入系统中，如对已有易币明细有任何疑问请联系小易</p>
        </div>
        <cube-scroll v-if="tabList.length != 0" class="table_scroll" ref="classifyScroll" :data="tabList" :options="options" @pulling-down="loadDownFun" @pulling-up="loadFun">
             <div class="table_coin_body" v-for="(item,index) in tabList">
                <div class="tab_left">
                    <div class="name_text">
                        <span v-if="item.ruleCode == 'JF000'">后台积分调整&nbsp;&nbsp;<span class="red-color">({{item.ruleDesc }})</span></span>
                        <span v-else> {{item.ruleDesc }}</span>
                    </div>
                    <div class="date_text">{{ radioTabIndex == 0 ? dateChange(item.effectiveTime):dateChange(item.expiredTime) }}</div>
                </div>
                <div class="tab_right" :class="!item.roleType ? 'red_pint_color':'black_point_color'">{{ !item.roleType ? "+":"" }}{{ item.points }}</div>
            </div>
        </cube-scroll>
        
        <div v-if="tabList.length == 0" class="not_body">
            <img src="../../assets/img/noting.png" alt="">
            <p>暂无数据</p>
        </div>
    </div>
</template>



<script>
import scroll from '@/components/common/scroll';
import { getPointPage, getMonthExpiresPointsList } from "@/api/memberApi.js";
import moment from 'moment';
export default {
    props: ["radioTabIndex"],
    data(){
        return {
            options: {
                pullUpLoad: {
                    threshold: 50,
                    txt: {
                        more: '',
                        noMore: '没有更多信息啦！'
                    }
                },
                pullDownRefresh: {
                    threshold: 60,
                    stopTime: 1000,
                    txt: '更新成功'
                },
                scrollbar: false,
            },
            tabList: [],
            pageForm: {
                pageNum: 1,
                pageSize: 10,
            },
            total: 0, // 总数
        }
    },
    watch: {
        radioTabIndex() {
            this.pageForm.pageNum = 1;
            this.tabList = [];
        }
    },
    components: {
        scroll,
    },
    mounted(){
        this.getPointPage();
    },
    methods: {
        loadDownFun() {
            this.pageForm.pageNum = 1;
            this.tabList = [];
            this.getPointPage();
            this.$refs.classifyScroll.forceUpdate();
        },
        // 滚动触底事件
        loadFun() {
            setTimeout(() => {
                if (this.tabList.length < this.total) {
                    this.pageForm.pageNum++;
                    this.getPointPage();
                    this.$nextTick(() => {
                        this.$refs.classifyScroll.refresh();
                    })
                } else {
                    this.$refs.classifyScroll.forceUpdate();
                }
            }, 1000);
        },
        // 处理时间格式
        dateChange(date) {
            return moment(date).format("YYYY-MM-DD HH:mm:ss");
        },
        // 查询明细列表
        getPointPage(type = 0) {
            let getDateUrl = type == 0 ? getPointPage:getMonthExpiresPointsList;
            let params = {
                ...this.pageForm,
                platformId: this.$route.query.platformId,
            }
            getDateUrl(params).then(res=>{
                this.tabList = this.tabList.concat(res.data.list)
                // this.tabList = res.data.list;
                this.total = res.data.total;
                // 处理如果points这个包含-号则是负数否则就是正数
                this.tabList.forEach((item,index)=>{
                    if (String(item.points).indexOf("-") == -1 && item.points !== 0) {
                        this.tabList[index].roleType = 0;
                    } else {
                        this.tabList[index].roleType = 1;
                    }
                })
            })
        }
    },
}
</script>



<style lang='scss' scoped>
.body {
    height: 100%;
    background-color: #ffffff;
}
.top_text {
    display: flex;
    align-items: flex-start;
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    text-align: left;
    font-style: normal;
    line-height: 16px;
}
.svg-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}
.table_coin_body {
    display: flex;
    justify-content: space-between;
    // margin-top: 22px;
    padding: 11px 0px;
    border-bottom: 1px solid #F5F5F5;
    .tab_left {
        .name_text {
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            line-height: 18px;
            text-align: left;
            font-style: normal;
            white-space: normal; /* 确保文本可以折行 */
            overflow: hidden; /* 隐藏超出的内容 */
            display: -webkit-box; /* 使用弹性盒子模型 */
            -webkit-line-clamp: 3; /* 限制显示的行数为2行 */
            -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
            line-height: 20px; /* 行高 */
            text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
            margin-right: 10px;
        }
        .date_text {
            font-weight: 400;
            font-size: 12px;
            color: #666666;
            line-height: 14px;
            text-align: left;
            font-style: normal;
            margin-bottom: 0px;
        }
        div {
            margin-bottom: 6px;
        }
    }
}
.red_pint_color {
    font-weight: 600;
    font-size: 15px;
    color: #FF4D4F;
    line-height: 21px;
    text-align: center;
    font-style: normal;
}
.black_point_color {
    font-weight: 600;
    font-size: 15px;
    color: #999999;
    line-height: 21px;
    text-align: center;
    font-style: normal;
}
.red-color{
    color: #FF4D4F;
}
.not_body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 30px;
    img {
        width: 160px;
    }
    p {
        margin-top: 10px;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        text-align: left;
        font-style: normal;
    }
}
.table_scroll {
    margin-top: 12px;
    max-height: 80vh;
    overflow-y: scroll;
}
</style>