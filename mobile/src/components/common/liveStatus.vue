<!-- 直播状态 进行中  即将开始 免费  直播等等标签的状态-->
<template>
  <div class="live-status-wrap " :class="layout === 'bottom-right' ? 'flex-vertical' : 'flex-left'">
    <span class="live-status-text" :class="getStatusClass" :style="liveStatusStyle" v-if="info.statusText && info.statusText !== '已完成'">{{ info.statusText }}</span>
    <span class="unit"></span>
    <div :class="getTypeClass" >
      <span v-show="!info.totalPaymentType" class="live-status-text type-text" :style="liveChargeTypeStyle">免费</span>
      <span class="live-status-text live-text" :style="liveTypeStyle">{{ info.conferenceType }}</span>
    </div>
  </div>
</template>
<script>
export default {
  name: "liveStatus",
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    // 直播状态的样式定义
    liveStatusStyle: {
      type: Object,
      default: () => {},
    },
    // 直播收费状态的样式定义
    liveChargeTypeStyle: {
      type: Object,
      default: () => {},
    },
    // 直播类型
    liveTypeStyle: {
      type: Object,
      default: () => {},
    },
    // 布局模式
    layout: {
      type: String,
      default: "normal",
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {
    getStatusClass() {
      return this.info.statusText === "进行中" ? "status-text" : this.info.statusText === "即将开始" ? "will-text" : "";
    },
    getTypeClass() {
      const layoutMap = {
        "normal": "flex-left",
        "bottom-right": this.info.conferenceType === '线下活动' ? "flex-bottom fixRight" : "flex-bottom flex-right"
      };
      return layoutMap[this.layout] || "";
    },
  },
  methods: {},
};
</script>
<style scoped lang="scss">
.live-status-wrap {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 10;
  .live-status-text {
    font-weight: 400;
    font-size: 10px;
    color: #ffffff;
    line-height: 10px;
    padding: 4px;
    border-radius: 2px;
    background: rgba(0, 0, 0, 0.5);
    height: fit-content;
    width: fit-content;
    margin-right: 6px;
    margin-top: 6px;
    white-space: nowrap;
  }
  .status-text {
    background: #52c41a;
    margin-left: 6px;
  }
  .will-text {
    background: #ff6b00;
    margin-left: 6px;
  }
  .fixRight {
    right: 0px;
    position: absolute;
    bottom: 4px;
  }
}
</style>
