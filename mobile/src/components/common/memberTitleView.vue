<template>
    <div>
      <div class="member_top" style="border-bottom: 0px;">
          <!--实名认证未实名、待审核、未通过提示--->
          <realnameAuthenTip  marginTop="-7px" marginBottom="12px"></realnameAuthenTip>
          <div class="mem_back_tit" :class="memberStatus ? 'mem_back_bg':'mem_not_bg'">
              <div class="left_title" v-if="memberStatus">
                  <p class="mem_text"><span><span class="lv_text">LV.{{ pointInfos.level }}</span><span class="leaveName" style="font-weight: bold;">{{ pointInfos.name }}</span></span></p>
                  <p class="mem_info_text">
                      <span @click="openMemberBtn" class="text">会员等级说明</span>
                      <svg class="svg-icon" aria-hidden="true">
                        <use v-bind:xlink:href="'#icon-Right'"></use>
                    </svg>
                  </p>
                  <div class="left-bottom-process">
                    <div class="process-bar flex-right flex-middle" :style="`width: ${widtMode}%;`">
                      <div class="process-bar-inner">
                        <div class="process-bar-outer">{{ pointInfos.memberPoints || 0 }}/{{ pointInfos.nextMemberPoints || pointInfos.memberPoints }}</div>
                        <!-- 直角三角形 -->
                        <div class="process-bar-outer-triangle"></div>
                      </div>
                    </div>
                  </div>
                  <p class="left_bot_text">当前总易币{{ pointInfos.memberPoints || 0 }}<span v-if="pointInfos.nextMemberPoints">，距下一等级还需{{ (pointInfos.nextMemberPoints || 0) - (pointInfos.memberPoints || 0) }}易币</span></p>
              </div>
              <div class="right_class">
                  <p class="right_number_text">{{ pointInfos.balance || 0 }}</p>
                  <p class="right_two_text">可用易币</p>
                  <p class="right_jump_btn" @click="jumpMore">赚更多</p>
                  <p class="right_bot_text" @click="jumpBtn">易币明细 ></p>
              </div>
          </div>
      </div>
      <div v-if="pointInfos.expiresPoints" class="middle_text">
        <img src="../../assets/img/iconPhone.png">
        <p>本月即将过期 {{ pointInfos.expiresPoints || 0 }} 易币</p>
        <!-- <p class="right_text">
          <span>立即使用</span>
          <svg class="svg-icon" aria-hidden="true">
            <use v-bind:xlink:href="'#icon-Right'"></use>
          </svg>
        </p> -->
      </div>
      <!-- <memberUserBanner></memberUserBanner> -->
      <memVantView :memDiaShow="memDiaShow" v-if="memDiaShow" @closeMethods="closeMethods"></memVantView>
    </div>
</template>



<script>
import { mapActions } from 'vuex';
import memberUserBanner from "../../views/home/<USER>/contentComponent/memberUserBanner.vue";
import memVantView from "@/components/common/memVantView";
import realnameAuthenTip from "@/views/userCenter/components/realnameAuthenTip.vue"
import png from "../../assets/img/infoYiBi.png"
import moment from 'moment';
export default {
    data(){
        return {
            memDiaShow: false,
            widtMode: "",
        }
    },
    watch: {
        
    },
    computed: {
        memberStatus() { // 会员状态
          return this.memberStatus = this.$store.state.memberStatus;
        },
        pointInfos() {
          // 查询积分信息
          this.widtMode = ((this.$store.state.pointInfos.memberPoints || 0)/(this.$store.state.pointInfos.nextMemberPoints || this.$store.state.pointInfos.memberPoints)) * 100;
          return this.$store.state.pointInfos;
        },
    },
    components: {
        memVantView,
        memberUserBanner,
        realnameAuthenTip
    },
    mounted(){
        this.getMemberPoints();
    },
    methods: {
        ...mapActions(['getMemberPoints']),
        // 跳转赚更多
        jumpMore() {
          this.$router.push({
            name: 'missionCenter',
          })
        },
        // 关闭
        closeMethods() {
          this.memDiaShow = false;
          this.$emit("scrollMeth",false);
        },
        // 打开会员等级说明弹窗
        openMemberBtn() {
          this.memDiaShow = true;
          this.$emit("scrollMeth",true);
        },
        // 跳转易币明细页
        jumpBtn() {
          this.$router.push({
            path: '/myCoin/info',
            query: {
              platformId: this.$route.query.platformId
            }
          })
        },
    },
}
</script>



<style lang='scss' scoped>
.member_top {
    width: 100%;
    background-image: url("../../assets/img/top_bg.png");
    // width: 375px;
    border: 2px solid;
    border-image: linear-gradient(173deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1)) 2 2;
    background-size: 100% auto;
    padding: 21px 15px 5px 15px;
    .mem_back_tit {
        width: 100%;
        height: 152px;
        // background-image: url("../../assets/img/member_icon.png");
        background-size: 100% 100%;
        padding: 33px 15px 9px 13px;
        display: flex;
        justify-content: space-between;
        .left_title {
            margin-right: 30px;
            .mem_text {
                font-weight: 500;
                font-size: 18px;
                color: #26527B;
                text-align: left;
                font-style: normal;
                .lv_text {
                    color: #26527B;
                    font-weight: 600;
                    font-size: 19px;
                    margin-right: 3px;
                    font-style: italic;
                    .leaveName {
                      font-weight: 500;
                      font-size: 18px;
                      color: #26527B;
                      text-align: left;
                      font-style: normal;
                    }
                }
            }
            .mem_info_text {
                font-weight: 400;
                font-size: 11px;
                color: #7C8DA0;
                text-align: left;
                font-style: normal;
                margin-top: 6px;
                display: flex;
                align-items: center;
                .svg-icon {
                  width: 10px;
                  height: 10px;
                }
                .text {
                  margin-top: 1px;
                }
            }
            .left_bot_text {
                width: 198px;
                height: 16px;
                font-weight: 400;
                font-size: 11px;
                color: #26527B;
                line-height: 16px;
                text-align: left;
                font-style: normal;
            }
        }
        .right_class {
            height: 100px;
            // background: red;
            display: flex;
            flex-direction: column;
            align-items: center;
            .right_number_text {
                font-weight: 600;
                font-size: 26px;
                color: #26527B;
                text-align: left;
                font-style: normal;
            }
            .right_two_text {
                margin-top: 2px;
                font-weight: 400;
                font-size: 12px;
                color: #26527B;
                text-align: left;
                font-style: normal;
            }
            .right_jump_btn {
                cursor: pointer;
                width: 66px;
                height: 28px;
                background: linear-gradient( 270deg, #E9F5FE 0%, #FFFFFF 100%);
                border-radius: 14px;
                line-height: 28px;
                align-items: center;
                font-weight: 500;
                font-size: 14px;
                color: #26527B;
                font-style: normal;
                display: flex;
                justify-content: center;
                margin-top: 16px;
            }
            .right_bot_text {
                font-weight: 400;
                font-size: 11px;
                color: #26527B;
                text-align: left;
                font-style: normal;
                margin-top: 7px;
            }
        }
    }
    .mem_back_bg {
        background-image: url("../../assets/img/phone_bg.png");
    }
    .mem_not_bg {
        background-image: url("../../assets/img/notBg.png");
    }
}
.left-bottom-process {
  width: 100%;
  height: 5px;
  background: #f1f8ff;
  border-radius: 3px;
  margin-bottom: 8px;
  margin-top: 30px;
  .process-bar {
    width: 0px;
    height: 5px;
    background: linear-gradient(90deg, #4e8ab2 0%, #26527b 100%);
    border-radius: 3px;
    .process-bar-inner {
      width: 3px;
      height: 3px;
      background: #fff;
      border-radius: 3px;
      position: relative;
      margin-right: 1px;
      .process-bar-outer {
        position: absolute;
        left: 0;
        top: -24px;
        padding: 2px 3px;
        font-weight: 500;
        font-size: 12px;
        color: #26527b;
        line-height: 12px;
        background: #fff;
        border-radius: 2px;
        font-size: 10px;
      }
      .process-bar-outer-triangle {
        width: 0;
        height: 0;
        border-top: 4px solid #fff;
        border-right: 4px solid transparent;
        position: absolute;
        left: 0;
        top: -9px;
      }
    }
  }
}
.dia_class {
  // .dialogWrap{
    position: absolute;
    left:0;
    top:0;
    z-index: 999;
    bottom:0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);
// }
}
.middle_text {
  width: 345px;
  height: 32px;
  background: #FEF7E9;
  border-radius: 3px;
  border: 1px solid #FFFFFF;
  margin: 0 auto;
  display: flex;
  align-items: center;
  // justify-content: space-between;
  font-weight: 400;
  font-size: 12px;
  color: #FF6B00;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  padding: 0px 12px;
  border-radius: 3px;
  border: 1px solid #FFFFFF;
  .right_text {
    display: flex;
    align-items: center;
  }
  p {
    margin-top: 3px;
  }
  img {
    height: 12px;
    margin-right: 4px;
  }
}
</style>