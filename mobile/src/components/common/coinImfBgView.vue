<template>
    <div class="body">
        <img :src="memCoinSettingForm.mallPicMobile" alt="" @click="jumpBtn">
    </div>
</template>



<script>
import { getMallPointsSwitchStatus } from '@/api/monetary'
import moment from 'moment';
export default {
    data(){
        return {
            
        }
    },
    watch: {
        
    },
    components: {
    },
    computed: {
        memCoinSettingForm() {
            return this.$store.state.memCoinSettingForm
        }
    },
    mounted(){
    },
    methods: {
        jumpBtn() {
            getMallPointsSwitchStatus().then(res=>{
                if (res.code == this.$successCode) {
                    if (res.data) {
                        this.$router.push({ path: '/mall' })
                    } else {
                        this.$toast('正在建设中...')
                    }
                }
            })
            
        },
    },
}
</script>



<style lang='scss' scoped>
.body {
    margin-top: 10px;
    padding: 0px 16px;
}
img {
    width: 100%;
    border-radius: 3px;
}
</style>