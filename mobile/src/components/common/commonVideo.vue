<!-- 视频组件 -->
<template>
  <div class="common-video-item" @click="handleVideoClick">
    <div class="video-item-poster">
      <img :src="item.coverUrl" alt="" />
    </div>
    <!-- 视频播放按钮 -->
    <div class="video-item-play flex-center flex-middle">
      <img src="https://oss.ienmore.com/frontUpload/partUpload/2025-06-03/71cf7a0aba3027e975850a642723a6c4.png" alt="" />
    </div>
    <!-- 视频标题 -->
    <div class="video-item-title">
      <span>{{ item.title }}</span>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {
    handleVideoClick() {
      this.$router.push({
        path: "/moduleDetail/video/" + this.item.id,
      });
    },
  },
};
</script>
<style scoped lang="scss">
.common-video-item {
  width: 167px;
  height: 223px;
  border-radius: 4px;
  position: relative;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.1);
  margin-right: 10px;
  margin-bottom: 10px;
  &:nth-child(2n) {
    margin-right: 0;
  }
  .video-item-poster {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  .video-item-play {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    img {
      width: 32px;
      height: 32px;
    }
  }
  .video-item-title {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 80px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(0, 0, 0, 0.7) 100%);
    padding: 34px 6px 10px 6px;
    box-sizing: border-box;
    border-radius: 0 0 6px 6px;
    span {
      font-weight: 500;
      font-size: 12px;
      color: #ffffff;
      line-height: 18px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }
  }
}
</style>
