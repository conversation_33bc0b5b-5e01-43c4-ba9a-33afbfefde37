<template>
  <div class="common-classify-list-container">
    <div v-if="configuration.extraInfo && configuration.extraInfo.hasIcon">
      <cube-slide :auto-play="false" :data="listHasIcon">
        <cube-slide-item v-for="(iconItem, iconIndex) in listHasIcon" :key="iconIndex">
          <ul :class="['hasIcon', configuration.extraInfo.model]">
            <li  v-for="(item, index) in iconItem" :key="item.id" @click="toList(item.codeValueId)">
              <div v-if="configuration.extraInfo.hasIcon" class="icon">
                <img :src="item.textValue" />
              </div>
              <div class="title">{{ item.codeValueDesc }}</div>
            </li>
          </ul>
        </cube-slide-item>
        <template slot="dots" slot-scope="props">
          <span class="my-dot" :class="{ active: props.current === index }" v-for="(item, index) in props.dots" :key="index"></span>
        </template>
      </cube-slide>
    </div>
  </div>
</template>

<script>
import { getClassifyLive } from "@/api/medicine/homePage";
export default {
  props: ["configuration"],
  data() {
    return {
      list: [],
      listHasIcon: [],
    };
  },
  mounted() {
    this.getClassifyLive();
  },
  watch: {
    configuration() {
      this.getClassifyLive();
    },
  },
  methods: {
    /**
     * 获取API URL
     * 根据预览环境选择合适的API地址
     * @returns {string} API URL
     */
    getApiUrl() {
      return this.isPreviewEnviroment 
        ? this.configuration.previewApiUrl 
        : this.configuration.apiUrl;
    },

    // 处理banner 分页
    paginateClassifyData(data) {
      const pageSize = 10;
      const paginatedData = [];
      
      for (let i = 0; i < data.length; i += pageSize) {
        paginatedData.push(data.slice(i, i + pageSize));
      }
      
      return paginatedData;
    },

    // 处理banner 数据
    handleClassifyDataSuccess(response) {
      this.list = response.data.children || [];
      this.listHasIcon = this.paginateClassifyData(this.list);
    },
    /**
     * 获取分类列表数据
     * 从API获取分类数据并进行分页处理
     */
    async getClassifyLive() {
      try {
        const apiUrl = this.getApiUrl();
        
        const response = await getClassifyLive(apiUrl);
        
        if (response.code === this.$successCode) {
          this.handleClassifyDataSuccess(response);
        } else {
          console.warn('获取分类数据失败:', response.info);
          this.$toast && this.$toast(response.info || '获取分类数据失败');
        }
      } catch (error) {
        console.error('获取分类数据失败:', error);
      }
    },

    /**
     * 构建搜索页面的查询参数
     * @param {string|number} codeValueId - 分类ID
     * @returns {Object} 查询参数对象
     */
    buildSearchQuery(codeValueId) {
      const baseQuery = {
        modelType: this.configuration.extraInfo.model,
        platformId: this.$route.query.platformId,
        domainType: 1
      };

      // 动态设置分类参数
      const typeKey = this.configuration.extraInfo.type;
      if (typeKey) {
        baseQuery[typeKey] = codeValueId;
      }

      return baseQuery;
    },

    // 构建搜索页面URL
    buildSearchUrl(queryParams) {
      const baseUrl = `${window.location.origin}${window.location.pathname}#/medicineSearch`;
      const queryString = Object.entries(queryParams)
        .filter(([key, value]) => value !== undefined && value !== null)
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&');
      
      return queryString ? `${baseUrl}?${queryString}` : baseUrl;
    },

    /**
     * 验证导航所需的配置
     * @returns {boolean} 配置是否有效
     */
    validateNavigationConfig() {
      if (!this.configuration.extraInfo) {
        console.warn('缺少 extraInfo 配置');
        return false;
      }

      if (!this.configuration.extraInfo.model) {
        console.warn('缺少 model 配置');
        return false;
      }

      if (!this.configuration.extraInfo.type) {
        console.warn('缺少 type 配置');
        return false;
      }

      return true;
    },

    /**
     * 跳转到搜索列表页面
     * 根据分类ID构建查询参数并跳转到搜索页面
     * @param {string|number} codeValueId - 分类ID
     */
    toList(codeValueId) {
      try {
        // 验证必要参数
        if (!codeValueId) {
          console.warn('分类ID缺失');
          return;
        }

        // 验证配置
        if (!this.validateNavigationConfig()) {
          return;
        }

        // 构建查询参数和URL
        const queryParams = this.buildSearchQuery(codeValueId);
        const searchUrl = this.buildSearchUrl(queryParams);
        // 执行页面跳转
        location.assign(searchUrl);

      } catch (error) {
        console.error('页面跳转失败:', error);
      }
    },
  },
};
</script>

<style lang="scss" scope>
.common-classify-list-container {
  //background: #ffffff;
  .hasIcon {
    padding-bottom: 20px;
    padding-top: 20px;
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    justify-content: flex-start;
    &.activity {
      li {
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .icon {
        display: inline-block;
        width: 36px;
        height: 36px;
        padding: 6px;
        background: #eff4fe;
        border-radius: 18px;
        svg {
          width: 24px;
          height: 24px;
        }
      }
      .title {
        color: #333;
        font-size: 13px;
        line-height: 18px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        white-space: nowrap;
      }
    }

    &.live {
      li {
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        box-sizing: border-box;
        flex-basis: 20%;
       
      }
      .icon {
        display: inline-block;
        width: 38px;
        height: 38px;
        svg {
          width: 24px;
          height: 24px;
        }
        img {
          width: 38px;
          height: 38px;
        }
      }
      .title {
        color: #333;
        font-size: 13px;
        margin-top: 10px;
        margin-bottom: 15px;
        line-height: 18px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        white-space: nowrap;
      }
    }
  }
}
</style>
<style lang="scss">
.common-classify-list-container {
  .cube-slide-dots > span.active {
    background: rgba(20, 100, 161, 1) !important;
    width: 11px;
    height: 3px;
  }
  .cube-slide-dots > span {
    width: 4px;
    height: 3px;
    background: rgba(20, 100, 161, 0.6);
    margin: 0 4px;
  }
  &.classify-list-container-new {
    .cube-slide-dots {
      display: none;
    }
  }
}
</style>
