/** 针对通用弹窗组件 */
<template>
  <div class="common-layout-dialog" v-if="visible">
    <div class="mask"></div>
    <div class="popup-content" :class="{ 'zoom-in': animationState === 'in', 'zoom-out': animationState === 'out' }">
        <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "commonLayoutDialog",
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      animationState: "",
    };
  },
  watch: {
    value(val) {
      if (val) {
        this.showPopup();
      } else {
        this.closePopup();
      }
    },
  },
  mounted() {},
  computed: {},
  methods: {
    showPopup() {
      this.visible = true;
      this.animationState = "in";
    },
    closePopup() {
      this.animationState = "out";
      setTimeout(() => {
        this.visible = false;
        this.$emit("input", false);
      }, 300); // 动画持续时间
    },
  },
};
</script>

<style scoped lang="scss">
.common-layout-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  .mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .popup-content {
    position: relative;
    z-index: 1000;
    transition: all 0.3s ease;
  }

  .zoom-in {
    animation: zoomIn 0.3s forwards;
  }

  .zoom-out {
    animation: zoomOut 0.3s forwards;
  }

  @keyframes zoomIn {
    from {
      transform: scale(0.5);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes zoomOut {
    from {
      transform: scale(1);
      opacity: 1;
    }
    to {
      transform: scale(0.5);
      opacity: 0;
    }
  }
}
</style>
