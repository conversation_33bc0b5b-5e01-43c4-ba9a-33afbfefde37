<!-- 新版直播列表每一项 -->
<template>
  <div class="newLiveItem">
    <div class="newLiveItemContent">
      <div class="flex-left">
        <div class="item-left unit-0" @click="linkUrl(itemData)">
          <img class="item-left-img" :src="itemData.coverImg || itemData.bannerImg" alt="" />
          <!-- 针对医疗的直播，需要显示直播状态 -->
          <live-status :info="itemData" layout="bottom-right" :liveChargeTypeStyle="{ marginBottom: '4px' }" :liveTypeStyle="{ marginBottom: '4px', marginRight: '4px' }"></live-status>
        </div>
        <div class="item-right unit flex-vertical">
          <div class="item-right-top" @click="linkUrl(itemData)">
            {{ itemData.name }}
          </div>
          <!-- 针对医疗添加索引文字 -->
          <div class="item-right-index">
            <span class="item-text">{{ itemData.subName }}</span>
          </div>
          <div class="unit"></div>
          <!-- 时间 和主办单位 -->
          <div class="item-right-middle" @click="linkUrl(itemData)">
            <span class="item-text">{{ itemData.beginTime ? moment(itemData.beginTime, 1, "/", "/") : "" }}</span>
            <span class="item-text right_bizName">{{ itemData.sponsorName || itemData.organizerName }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import liveStatus from "@/components/common/liveStatus.vue";
export default {
  name: "newLiveItem",
  props: {
    itemData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      isShowEllipsis: false, // 是否显示右边的省略
      platformId: this.$route.query.platformId,
    };
  },
  created() {},
  components: {},
  computed: {},
  mounted() {},
  methods: {
    linkUrl(item) {
      if (!this.isPreviewEnviroment) {
        item.id = item.itemId;
        this.naviToDetails(this.$route.query.platformId, item.itemType, item);
      }
    },
  },
  components: {
    liveStatus,
  },
};
</script>
<style scoped lang="scss">
.newLiveItem {
  background: #ffffff;
  width: 100%;
  padding: 0 15px 12px 15px;
  box-sizing: border-box;
  border-bottom: 1px solid #eee;
  margin-bottom: 12px;
  .newLiveItemContent {
    width: 100%;
    .item-left {
      width: 120px;
      height: 72px;
      margin-right: 10px;
      flex-shrink: 0;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      .item-left-payType {
        position: absolute;
        top: 2px;
        left: 2px;
        width: 40px;
        height: 20px;
        background: rgba(0, 0, 0, 0.4);
        color: #fff;
        border-radius: 2px;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        text-align: center;
      }
      .item-left-img {
        width: 100%;
        height: 72px;
        border-radius: 4px;
        object-fit: contain;
      }
    }
    .item-right {
      .item-right-top {
        height: 36px;
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        line-height: 18px;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .item-right-middle {
        margin-top: 6px;
        display: flex;
        justify-content: space-between;
        .item-text {
          font-size: 12px;
          font-weight: 400;
          color: #666666;
          margin-right: 9px;
        }
        .right_bizName {
          float: right;
          margin-right: 0;
          max-width: 115px;
          height: 12px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .item-right-index {
        margin-top: 4px;
        .item-text {
          font-weight: 400;
          font-size: 12px;
          color: #666666;
          line-height: 14px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
