<!-- 邀请生成canvas] -->
<template>
    <div class='inviteCanvasWrap'>
        <scroll class="content-box">
            <div class="inviteCanvas">
                <div class="inviteCanvas-img flex-center">
                    <div class="inviteCanvasContent">
                        <div class="title_top">会员等级说明</div>
                        <div v-if="pointInfos.levelDesc.length" v-for="(item,index) in pointInfos.levelDesc">
                            <div v-if="item.type == 'text'" class="every text" v-html="item.text">
                            </div>
                            <div v-if="item.type == 'img'" class="every image">
                                <img :src="item.url" alt=""/>
                            </div>
                        </div>
                        <div v-if="!pointInfos.levelDesc.length" class="not_img">
                            <img src="../../assets/img/noting.png" alt="">
                            <p>暂无数据</p>
                        </div>
                    </div>
                </div>
                <div class="inviteCanvasBottom flex-center flex-middle">
                    <svg class="svg-icon" aria-hidden="true">
                        <use v-bind:xlink:href="'#icon-close'" @click="closeBtn()"></use>
                    </svg>
                </div>
            </div>
        </scroll>

    </div>
</template>

<script>
export default {
    props: ["memDiaShow"],
    data () {
        return {
            canvasImg: '',
            rulesControl: false,
            rulesTitle: '',
            indicationStatus: false,
            discountCodeRules: '',
        };
    },

    created () {
    },

    components: {

    },

    watch: {
    },

    computed: {
        pointInfos() {
          return this.$store.state.pointInfos;
        },
    },

    mounted () {
    },
    methods: {
        // 关闭
        closeBtn() {
            this.$emit('closeMethods');
        },
    },
}

</script>
<style  scoped lang="scss">
.inviteCanvasWrap {
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.7);
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    // padding: 0px 38px 0 38px;
    box-sizing: border-box;
    .content-box {
        height: 100%;
        // background: rgba(0, 0, 0, 0.7);
    }
}
.inviteCanvas-img {
    width: 100%;
    .inviteCanvasContent {
        padding: 15px;
        background: #ffffff;
        width: 280px;
        height: 476px;
        z-index: 2000;
        margin-top: 45px;
        border-radius: 10px;
        max-height: 100%;
        overflow-y: auto;
        .invateImage {
            width: 100%;
            height: 100%;
        }
        .title_top {
            display: flex;
            justify-content: center;
            margin-bottom: 12px;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            line-height: 22px;
            text-align: left;
            font-style: normal;
        }
        .text {
            line-height: 20px;
        }
    }
}
.inviteCanvasBottom {
    color: red;
    width: 100%;
    height: 32px;
    margin-top: 28px;
    z-index: 2000;
    position: absolute;
    img {
        // width: 32px;
        height: 32px;
    }
}
.canvasImages {
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 8px;
}
.invite-group-btn {
    margin-top: 12px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    margin-bottom: 15px;
    .invite-btn-img {
        width: 135px;
        height: 44px;
        font-size: 15px;
        background: #fff8f3;
        box-shadow: inset 0px 8px 20px 0px rgba(255, 107, 0, 0.3);
        border-radius: 27px;
        color: var(--color-primary);
        line-height: 18px;
        text-align: center;
        margin-right: 10px;
    }
    .invite-btn-forward {
        width: 135px;
        height: 44px;
        font-size: 14px;
        background: linear-gradient(138deg, #ffa400 0%, #ff6b00 100%);
        box-shadow: inset 0px 2px 5px 0px #ffe3cf;
        border-radius: 27px;
        color: #ffffff;
        line-height: 44px;
        text-align: center;
        word-break: break-word;
        img {
            width: 23px;
            height: 23px;
            margin-right: 7px;
        }
    }
}
.activityRule {
    width: 100%;
    height: 16px;
    font-size: 13px;
    text-align: center;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #eeeeee;
    line-height: 16px;
}
:deep(.rulesSheet) {
    .van-action-sheet__header {
        text-align: left;
        padding-left: 16px;
        .van-action-sheet__close {
            color: #333;
            font-size: 15px;
        }
    }
    .van-action-sheet__content {
        .content {
            .rules-content {
                width: 100%;
                border-top: 1px solid #f1f1f1;
                padding: 13px 15px 100px;
                box-sizing: border-box;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #666666;
                line-height: 20px;
            }
        }
    }
}
.indicationMask {
    width: 100%;
    background: rgb(102,102,102);
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
    img {
        position: absolute;
        right: 24px;
        top: 10px;
        width: 99px;
        height: 71px;
    }
    .indicationText {
        margin-top: 90px;
        text-align: center;
        .indicationText-top {
            font-size: 16px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 23px;
            text-shadow: 0px 2px 10px rgba(0, 0, 0, 0.5);
            margin-bottom: 11px;
        }
        .indicationText-bottom {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            text-shadow: 0px 2px 10px rgba(0, 0, 0, 0.5);
        }
    }
}
.every {
    margin-bottom: 15px;
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    text-align: left;
    font-style: normal;
}
.svg-icon {
    width: 32px;
}
.not_img {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    color: #666666;
    font-size: 14px;
    height: 90%;
    img {
        height: 83px;
        width: auto;
        margin-top: 20px;
        margin-bottom: 15px;
    }
}
</style>