<template>
    <div class="chartList_wrap flex-left">
        <div class="unit-1-3 chartlist_left">
          <img :src="item.url" alt="">
        </div>
        <div class="unit-2-3 chartlist_right">
          <div class="chartlist_right_top">
            {{item.courseName}}
          </div>
          <div class="chartlist_right_bottom">
            <span class="chartlist_right_s1" v-if="item.pageView"></span>
            <span class="chartlist_right_s2" v-if="item.pageView">{{item.pageView}}</span>
            <span class="chartlist_right_s3" v-if="item.praiseCount"></span>
            <span class="chartlist_right_s4" v-if="item.praiseCount">{{item.praiseCount}}</span>
            <span class="chartlist_right_s5" v-if="item.collect"></span>
            <span class="chartlist_right_s6" v-if="item.collect">{{item.collect}}</span>
          </div>
        </div>
    </div>
</template>
<script>
/**
 * 个人中心页面使用，如个人中心被清除则删掉
 */
    export default {
        name: "chatList",
        props:['item'],
        components:{
          
        },
        data(){
            return {
              
            }
        },
        methods:{
            
        }
    }
</script>

<style scoped lang="scss">

 .chartList_wrap{
   width:100%;
   height: 94px;
   border-bottom:1px solid #e4e3e3;
 }
 .chartlist_left{
   display: flex;
   align-items: center;
 }
   .chartlist_left img{
     width:120px;
     height: 70px;
     
     border-radius: 5px;
   }
   .chartlist_right{
     padding: 9px 0 0 17px;
     box-sizing: border-box;
   }
   .chartlist_right_top{
     height: 42px;
     font-size: 14px;
     color:#333;
     line-height: 22px;
     display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
   }
   .chartlist_right_bottom{
     margin-top: 12px;
     font-size:10px;
   }
   .chartlist_right_s1{
     float: left;
     width: 10px;
     height: 9px;
     background: url('../../assets/img/video.png') no-repeat;
     background-size:100% 100%;
     margin-right: 6px;
   }
   .chartlist_right_s2{
      float: left;
     margin-right: 8px;
   }
   .chartlist_right_s3{
     float: left;
     width: 10px;
     height: 9px;
     background: url('../../assets/img/good.png') no-repeat;
     background-size:100% 100%;
      margin-right: 4px;
   }
   .chartlist_right_s4{
      float: left;
     margin-right: 10px;
   }
   .chartlist_right_s5{
      float: left;
     width: 10px;
     height: 9px;
     background: url('../../assets/img/start1.png') no-repeat;
     background-size:100% 100%;
      margin-right: 4px;
   }

</style>