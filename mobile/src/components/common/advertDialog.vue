<template>
  <div class="ad-dialog-wrap" @touchmove.prevent>
    <div class="ad-dialig-content flex-middle flex-center flex-vertical" :class="animationClass" data-wow-duration="0.6s" data-wow-delay="0.5s">
      <img :src="advertisement.picture" alt="" class="ad-img" @click="handlerPage" />
      <img src="../../assets/img/close_white.png" alt="" class="ad-close" @click="closeDialog" />
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      animationClass: "wow zoomIn",
    };
  },
  inject: ["updateWowDom"],
  mounted() {
    this.$nextTick(() => {
      this.updateWowDom();
    });
  },
  computed: {
    advertisement() {
      return this.$store.state.advertisement;
    },
  },
  methods: {
    handlerPage() {
      if (this.advertisement.url) {
        window.location.href = this.advertisement.url;
      }
    },
    closeDialog() {
      this.animationClass = "wow zoomOut";
      setTimeout(() => {
        this.$store.commit("changeIsShowAd", false);
        this.$store.commit("changeAdvertisement", {});
        this.animationClass = "wow zoomIn";
      }, 800);
    },
  },
};
</script>
<style scoped lang="scss">
.ad-dialog-wrap {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.4);
  position: fixed;
  z-index: 10;
  top: 0;
  left: 0;
  .ad-dialig-content {
    width: 100%;
    height: 100%;
    .ad-img {
      width: 310px;
      height: auto;
      border-radius: 10px;
    }
    .ad-close {
      width: 28px;
      height: 28px;
      margin-top: 15px;
    }
  }
}
</style>
