/** 针对bioChina活动的弹窗组件 */
<template>
  <commonLayoutDialog v-model="bioChinaShow">
    <div class="bio-china-content">
      <div class="bio-china-content-title flex-middle">
        <img src="https://oss.ienmore.com/frontUpload/partUpload/2025-02-25/9fab53150e14c57d97ecaebdad0ce099.png" alt="" />
        <span>温馨提示</span>
      </div>
      <div class="bio-china-content-desc">您可以使用BIOCHINA2025报名时上传的信息完成一键实名</div>
      <div class="bio-china-content-card">
        <img :src="bioChinaImg" alt="" />
      </div>
      <div class="bio-china-content-btn flex-center">
        <!-- 暂不实名 -->
        <div class="bio-china-content-btn-cancel" @click="close">暂不实名</div>
        <!-- 立即实名 -->
        <div class="bio-china-content-btn-confirm" @click="confirm">一键实名</div>
      </div>
    </div>
  </commonLayoutDialog>
</template>
<script>
import commonLayoutDialog from "@/components/common/commonLayoutDialog.vue";
import { getOneKeyAuth } from "@/api/memberApi";
import { mapActions } from "vuex";
export default {
  data() {
    return {};
  },
  components: {
    commonLayoutDialog,
  },
  mounted() {},
  computed: {
    bioChinaShow() {
      return this.$store.state.bioChinaShow;
    },
    bioChinaImg() {
      return this.$store.state.bioChinaImg;
    },
  },
  methods: {
    ...mapActions(["getMemberAuthStatus"]),
    async close() {
      this.$store.commit("changeBioChinaShow", false);
      await new Promise((resolve) => setTimeout(resolve, 300));
      this.$store.commit("changeUserPopShow", true);
    },
    async confirm() {
      const params = {
        businessUrl: this.bioChinaImg,
      };
      await getOneKeyAuth(params);
      this.$store.commit("changeBioChinaShow", false);
      this.$toast("实名认证成功");
      this.getMemberAuthStatus();
    },
  },
};
</script>
<style scoped lang="scss">
.bio-china-content {
  width: 345px;
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.2);
  border-radius: 4px 4px 4px 4px;
  padding: 30px 20px 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  .bio-china-content-title {
    margin-bottom: 10px;
    img {
      width: 24px;
      height: 24px;
      margin-right: 10px;
    }
    span {
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      line-height: 24px;
    }
  }
  .bio-china-content-desc {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 18px;
    margin-bottom: 10px;
  }
  .bio-china-content-card {
    width: 305px;
    height: 305px;
    background-color: #eee;
    margin-bottom: 20px;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  .bio-china-content-btn {
    .bio-china-content-btn-cancel {
      margin-right: 15px;
      border-radius: 3px 3px 3px 3px;
      border: 1px solid #d9d9d9;
      padding: 9px 14px;
      font-weight: 400;
      font-size: 12px;
      color: #666666;
      line-height: 13px;
    }
    .bio-china-content-btn-confirm {
      border-radius: 3px 3px 3px 3px;
      border: 1px solid #d9d9d9;
      padding: 9px 14px;
      font-weight: 400;
      font-size: 12px;
      color: #fff;
      line-height: 13px;
      background-color: var(--color-primary);
    }
  }
}
</style>
