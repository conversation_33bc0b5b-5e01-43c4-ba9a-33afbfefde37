<template>
    <div class="infoItem">
        <div @click="itemClick" class="item-body">
            <div class="item-image-container">
                <img class="item-image" :src="info.bannerImg" alt="">
            </div>
            <div class="item-right-container">
                <div class="item-title-box">
                    <div class="item-title">{{info.name}}</div>
                </div>
                <div class="info-detail-between flex-left flex-middle">
                    <div class="unit-0 info-left">
                        <span style="margin-right:4px;">{{info.statusText}}</span>
                        <span v-if="info.duration">{{info.duration}}分钟</span>
                    </div>
                    <div class="unit"></div>
                    <div class="unit-0">
                        <div class="info-btn" v-if="info.signStatus != '1'">报名</div>
                        <div class="disbtn" v-else>已报名</div>
                    </div>
                </div>
                <div style="border:.5px solid #eee;margin-top:13px;"></div>
            </div>
        </div>
    </div>
</template>
<script>
export default {

    components: {},
    props: ['info'],
    data () {
        return {
            content: {
                coverImg: '',
                name: '',

            }
        };
    },
    computed: {

    },
    mounted () {

    },
    methods: {
        itemClick () {
            let info = JSON.parse(JSON.stringify(this.info))
            this.naviToDetails(localStorage.getItem('platformId'), this.info.type, info);
        },
    }
}
</script>
<style lang='scss' scoped>
.v-line-speech {
    margin-left: 145px;
    height: 1px;
    background-color: #eeeeee;
    width: calc(100% - 159px);
    transform: scaleY(0.5);
}
.infoItem {
}
.item-body {
    padding: 10px 15px;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: row;
}
.item-image-container {
    flex-shrink: 0;
    height: 80px;
    width: 120px;
    position: relative;
    margin-right: 10px;
}
.item-right-container {
    display: flex;
    flex-direction: column;
    width: 220px;
}
.item-image {
    border-radius: 2px;
    height: 72px;
    width: 120px;
}
.item-tag {
    position: absolute;
    top: 0;
    left: 0;
    color: white;
    font-size: 10px;
    background-color: var(--color-primary);
    opacity: 0.8;
    border-radius: 4px 0 4px 0;
    padding: 3px;
}
.item-title-box {
    height: 40px;
}
.item-title {
    font-weight: 400;
    word-break: break-word;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    font-size: 14px;
    line-height: 18px;
}

.info-detail {
    margin-top: 6px;
    justify-content: left;

    height: 16px;
    font-size: 11px;
    .tagDetail {
        margin-right: 6px;
        padding: 0 6px;
        line-height: 16px;
        display: inline-block;
    }
    .lines1 {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }
}

.info-detail-between {
    font-size: 12px;
    margin-top: 6px;
    .info-left{
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
    }
    .info-btn{
        width: 54px;
        height: 24px;
        background: var(--color-primary);
        border-radius: 13px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        text-align: center;
        line-height: 24px;
    }
    .disbtn{
        width: 54px;
        height: 24px;
        background: #F5F5F5;
        border-radius: 13px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999;
        text-align: center;
        line-height: 24px;
    }
}
</style>