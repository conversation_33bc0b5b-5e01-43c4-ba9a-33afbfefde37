/** * 产品标签组件 */

<template>
  <span class="product-tag-wrap" :style="{ color: item.textColor, background: item.bgColor }">{{ item.name }}</span>
</template>
<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {},
};
</script>
<style scoped lang="scss">
.product-tag-wrap {
  border-radius: 2px;
  padding: 2px 6px;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  margin-right: 8px;
  display: inline-block;
  margin-bottom: 3px;
}
</style>
