/** 产品中等模块大小 */
<template>
  <div class="product-medium-wrap flex-left" @click="handleClick">
    <div class="product-medium-img-container unit-0">
      <img :src="item.productImg" alt="" />
    </div>
    <div class="product-medium-info unit flex-vertical">
      <div class="product-medium-name">{{ item.name }}</div>
      <div class="product-medium-tag-container" :class="{'carousel-tag-container': !isPreviewEnviroment && isIOS, 'tag-container': type != 'carousel'}">
        <product-tag v-for="(item, index) in categoryList" :key="index" :item="item" />
      </div>
      <div class="unit"></div>
      <div class="product-medium-company-container flex-middle">
        <div class="product-medium-company-img unit-0">
          <img :src="item.busImg" alt="" />
        </div>
        <div class="product-medium-company-name unit">
          <span>{{ item.busName }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import ProductTag from "./productTag.vue";
import { exhibitorClientUrl } from "@/config/env";
export default {
  props: {
    item: {
      type: Object,
      default: () => {},
    },
    // 判断类型
    type: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      categoryList: [],
    };
  },
  components: {
    ProductTag,
  },
  mounted() {
    console.log(this);
    // 进行分类的组合
    const formatTags = (tagString, config) => {
      if (!tagString) return [];
      return tagString.split(",").map((name) => ({
        name,
        textColor: config.textColor,
        bgColor: config.bgColor,
      }));
    };

    const tagConfigs = {
      classify: {
        textColor: "#FF6B00",
        bgColor: "rgba(255,107,0,0.1)",
      },
      laboratory: {
        textColor: "#00629F",
        bgColor: "rgba(0,98,159,0.1)",
      },
    };

    this.categoryList = [...formatTags(this.item.classifyName, tagConfigs.classify), ...formatTags(this.item.laboratoryClName, tagConfigs.laboratory)];
  },
  computed: {
    // 判断是否是苹果手机
    isIOS() {
      const ua = navigator.userAgent;
      return /(iPhone|iPad|iPod|iOS)/i.test(ua);
    },
  },
  methods: {
    handleClick() {
      let url = `${exhibitorClientUrl}/productStore?productId=${this.item.id}&exhibitorId=${this.item.exhibitorId}`;
      window.location.href = url;
    },
  },
};
</script>
<style scoped lang="scss">
.product-medium-wrap {
  width: 100%;
  background: #f7f7f7;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #efefef;
  padding: 4px;
  .product-medium-img-container {
    width: 84px;
    height: 79px;
    margin-right: 8px;
    background: #ffffff;
    flex-shrink: 0;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      border-radius: 4px;
    }
  }
  .product-medium-info {
    min-width: 0;
    .product-medium-name {
      font-weight: 400;
      font-size: 13px;
      color: #333333;
      line-height: 15px;
      margin-bottom: 6px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
    }
    .product-medium-tag-container {
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      width: 100%;  
    }
    .carousel-tag-container {
      white-space: nowrap;
    }
    .tag-container {
      display: -webkit-box;
    }
    .product-medium-company-container {
      width: 100%;
      .product-medium-company-img {
        width: 16px;
        height: 16px;
        margin-right: 6px;
        background: #ffffff;
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
      .product-medium-company-name {
        min-width: 0;
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        line-height: 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>
