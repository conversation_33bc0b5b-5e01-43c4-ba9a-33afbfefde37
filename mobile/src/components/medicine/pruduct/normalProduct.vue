/** 产品普通模块 */
<template>
  <div class="product-normal-wrap" @click="handleClick">
    <div class="product-normal-img-container">
      <img :src="item.productImg" alt="" />
    </div>
    <div class="product-normal-info">
      <div class="product-normal-name">{{ item.name }}</div>
      <div class="product-normal-tag-container" :class="{'tag-container': !isPreviewEnviroment && isIOS}">
        <product-tag v-for="(item, index) in categoryList" :key="index" :item="item" />
      </div>
      <div class="product-normal-company-container flex-middle">
        <div class="product-normal-company-img unit-0">
          <img :src="item.busImg" alt="" />
        </div>
        <div class="product-normal-company-name unit">
          <span>{{ item.busName }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import productTag from "./productTag.vue";
import { exhibitorClientUrl } from "@/config/env";
export default {
  name: "normalProduct",
  props: {
    item: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    productTag,
  },
  data() {
    return {
      // 组合分类
      categoryList: [],
    };
  },
  mounted() {
    // 进行分类的组合
    const formatTags = (tagString, config) => {
      if (!tagString) return [];
      return tagString.split(",").map((name) => ({
        name,
        textColor: config.textColor,
        bgColor: config.bgColor,
      }));
    };

    const tagConfigs = {
      classify: {
        textColor: "#FF6B00",
        bgColor: "rgba(255,107,0,0.1)",
      },
      laboratory: {
        textColor: "#00629F",
        bgColor: "rgba(0,98,159,0.1)",
      },
    };

    this.categoryList = [...formatTags(this.item.classifyName, tagConfigs.classify), ...formatTags(this.item.laboratoryClName, tagConfigs.laboratory)];
  },
  computed: {
    isIOS() {
      const ua = navigator.userAgent;
      return /(iPhone|iPad|iPod|iOS)/i.test(ua);
    },
  },
  methods: {
    handleClick() {
        let url = `${exhibitorClientUrl}/productStore?productId=${this.item.id}&exhibitorId=${this.item.exhibitorId}`;
        window.location.href = url;
    },
  },
};
</script>
<style scoped lang="scss">
.product-normal-wrap {
  width: 167px;
  padding: 0 6px;
  box-sizing: border-box;
  background: #fff;
  .product-normal-img-container {
    width: 100%;
    height: 167px;
    margin-bottom: 8px;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      border-radius: 4px;
    }
  }
  .product-normal-info {
    width: 100%;
    .product-normal-name {
      height: 32px;
      font-weight: 400;
      font-size: 13px;
      color: #333333;
      line-height: 16px;
      // 2行省略
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-word;
      margin-bottom: 8px;
    }
    .product-normal-tag-container {
      margin-bottom: 8px;
      width: 100%;
      //1行省略
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
    .tag-container {
      white-space: nowrap;
    }
    .product-normal-company-container {
      width: 100%;
      margin-bottom: 10px;
      .product-normal-company-img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
      .product-normal-company-name {
        min-width: 0px;
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        line-height: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
