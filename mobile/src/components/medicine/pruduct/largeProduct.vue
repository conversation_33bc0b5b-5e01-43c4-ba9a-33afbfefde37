/** 产品大图展示形式 */
<template>
  <div class="product-large-wrap" @click="handleClick">
    <div class="product-large-top">
      <div class="product-large-top-img">
        <img :src="item.productImg" alt="" />
      </div>
      <div class="product-large-top-tag" :class="{ 'tag-container': isIOS && !isPreviewEnviroment }">
        <product-tag v-for="(item, index) in categoryList" :key="index" :item="item" />
      </div>
    </div>
    <div class="product-large-bottom">
      <div class="product-large-bottom-info-name">{{ item.name }}</div>
      <div class="product-large-bottom-info-company flex-middle">
        <div class="product-large-bottom-info-company-img unit-0">
          <img :src="item.busImg" alt="" />
        </div>
        <div class="product-large-bottom-info-company-name unit">
          {{ item.busName }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import ProductTag from "./productTag.vue";
import { exhibitorClientUrl } from "@/config/env";
export default {
  props: {
    item: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    ProductTag,
  },
  data() {
    return {
      categoryList: [],
    };
  },
  mounted() {
    // 进行分类的组合
    const formatTags = (tagString, config) => {
      if (!tagString) return [];
      return tagString.split(",").map((name) => ({
        name,
        textColor: config.textColor,
        bgColor: config.bgColor,
      }));
    };

    const tagConfigs = {
      classify: {
        textColor: "#FF6B00",
        bgColor: "rgba(255,107,0,0.1)",
      },
      laboratory: {
        textColor: "#00629F",
        bgColor: "rgba(0,98,159,0.1)",
      },
    };

    this.categoryList = [...formatTags(this.item.classifyName, tagConfigs.classify), ...formatTags(this.item.laboratoryClName, tagConfigs.laboratory)];
  },
  computed: {
    isIOS() {
      const ua = navigator.userAgent;
      return /(iPhone|iPad|iPod|iOS)/i.test(ua);
    },
  },
  methods: {
    handleClick() {
      let url = `${exhibitorClientUrl}/productStore?productId=${this.item.id}&exhibitorId=${this.item.exhibitorId}`;
      window.location.href = url;
    },
  },
};
</script>
<style scoped lang="scss">
.product-large-wrap {
  width: 100%;
  background: #f7f7f7;
  border-radius: 6px;
  padding: 5px;
  box-sizing: border-box;
  .product-large-top {
    width: 100%;
    height: 294px;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #e5e5e5;
    background: #fff;
    padding: 10px;
    box-sizing: border-box;
    .product-large-top-img {
      width: 100%;
      height: 244px;
      img {
        width: 100%;
        height: 100%;
        object-fit: fill;
        border-radius: 4px;
      }
    }
    .product-large-top-tag {
      width: 100%;
      height: 20px;
      margin-top: 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
    .tag-container {
      white-space: nowrap;
    }
  }
  .product-large-bottom {
    width: 100%;
    margin-top: 10px;
    margin-bottom: 5px;
    .product-large-bottom-info-name {
      width: 100%;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 8px;
    }
    .product-large-bottom-info-company {
      width: 100%;
      .product-large-bottom-info-company-img {
        width: 16px;
        height: 16px;
        margin-right: 6px;
        background: #fff;
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
      .product-large-bottom-info-company-name {
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        line-height: 16px;
      }
    }
  }
}
</style>
