/** 轮播图产品模式 */
<template>
  <div class="carousel-product-wrap">
    <cube-slide :data="sourceData" :interval="3000">
      <cube-slide-item v-for="(item, index) in sourceData" :key="index">
        <medium-product :item="item" type="carousel"/>
      </cube-slide-item>
    </cube-slide>
  </div>
</template>
<script>
import MediumProduct from "./mediumProduct.vue";
export default {
  props: {
    sourceData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  components: {
    MediumProduct,
  },
  mounted() {},
  computed: {},
  methods: {},
};
</script>
<style scoped lang="scss">
.carousel-product-wrap {
  width: 100%;
  height: 100px;
  box-sizing: content-box;
  :deep(.cube-slide-dots) {
    span {
      width: 4px;
      height: 3px;
      background: rgba(9,125,180,0.5);
    }
    .active {
      background: var(--color-primary);
      height: 3px;
      width: 11px;
    }
  }
}
</style>
