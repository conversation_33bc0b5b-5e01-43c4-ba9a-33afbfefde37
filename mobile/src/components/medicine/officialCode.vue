<!-- 底部弹出的 长按关注公众号 组件 -->
<template>
    <div v-show="visiable" class="hidden-message" @touchmove.prevent>
        <div class="hidden-mask" @click="close"></div>
        <div class="hidden-card-outside">
					<div class="hidden-card">
						<div :style="remindList&&remindList.length>1?'width: 50%;':''" class="hidden-message-title" v-for="item,index in remindList" :key="index">
								<span>{{item.shareTitle}}</span>
						</div>
					</div>
					<div class="hidden-card">
						<div :class="index==1?'second secondBorder':'second' " v-for="item,index in remindList" :key="index" :style="remindList.length&&remindList.length>1?'width:50%':''">
							<!-- <div class="hidden-message-title">
									<span>{{item.shareTitle}}</span>
							</div> -->
							<div class="QRcode">
									<img class="info-image" :src="item.shareImgUrl" alt="">
									<i></i>
									<i></i>
									<i></i>
									<i></i>
							</div>
							<div class="shareDesc" v-html="shareDesc(item.shareDesc)"></div>
						</div>
					</div>
        </div>
    </div>
</template>

<script>
import { getHomeShare } from '@/api/medicine/homePage'
export default {
    name: 'officialCode',
    props: [ 'visiable'],
		data() {
			return {
				remindList: ''
			}
		},
		mounted() {
			this.getHomeShare()
		},
    methods: {
        // 关闭
        close() {
            this.$emit("update:visiable",false);
        },
				shareDesc(array) {
					let array1 = []
					for (let child of array.split(/\n/g)) {
						if (child.length>14) {
							array1.push(child.substring(0,14))
						} else {
							array1.push(child)
						}
					}
					return array1.join('<br>')
				},
				getHomeShare () {
            getHomeShare({ type: 'remind' }).then(res => {
                if (res.code == this.$successCode) {
                    this.remindList = res.data
                } else {
                    this.$message.error(res.info);
                }
            })
        },
    },
}
</script>

<style scoped lang="scss">
$color: #004da1;
.hidden-message {
    z-index: 100;
    position: fixed;
    width: 100%;
    // bottom: 0px;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
        background: rgba($color: #000000, $alpha: 0.3);
    .hidden-mask {
        flex: 1;
    }
		.hidden-card-outside{
			width: 100%;
			background: #ffffff;
			padding: 30px 0 55px;
			border-top-left-radius: 10px;
			border-top-right-radius: 10px;
			position: fixed;
			left:0;
			bottom:0;
			z-index: 200;
		}
    .hidden-card {
        width: 100%;
				float: left;
    }
		.second{
			padding: 0 20px;
			text-align: center;
			position: relative;
			float: left;
			width: 100%;
		}
		.secondBorder .QRcode::before{
				display: inline-block;
				width: 1px;
				height: 120px;
				background-color: #DEDEDE;
				content: "";
				position: absolute;
				top: 0;
				left: -36px;
		}
}
.QRcode{
	position: relative;
	padding: 7px;
	margin: 0 auto 22px;
	width: 114px;
	height: 114px;
	box-sizing: border-box;
	position: relative;
	.info-image {
		width: 100px;
		height: 100px;
	}
	i{
		position: absolute;
		display: inline-block;
		width: 14px;
		height: 14px;
	}
	i:nth-of-type(1){
		top: 0;
		left: 0;
		border-top: 0.5px solid #1464A1;
		border-left: 0.5px solid #1464A1;
	}
	i:nth-of-type(2){
		top: 0;
		right: 0;
		border-top: 0.5px solid #1464A1;
		border-right: 0.5px solid #1464A1;
	}
	i:nth-of-type(3){
		bottom: 0;
		left: 0;
		border-bottom: 0.5px solid #1464A1;
		border-left: 0.5px solid #1464A1;
	}
	i:nth-of-type(4){
		bottom: 0;
		right: 0;
		border-bottom: 0.5px solid #1464A1;
		border-right: 0.5px solid #1464A1;
	}
}
.hidden-message-title {
		text-align: center;
		color: #333333;
		font-size: 14px;
		margin-bottom: 17px;
		line-height: 20px;
		
		
			padding: 0 20px;
		float: left;
		width: 100%;
		margin: 0 auto 22px;
}
.shareDesc{
	line-height:18px;
	color:#666;
	font-size: 13px;
}
</style>