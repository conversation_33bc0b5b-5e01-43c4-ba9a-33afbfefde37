<template>
    <div class="infoRecommend">
        <div @click="itemClick" class="item-body">
            <div class="item-image-container" v-if="!styleType">
                <img class="item-image" :src="info.bannerImg" alt="">
								<liveIngIcon class="liveing" v-if="info.liveInPorgress==1"></liveIngIcon>
                <!-- <div v-show="true" class="item-tag">{{info.conferenceType}}</div> -->
            </div>
            <div class="item-right-container">
                <div class="item-title-box">
                    <div class="item-title">{{info.activityName || info.name}}</div>
                </div>
                <div>
                    <!-- <div class="info-detail-between">
                    </div> -->
                    <div class="infoRecommend-detail">
											<div class="tagDetail"
												:key="indexs+'tag'"
												v-if="tag"
												:style="'background: rgba(235, 123, 33, 0.2);color:rgba(235, 123, 33, 1)'"
												v-for="(tag, indexs) in info.channelList">
												{{tag}}
											</div>
											<div class="tagDetail"
												:key="indexs+'typeTag'"
												v-if="tag"
												:style="'background: rgba(0, 137, 134, 0.2);color:rgba(0, 137, 134, 1)'"
												v-for="(tag, indexs) in info.typeList">
												{{tag}}
											</div>
											<div class="tagDetail"
												:key="indexs"
												v-if="tag"
												:style="'background: rgba(6, 91, 152, 0.2);color:rgba(6, 91, 152, 1)'"
												v-for="(tag, indexs) in info.classifyList">
												{{tag}}
											</div>
                    </div>
                </div>
            </div>
            <div class="item-image-container" v-if="styleType=='right'" style="margin: 0 0 0 10px;">
                <img class="item-image" :src="info.bannerImg" alt="">
                <!-- <div v-show="true" class="item-tag">{{info.conferenceType}}</div> -->
            </div>
        </div>
        <!-- <div v-show="!iflast" class="v-line"></div> -->
    </div>
</template>
<script>
import liveIngIcon from '@/views/home/<USER>/medicalCompontent/liveIngIcon.vue';
export default {
    /**
     *  临时页面的信息流 item
     */
    components: {
			liveIngIcon,
		},
		// styleType 样式，默认图片在右边，，right  图片在左边
    props: ['info', 'iflast', 'showTag','styleType'],
    data () {
        return {
        };
    },
    computed: {

    },
    mounted () {

    },
    methods: {
        itemClick () {
            let info = JSON.parse(JSON.stringify(this.info))
            // info.id = info.itemId
						if (info.itemType=="activity") {
							this.naviToDetails(this.$route.query.platformId, info.itemType, info)
						} else {
							info.id = info.itemId
							this.naviToDetails(localStorage.getItem('platformId'), info.itemType, info)
						}
            
        },
    }
}
</script>
<style lang='scss' scoped>
.infoRecommend {
}
.item-body {
    padding:10px;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    background: #fff;
    margin-top: 15px;
    border-radius: 4px;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
}
.item-image-container {
    flex-shrink: 0;
    height: 72px;
    width: 120px;
    position: relative;
    margin-right: 10px;
}
.item-right-container {
    width: 192px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.liveing{
	position: absolute;
	top: 0px;
	left: 0px;
}
.item-image {
    border-radius: 5px;
    height: 72px;
    width: 120px;
}
.item-tag {
    position: absolute;
    bottom: 2px;
    right: 2px;
    color: white;
    font-size: 10px;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0.8;
    border-radius: 1px;
    padding: 3px;
}
.item-title-box {
    min-height: 56px;
}
.item-title {
    font-weight: 500;
    word-break: break-word;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    font-size: 13px;
    line-height: 18px;
		font-size: 14px;
		color: #333;
		font-weight: bold;
		white-space: normal;
}

.infoRecommend-detail {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    /* align-items:flex-end; */
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    	height: 16px;
    	font-size: 11px;
    	.tagDetail{
    		margin-right: 6px;
    		margin-bottom: 5px;
    		padding: 0 6px;
    		line-height: 16px;
    		display: inline-block;
				border-radius: 1px;
    	}
}

.info-detail-between {
    margin-top: 5px;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    align-items: center;
    // margin-top: 8px;
    justify-content: space-between;
    > div {
        width: 100%;
        font-size: 12px;
        font-weight: 400;
        color: rgba(102, 102, 102, 1);
        line-height: 17px;
    }
}
.info-tag {
    display: flex;
    align-items: center;
    background: #ffecde;
    border-radius: 2px;
    margin-right: 5px;
    color: #ff620d;
    padding: 3px 5px;
    font-size: 10px;
}
</style>