<template>
  <div class="collect-exhibitor-item-wrap" @click="goExhibitorDetail(info)">
    <div class="exhibitor-item-content flex-left">
      <div class="exhibitor-item-img-container unit-0 flex-center flex-middle">
        <img class="exhibitor-item-img unit-0" :src="info.bannerImg" alt="" />
      </div>
      <div class="exhibitor-item-info unit">
        <div class="exhibitor-item-title">{{ info.name }}</div>
        <div class="exhibitor-item-company">{{ info.busName }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import { exhibitorClientUrl } from "@/config/env";
export default {
  name: "collectExhibitorItem",
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {
    goExhibitorDetail(info) {
      let url = `${exhibitorClientUrl}/sellerStore?id=${info.itemId}`;
      window.location.href = url;
    },
  },
};
</script>
<style scoped lang="scss">
.collect-exhibitor-item-wrap {
  width: 100%;
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  .exhibitor-item-content {
    width: 100%;
    padding: 10px;
    border-radius: 4px;
    background: #fff;
    box-sizing: border-box;
    .exhibitor-item-img-container {
      width: 60px;
      height: 60px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #eeeeee;
      padding: 8px;
      box-sizing: border-box;
      .exhibitor-item-img {
        object-fit: contain;
      }
    }
    .exhibitor-item-info {
      min-width: 0;
      overflow: hidden;
      margin-left: 10px;
      .exhibitor-item-title {
        margin-top: 5px;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 22px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-bottom: 8px;
      }
      .exhibitor-item-company {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
