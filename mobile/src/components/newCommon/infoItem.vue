<template>
<div class="infoItem" :class="{isLiveRoom: info.itemType=='liveRoom'}">
    <template v-if="info.itemType=='liveRoom'">
        <div class="live-room-item" @click="toLiveRoom(info.id)">
          <img :src="info.logo">
          <div class="live-room-name"><div>{{info.name}}</div></div>
        </div>
    </template>
    <template v-else>
        <div @click="itemClick" class="item-body">
            <div class="item-image-container">
                <img class="item-image" :src="info.coverImg" alt="" v-if="info.itemType!='activity'">
                <img class="item-image" :src="info.cover" alt="" v-else>
                <div v-show="showTag" class="item-tag">{{info.conferenceType}}</div>
            </div>
            <div class="item-right-container">
                <div class="item-title-box">
                    <div class="item-title">{{info.name}}</div>
                </div>
                <div>
                    <div class="info-detail">
                        <span class="info-tag" v-for="(tag,index) in info.tagList" v-show="ifshowTag(info.tagList,index)">{{tag.tagName}}</span>
                    </div>

                    <div class="info-detail-between">
                        <div>
                            <svg v-if="[TYPE[0],TYPE[1],TYPE[5],TYPE[6]].includes(info.type)" style="margin-right:4px;" width="12px" height="12px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <g id="画板备份-2" transform="translate(-33.000000, -1675.000000)" fill-rule="nonzero">
                                        <g id="编组-12" transform="translate(20.000000, 1534.000000)">
                                            <g id="时间备份-8" transform="translate(11.000000, 139.000000)">
                                                <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="24" height="24"></rect>
                                                <path d="M12,2 C6.45833334,2 2,6.45833334 2,12 C2,17.5416667 6.45833334,22 12,22 C17.5416667,22 22,17.5416667 22,12 C22,6.45833334 17.5416667,2 12,2 Z M16.875,13.6666667 L11.2083333,13.6666667 L11.1666667,13.6666667 C11.0416667,13.6666667 10.9166667,13.5833333 10.8333333,13.4583333 C10.7916667,13.375 10.75,13.3333333 10.75,13.25 L10.75,13.2083333 L10.75,7.45833334 C10.75,7.20833334 10.9166667,7 11.1666667,7 C11.4166667,7 11.5833333,7.20833334 11.5833333,7.45833334 L11.5833333,12.8333333 L16.875,12.8333333 C17.1666667,12.8333333 17.4166667,13 17.4166667,13.25 C17.4166667,13.5 17.1666667,13.6666667 16.875,13.6666667 L16.875,13.6666667 Z" id="形状" fill="#DADADA"></path>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </svg>
                            <span v-if="[TYPE[0],TYPE[1]].includes(info.type)">{{formatTime(info.beginTime || info.publishTime || info.createTime,true)}}</span>
                            <span v-if="[TYPE[5],TYPE[6]].includes(info.type)">{{formatTime( info.beginTime || info.publishTime || info.createTime,'dateTime')}} </span>
                            <span v-if="[TYPE[0],TYPE[1]].includes(info.type)&&info.endTime">-{{formatTime(info.endTime,'noYear')}}</span>
                        </div>

                        <div v-if="info.type == TYPE[0]">
                            <svg width="12px" height="13px" viewBox="0 0 24 26" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
                                <title>编组 15</title>
                                <desc>Created with Sketch.</desc>
                                <defs>
                                    <polygon id="path-1" points="0.000189473684 0.09625 17.7841895 0.09625 17.7841895 23.0769231 0.000189473684 23.0769231"></polygon>
                                </defs>
                                <g id="行家活动0507/我的收藏修改" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <g id="行家活动-首页" transform="translate(-631.000000, -1456.000000)">
                                        <g id="编组" transform="translate(631.000000, 1456.000000)">
                                            <g id="编组-15" transform="translate(3.000000, 0.865192)">
                                                <g id="编组" transform="translate(0.000000, 0.000000)">
                                                    <mask id="mask-2" fill="white">
                                                        <use xlink:href="#path-1"></use>
                                                    </mask>
                                                    <g id="Clip-2"></g>
                                                    <path d="M8.89218947,0.09625 C3.98103158,0.09625 0.000189473684,4.13759615 0.000189473684,9.12125 C0.000189473684,11.3164423 0.776084211,13.3933654 2.16208421,15.0202885 L8.0964,22.6847115 C8.50187368,23.2077885 9.28250526,23.2077885 9.68797895,22.6847115 L15.6232421,15.0202885 C17.0082947,13.3933654 17.7841895,11.3164423 17.7841895,9.12125 C17.7841895,4.13759615 13.8033474,0.09625 8.89218947,0.09625" id="Fill-1" fill="#DADADA"></path>
                                                </g>
                                                <path d="M8.89218947,5.33451923 C6.83166316,5.33451923 5.16145263,7.02971154 5.16145263,9.12105769 C5.16145263,11.2133654 6.83166316,12.9085577 8.89218947,12.9085577 C10.9527158,12.9085577 12.6238737,11.2133654 12.6238737,9.12105769 C12.6238737,7.02971154 10.9527158,5.33451923 8.89218947,5.33451923" id="Fill-3" fill="#FFFFFF"></path>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </svg>&nbsp
                            <span v-if="info.city">{{formatTag(formatAddress(info),4)}}</span>
                            <span v-else>待定</span>
                        </div>
                    </div>
                    <div class="info-detail" v-if="info.pageView && info.updateNum!==null">
                        <div>
                            <svg width="9px" height="9px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <g transform="translate(-307.000000, -3088.000000)">
                                        <g transform="translate(20.000000, 2958.000000)">
                                            <g transform="translate(284.000000, 127.000000)">
                                                <g fill="#000000" fill-rule="nonzero" opacity="0">
                                                    <rect x="0" y="0" width="24" height="24"></rect>
                                                </g>
                                                <g transform="translate(3.000000, 0.000000)" stroke="#D8D8D8">
                                                    <path d="M6.09545405,5.52159471 C6.05891079,5.53037859 6.03037859,5.55891079 6.02159471,5.59545405 L3.88179105,14.4976071 C3.87809848,14.5129692 3.87809848,14.5289876 3.88179105,14.5443496 C3.89469863,14.5980486 3.9486939,14.6311165 4.00239286,14.618209 L12.904546,12.4784053 C12.9410892,12.4696214 12.9696214,12.4410892 12.9784053,12.404546 L15.118209,3.50239286 C15.1219015,3.48703082 15.1219015,3.47101243 15.118209,3.45565039 C15.1053014,3.40195142 15.0513061,3.36888347 14.9976071,3.38179105 L6.09545405,5.52159471 Z" id="矩形" fill="#D8D8D8" transform="translate(9.500000, 9.000000) rotate(-315.000000) translate(-9.500000, -9.000000) "></path>
                                                    <path d="M14.138842,8.73284301 C14.641218,8.61208728 15.1244793,8.91675173 15.2182363,9.41333012 C15.2422652,9.54059797 15.2386872,9.672986 15.2077387,9.80173973 L13.232659,18.0185974 C13.1466344,18.3764826 12.8611716,18.6619455 12.5032863,18.7479701 L4.28642868,20.7230498 C3.78405265,20.8438055 3.30079137,20.5391411 3.2070344,20.0425627 C3.18300547,19.9152948 3.18658352,19.7829068 3.21753196,19.6541531" id="路径" transform="translate(9.212635, 14.727946) rotate(-315.000000) translate(-9.212635, -14.727946) "></path>
                                                    <path d="M14.138842,5.73284301 C14.641218,5.61208728 15.1244793,5.91675173 15.2182363,6.41333012 C15.2422652,6.54059797 15.2386872,6.672986 15.2077387,6.80173973 L13.232659,15.0185974 C13.1466344,15.3764826 12.8611716,15.6619455 12.5032863,15.7479701 L4.28642868,17.7230498 C3.78405265,17.8438055 3.30079137,17.5391411 3.2070344,17.0425627 C3.18300547,16.9152948 3.18658352,16.7829068 3.21753196,16.6541531" id="路径备份-4" transform="translate(9.212635, 11.727946) rotate(-315.000000) translate(-9.212635, -11.727946) "></path>
                                                </g>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </svg>
                            &nbsp 已更新至第{{info.updateNum}}期
                        </div>
                        <div>

                            <svg width="12.5px" height="12px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
                                <title>编组备份 5</title>
                                <desc>Created with Sketch.</desc>
                                <defs>
                                    <polygon id="path-1" points="0 0.515 18.879 0.515 18.879 14 0 14"></polygon>
                                </defs>
                                <g id="行家活动0507/我的收藏修改" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <g id="行家活动-首页" transform="translate(-310.000000, -2864.000000)">
                                        <g id="编组备份-6" transform="translate(310.000000, 2864.000000)">
                                            <g id="编组备份-5" transform="translate(2.000000, 4.000000)">
                                                <g id="编组" transform="translate(0.000000, 0.485300)">
                                                    <mask id="mask-2" fill="white">
                                                        <use xlink:href="#path-1"></use>
                                                    </mask>
                                                    <g id="Clip-2"></g>
                                                    <path d="M18.7915,7.0253 C18.0855,5.1313 16.8385,3.5133 15.1895,2.3453 C13.5005,1.1503 11.5145,0.5143 9.4395,0.5143 C7.3655,0.5143 5.3795,1.1473 3.6895,2.3453 C2.0385,3.5163 0.7935,5.1343 0.0875,7.0253 L-0.0005,7.2573 L0.0875,7.4893 C0.7935,9.3833 2.0415,11.0013 3.6895,12.1693 C5.3795,13.3653 7.3655,14.0003 9.4395,14.0003 C11.5145,14.0003 13.4995,13.3673 15.1895,12.1693 C16.8405,10.9983 18.0855,9.3803 18.7915,7.4893 L18.8795,7.2573 L18.7915,7.0253 Z" id="Fill-1" fill="#DADADA" mask="url(#mask-2)"></path>
                                                </g>
                                                <path d="M9.4399,4.1439 C7.4659,4.1439 5.8589,5.7589 5.8589,7.7429 C5.8589,9.7259 7.4659,11.3409 9.4399,11.3409 C11.4129,11.3409 13.0209,9.7259 13.0209,7.7429 C13.0209,5.7589 11.4129,4.1439 9.4399,4.1439" id="Fill-3" fill="#DADADA"></path>
                                                <path d="M9.4399,4.1439 C7.4659,4.1439 5.8589,5.7589 5.8589,7.7429 C5.8589,9.7259 7.4659,11.3409 9.4399,11.3409 C11.4129,11.3409 13.0209,9.7259 13.0209,7.7429 C13.0209,5.7589 11.4129,4.1439 9.4399,4.1439 Z" id="Stroke-5" stroke="#FFFFFF"></path>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </svg>&nbsp{{info.pageView}} 浏览
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-show="!iflast" class="v-line"></div>
    </template>
</div>
</template>
<script>
export default {
    /**
     *  临时页面的信息流 item
     */
    components: {},
    props: ['info', 'iflast', 'showTag'],
    data () {
        return {
        };
    },
    computed: {

    },
    mounted () {

    },
    methods: {
        itemClick () {
            let info = JSON.parse(JSON.stringify(this.info))
            // info.id = info.itemId
            this.naviToDetails(localStorage.getItem('platformId'), this.info.type, info)
        },
        // 跳转 单个 企业直播间
        toLiveRoom(id) {
            this.$router.push({name: 'liveRoom', params: {id}})
        }
    }
}
</script>
<style lang='scss' scoped>
.infoItem {
    &.isLiveRoom {
        margin: 16px 15px 0;
        & + .isLiveRoom {
            margin-top: 10px;
        }
    }
}
.live-room-item {
    display: flex;
    padding: 10px;
    background: rgba(9, 96, 159, 0.1);
    border-radius: 4px;
    img {
        width: 108px;
        height: 44px;
        flex-shrink: 0;
    }
    .live-room-name {
        width: 100%;
        margin-left: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        color: #1464A1;
        div {
            line-height: 20px;
            max-height: 40px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
    }
}
.item-body {
    padding: 10px 15px;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: row;
}
.item-image-container {
    flex-shrink: 0;
    height: 80px;
    width: 120px;
    position: relative;
    margin-right: 10px;
}
.item-right-container {
    display: flex;
    flex-direction: column;
    width: 220px;
}
.item-image {
    border-radius: 5px;
    height: 80px;
    width: 120px;
}
.item-tag {
    position: absolute;
    top: 0;
    left: 0;
    color: white;
    font-size: 10px;
    background-color: var(--color-primary);
    opacity: 0.8;
    border-radius: 4px 0 4px 0;
    padding: 3px;
}
.item-title-box {
    height: 40px;
}
.item-title {
    font-weight: 400;
    word-break: break-word;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    font-size: 14px;
    line-height: 18px;
}

.info-detail {
    min-height: 18px;
    display: flex;
    justify-content: left;
    > div {
        font-size: 12px;
        line-height: 16px;
        color: #999;
        margin-right: 10px;
        display: flex;
        align-items: center;
        span {
            margin-left: 2px;
        }
    }
}

.info-detail-between {
    display: flex;
    flex-direction: row;
    font-size: 12px;
    align-items: center;
    margin-top: 8px;
    justify-content: space-between;
    > div {
        margin-right: 5px;
        display: flex;
        flex-direction: row;
        align-items: center;
        color: #999;
    }
}
.info-tag {
    display: flex;
    align-items: center;
    background: #ffecde;
    border-radius: 2px;
    margin-right: 5px;
    color: #ff620d;
    padding: 3px 5px;
    font-size: 10px;
}
</style>