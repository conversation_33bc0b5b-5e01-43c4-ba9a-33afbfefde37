<template>
    <div class="infoItemSeries">
        <div @click="itemClick" class="item-body">
            <div class="item-image-container">
                <div class="item-image">
                    <img class="item-image" :src="info.coverImg" alt="">
                    <div class="item-tag">直播</div>
                </div>
                <!-- <div v-show="showTag" class="item-tag">{{info.conferenceType}}</div> -->
                <svg class="num-tag" width="17px" height="19px" viewBox="0 0 17 19" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <defs>
                        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.8518792%" :id="'linearGradient-'+(index+10)">
                            <stop :stop-color="numTagBackground[index][0]" offset="0%"></stop>
                            <stop :stop-color="numTagBackground[index][1]" offset="100%"></stop>
                        </linearGradient>
                    </defs>
                    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g id="1.1首页改版" transform="translate(-30.000000, -838.000000)">
                            <g id="编组-31" transform="translate(30.000000, 838.000000)">
                                <g id="编组-30">
                                    <path d="M1,0 L16,0 C16.5522847,-1.01453063e-16 17,0.44771525 17,1 L17,19 L17,19 L8.68834377,15.4375 L0,19 L0,1 C-6.76353751e-17,0.44771525 0.44771525,1.01453063e-16 1,0 Z" id="矩形备份-16" :fill="'url(#linearGradient-'+(index+10)+')'"></path>
                                    <text id="1备份" font-family="PingFangSC-Semibold, PingFang SC" font-size="10" font-weight="500" fill="#414141">
                                        <tspan x="6" y="11">{{index+1}}</tspan>
                                    </text>
                                </g>
                            </g>
                        </g>
                    </g>
                </svg>
            </div>
            <div class="item-right-container">
                <div class="item-title-box">
                    <div class="item-title">{{info.name}}</div>
                </div>
                <div>
                    <div class="info-detail">
                        <div>
                            <div>{{formatTime(info.beginTime,'YYYY-MM-DD HH:mm')}}</div>
                            <span>
                                <template v-if="info.enlistStatus=='未报名'">去报名</template>
                                <template v-if="info.enlistStatus=='已报名'">进入直播</template>
                            </span>
                        </div>
                        <div>
                            <img v-if="info.guestCompanyLogo" :src="info.guestCompanyLogo" alt="">
                            <img v-else :src="require('@/assets/img/defaultCompany.png')" alt="">
                            <div style="display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 1;overflow: hidden;">{{info.guestCompany}}</div>
                        </div>
                    </div>
                </div>
                <div v-show="!iflast" class="v-line"></div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    /**
     *  临时页面的信息流 item
     */
    components: {},
    props: ['info', 'iflast', 'showTag', 'index'],
    data () {
        return {
            numTagBackground: [
                ['#F6E551', '#F5A70C'], ['#C3C3C3', '#A3A2A0'], ['#F4AC62', '#FF9622']
            ]
        };
    },
    computed: {

    },
    mounted () {

    },
    methods: {
        itemClick () {
            this.naviToDetails(localStorage.getItem('platformId'), this.info.type, this.info)
        }
    }
}
</script>
<style lang='scss' scoped>
.infoItemSeries {
    padding: 10px 0 0px 0;
}
.item-body {
    padding: 0 10px;
    width: 100%;
    display: flex;
    flex-direction: row;
    margin: 0 0 10px 0;
}
.item-image-container {
    flex-shrink: 0;
    height: 72px;
    width: 120px;
    position: relative;
    margin-right: 10px;
}
.item-right-container {
    display: flex;
    flex-direction: column;
    width: 220px;
}
.item-image {
    border-radius: 5px;
    height: 72px;
    width: 120px;
}
.item-tag {
    position: absolute;
    right: 2px;
    bottom: 2px;
    color: white;
    font-size: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0.8;
    border-radius: 1px;
    padding: 3px;
}
.item-title-box {
    min-height: 40px;
}
.item-title {
    font-weight: 500;
    word-break: break-word;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    font-size: 13px;
    line-height: 18px;
}

.info-detail {
    min-height: 25px;
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    justify-content: left;
    :nth-child(1) {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-size: 12px;
        line-height: 16px;
        color: #999;
        display: flex;
        align-items: flex-end;
        span {
            height: 22px;
            padding: 0 10px;
            margin: 0;
            text-align: center;
            background: rgba(255, 107, 0, 0.1);
            border-radius: 12px;
            font-size: 12px;
            font-weight: 400;
            color: rgba(255, 107, 0, 1);
            line-height: 22px;
        }
    }
    :nth-child(2) {
        display: flex;
        margin-top: 6px;
        margin-bottom: 5px;
        img {
            width: 44px;
            height: 18px;
            margin: 0;
            padding: 0;
            margin-right: 6px;
            border-radius: 1px;
            box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.05);
        }
        div {
            margin: 0;
            font-size: 12px;
            font-weight: 400;
            color: rgba(51, 51, 51, 1);
            line-height: 18px;
        }
    }
}

.info-tag {
    display: flex;
    align-items: center;
    background: #ffecde;
    border-radius: 2px;
    margin-right: 5px;
    color: #ff620d;
    padding: 3px 5px;
    font-size: 10px;
}
.more-icon {
    flex-shrink: 0;
    font-size: 12px;
    border: 1px solid #ff620d;
    border-radius: 4px;
    height: 25px;
    width: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
        float: right;
        color: #ff620d;
    }
    img {
        margin-left: 5px;
        height: 16px;
    }
}
.num-tag {
    position: absolute;
    top: -3px;
    left: 5px;
}
.v-line {
    margin: 0;
}
</style>
