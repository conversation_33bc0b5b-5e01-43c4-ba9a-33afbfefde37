<!-- 底部弹出的 长按关注公众号 组件 -->
<template>
    <div v-show="visiable" class="hidden-message" @touchmove.prevent>
        <div class="hidden-mask" @click="close"></div>
        <div class="hidden-card">
            <!-- <div style="width:97%;">
                <span style="float:right;" @click="close">
                    <svg t="1584499440757" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="16729" width="15" height="15">
                        <path d="M590.5 511.5L997.5 104c20.5-20.5 20.5-54.5 0-75l-2-2c-20.5-20.5-54.5-20.5-75 0L513 435 106 26.5c-20.5-20.5-54.5-20.5-75 0l-2.5 2.5C7.5 49.5 7.5 83.5 28.5 104l407.5 407.5-407.5 407c-20.5 20.5-20.5 54.5 0 75l2 2c20.5 20.5 54.5 20.5 75 0l407.5-407.5 407.5 407.5c20.5 20.5 54.5 20.5 75 0l2-2c20.5-20.5 20.5-54.5 0-75l-407-407z" p-id="16730" fill="#cdcdcd"></path>
                    </svg>
                </span>
            </div> -->
            <div class="hidden-message-title">
                <!-- <svg t="1584500318387" class="icon" viewBox="0 0 2824 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10889" data-spm-anchor-id="a313x.7781069.0.i0" width="20" height="20">
                    <path d="M971.086095 0H552.696065a102.667276 102.667276 0 0 1-5.809905 12.672209L0 1024h416.251824L963.146336 12.692254c2.321958-4.298127 5.001396-8.527765 7.939759-12.692254zM1897.916346 0H1479.526316a102.667276 102.667276 0 0 1-5.809905 12.672209L926.831922 1024h416.251824L1889.976587 12.692254c2.321958-4.298127 5.001396-8.527765 7.939759-12.692254zM2824.746597 0h-418.390029a102.667276 102.667276 0 0 1-5.809906 12.672209L1853.662173 1024h416.251824L2816.806838 12.692254c2.323628-4.298127 5.001396-8.527765 7.939759-12.692254z" p-id="10890" fill="#ff620d"></path>
                </svg> -->
                <span>关注公众号</span>
                <!-- <svg t="1584500318387" class="icon" viewBox="0 0 2824 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10889" data-spm-anchor-id="a313x.7781069.0.i0" width="20" height="20">
                    <path d="M971.086095 0H552.696065a102.667276 102.667276 0 0 1-5.809905 12.672209L0 1024h416.251824L963.146336 12.692254c2.321958-4.298127 5.001396-8.527765 7.939759-12.692254zM1897.916346 0H1479.526316a102.667276 102.667276 0 0 1-5.809905 12.672209L926.831922 1024h416.251824L1889.976587 12.692254c2.321958-4.298127 5.001396-8.527765 7.939759-12.692254zM2824.746597 0h-418.390029a102.667276 102.667276 0 0 1-5.809906 12.672209L1853.662173 1024h416.251824L2816.806838 12.692254c2.323628-4.298127 5.001396-8.527765 7.939759-12.692254z" p-id="10890" fill="#ff620d"></path>
                </svg> -->
            </div>
            <div>
                <img class="info-image" :src="qrCodeUrl" alt="">
            </div>
            <div style="color:#333;">长按二维码或用微信扫一扫关注</div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'officialCode',
    props: [ 'visiable'],
    methods: {
        // 关闭
        close() {
            this.$emit("update:visiable",false);
        }
    },
}
</script>

<style scoped lang="scss">
$color: #004da1;
.hidden-message {
    z-index: 100;
    position: fixed;
    width: 100%;
   // bottom: 0px;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-size: 14px;
    .hidden-mask {
        flex: 1;
        background: rgba($color: #000000, $alpha: 0.2);
    }
    .hidden-card {
        display: flex;
        align-items: center;
        flex-direction: column;
        width: 100%;
        background: #f5f5f5;
        padding-bottom: 30px;
        position: fixed;
        left:0;
        bottom:0;
    }
    div {
        padding: 5px;
    }
}
.info-image {
    height: 150px;
    width: 150px;
}
.hidden-message-title {
    display: flex;
    flex-direction: row;
    align-items: center;
    span {
        margin: 5px;
        // font-weight: 400;
    }
}
</style>