<template>
    <div class="'filterArea'" v-show="innerShow">
        <div :class="'content' +(ifshow?'':' fade')">
            <div class="title">
                筛选内容
            </div>
            <div :class="'option-item'+(innerIndex != index?'':' option-item-selected')" v-for="(item,index) in options" @click="innerIndex = index">
                {{item.text}}
            </div>
            <div class="button-group">
                <div @click='calcle'>取消</div>
                <div @click='makeSure'>确定</div>
            </div>
            <div @click='calcle' class="shadow"></div>
        </div>
    </div>
</template>
<script>
export default {
    components: {},
    props: ['ifshow', 'options', 'selectPickerValue'],
    watch: {
        ifshow: {
            handler (val) {
                if (val) {
                    this.innerShow = true
                    document.getElementById('mainScroll').style.overflow = 'hidden'
                    document.getElementById('mainScroll').scrollTop = '0'
                } else {
                    document.getElementById('mainScroll').style.overflow = 'scroll'
                    setTimeout(() => {
                        this.innerShow = false
                    }, 300);
                }
                this.options.map((item, index) => {
                    if (item.value == this.selectPickerValue) {
                        this.innerIndex = index
                    }
                })
                // console.log(val);
                // console.log(document.getElementById('mainScroll').style.overflow);
            },
            immediate: true,
        },
    },
    data () {
        return {
            innerIndex: 0,
            innerShow: false,

        };
    },
    computed: {},
    mounted () {
    },
    methods: {
        calcle () {
            this.$emit('cancle')
        },
        makeSure () {
            this.$emit('submit', this.innerIndex)
        },
    }
}
</script>
<style lang='scss' scoped>
.filterArea {
    overflow: hidden;
    z-index: 98;
    width: 100%;
    height: calc(100vh - 100px);
}

.content {
    transition-duration: 300ms;
}
.fade {
    transform: translateY(-300px);
    opacity: 0;
}

.title {
    font-size: 16px;
    color: #333;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
}
.option-item {
    font-size: 14px;
    color: #333;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 35px;
}
.option-item-selected {
    color: #ff620d;
    background-color: #f1f1f1;
}
.button-group {
    display: flex;
    flex-direction: row;
    div {
        width: 50%;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    :nth-child(1) {
        background-color: white;
        color: #999;
    }
    :nth-child(2) {
        background-color: #ff620d;
        color: white;
    }
}
.shadow {
    height: 100vh;
    background: rgba($color: #000000, $alpha: 0.2);
}
</style>