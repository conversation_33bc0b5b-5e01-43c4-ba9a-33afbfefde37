<template>
  <div @click="goDetail(item)" class="inquiryItem" :key="item.id">
    <div class="inquiryItem-top">
      <span class="text">询价时间：{{ formatTime(item.inquiryTime, "dateTime") }}</span>
      <span :class="'status' + item.status">{{ item.status == 2 ? "已处理" : "待处理" }}</span>
    </div>
    <div class="inquiryItem-bottom">
      <div class="bottom-left">
        <img v-if="item.productId !== '' && item.productId !== null" :src="item.picUrl ? item.picUrl : picUrl" alt="" />
        <img v-else :src="item.logoUrl ? item.logoUrl : busUrl" alt="" />
      </div>
      <div class="bottom-right">
        <div class="picName flex-top">
          <span class="from" v-if="item.productId !== '' && item.productId !== null">{{ item.types }}</span>
          <span class="picName-text">{{ item.productId !== "" && item.productId !== null ? item.productName : item.busName }}</span>
        </div>
        <div class="picInfo" v-if="item.productId !== '' && item.productId !== null">
          <span>货号：{{ item.numbers }}</span>
          <span>规格：{{ item.specsName }}</span>
          <span>品牌：{{ item.brandName }}</span>
        </div>
        <div v-else style="height: 40px"></div>
        <div class="shopInfo">
          <span class="shopName">
            <img :src="item.logoUrl ? item.logoUrl : busUrl" alt="" />
            {{ item.busName }}
          </span>
          <span class="expectedContactType">
            <img v-if="item.expectedContactType == 1" src="../../assets/img/phone.png" alt="" />
            <img v-else src="../../assets/img/email.png" alt="" />
            {{ item.expectedContactType == 1 ? "电话沟通" : "邮件沟通" }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "inquiryInfoItem",
  props: {
    item: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {
    goDetail(item) {
      sessionStorage.setItem("myInquiryDetail", JSON.stringify(item));
      this.$router.push({ name: "myInquiryDetail", params: { id: item.id } });
    },
  },
};
</script>
<style scoped lang="scss">
.inquiryItem {
  width: 100%;
  padding: 0 15px;
  border-radius: 6px;
  margin: 12px 0;
  box-sizing: border-box;
  .inquiryItem-top {
    width: 100%;
    height: 36px;
    background: #ffffff;
    box-shadow: 0px 1px 0px 0px rgba(239, 239, 239, 0.5);
    border-radius: 6px 6px 0px 0px;
    line-height: 36px;
    padding-left: 10px;
    .text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #666666;
    }
    .status1,
    .status2 {
      display: inline-block;
      width: 48px;
      height: 22px;
      background: #bfbfbf;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      float: right;
      text-align: center;
      line-height: 22px;
      margin: 7px 9px;
    }
    .status1 {
      background: #ff4d4f;
    }
  }
  .inquiryItem-bottom {
    width: 100%;
    background: #ffffff;
    padding-top: 15px;
    display: flex;
    padding-bottom: 15px;
    .bottom-left {
      min-width: 70px;
      height: 100%;
      padding-left: 10px;
      img {
        width: 60px;
      }
    }
    .bottom-right {
      padding-left: 15px;
      padding-right: 23px;
      .picName {
        //line-height: 20px;
        .from {
          padding: 2px 4px;
          background: #31b3fe;
          border-radius: 2px;
          color: #fff;
          font-size: 12px;
          text-align: center;
          margin-right: 6px;
        }
        .picName-text {
          width: 200px;
          height: 40px;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          color: #333;
          font-size: 14px;
          line-height: 20px;
        }
      }
      .picInfo {
        margin-top: 6px;
        span {
          display: block;
          margin-bottom: 6px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #666666;
          width: 265px;
          height: 12px;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          display: -webkit-box;
          -webkit-box-orient: vertical;
        }
      }
      .shopInfo {
        display: flex;
        img {
          width: 20px;
          height: 20px;
          display: inline-block;
          vertical-align: middle;
        }
        .shopName {
          display: inline-block;
          width: 170px;
          height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          display: -webkit-inline-box;
          -webkit-box-orient: vertical;
          color: #333;
          font-size: 12px;
        }
        .expectedContactType {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #097db4;
          line-height: 16px;
          img {
            width: 14px;
            height: 14px;
            vertical-align: middle;
          }
        }
      }
    }
  }
}
</style>
