<template>
  <div>
      <div class="addXiaoyi" v-if="(sliderInfoStatus.addxiaoyi || sliderInfoStatus.addweixin) && isShowIcon">
          <img :src="require(`../../assets/${platformIds}/add_icon.png`) " alt="" @click="showHidden = !showHidden">
      </div>
        <officialCode style="bottom:45px;" :visiable.sync="showHidden"></officialCode>
        <officialCode1 style="bottom:45px;" :visiable.sync="showHidden1"></officialCode1>
  </div>
</template>

<script>
import officialCode from '@/components/medicine/officialCode';
import officialCode1 from '@/components/newCommon/officialCode';
// import {  } from '@/api/';
export default {
  data () {
    return {
            platformIds:1,
            showHidden: false,
            showHidden1: false,
            isShowIcon:true  // 默认是展示
    };
  },
    watch: {
        $route: {
            handler (val) {
                this.platformIds = localStorage.getItem('platformId');
                // console.log('路由变化',val)
                if(val.name == 'targetPage' && val.params.pageId == 17){
                  this.isShowIcon = false
                }else{
                  this.isShowIcon = true
                }
            },
            deep: true,
            immediate: true
        }
    },

  created () { },

  components: {
		officialCode,
		officialCode1,
	},

  computed: {
   
  },

  mounted () { },

  methods: {
  }
}

</script>
<style  scoped  lang='scss'>
.addXiaoyi {
    position: fixed;
    height: 80px;
    width: 80px;
    text-align: center;
    right: 8px;
    bottom: 11vh;
    z-index: 99;
}
</style>
