<template>
    <div class="homeButton" v-show="isShare && !isHome">
        <div @click="goHome">
            <img :src="require(`../../assets/${platformIds}/floating_home.png`) " alt="">
        </div>
    </div>
</template>
<script>

import { homePageUrl } from '@/config/env';

export default {
    components: {},
    props: [],
    data () {
        return {
            isShare: null,
            isHome: null,
            platformIds:1
        };
    },
    watch: {
        $route: {
            handler (val) {
                this.isShare = val.query.isShare;
                this.platformIds = localStorage.getItem('platformId');
                if (val.name == 'targetPage') {
                    this.isHome = true
                }
            },
            deep: true,
            immediate: true
        }
    },
    computed: {},
    mounted () {
    },
    methods: {
        goHome () {
            let url ;
            if(this.platformIds == 3){
                window.location.replace('https://bio.cnhangjia.com/info/#' + '/home/' + '?' + `&platformId=${localStorage.getItem('platformId')}&bizId=${localStorage.getItem('bizId')}`);
            }else{
                window.location.replace('https://live.cnhangjia.com/info/#' + '/home/' + '?' + `&platformId=${localStorage.getItem('platformId')}&bizId=${localStorage.getItem('bizId')}`);
            }
            
        }
    }
}
</script>
<style lang='scss' scoped>
.homeButton {
    position: fixed;
    height: 60px;
    width: 60px;
    border-radius: 50%;
    text-align: center;
    right: 8px;
    bottom: 20.5vh;
    z-index: 99;
}
</style>