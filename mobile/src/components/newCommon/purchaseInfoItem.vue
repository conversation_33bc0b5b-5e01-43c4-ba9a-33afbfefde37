<template>
  <div class="purchase-info-item-wrap">
    <div class="purchase-info-item-container">
      <div class="purchase-top">{{ info.productName }}</div>
      <div class="purchase-middle">
        <span>品牌：</span>
        <span>{{ info.productBrand }}</span>
      </div>
      <div class="purchase-bottom flex-middle">
        <div class="purchase-bottom-left">
          <span>发布时间 </span>
          <span>{{ formatTime(info.createTime, "dateTime") }}</span>
        </div>
        <div class="unit"></div>
        <div class="purchase-bottom-right" @click="toSeeInfo">查看详情</div>
      </div>
      <div class="purchase-tip">{{ info.processingStatus == 2 ? "废弃" : info.processingStatus == 1 ? "已处理" : "未处理" }}</div>
    </div>
  </div>
</template>
<script>
import {  exhibitorClientUrl} from '@/config/env'
export default {
  name: "purchaseInfoItem",
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {
    toSeeInfo() {
      window.location.href =exhibitorClientUrl + "/wantVuyDetail?id=" + this.info.id;
    },
  },
};
</script>
<style scoped lang="scss">
.purchase-info-item-wrap {
  width: 100%;
  padding: 0px 15px;
  box-sizing: border-box;
  margin-top: 12px;

  .purchase-info-item-container {
    width: 100%;
    background: linear-gradient(90deg, #dfedf8 0%, #ffffff 100%);
    border-radius: 4px;
    padding: 10px;
    box-sizing: border-box;
    position: relative;
    .purchase-top {
      width: calc(100% - 58px);
      font-weight: 400;
      font-size: 13px;
      color: #333333;
      line-height: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 10px;
    }

    .purchase-middle {
      width: 100%;
      font-weight: 400;
      font-size: 11px;
      color: #999999;
      line-height: 11px;
    }

    .purchase-bottom {
      width: 100%;
      .purchase-bottom-left {
        font-weight: 400;
        font-size: 11px;
        color: #999999;
        line-height: 11px;
      }

      .purchase-bottom-right {
        width: 74px;
        height: 26px;
        background: #ffffff;
        border-radius: 2px;
        border: 1px solid var(--color-primary);
        font-weight: 400;
        font-size: 12px;
        color: #097db4;
        line-height: 26px;
        text-align: center;
      }
    }

    .purchase-tip {
      position: absolute;
      right: 0px;
      top: 0px;
      width: 48px;
      height: 22px;
      background: #ff4d4f;
      border-radius: 0px 2px 0px 4px;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      line-height: 22px;
      text-align: center;
    }
  }
}
</style>
