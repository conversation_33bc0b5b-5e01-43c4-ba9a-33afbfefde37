<template>
  <div class="infoItem">
    <div @click="itemClick" :class="info.type == TYPE[0] ? 'item-body-type0' : 'item-body'">
      <div :class="info.type == TYPE[0] ? 'item-image-container-type0' : 'item-image-container'">
        <img class="item-image" :src="info.bannerImg" alt="" />
        <!-- <img class="item-image" :src="info.bannerImg" alt="" v-else> -->
        <div v-show="showTag" class="item-tag" v-if="info.type !== TYPE[0]">{{ info.conferenceType }}</div>
        <div v-if="info.type == TYPE[0]" class="item-img-name-type0">
          <span class="item-img-name-type0-name">{{ info.name }}</span>
        </div>
      </div>
      <div :class="info.type == TYPE[0] ? 'item-right-container-type0' : 'item-right-container'">
        <div class="item-title-box" v-if="info.type !== TYPE[0]">
          <div :class="info.itemType == 10 ? 'item-title-line1' : 'item-title'">{{ info.name }}</div>
        </div>
        <div v-if="[TYPE[0], TYPE[5], TYPE[6], TYPE[9], TYPE[10]].includes(info.type)" :class="info.type == TYPE[0] ? '' : 'info-detail-between'">
          <!-- <div></div> -->
          <div>
            <span v-if="[TYPE[5]].includes(info.type)">{{ formatTime(info.beginTime || info.publishTime || info.createTime, "dateTime") }} </span>
            <span style="margin-top: 34px" v-if="[TYPE[6]].includes(info.type) && info.status != 0 && !info.guestCompany">开播时间 {{ formatTime(info.beginTime, "YYYY-M-DD HH:mm") }}</span>
            <span style="margin-top: 34px" v-if="[TYPE[6]].includes(info.type) && info.status == 0 && info.duration && !info.guestCompany"><span style="margin-right: 10px">已结束</span>{{ info.duration }}分钟</span>
          </div>
          <div v-if="info.type == TYPE[0]" class="type0">
            <div style="margin-top: 8px; color: #666666">
              <span class="type0-text" v-if="[TYPE[0]].includes(info.type)">活动时间：{{ formatTime(info.beginTime, "YYYY-MM-DD HH:mm") }}</span>
            </div>
            <div style="margin-top: 8px; color: #666666">
              <span class="type0-text">活动地点：{{ info.address ? info.address : "待定" }}</span>
            </div>
            <div v-if="info.type == TYPE[0]" class="eleBill" @click.stop="eleBill">电子票</div>
          </div>

          <div v-if="info.type == TYPE[9]">
            <div style="color: #666">
              {{ info.status == 0 ? "已结束" : "更新中" }}
            </div>
            <div style="margin-left: 10px; color: #666">
              {{ `${info.updateNum}期/共${info.renewalPeriod}期` }}
            </div>
          </div>
        </div>
        <div class="guest-detail" v-if="info.guestCompany && info.type != TYPE[10]">
          <div>
            <template v-if="info.status == 1">开播时间 {{ formatTime(info.beginTime, "YYYY-M-DD HH:mm") }}</template>
            <template v-if="info.status == 0"><span style="margin-right: 10px">已结束</span>{{ info.duration }}分钟</template>
          </div>
          <div class="guest-info-detail" v-show="!closeExhibitor">
            <img :src="info.guestCompanyLogo ? info.guestCompanyLogo + SquareImgSuffix : require('@/assets/img/defaultCompany.png')" alt="" />

            <div style="display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 1; overflow: hidden">{{ info.guestCompany }}</div>
          </div>
        </div>
        <!-- 产品的显示展商名称和logo -->
        <div class="guest-detail" v-else-if="info.exhibitorName && info.type == TYPE[10]" v-show="!closeExhibitor">
          <!-- 产品的显示标语 -->
          <div style="color: #333333" class="item-slogan">{{ info.slogan }}</div>
          <div class="guest-info-detail" style="margin-top: 0">
            <img :src="info.exhibitorLogo ? info.exhibitorLogo + SquareImgSuffix : require('@/assets/img/defaultCompany.png')" alt="" />
            <div style="display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 1; overflow: hidden">{{ info.exhibitorName }}</div>
          </div>
        </div>
        <div :class="seePerInfoClass(platformId)" @click.stop="toSeeInfo" v-if="routeVerity && info.type != TYPE[0]">查看报名信息</div>
        <div v-show="!iflast" class="v-line" :style="{ bottom: routeVerity ? '-35px' : '-15px' }"></div>
      </div>
    </div>
  </div>
  <!-- </div> -->
</template>
<script>
import { clickElectronicTicket } from "@/api/userCenter";
export default {
  /**
   *  临时页面的信息流 item
   */
  components: {},
  props: ["info", "iflast", "showTag", "closeExhibitor"],
  data() {
    return {};
  },
  computed: {
    platformId() {
      if (localStorage.getItem("platformId") == 1) {
        return true;
      } else {
        return false;
      }
    },
    routeVerity() {
      if (this.$route.name == "myActivity") {
        return true;
      } else {
        return false;
      }
    },
  },
  mounted() {},
  methods: {
    seePerInfoClass(val) {
      if (val) {
        return "seePerInfoHJ seePerInfo";
      } else {
        return "seePerInfoYL seePerInfo";
      }
    },
    itemClick() {
      let info = JSON.parse(JSON.stringify(this.info));
      // info.id = info.itemId
      this.naviToDetails(localStorage.getItem("platformId"), this.info.type, info);
    },
    eleBill() {
      console.log(this.info);
      // clickElectronicTicket({code:this.info.code}).then(res=>{
      //     window.location.href=res.data.codeUrl

      // })
      window.location.href = this.info.codeUrl;
    },
    toSeeInfo() {
      this.$router.push({ name: "userInfoList", query: { moduleId: this.info.id, bizId: this.info.bizId, moduleType: this.info.type, businessType: this.info.itemType + "Sign" } });
    },
  },
};
</script>
<style lang="scss" scoped>
.infoItem {
  margin-bottom: 15px;
}
.item-body,
.item-body-type0 {
  padding: 10px 15px;
  width: 100%;
  display: flex;
  flex-direction: row;
  padding-bottom: 0;
}
.item-body-type0 {
  flex-direction: column;
  width: 96%;
  margin-left: 8px;
  background: url("../../assets/img/offline.png") no-repeat;
  background-size: 100% 100%;
  margin-right: 10px;
  margin-top: 20px;
}
.item-image-container {
  flex-shrink: 0;
  height: 72px;
  width: 120px;
  position: relative;
  margin-right: 10px;
}
.item-image-container-type0 {
  flex-shrink: 0;
  height: 72px;
  width: 249px;
  position: relative;
  margin-right: 10px;
}
.item-right-container {
  display: flex;
  flex-direction: column;
  width: 220px;
  min-height: 72px;
  position: relative;
}
.item-right-container-type0 {
  min-height: 20px;
}
.item-image {
  border-radius: 5px;
  height: 72px;
  width: 120px;
}
.item-tag {
  position: absolute;
  right: 2px;
  bottom: 2px;
  color: white;
  font-size: 10px;
  background-color: var(--color-primary);
  opacity: 0.8;
  border-radius: 1px;
  padding: 3px;
}
.item-title-box {
  height: 17px;
}
.item-title {
  font-weight: 400;
  word-break: break-word;
  text-overflow: -o-ellipsis-lastline;
  text-overflow: ellipsis;
  display: -webkit-box;
  font-size: 14px;
  line-height: 20px;
  overflow: hidden;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
.item-title-line1 {
  @extend .item-title;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.item-slogan {
  font-size: 12px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  line-height: 17px;
  margin-bottom: 6px;
  overflow: hidden;
  display: -webkit-box !important;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.info-detail-between {
  flex: 1;
  display: flex;
  flex-direction: column;
  font-size: 12px;
  align-items: left;
  margin-top: 8px;
  justify-content: space-between;
  > div {
    margin-right: 5px;
    display: flex;
    flex-direction: row;
    align-items: center;
    color: #666;
  }
}
.info-detail-between.product-between {
  margin-top: 7px;
  justify-content: normal;
}
.info-tag {
  display: flex;
  align-items: center;
  background: #ffecde;
  border-radius: 2px;
  margin-right: 5px;
  color: #ff620d;
  padding: 3px 5px;
  font-size: 10px;
}
.v-line {
  position: absolute;
  // bottom: -25px;
  background-color: #eeeeee;
}
.type0 {
  .type0-text {
    font-size: 13px;
    font-weight: 400;
  }
}
.guest-detail {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: left;
  > div {
    font-size: 12px;
    line-height: 16px;
    color: #666;
    margin-right: 10px;
    display: flex;
    align-items: center;
  }
  .guest-info-detail {
    margin-top: 0px;
    img {
      height: 18px;
      width: 44px;
      border-radius: 1px;
      box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.05);
    }
    div {
      margin-left: 6px;
      font-size: 12px;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      line-height: 17px;
    }
  }
}
.item-img-name-type0 {
  display: inline-block;
  position: absolute;
  // top: 10px;
  left: 127px;
  width: 130px;
}
.item-img-name-type0-name {
  width: 130px;
  overflow: hidden;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-height: 16px;
  font-size: 14px;
  font-weight: 400;
}
.eleBill {
  width: 84px;
  height: 140px;
  // position: absolute;
  // top: 0;
  // right: 7px;
  float: right;
  margin-top: -130px;
  margin-right: -11px;
  font-size: 13px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #444444;
  text-align: center;
  line-height: 140px;
}
.seePerInfo {
  width: 84px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  color: var(--color-primary);
  opacity: 0.85;
  border-radius: 1px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  position: absolute;
  bottom: -25px;
  cursor: pointer;
}
.seePerInfoHJ {
  background: rgba($color: #ff6b00, $alpha: 0.15);
}
.seePerInfoYL {
  background: rgba($color: #097db4, $alpha: 0.15);
}
</style>
