<template>
  <div class="collect-product-item-wrap" @click="goProductDetail(item)">
    <div class="product-item-content flex-left">
      <div class="product-item-img-content unit-0 flex-center flex-middle">
        <img class="product-item-img" :src="item.bannerImg" alt="" />
      </div>
      <div class="product-item-info unit">
        <div class="product-item-title">{{ item.name }}</div>
        <product-info :item="item"></product-info>
        <div class="product-item-desc flex-left flex-wrap">
          <span class="product-item-mark-classify" v-if="item.classifyName.length" v-for="subItem in item.classifyName">{{ subItem }}</span>
          <span class="product-item-mark-laboratoryClName" v-if="item.laboratoryClName.length" v-for="subItem in item.laboratoryClName">{{ subItem }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { exhibitorClientUrl } from "@/config/env";
import ProductInfo from "./productInfo";
export default {
  name: "collectProductItem",
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      item: {},
    };
  },
  created() {
    this.item = JSON.parse(JSON.stringify(this.info));
    if (this.item.classifyName) {
      this.item.classifyName = this.item.classifyName.split(",");
    }
    if (this.item.laboratoryClName) {
      this.item.laboratoryClName = this.item.laboratoryClName.split(",");
    }
  },
  components: {
    ProductInfo,
  },
  mounted() {},
  computed: {},
  methods: {
    goProductDetail(item) {
      let url = `${exhibitorClientUrl}/productStore?productId=${item.itemId}&exhibitorId=${item.exhibitorId}`;
      window.location.href = url;
    },
  },
};
</script>
<style scoped lang="scss">
.collect-product-item-wrap {
  width: 100%;
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  .product-item-content {
    width: 100%;
    padding: 10px;
    border-radius: 4px;
    background: #fff;
    box-sizing: border-box;
    .product-item-img-content {
      width: 100px;
      height: 100px;
      margin-right: 10px;
      padding: 12px;
      box-sizing: border-box;
      .product-item-img {
       object-fit: contain;
      }
    }

    .product-item-info {
      min-width: 0;
      overflow: hidden;
      .product-item-title {
        width: 100%;
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 8px;
      }
      .product-item-rule {
        .product-item-rule-item {
          .product-item-rule-item-left {
            font-weight: 400;
            font-size: 12px;
            color: #666666;
            line-height: 18px;
            width: calc(100% - 1px);
            padding: 0 4px;
            box-sizing: border-box;
            .item-name {
              width: 100%;
              height: 18px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              text-align: center;
            }
          }
          .product-item-rule-item-line {
            width: 1px;
            height: 15px;
            background: #eeeeee;
          }
        }
      }
    }
    .product-item-desc {
      width: 100%;
      margin-top: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .product-item-mark-classify {
        font-weight: 400;
        font-size: 11px;
        color: #ff6b00;
        line-height: 16px;
        border-radius: 2px;
        padding: 4px 5px;
        background: rgba(255, 107, 0, 0.1);
        margin-right: 6px;
      }
      .product-item-mark-laboratoryClName {
        font-weight: 400;
        font-size: 11px;
        color: #097db4;
        line-height: 16px;
        border-radius: 2px;
        padding: 4px 5px;
        background: rgba(9, 125, 180, 0.1);
      }
    }
  }
}
</style>
