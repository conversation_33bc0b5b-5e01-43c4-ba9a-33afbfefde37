<template>
    <div class="infoItemRight">
        <div @click="itemClick" class="item-body">
            <div class="item-image-container">
                <img class="item-image" :src="(info.coverImg  || info.bannerImg)" alt="">
                <div v-show="showTag" class="item-tag">{{info.conferenceType}}</div>
            </div>
            <div class="item-right-container">
                <div class="item-title-box">
                    <div class="item-title">{{info.name}}</div>
                </div>
                <div>
                    <div class="info-detail">
                        <span class="info-tag" v-for="(tag,index) in info.tagList" v-show="ifshowTag(info.tagList,index)">{{tag.tagName}}</span>
                    </div>
                    <div class="info-detail" v-if="info.type == TYPE[0]">
                        <div>
                            <svg width="12px" height="13px" viewBox="0 0 24 26" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <title>编组 15</title>
                                <desc>Created with Sketch.</desc>
                                <defs>
                                    <polygon id="path-1" points="0.000189473684 0.09625 17.7841895 0.09625 17.7841895 23.0769231 0.000189473684 23.0769231"></polygon>
                                </defs>
                                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <g id="行家活动-首页" transform="translate(-631.000000, -1456.000000)">
                                        <g id="编组" transform="translate(631.000000, 1456.000000)">
                                            <g id="编组-15" transform="translate(3.000000, 0.865192)">
                                                <g id="编组" transform="translate(0.000000, 0.000000)">
                                                    <mask id="mask-2" fill="white">
                                                        <use xlink:href="#path-1"></use>
                                                    </mask>
                                                    <g id="Clip-2"></g>
                                                    <path d="M8.89218947,0.09625 C3.98103158,0.09625 0.000189473684,4.13759615 0.000189473684,9.12125 C0.000189473684,11.3164423 0.776084211,13.3933654 2.16208421,15.0202885 L8.0964,22.6847115 C8.50187368,23.2077885 9.28250526,23.2077885 9.68797895,22.6847115 L15.6232421,15.0202885 C17.0082947,13.3933654 17.7841895,11.3164423 17.7841895,9.12125 C17.7841895,4.13759615 13.8033474,0.09625 8.89218947,0.09625" id="Fill-1" fill="#DADADA" mask="url(#mask-2)"></path>
                                                </g>
                                                <path d="M8.89218947,5.33451923 C6.83166316,5.33451923 5.16145263,7.02971154 5.16145263,9.12105769 C5.16145263,11.2133654 6.83166316,12.9085577 8.89218947,12.9085577 C10.9527158,12.9085577 12.6238737,11.2133654 12.6238737,9.12105769 C12.6238737,7.02971154 10.9527158,5.33451923 8.89218947,5.33451923" id="Fill-3" fill="#FFFFFF"></path>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </svg>
                            <span v-if="info.city">{{formatTag(formatAddress(info),8)}}</span>
                            <span v-else>待定</span>
                        </div>
                    </div>
                    <div class="info-detail">
                        <div>
                            <svg width="12px" height="12px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <g id="画板备份-2" transform="translate(-33.000000, -1675.000000)" fill-rule="nonzero">
                                        <g id="编组-12" transform="translate(20.000000, 1534.000000)">
                                            <g id="时间备份-8" transform="translate(11.000000, 139.000000)">
                                                <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="24" height="24"></rect>
                                                <path d="M12,2 C6.45833334,2 2,6.45833334 2,12 C2,17.5416667 6.45833334,22 12,22 C17.5416667,22 22,17.5416667 22,12 C22,6.45833334 17.5416667,2 12,2 Z M16.875,13.6666667 L11.2083333,13.6666667 L11.1666667,13.6666667 C11.0416667,13.6666667 10.9166667,13.5833333 10.8333333,13.4583333 C10.7916667,13.375 10.75,13.3333333 10.75,13.25 L10.75,13.2083333 L10.75,7.45833334 C10.75,7.20833334 10.9166667,7 11.1666667,7 C11.4166667,7 11.5833333,7.20833334 11.5833333,7.45833334 L11.5833333,12.8333333 L16.875,12.8333333 C17.1666667,12.8333333 17.4166667,13 17.4166667,13.25 C17.4166667,13.5 17.1666667,13.6666667 16.875,13.6666667 L16.875,13.6666667 Z" id="形状" fill="#DADADA"></path>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </svg>
                            <span>{{formatTime(info.beginTime || info.publishTime || info.createTime,true)}}</span>
                            <span v-if="info.endTime">-{{formatTime(info.endTime,'noYear')}}</span>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div v-show="!iflast" class="v-line"></div>
    </div>
</template>
<script>
export default {
    /**
     *  临时页面的信息流 item
     */
    components: {},
    props: ['info', 'iflast', 'showTag'],
    data () {
        return {
        };
    },
    computed: {

    },
    mounted () {

    },
    methods: {
        itemClick () {
            this.naviToDetails(localStorage.getItem('platformId'), this.info.type, this.info)
        }
    }
}
</script>
<style lang='scss' scoped>
.infoItemRight {
    padding: 10px 0 0px 0;
}
.item-body {
    padding: 0 12px;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: row-reverse;
    margin: 0 0 10px 0;
}
.item-image-container {
    flex-shrink: 0;
    height: 80px;
    width: 120px;
    position: relative;
    margin-left: 10px;
}
.item-right-container {
    display: flex;
    flex-direction: column;
    width: 220px;
}
.item-image {
    border-radius: 5px;
    height: 80px;
    width: 120px;
}
.item-tag {
    position: absolute;
    top: 0;
    left: 0;
    color: white;
    font-size: 10px;
    background-color: #ff6b00;
    opacity: 0.8;
    border-radius: 4px 0 4px 0;
    padding: 3px;
}
.item-title-box {
    height: 40px;
}
.item-title {
    font-weight: 500;
    word-break: break-word;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    font-size: 14px;
    line-height: 18px;
}

.info-detail {
    min-height: 18px;
    margin-top: 5px;
    display: flex;
    justify-content: left;
    > div {
        font-size: 12px;
        line-height: 16px;
        color: #999;
        margin-right: 10px;
        display: flex;
        align-items: center;
        span {
            margin-left: 2px;
        }
    }
    img {
        height: 12px;
        width: 12px;
    }
}
.info-tag {
    display: flex;
    align-items: center;
    background: #ffecde;
    border-radius: 2px;
    margin-right: 5px;
    color: #ff620d;
    padding: 3px 5px;
    font-size: 10px;
}
</style>