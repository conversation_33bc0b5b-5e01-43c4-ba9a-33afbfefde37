<template>
    <div class="infoRecommend">
        <div @click="itemClick" class="item-body">
            <div class="item-image-container">
                <img class="item-image" :src="info.coverImg" alt="">
                <div v-show="true" class="item-tag">{{info.conferenceType}}</div>
            </div>
            <div class="item-right-container">
                <div class="item-title-box">
                    <div class="item-title">{{info.name}}</div>
                </div>
                <div>
                    <!-- <div class="info-detail-between">
                    </div> -->
                    <div class="infoRecommend-detail">
                        <div class="infoRecommend-detail-left">
                            <div>
                                <span>{{formatTime( info.beginTime || info.publishTime || info.createTime,'YYYY-MM-DD HH:mm')}}</span>
                            </div>
                            <div v-if="info.type == TYPE[0]" style="display: flex;align-items: center;">
                                <svg style="margin-right:5px;" width="12px" height="13px" viewBox="0 0 24 26" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                    <title>编组 15</title>
                                    <desc>Created with Sketch.</desc>
                                    <defs>
                                        <polygon id="path-1" points="0.000189473684 0.09625 17.7841895 0.09625 17.7841895 23.0769231 0.000189473684 23.0769231"></polygon>
                                    </defs>
                                    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <g id="行家活动-首页" transform="translate(-631.000000, -1456.000000)">
                                            <g id="编组" transform="translate(631.000000, 1456.000000)">
                                                <g id="编组-15" transform="translate(3.000000, 0.865192)">
                                                    <g id="编组" transform="translate(0.000000, 0.000000)">
                                                        <mask id="mask-2" fill="white">
                                                            <use xlink:href="#path-1"></use>
                                                        </mask>
                                                        <g id="Clip-2"></g>
                                                        <path d="M8.89218947,0.09625 C3.98103158,0.09625 0.000189473684,4.13759615 0.000189473684,9.12125 C0.000189473684,11.3164423 0.776084211,13.3933654 2.16208421,15.0202885 L8.0964,22.6847115 C8.50187368,23.2077885 9.28250526,23.2077885 9.68797895,22.6847115 L15.6232421,15.0202885 C17.0082947,13.3933654 17.7841895,11.3164423 17.7841895,9.12125 C17.7841895,4.13759615 13.8033474,0.09625 8.89218947,0.09625" id="Fill-1" fill="#DADADA" mask="url(#mask-2)"></path>
                                                    </g>
                                                    <path d="M8.89218947,5.33451923 C6.83166316,5.33451923 5.16145263,7.02971154 5.16145263,9.12105769 C5.16145263,11.2133654 6.83166316,12.9085577 8.89218947,12.9085577 C10.9527158,12.9085577 12.6238737,11.2133654 12.6238737,9.12105769 C12.6238737,7.02971154 10.9527158,5.33451923 8.89218947,5.33451923" id="Fill-3" fill="#FFFFFF"></path>
                                                </g>
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                                <span v-if="info.city">{{formatTag(info.city,5)}}</span>
                                <span v-else>待定</span>
                            </div>
                        </div>
                        <div class="livePrice" v-show="info.type!= this.TYPE[0]">
                            <template v-if="info.livePrice>0"> {{info.livePrice}}元</template>
                            <template v-if="!info.livePrice"> 免费</template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- <div v-show="!iflast" class="v-line"></div> -->
    </div>
</template>
<script>
export default {
    /**
     *  临时页面的信息流 item
     */
    components: {},
    props: ['info', 'iflast', 'showTag'],
    data () {
        return {
        };
    },
    computed: {

    },
    mounted () {

    },
    methods: {
        itemClick () {
            let info = JSON.parse(JSON.stringify(this.info))
            // info.id = info.itemId
            this.naviToDetails(localStorage.getItem('platformId'), this.info.type, info)
        },
    }
}
</script>
<style lang='scss' scoped>
.infoRecommend {
}
.item-body {
    padding:10px;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    background: #fff;
    margin-top: 15px;
    border-radius: 4px;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
}
.item-image-container {
    flex-shrink: 0;
    height: 90px;
    width: 150px;
    position: relative;
    margin-right: 10px;
}
.item-right-container {
    width: 172px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.item-image {
    border-radius: 5px;
    height: 90px;
    width: 150px;
}
.item-tag {
    position: absolute;
    bottom: 2px;
    right: 2px;
    color: white;
    font-size: 10px;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0.8;
    border-radius: 1px;
    padding: 3px;
}
.item-title-box {
    min-height: 60px;
}
.item-title {
    font-weight: 500;
    word-break: break-word;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    font-size: 13px;
    line-height: 18px;
}

.infoRecommend-detail {
    min-height: 18px;
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    align-items:flex-end;
    .infoRecommend-detail-left {
        display: flex;
        flex-direction: column;
        div {
            font-size: 12px;
            font-weight: 400;
            color: rgba(102, 102, 102, 1);
            line-height: 17px;
        }
    }

    .livePrice {
        display: flex;
        flex-direction: column-reverse;
        font-size: 12px;
        font-weight: 500;
        line-height: 17px;
        color: rgba(255, 107, 0, 1);
    }
}

.info-detail-between {
    margin-top: 5px;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    align-items: center;
    // margin-top: 8px;
    justify-content: space-between;
    > div {
        width: 100%;
        font-size: 12px;
        font-weight: 400;
        color: rgba(102, 102, 102, 1);
        line-height: 17px;
    }
}
.info-tag {
    display: flex;
    align-items: center;
    background: #ffecde;
    border-radius: 2px;
    margin-right: 5px;
    color: #ff620d;
    padding: 3px 5px;
    font-size: 10px;
}
</style>