<template>
  <div class="collect-live-item-wrap">
    <div class="collect-live-item-content flex-left" v-if="info.itemType != 'liveRoom'" @click="itemClick">
      <img class="collect-live-item-img unit-0" :src="info.bannerImg" alt="" />
      <div class="collect-live-item-info flex-vertical unit">
        <div class="collect-live-item-title">{{ info.name }}</div>
        <div class="unit"></div>
        <div class="collect-live-item-desc flex-bottom">
          <span class="collect-live-item-time unit" v-if="info.beginTime">{{ formatTime(info.beginTime) }}</span>
          <span class="collect-live-item-desc-text">{{ info.sponsorName }}</span>
        </div>
      </div>
    </div>
    <div class="collect-live-room-content flex-middle" v-else @click="toLiveRoom(info.itemId)">
      <img class="collect-live-room-img unit-0" :src="info.bannerImg" />
      <div class="live-room-name unit flex-center flex-middle">{{ info.itemName }}</div>
    </div>
  </div>
</template>
<script>
export default {
  name: "collectLiveItem",
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {
    toLiveRoom(id) {
      this.$router.push({ name: "liveRoom", params: { id } });
    },
    itemClick() {
      let info = JSON.parse(JSON.stringify(this.info));
      this.naviToDetails(localStorage.getItem("platformId"), this.info.itemType, info);
    },
  },
};
</script>
<style scoped lang="scss">
.collect-live-item-wrap {
  width: 100%;
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  .collect-live-item-content {
    width: 100%;
    padding: 10px;
    border-radius: 4px;
    background: #fff;
    box-sizing: border-box;
    height: 92px;
    .collect-live-item-img {
      width: 120px;
      margin-right: 10px;
      border-radius: 4px;
    }
    .collect-live-item-info {
      min-width: 0;
      overflow: hidden;
      .collect-live-item-title {
        font-size: 14px;
        color: #333333;
        line-height: 17px;
        // 2行省略
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
      .collect-live-item-desc {
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        line-height: 14px;
      }
    }
  }
  .collect-live-room-content {
    padding: 10px;
    background: rgba(9, 96, 159, 0.1);
    border-radius: 4px;
    .collect-live-room-img {
      width: 108px;
    }
    .live-room-name {
      margin-left: 10px;
      color: #1464a1;
      line-height: 20px;
      max-height: 40px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }
}
</style>
