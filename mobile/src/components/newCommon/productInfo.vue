<template>
  <div class="product-item-rule flex-middle">
    <div class="product-item-rule-item unit-1-3 flex-middle">
      <div class="product-item-rule-item-left unit">
        <div class="item-name">货号</div>
        <div class="item-name">{{ item.numbers }}</div>
      </div>
      <div class="product-item-rule-item-line unit-0"></div>
    </div>
    <div class="product-item-rule-item unit-1-3 flex-middle">
      <div class="product-item-rule-item-left unit">
        <div class="item-name">规则</div>
        <div class="item-name">{{ item.specsName }}</div>
      </div>
      <div class="product-item-rule-item-line"></div>
    </div>
    <div class="product-item-rule-item unit-1-3 flex-middle">
      <div class="product-item-rule-item-left unit">
        <div class="item-name">品牌</div>
        <div class="item-name">{{ item.brandName }}</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "productInfo",
  props: {
    item: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {},
};
</script>
<style scoped lang="scss">
.product-item-rule {
  .product-item-rule-item {
    .product-item-rule-item-left {
      font-weight: 400;
      font-size: 12px;
      color: #666666;
      line-height: 18px;
      width: calc(100% - 1px);
      padding: 0 2px;
      box-sizing: border-box;
      .item-name {
        width: 100%;
        height: 18px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
      }
    }
    .product-item-rule-item-line {
      width: 1px;
      height: 15px;
      background: #eeeeee;
    }
  }
}
</style>
