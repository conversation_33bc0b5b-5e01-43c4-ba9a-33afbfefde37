<template>
    <div class="scroll-list-wrap">
        <div class="searchHeadWrap">
            <div class='flex-left unit-0'>
                <div class="searchLeft unit flex-middle">
                    <div class="center_search">
                        <img src="@/assets/img/search.png" alt="">
                        <iframe id="rfFrame" name="rfFrame" src="about:blank" style="display:none;"></iframe>
                        <form action="#" class="form_search" @submit.prevent="search" target="rfFrame">
                            <div class="form-search-div">
                                <input type="text" v-model="searchData" autofocus="autofocus" placeholder="请输入搜索关键字" id="searchInput" @change="changeSearchDatas" />
                                <div v-show="searchData" class="clear-button" @click="searchData=''">
                                    <svg width="8px" height="8px" viewBox="0 0 15 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <g id="画板备份-7" transform="translate(-572.000000, -176.000000)" fill-rule="nonzero">
                                                <g id="删除" transform="translate(570.000000, 174.000000)">
                                                    <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="20" height="19"></rect>
                                                    <polygon id="路径" fill="#999999" points="17.8947368 15.9285714 11.1278196 9.5 17.8947368 3.07142858 16.7669173 2 10 8.42857142 3.23308272 2 2.10526316 3.07142858 8.87218044 9.5 2.10526316 15.9285714 3.23308272 17 10 10.5714286 16.7669173 17"></polygon>
                                                </g>
                                            </g>
                                        </g>
                                    </svg>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="searchRight unit-0 flex-center flex-middle">
                    <div @click="search" class="search-button" v-if="!dominAllList.length">搜索</div>
                    <div class="filter-button" v-else @click="filterShow = !filterShow">
                        <span>筛选</span>
                        <i :class="'iconfont'+' iconlujing' + (filterShow?' rotate':'')"></i>
                    </div>
                </div>
                <cube-popup type="my-popup" :mask="false" content="<i style='padding:20px;background:rgba(0,0,0,.8);color:#fff'>搜索内容不能为空</i>" ref="myPopup" />
            </div>
            <div class="filter" v-show="filterShow">
                <span>筛选</span>
                <div class="filter-item-list">
                    <div v-for="item in dominList" @click="filterItemClick(item)" :class="computedItemStyle(item)">
                        {{item.name}}
                        <i class="iconfont icongouxuan1 selected-icon" v-show="!!computedItemStyle(item)"></i>
                    </div>
                </div>
                <div class="filter-button-group">
                    <div @click="filterList = []">重置</div>
                    <div @click="filterDone">确定</div>
                </div>
                <div class="filter-cover" @click="filterShow = false"></div>
            </div>
        </div>
        <cube-scroll v-if="List.length" class="list-scroll" ref="scroll" :data="List" :options="options" @pulling-down="onPullingDown" @pulling-up="onPullingUp">
            <infoItemSearchZly v-if="systemStyle == 'zly'" v-for="(item,index) in List" :key='index' :info='item' :iflast='index == List.length-1' :showTag='true'></infoItemSearchZly>
            <infoItem  v-if="systemStyle == null" v-for="(item,index) in List" :key='index' :info='item' :iflast='index == List.length-1' :showTag='true'></infoItem>
        </cube-scroll>
        <div v-if="List.length == 0" class="no-content">
            暂无内容
        </div>
    </div>
</template>
<script>
/**
 *  
 * 修改为统一使用的列表
 * 默认查询视图
 * 
 */
import infoItem from "./infoItem"
import infoItemSearchZly from "./infoItemSearchZly"
import { getInfoByDynamicUrl, } from '@/api/configurable/common'
import { platformBizApi } from '@/config/env';
import { getConferenceFront, getLiveInfoListByActivityId } from '@/api/configurable/home'
import CONSTANT from '@/config/config_constant';
export default {
    components: {
        infoItem,
        infoItemSearchZly
    },
    props: [],
    data () {
        return {
            searchData: '',
            configuration: {},
            List: [],
            options: {
                pullUpLoad: {
                    threshold: 50,
                    stopTime: 1000,
                    txt: {
                        more: '',
                        noMore: '没有更多信息啦！'
                    }
                },
                pullDownRefresh: {
                    threshold: 60,
                    stopTime: 1000,
                    txt: '更新成功'
                },
                scrollbar: false
            },
            pageData: {
                pageNum: 1,
                pageSize: 10,
            },
            params: {},
            filterShow: false,
            dominAllList: [],
            dominList: [],
            filterList: [],
        };
    },
    computed: {
        listMode () {
            return this.$route.params.mode
        },
    },
    watch: {
        filterShow (val) {
            if (val) {

            } else {

            }
        }
    },
    created () {
        //获取全部的行业信息
        let params = {
            platformId: localStorage.getItem('platformId')
        }
        if (localStorage.getItem(CONSTANT.token)) {
            getInfoByDynamicUrl(platformBizApi + '/officialMenu/industry', params).then(res => {
                this.dominAllList = res.data
                // 获取关注的行业信息
                getInfoByDynamicUrl(platformBizApi + `/conferenceFront/getDomain`, { platformId: localStorage.getItem('platformId') }).then(res => {
                    if (res.data.length) {
                        let tempData = []
                        this.dominAllList.map(item => {
                            // res.data.map(innerItem => {
                                // if (item.codeValueId == innerItem.channel) {
                                    tempData.push(item)
                                // }
                            // })
                        })
                        this.dominList = tempData
                    }
                }, () => {
                    this.$store.state.dominPage.domainShow = true
                })
            })
        }    
    },
    mounted () {
        this.params = this.$route.query
        this.getInfo()



    },
    methods: {
        search () {
            // if (this.searchData) {
                this.params.searchName = this.searchData
                this.pageData.pageNum = 1
                this.List = []
                this.getInfo()
            // } else {
            //     let component = this.$refs.myPopup;
            //     component.show()
            //     setTimeout(() => {
            //         component.hide()
            //     }, 2000)
            // }

        },
        changeSearchDatas () {
            this.params.searchName = this.searchData
            this.getInfo()
        },
        getInfo (mode) {
            let p = JSON.parse(JSON.stringify(this.params))
            p.pageSize = this.pageData.pageSize
            p.pageNum = this.pageData.pageNum
            getConferenceFront(p).then(res => {
                if (mode == 'next') {
                    this.List = this.List.concat(res.data.list)
                } else {
                    this.List = res.data.list
                }
                if (res.data.list.length == 0) {
                    this.pageData.pageNum--
                    this.$refs.scroll.forceUpdate()
                }
            })
            this.$forceUpdate()
        },
        onPullingDown () {
            this.pageData.pageNum = 1
            this.getInfo()
        },
        onPullingUp () {
            this.pageData.pageNum++
            setTimeout(() => {
                this.getInfo('next')
            }, 300);
        },
        filterItemClick (item) {
            let flag = false
            this.filterList.map((filterItem, index) => {
                if (filterItem.codeValueId == item.codeValueId) {
                    this.filterList.splice(index, 1)
                    flag = true
                }
            })
            if (flag == false) {
                this.filterList.push(item)
            }
            this.$forceUpdate()
        },
        computedItemStyle (item) {
            let flag = false
            this.filterList.map((filterItem, index) => {
                if (filterItem.codeValueId == item.codeValueId) {
                    flag = true
                }
            })
            if (flag) {
                return "filter-item-selected"
            } else {
                return ""
            }
        },
        filterDone () {
            let tempArray = this.filterList.map(item => {
                return item.codeValueId
            })
            this.params.channelList = tempArray.join(",")
            this.pageData.pageNum = 1
            this.List = []
            this.getInfo()
            this.filterShow = false;
        }
    }
}
</script>
<style lang='scss' scoped>
.scroll-list-wrap {
    background-color: #fff;
}

.filter-button {
    height: 20px;
    font-size: 14px;
    color: #999999;
    line-height: 20px;
    margin-left: 14px;
    display: flex;
    align-items: center;
    width: fit-content;
    flex-wrap: nowrap;
    > i {
        transition-duration: 300ms;
        transform: scale(0.4);
        margin: -4px;
    }
}
.rotate {
    transform: scale(0.4) rotate(180deg) !important;
}
.filter {
    width: calc(100vw - 20px);
    background-color: #ffffff;
    bottom: 0;
    transform: translateY(99%);
    position: absolute;
    > span {
        display: inline-block;
        margin: 10px 0 0 0;
        height: 20px;
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        line-height: 20px;
    }
}
.filter-item-list {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    > div {
        position: relative;
        height: 32px;
        background: #f4f4f4;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 400;
        color: #444444;
        line-height: 20px;
        padding: 0 25px;
        margin-right: 15px;
        margin-top: 10px;
    }
}
.filter-button-group {
    margin-top: 18px;
    margin-left: -15px;
    margin-right: -15px;
    display: flex;
    > div {
        width: 50%;
        height: 40px;
        line-height: 40px;
        text-align: center;
    }

    :nth-child(1) {
        background: #ffebdd;
        color: #ff6b00;
    }
    :nth-child(2) {
        background: #ff6b00;
        color: #ffffff;
    }
}
.filter-cover {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
    height: 100vh;
    width: 110vw;
    margin-left: -15px;
}
.filter-item-selected {
    background: #fff7f2 !important;
    color: #ff6b00 !important;
    position: relative;
}
.selected-icon {
    position: absolute;
    right: 0;
    bottom: -2.5px;
}
</style>

<style  scoped>
.searchHeadWrap {
    z-index: 2;
    width: 100%;
    padding: 3px 12px 3px 12px;
    position: sticky;
    background-color: #fff;
}
.searchLeft {
    width: 45px;
    height: 46px;
}

.searchRight {
    height: 46px;
    box-sizing: border-box;
}
.center_search {
    height: 35px;
    width: 100%;
    display: flex;
    align-items: center;
    background: #f5f5f5;
    border-radius: 4px;
}
.center_search img {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    vertical-align: middle;
    margin-left: 13px;
}
#searchInput {
    outline: none;
    background: transparent;
    border: none;
    font-size: 16px;
    line-height: 20px;
    width: 90%;
    padding-left: 2px;
}
#searchInput::placeholder {
    font-size: 16px;
    color: #999;
}

.form_search {
    width: 100%;
}
.form-search-div {
    width: 100%;
    display: flex;
}
.search-button {
    margin-left: 8px;
    height: 35px;
    width: 60px;
    line-height: 35px;
    text-align: center;
    color: #333;
    border-radius: 15px;
    background: #f5f5f5;
    border-radius: 4px;
}
.item-list {
    border-top: 10px solid #f5f5f5;
    background-color: #fff;
}
.no-content {
    text-align: center;
    margin-top: 30px;
    color: #999;
}
.list-scroll {
    height: calc(100% - 46px);
    overflow: hidden;
    background-color: #fff;
}
</style>