 .w-e-text-container {
    position: relative;

.w-e-progress {
    position: absolute;
    background-color: #1e88e5;
    bottom: 0;
    left: 0;
    height: 1px;
}
}
.w-e-text {
    overflow-y: scroll;
    padding: 0 15px;
p,h1,h2,h3,h4,h5,table,pre {
    margin: 10px 0;
    line-height: 1.6;
}

ul, ol {
   
}

blockquote {
    display: block;
    border-left: 8px solid #d0e5f2;
    padding: 5px 10px;
    margin: 10px 0;
    line-height: 1.4;
    font-size: 100%;
    background-color: #f1f1f1;
}

code {
    display: inline-block;
    background-color: #f1f1f1;
    border-radius: 3px;
    padding: 3px 5px;
    margin: 0 3px;
}

pre {

code {
    display: block;
}
}

table {
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;

td,th {
    border-bottom: 1px solid #ccc;
    border-right: 1px solid #ccc;
    padding: 3px 5px;
}

th {
    border-bottom: 2px solid #ccc;
    text-align: center;
}
}

&:focus {
     outline: none;
 }

img {
    cursor: pointer;

&:hover {
     box-shadow: 0 0 5px #333;
 }
}
}
h1{
    font-size: 36px;
}
h2{
    font-size: 30px;
}
h3{
    font-size: 24px;
}
h4{
    font-size: 18px;
}
h5{
    font-size: 14px;
}