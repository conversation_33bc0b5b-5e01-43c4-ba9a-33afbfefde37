.contentText .w-e-text-container {
    position: relative;
}
.contentText .w-e-progress {
    position: absolute;
    background-color: #1e88e5;
    bottom: 0;
    left: 0;
    height: 1px;
}

.contentText .w-e-text {
    overflow-y: scroll;
    padding: 0 15px;
}
.contentText p,.contentText h1,.contentText h2,.contentText h3,.contentText h4,.contentText h5,.contentText table, .contentText pre {
    margin: 10px 0;
    line-height: 20px;
}
blockquote {
    display: block;
    border-left: 8px solid #d0e5f2;
    padding: 5px 10px;
    margin: 10px 0;
    line-height: 1.4;
    font-size: 100%;
    background-color: #f1f1f1;
}

code {
    display: inline-block;
    background-color: #f1f1f1;
    border-radius: 3px;
    padding: 3px 5px;
    margin: 0 3px;
}

pre {

}
code {
    display: block;
}
table {
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
}
td,th {
    border-bottom: 1px solid #ccc;
    border-right: 1px solid #ccc;
    padding: 3px 5px;
}
th {
    border-bottom: 2px solid #ccc;
    text-align: center;
}
img {
    cursor: pointer;
} 
.contentText h1{
    font-size: 36px;
}
.contentText h2{
    font-size: 30px;
}
.contentText h3{
    font-size: 24px;
}
.contentText h4{
    font-size: 18px;
}
.contentText h5{
    font-size: 14px;
}
.contentText p{
    font-size: 15px;
}