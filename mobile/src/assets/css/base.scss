body{
  background: #fff;
}
// .container{
//   font-size: 14px;
//   position: absolute;
//   top: 0;
//   right: 0;
//   left: 0;
//   bottom: 0;
//   overflow: hidden;
// }
.pull-left{
  float: left;
}
.pull-right{
  float: right;
}
.text-center{
  text-align: center;
}
.text-right{
  text-align: right;
}
.mlr10{
  margin: 0 10px;
}
.pdlr10{
  padding: 0 10px;
}
.pd10{
  padding: 10px;
}
.mb10{
  margin-bottom: 10px;
}
.mb5{
  margin-bottom: 5px;
}
.text-small{
  font-size: .8em !important;
}
a{
  text-decoration: none;
}
/*vue-router transition*/
// .v-enter{
//   opacity: 0;
// }
// .v-enter-active{
//   transition: 2s;

// }
// .v-enter-to{
//   opacity: 1;
// }
.no-data{
  color: #999;
  text-align: center;
  margin-top: 100px;
}
.cube-tab_active{
  color: #ff620d!important;
}
.cube-tab div{
  margin-top: 6px;
  font-size: 15px;
}
i[class^="cubeic-"], i[class*=" cubeic-"]{
    font-size: 18px !important;
}

.classify-list-container .cube-slide-dots{
  position: absolute;
  bottom:20px !important;
}
.cube-slide-item img{
  width: 100%;
}
.cube-tab-bar-slider{
  left:25% !important;
  margin-left: -13px !important;
  width: 27px!important;
  background-color:#ff620d !important;
}
.header-avator .cube-upload-btn{
  width: 60px;
 height: 60px;
 border-radius: 50%;
 background: #eadfce!important;
}
.cube-upload-def{
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background:transparent;
}
.header-avator .cube-upload-btn:active .cube-upload-btn-def{
  width: 60px;
  height: 60px;
  border-radius: 50%;
}

.cube-upload-file-def > .cubeic-wrong{
  width: 100%;
  height: 100%;
}

blockquote {
  display: block;
  border-left: 8px solid #d0e5f2;
  padding: 5px 10px;
  margin: 10px 0;
  line-height: 1.4;
  font-size: 100%;
  background-color: #f1f1f1;
}

.compagesmall .cube-rate-item{
  width: 18px !important;
} 
.cube-loading-spinner{
  color:#FF620D!important;
}

.border-bottom-1px::after {
  border-bottom: 1px solid #eee;
  left: 0;
  bottom: 0;
  width: 100%;
  transform-origin: 0 bottom;
}
.lines2{
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}
.lines1{
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
}
::-webkit-scrollbar {
  width: 0px;
  height: 0px;
  background-color: #fff;
  border-radius: 4px;
}
:root {
  --color-primary: #ff620d; 
}

// 去掉 input rype=search 的本身默认的放大镜图标，兼容iphoneX
	input[type="search"]{-webkit-appearance:none;}
	input::-webkit-search-cancel-button {display: none;}

  .v-line {
    height: 1px;
    background-color: #e5e5e5;
    width: 100%;
    transform: scaleY(0.5);
  }
  * {
    -webkit-tap-highlight-color: transparent;
    -webkit-overflow-scrolling: touch;
  }
  
  img {
    border: none;
  }

  .cube-toast-tip {
   max-height: 60px !important;
  }
