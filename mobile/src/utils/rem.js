// 设置 rem 函数
function setRem() {
    // 320 默认大小16px; 320px = 20rem ;每个元素px基础上/16
    let htmlWidth = document.documentElement.clientWidth || document.body.clientWidth;
    //得到html的Dom元素
    let htmlDom = document.getElementsByTagName('html')[0];

    // setTimeout(()=>{
    //     htmlDom.style.fontSize = htmlWidth / 10 + 'px';
    // },2000)
    htmlDom.style.fontSize = htmlWidth / 10 + 'px';
    //设置根元素字体大小

    if(document.getElementById('mainScroll')){
        document.getElementById('mainScroll').style.height = (window.innerHeight - 53) + 'px';
    }

    if(document.getElementById('app')){
        document.getElementById('app').style.height = window.innerHeight + 'px';
    }
}
// 初始化
setRem();
// 改变窗口大小时重新设置 rem
window.onresize = function () {
    setRem()
}

export default setRem;