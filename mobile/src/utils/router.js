import CONSTANT from "../config/config_constant";
export default function (Router) {
    const originalPush = Router.prototype.push;
    //改写原油的replace 和 push  使其携带固定参数
    
    // const originalReplace = Router.prototype.replace;
    // Router.prototype.replace = function replace(location) {
    //     addRouterInfo(location);
    //     return originalReplace.call(this, location).catch(err => err)
    // }
    Router.prototype.push = function push(location) {
        addRouterInfo(location);
        return originalPush.call(this, location).catch(err => err)
    }

}

function addRouterInfo(location) {
    if (localStorage.getItem(CONSTANT.USERINFO)) {
        let platformInfo = JSON.parse(localStorage.getItem(CONSTANT.USERINFO));
        if (!location.query) {
            location.query = {};
        }
        location.query.platformId = platformInfo.platformId;
        location.query.bizId = platformInfo.bizId;
        location.query.domainType = platformInfo.domainType;
    }
}


