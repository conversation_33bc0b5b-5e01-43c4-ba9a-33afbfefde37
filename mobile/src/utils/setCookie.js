var setCookie = function (name, value, day, domain=".ienmore.com",path="/") {
    if (day !== 0) { //当设置的时间等于0时，不设置expires属性，cookie在浏览器关闭后删除
        var expires = day * 24 * 60 * 60 * 1000;
        var date = new Date(+new Date() + expires);
         document.cookie = name + "=" + escape(value) + ";expires=" + date.toUTCString()+";path="+path+";domain="+domain;
    } else {
        document.cookie = name + "=" + escape(value);
    }
};
export default setCookie;