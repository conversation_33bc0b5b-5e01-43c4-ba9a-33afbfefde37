<template>
  <div id="app">
    <urlError v-if="showError"></urlError>
    <div v-else-if="show" class="appContainer">
      <!--首页、线上直播的顶部banner不显示在这，显示在页面中，因为滚动条滚动问题-->
       <top-banner v-if="topBannerInfo && $route.name != 'targetPage'" :configuration="topBannerInfo"></top-banner>
      <!-- 行家  选择关注行业 -->
      <dominPage></dominPage>
      <loginPage :language="'zh-cn'" :getChannelCode="true" @getChannelCode="getChannelCode"></loginPage>
      <keep-alive :include="cachedComponents">
        <router-view class="container" :key="$route.fullPath" />
      </keep-alive>
      <homeButton></homeButton>
      <addShare></addShare>
      <userPopoView style="bottom: 200px" class="userPopClass" :visiable="userPopShow" v-if="userPopShow"></userPopoView>
      <!-- 广告弹窗 -->
      <advert-dialog v-if="isShowAd"></advert-dialog>
      <!-- 签到成功 -->
      <sign-in-dialog v-if="isSignInStatus" @closeSignIn="closeSignIn"></sign-in-dialog>
      <!-- 去支付萱蕚地址 -->
      <addAgressView style="bottom: 200px" class="userPopClass" v-if="adressShow" :visiable="adressShow" @changeUserPopShow="changeUserPopShow"></addAgressView>
      <!-- 易享商城首页顶部导航栏 -->
      <topPopView v-if="showPopup"></topPopView>
      <!-- 针对bioChina活动弹窗 -->
      <bioChina></bioChina>
    </div>
  </div>
</template>

<script>
import userPopoView from "../src/components/common/userPopoView.vue";
import addAgressView from "../src/views/userCenter/components/addAgressView.vue";
import topPopView from "../src/views/userCenter/components/topPopView";
import { wxAuthUrl } from "@/config/env";
import CONSTANT from "@/config/config_constant";
import Axios from "axios";
import homeButton from "@/components/newCommon/homeButton";
import addShare from "@/components/newCommon/addShare";
import dominPage from "@/views/home/<USER>/zlyComponent/dominPage";
import setRem from "@/utils/rem";
import { dataActionUtils } from "@enmore/common-data-action";
import advertDialog from "@/components/common/advertDialog";
import { getAdvertisement } from "@/api/configurable/home";
import { getInvitationCode, invitationCodeMemberRel } from "./api/login";
import { WOW } from "wowjs";
import setCookie from "./utils/setCookie";
import { validateInvitationCode } from "./api/login";
import urlError from "./views/urlError/index.vue";
import delCookie from "./utils/delCookie";
import getCookie from "./utils/getCookie";
import SignInDialog from "@/components/common/signInDialog";
import bioChina from "@/components/medicine/bioChina.vue";
import topBanner from "./components/common/topBanner";
import { getOfficialMenu, getOfficialMenuPreview } from "./api/configurable/home";
export default {
  components: {
    homeButton,
    dominPage,
    addShare,
    advertDialog,
    urlError,
    SignInDialog,
    userPopoView,
    addAgressView,
    topPopView,
    bioChina,
    topBanner
  },
  data() {
    return {
      wow: null,
      showError: false,
      topBannerInfo: null ,//顶部banner信息
      allConfig: null,
      pageContent: null,
      bottomNavTab: null,
      show: false
    };
  },
  created() {
    this.shareFun();
  },
  provide() {
    return {
      getAdvertisement: this.getAdvertisement,
      updateWowDom: this.updateWowDom,
    };
  },
  computed: {
    showPopup() {
      return this.$store.state.showPopup;
    },
    // 地址
    adressShow() {
      return this.$store.state.adressShow;
    },
    // 审核弹窗全局状态获取
    userPopShow() {
      return this.$store.state.userPopShow;
    },
    // 微信分享
    shareInfo() {
      return this.$store.state.shareInfo;
    },
    isShowAd() {
      return this.$store.state.isShowAd;
    },
    isSignInStatus() {
      return this.$store.state.isSignInStatus;
    },
  },
  watch: {
    shareInfo(val) {
      this.wxShareConfig(val);
    },
    $route: {
      handler(val) {
        this.setPageContentConfig(JSON.parse(JSON.stringify(this.allConfig)));
        if (val.path == "/mall" || val.path.includes("/mall/goods")) {
          this.cachedComponents = ["mallBody"];
        } else {
          this.cachedComponents = [];
          this.$store.commit("mallScrollFun", 0);
        }
        if (!val.path.includes("/mall/goods") && !val.path.includes("/mall/search")) {
          this.$store.commit("mallPopKeyWordFun", "");
        }
        this.wxShareConfig(val);
      },
      deep: true,
    },
  },
  async mounted() {
    let url = new URLSearchParams(location.hash);
    if (url.get("invitationCodeId")) {
      await this.validateInvitationCode(url.get("invitationCodeId"));
      if (process.env.VUE_APP_CURRENTMODE == "production") {
        setCookie("invitationCodeId", url.get("invitationCodeId"), 1 / 48, ".morebio.cn");
      } else {
        setCookie("invitationCodeId", url.get("invitationCodeId"), 1 / 48, ".ienmore.com");
      }
    }
    if (localStorage.getItem(CONSTANT.token)) {
      localStorage.setItem("dataAtionToekn", localStorage.getItem(CONSTANT.token));
    }
    this.setDataActionData();
    this.$nextTick(() => {
      // 在dom渲染完后,再执行动画
      this.wow = new WOW({
        live: true,
      });
      this.wow.init();
      // 设置窗口大小
      setRem();
      try {
        // 设置颜色
        this.setStyle();
      } catch (e) {}
    });
    let that = this;
    this.getCongfigration();
    // 监听变化
    window.addEventListener("message", (ev) => {
      try {
        let data = JSON.parse(ev.data);
        if (typeof data == "object" && "toLogin" in data) {
          // 吊起登录
          that.$store.commit("changeLoginShow", true);
        }
      } catch (e) { }
      this.getCongfigration();
    });
  },
  methods: {
    getCongfigration() {
      const menuApi = this.isPreviewEnviroment ? getOfficialMenuPreview : getOfficialMenu;
      menuApi({ pid: 0, platformId: localStorage.getItem("platformId") }).then((res) => {
        if (res.code != this.$successCode) return;
        localStorage.setItem("officialMenu", JSON.stringify(res));
        this.allConfig = res.data;
        this.$store.commit('changeAllConfig', this.allConfig);
        this.setPageContentConfig(this.allConfig);
      });
    },
    setPageContentConfig(data) {
      if (data && data.length && data[0].menuKey == "bottomNavTab") {
        this.show = false;
        this.bottomNavTab = data;
        this.$store.commit('changeBottomNavTab', this.bottomNavTab);
        const currentItem = this.bottomNavTab.find(item => item.id == (this.$route.params.pageId || sessionStorage.getItem('currentPageId')));
        if (currentItem) {
          if (currentItem.components.length){
            //获取顶部banner位信息
            let topBannerIndex = currentItem.components.findIndex(item => {
              return item.menuKey == 'topBanner'
            })
            if (topBannerIndex > -1) {
              this.topBannerInfo = currentItem.components[topBannerIndex];
            }else{
              this.topBannerInfo = null;
            }
          }
          this.pageContent = currentItem.components.length ? currentItem.components : currentItem.children;
          this.$store.commit('changePageContent', this.pageContent);
          this.$forceUpdate();
        }
        this.show = true;
      }
    },    // 地址选择返回事件
    changeUserPopShow() {
      this.$store.commit("adressFuntion", false);
    },
    shareFun() {
      let url = encodeURIComponent(location.href.split("#")[0]);
      Axios.get(wxAuthUrl + "wechat/jsApi" + "?url=" + url + "&from=info_" + localStorage.getItem("platformId")).then((res) => {
        let data = res.data.data;
        wx.config({
          debug: false,
          appId: data.appid,
          timestamp: data.timeStamp,
          nonceStr: data.nonceStr,
          signature: data.signature,
          jsApiList: [
            "showMenuItems",
            "onMenuShareAppMessage",
            "onMenuShareTimeline",
            "onMenuShareQQ",
            "onMenuShareWeibo",
            "hideOptionMenu",
            "showOptionMenu",
            "chooseImage",
            "uploadImage",
            "getLocation",
            "startRecord",
            "stopRecord",
            "onVoiceRecordEnd",
            "playVoice",
            "pauseVoice",
            "stopVoice",
            "onVoicePlayEnd",
            "uploadVoice",
            "downloadVoice",
            "chooseWXPay",
          ],
        });
      });
    },
    setDataActionData() {
      let params = {
        bizId: localStorage.getItem("bizId") || this.$route.query.platformId,
        moduleId: "1",
        moduleType: "a",
        platformId: this.$route.query.platformId, //平台id
        businessSeq: "a", //业务类型序列号
        sysCode: "info", //系统标识
        systemType: "info_official",
        languageType: 0,
        openId: localStorage.getItem("proOpenId"),
        domainType: 1,
      };
      //系统标识参数
      this.$store.commit("changeSystemInfo", params);
      //生成埋点批次号
      this.$store.commit("createBatchNumber");
      //获取访问途径
      this.$store.commit("changeClientType");
      //获取进入来源
      this.$store.commit("changeAccessType");
      // 更改webType
      this.$store.commit("changeWebType", 1);

      dataActionUtils.submitLastData(this);
      dataActionUtils.handlerBlur(this);
      dataActionUtils.sendLoopStaySeconds(this);
    },
    //设置颜色
    setStyle() {
      setTimeout(() => {
        if (sessionStorage.getItem(CONSTANT.USERINFO)) {
          let userInfo = JSON.parse(sessionStorage.getItem(CONSTANT.USERINFO));
          let customerStyle = JSON.parse(userInfo.customerStyle);
          document.getElementsByTagName("body")[0].style.setProperty("--color-primary", customerStyle.primaryColor);
          document.getElementsByTagName("body")[0].style.setProperty("--color-columnBg", customerStyle.columnBg);
        } else {
          this.setStyle();
        }
      }, 200);
    },
    // 设置广告
    getAdvertisement(pageId) {
      // 进行判断弹窗广告
      let advertParams = {
        platformId: 3,
        webPlatform: 2,
        url: encodeURI(location.href),
      };
      if (!sessionStorage.getItem("isAdvert" + pageId)) {
        getAdvertisement(advertParams).then((res) => {
          if (res.code == "001" && res.data) {
            this.$store.commit("changeIsShowAd", true);
            this.$store.commit("changeAdvertisement", res.data);
            sessionStorage.setItem("isAdvert" + pageId, 1);
          }
        });
      }
    },
    // 手动更新dom变化
    updateWowDom() {
      this.wow.sync();
    },
    getChannelCode(refsh, token) {
      // 从cookie中获取邀请码
      const invitationCodeId = getCookie("invitationCodeId");

      // 处理邀请码与会员关联
      const handleInvitationCodeMemberRel = (code) => {
        const params = {
          code: code,
          token: token,
        };

        invitationCodeMemberRel(params).then((res) => {
          if (res.code !== this.$successCode) {
            this.$toast(res.info);
          }
        });
      };

      if (invitationCodeId) {
        // 使用已有邀请码
        handleInvitationCodeMemberRel(invitationCodeId);
      } else {
        // 获取新渠道码
        const params = {
          businessType: "personalCenter",
          sysCode: "exhibitor",
          moduleType: "exhibitor",
          businessId: 3,
          languageType: 0, // 根据当前语言
          platformId: 3,
          moduleId: 3, // 模块的id
        };

        getInvitationCode(params).then((res) => {
          if (res.code == this.$successCode && res.data) {
            handleInvitationCodeMemberRel(res.data.id);
          } else {
            this.$toast(res.info);
          }
        });
      }
    },
    validateInvitationCode(code) {
      validateInvitationCode({ invitationCodeId: code }).then((res) => {
        if (res.code == this.$successCode) {
          if (res.data == 0) {
            // this.$router.push({
            // 			name:'urlError'
            // 		 })
            delCookie("invitationCodeId");
            this.showError = true;
          } else {
            this.showError = false;
          }
        } else {
          this.$toast(res.info);
        }
      });
    },
    // 关闭签到
    closeSignIn() {
      this.$store.commit("changeIsSignInStatus", false);
    },
  },
};
</script>

<style lang="scss">
html,
body {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  -webkit-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
  #app {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    .appContainer {
      height: 100%;
      .container {
        height: 100%;
        font-size: 14px;
      }
    }
  }
}
</style>
