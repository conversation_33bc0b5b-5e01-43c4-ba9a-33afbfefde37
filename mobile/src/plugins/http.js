import axios from "axios";
import {
  baseUrl,
  wxAuthUrl,
  pointBaseUrl,
  platformBizApi,
  memberApi,
  clientBaseUrl,
  system,
  orderUrl,
  formApi,
  authUrl,
  systemApiUrl,
  signOrderUrl,
  platformApi,
  productUrl,
  memberAutjApi,
  textLiveApi,
} from "../config/env";
const $local = require("store");
import CONSTANT from "@/config/config_constant";
import "nprogress/nprogress.css";
import store from "../store";
import router from "@/router";
import getCookie from "../utils/getCookie";
export default function axiosHttp(type) {
  let url = "";
  switch (type) {
    case 0:
      url = baseUrl;
      break;
    case 1:
      url = wxAuthUrl;
      break;
    case 2:
      url = pointBaseUrl;
      break;
    case 3:
      url = platformBizApi;
      break;
    case 4:
      url = memberApi;
      break;
    case 5:
      url = clientBaseUrl;
      break;
    case 6:
      url = system;
      break;
    case 7:
      url = orderUrl;
      break;
    case 8:
      url = formApi;
      break;
    case 9:
      url = authUrl;
      break;
    case 10:
      url = systemApiUrl;
      break;
    case 11:
      url = platformApi;
      break;
    case "signOrderUrl":
      url = signOrderUrl;
      break;
    case "productUrl":
      url = productUrl;
      break;
    case "memberAutjApi":
      url = memberAutjApi;
      break;
    case "textLiveApi":
      url = textLiveApi;
      break;
  }
  const Axios = axios.create({
    baseURL: url,
  });
  Axios.interceptors.request.use(
    (config) => {
      // NProgress.start();
      let token = getCookie(CONSTANT.Authorization);
      let openId = localStorage.getItem(CONSTANT.openId);
      let platformId = localStorage.getItem("platformId");
      let bizId = localStorage.getItem("bizId");
      let webPlatform = 2; // 手机端
      if (platformId) {
        if (config.method == "get") {
          if (config.params) {
            config.params = Object.assign(config.params, {
              platformId,
              bizId: config.params.bizId ? config.params.bizId : bizId,
              webPlatform: config.params.webPlatform ? config.params.webPlatform : webPlatform,
            });
          } else {
            config.params = {
              platformId,
              bizId,
              webPlatform,
            };
          }
        } else if (config.method == "post" || config.method == "put") {
          if (config.data) {
            config.data = Object.assign(config.data, {
              platformId,
              bizId: config.data.bizId ? config.data.bizId : bizId,
              webPlatform,
            });
          } else {
            config.data = {
              platformId,
              bizId,
              webPlatform,
            };
          }
        }
      } else {
        console.log("platFormId丢失");
      }
      if (token) {
        config.headers.common["Authorization"] = token;
      }
      if (openId) {
        config.headers.common["wxAuthorization"] = openId;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
  Axios.interceptors.response.use(
    (response) => {
      // NProgress.done();
      let data = response.data;
      if (data.code > 100) {
      }
      return data;
    },
    (error) => {
      // NProgress.done();
      if (error.response) {
        switch (error.response.status) {
          case 401:
            // 如果不是预览环境 就需要打开登录
            const isPreviewEnviroment = process.env.VUE_APP_CURRENTMODE.includes("Preview");
            if (router.app.$route.name != "targetPage" || (router.app.$route.name == "targetPage" && router.app.$route.params.typeId != 0)) {
              localStorage.setItem("courseType", null);
              localStorage.setItem("courseItemId", null);
              localStorage.setItem("registerChannel", 2);
              if (!isPreviewEnviroment) {
                store.commit("changeLoginShow", true);
                if (new Date().getTime() - localStorage.getItem("routerBeforeTime") < 500) {
                  store.state.loginOption = "open";
                } else {
                  store.state.loginOption = "touch";
                }
              }
            }
            break;
        }
      }
      return Promise.reject(error.response.data);
    }
  );
  Axios.all = axios.all;
  return Axios;
}
