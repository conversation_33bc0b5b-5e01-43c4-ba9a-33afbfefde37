import {
  BASE_URL_TEST,
  DATA_ACTION_URL_TEST,
  SYSTEM_URL_TEST,
  AUTH_URL_TEST,
  BASE_URL,
  DATA_ACTION_URL,
  SYSTEM_URL,
  AUTH_URL,
  H_URL,
  EXHIBITOR_URL_TEST,
  EXHIBITOR_URL,
  EXHIBITOR_CLIENTURL_TEST,
  EXHIBITOR_CLIENTURL,
  TEST_LIVE_ENMORE,
} from './host';


let baseUrl = '';
let pointBaseUrl = ''; //埋点api
let clientBaseUrl = ""; // 直播订单 接口地址
let wxAuthUrl = '';
let platformBizApi = "" //首页配置地址
let memberApi = ''; //个人中心api
let system = ''; // 查询直播接口的地址
let homePageUrl = ''; //首页包地址
let orderUrl = ''; // 支付接口地址
let uploadUrl = ''; // 上传图片地址
let formApi = '';
let authUrl = '';
let liveUrl = '';
let systemApiUrl = ''; // 超管 （易贸公司信息）
let systemUrl = ''; // 超管 访问地址（易贸公司信息）
let signOrderUrl = '';
let platformApi=''
let productUrl = '';
let exhibitorUrl = '';
let exhibitorClientUrl = '';
let memberAutjApi = ''; // 会员信息API
let textLiveApi = '';
switch (process.env.VUE_APP_CURRENTMODE) {
  case 'development':
    platformBizApi = BASE_URL_TEST + '/platform-biz-api';
    baseUrl = BASE_URL_TEST + '/info-api';
    homePageUrl = BASE_URL_TEST + "/info/";

    pointBaseUrl = DATA_ACTION_URL_TEST + '/data_action-api';

    clientBaseUrl = SYSTEM_URL_TEST + "/client/";
    system = SYSTEM_URL_TEST + '/system/';
    uploadUrl = SYSTEM_URL_TEST + '/commonFile/upload/oss_ievent_public';
    liveUrl = SYSTEM_URL_TEST + "/#/zh-cn";


    wxAuthUrl = AUTH_URL_TEST + '/member-api/';
    memberApi = AUTH_URL_TEST + '/member-api/';
    orderUrl = AUTH_URL_TEST + '/order-api';
    formApi = AUTH_URL_TEST + '/form-api';
    authUrl = AUTH_URL_TEST + '/biz-api';
    systemApiUrl = AUTH_URL_TEST + '/system-api';
    systemUrl = AUTH_URL_TEST + '/system';
    signOrderUrl = AUTH_URL_TEST + '/sign-order-api';
    platformApi = AUTH_URL_TEST+'/platform-service-api'
    productUrl = AUTH_URL_TEST + '/product-api';
    exhibitorUrl = EXHIBITOR_URL_TEST + '/exhibitor-api';
    exhibitorClientUrl = EXHIBITOR_CLIENTURL_TEST;
    memberAutjApi = AUTH_URL_TEST + '/member-level-api';
    textLiveApi = TEST_LIVE_ENMORE + '/platform-biz-api';
    break;
  case 'production':
    platformBizApi = BASE_URL + '/platform-biz-api';
    baseUrl = BASE_URL + '/info-api';
    clientBaseUrl = BASE_URL + "/client/";
    memberApi = BASE_URL + '/member-api/'
    system = BASE_URL + '/system/';
    uploadUrl = BASE_URL + '/commonFile/upload/oss_ievent_public';


    pointBaseUrl = DATA_ACTION_URL + '/data_action-api';

    homePageUrl = SYSTEM_URL + '/info/';
    liveUrl = SYSTEM_URL + "/mobile/#/zh-cn";


    wxAuthUrl = AUTH_URL + '/member-api/';
    orderUrl = AUTH_URL + '/order-api';
    formApi = AUTH_URL + '/form-api';
    authUrl = AUTH_URL + '/biz-api';
    systemApiUrl = AUTH_URL + '/system-api';
    systemUrl = AUTH_URL;
    signOrderUrl = AUTH_URL + '/sign-order-api';
    platformApi = H_URL+'/platform-service-api'
    productUrl = AUTH_URL + '/product-api';
    memberAutjApi =  AUTH_URL + '/member-level-api';
    exhibitorUrl = EXHIBITOR_URL + '/exhibitor-api';
    exhibitorClientUrl = EXHIBITOR_CLIENTURL;
    textLiveApi = BASE_URL + '/platform-biz-api';
    break;
  case 'test':
    platformBizApi = BASE_URL_TEST + '/platform-biz-api';
    baseUrl = BASE_URL_TEST + '/info-api';
    homePageUrl = BASE_URL_TEST + "/info/";

    pointBaseUrl = DATA_ACTION_URL_TEST + '/data_action-api';

    clientBaseUrl = SYSTEM_URL_TEST + "/client/";
    system = SYSTEM_URL_TEST + '/system/';
    uploadUrl = SYSTEM_URL_TEST + '/commonFile/upload/oss_ievent_public';
    liveUrl = SYSTEM_URL_TEST + "/#/zh-cn";


    wxAuthUrl = AUTH_URL_TEST + '/member-api/';
    memberApi = AUTH_URL_TEST + '/member-api/';
    orderUrl = AUTH_URL_TEST + '/order-api';
    formApi = AUTH_URL_TEST + '/form-api';
    authUrl = AUTH_URL_TEST + '/biz-api';
    systemApiUrl = AUTH_URL_TEST + '/system-api';
    systemUrl = AUTH_URL_TEST + '/system';
    signOrderUrl = AUTH_URL_TEST + '/sign-order-api';
    platformApi = AUTH_URL_TEST+'/platform-service-api'
    productUrl = AUTH_URL_TEST + '/product-api';
    exhibitorUrl = EXHIBITOR_URL_TEST + '/exhibitor-api';
    exhibitorClientUrl = EXHIBITOR_CLIENTURL_TEST;
    memberAutjApi = AUTH_URL_TEST + '/member-level-api';
    textLiveApi = TEST_LIVE_ENMORE + '/platform-biz-api';
    break;
  case 'developmentPreview':
    platformBizApi = BASE_URL_TEST + '/platform-biz-api';
    baseUrl = BASE_URL_TEST + '/info-api';
    homePageUrl = BASE_URL_TEST + "/info/";

    pointBaseUrl = DATA_ACTION_URL_TEST + '/data_action-api';

    clientBaseUrl = SYSTEM_URL_TEST + "/client/";
    system = SYSTEM_URL_TEST + '/system/';
    uploadUrl = SYSTEM_URL_TEST + '/commonFile/upload/oss_ievent_public';
    liveUrl = SYSTEM_URL_TEST + "/#/zh-cn";

    wxAuthUrl = AUTH_URL_TEST + '/member-api/';
    memberApi = AUTH_URL_TEST + '/member-api/';
    orderUrl = AUTH_URL_TEST + '/order-api';
    formApi = AUTH_URL_TEST + '/form-api';
    authUrl = AUTH_URL_TEST + '/biz-api';
    systemApiUrl = AUTH_URL_TEST + '/system-api';
    systemUrl = AUTH_URL_TEST + '/system';
    signOrderUrl = AUTH_URL_TEST + '/sign-order-api';
    platformApi = AUTH_URL_TEST+'/platform-service-api'
    productUrl = AUTH_URL_TEST + '/product-api';
    exhibitorUrl = EXHIBITOR_URL_TEST + '/exhibitor-api';
    exhibitorClientUrl = EXHIBITOR_CLIENTURL_TEST;
    memberAutjApi = AUTH_URL_TEST + '/member-level-api';
    textLiveApi = TEST_LIVE_ENMORE + '/platform-biz-api';
    break;
  case 'testPreview':

    platformBizApi = BASE_URL_TEST + '/platform-biz-api';
    baseUrl = BASE_URL_TEST + '/info-api';
    homePageUrl = BASE_URL_TEST + "/info/";

    pointBaseUrl = DATA_ACTION_URL_TEST + '/data_action-api';

    clientBaseUrl = SYSTEM_URL_TEST + "/client/";
    system = SYSTEM_URL_TEST + '/system/';
   
    uploadUrl = SYSTEM_URL_TEST + '/commonFile/upload/oss_ievent_public';
    liveUrl = SYSTEM_URL_TEST + "/#/zh-cn";


    wxAuthUrl = AUTH_URL_TEST + '/member-api/';
    memberApi = AUTH_URL_TEST + '/member-api/';
    orderUrl = AUTH_URL_TEST + '/order-api';
    formApi = AUTH_URL_TEST + '/form-api';
    authUrl = AUTH_URL_TEST + '/biz-api';
    systemApiUrl = AUTH_URL_TEST + '/system-api';
    systemUrl = AUTH_URL_TEST + '/system';
    signOrderUrl = AUTH_URL_TEST + '/sign-order-api';
    platformApi = AUTH_URL_TEST+'/platform-service-api'
    productUrl = AUTH_URL_TEST + '/product-api';
    exhibitorUrl = EXHIBITOR_URL_TEST + '/exhibitor-api';
    exhibitorClientUrl = EXHIBITOR_CLIENTURL_TEST;
    memberAutjApi = AUTH_URL_TEST + '/member-level-api';
    textLiveApi = TEST_LIVE_ENMORE + '/platform-biz-api';
    break;
  case 'productionPreview':
    
    platformBizApi = BASE_URL + '/platform-biz-api';
    baseUrl = BASE_URL + '/info-api';
    clientBaseUrl = BASE_URL + "/client/";
    memberApi = BASE_URL + '/member-api/'
    system = BASE_URL + '/system/';
    uploadUrl = BASE_URL + '/commonFile/upload/oss_ievent_public';


    pointBaseUrl = DATA_ACTION_URL + '/data_action-api';

    homePageUrl = SYSTEM_URL + '/info/';
   
    liveUrl = SYSTEM_URL + "/mobile/#/zh-cn";


    wxAuthUrl = AUTH_URL + '/member-api/';
    orderUrl = AUTH_URL + '/order-api';
    formApi = AUTH_URL + '/form-api';
    authUrl = AUTH_URL + '/biz-api';
    systemApiUrl = AUTH_URL + '/system-api';
    systemUrl = AUTH_URL;
    signOrderUrl = AUTH_URL + '/sign-order-api';
    platformApi = AUTH_URL+'/platform-service-api'
    productUrl = AUTH_URL + '/product-api';
    exhibitorUrl = EXHIBITOR_URL + '/exhibitor-api';
    exhibitorClientUrl = EXHIBITOR_CLIENTURL;
    memberAutjApi = AUTH_URL + '/member-level-api';
    textLiveApi = AUTH_URL + '/platform-biz-api';
    break;
}

export {
  platformBizApi,
  baseUrl,
  pointBaseUrl,
  clientBaseUrl,
  wxAuthUrl,
  memberApi,
  system,
  homePageUrl,
  orderUrl,
  uploadUrl,
  formApi,
  authUrl,
  liveUrl,
  systemApiUrl,
  systemUrl,
  signOrderUrl,
  platformApi,
  productUrl,
  exhibitorUrl,
  exhibitorClientUrl,
  memberAutjApi,
  textLiveApi,
}