const CONSTANT = {
  token: "proToken",
  user: "proMember",
  openId: "proOpenId",
  isPersonal: false,
  USERINFO: "platfromDetails",
  Authorization: "Authorization",
  inquiryEffective: "root_system_exhibitor_procure_effective_time",
  // 定义个人中心页面路由
  personalCenterRoutes: ["userInfoSet", "live", "activity", "inquiry", "collect", "missionCenter", "myCoin", "mallBody", "myOrder", "mySpeech", "discount", "myBook", "myInvoice"],
  // 定义榜单的头部数据
  leaderBoardsHeader: [
    "https://oss.ienmore.com/frontUpload/partUpload/2025-06-03/5894022cc9a5a9111e1f72012c6515e8.png",
    "https://oss.ienmore.com/frontUpload/partUpload/2025-06-03/3ad19bbc78bf1ed3d3f41abf58583e44.png",
    "https://oss.ienmore.com/frontUpload/partUpload/2025-06-03/65f205684329e8a9bf9b4625c101cc3c.png",
  ],
  // 定义排行榜的数据
  leaderBoardsList: [
    {
      title: "",
      background: "linear-gradient(180deg, #FFEAED 0%, #FFFFFF 12%), #FFFFFF",
      list: [],
    },
    {
      title: "",
      background: "linear-gradient(180deg, #FFEDC4 0%, #FFFFFF 12%), #FFFFFF",
      list: [],
    },
    {
      title: "",
      background: "linear-gradient(180deg, #D8F3FE 0%, #FFFFFF 12%), #FFFFFF",
      list: [],
    },
  ],
  // 定义职能分类的banner 的网格展示形式
  funClassifyBanner: {
    // 大图展示
    big: {
      gridTemplateColumns: "1fr",
      gridTemplateRows: "1fr",
    },
    // 1大图 加 4 小图
    oneAndFour: {
      gridTemplateColumns: "repeat(4, 1fr)",
      gridTemplateRows: "auto auto",
      gridTemplateAreas: `
        "big-mode-0 big-mode-0 big-mode-0 big-mode-0"
        "small-mode-1 small-mode-2 small-mode-3 small-mode-4"
      `,
    },
    // 1大图 加 7 小图
    oneAndSeven: {
      gridTemplateColumns: "repeat(4, 1fr)",
      gridTemplateRows: "auto auto auto auto",
      gridTemplateAreas: `
        "big-mode-0 big-mode-0 big-mode-0 small-mode-1"
        "big-mode-0 big-mode-0 big-mode-0 small-mode-2"
        "big-mode-0 big-mode-0 big-mode-0 small-mode-3"
        "small-mode-4 small-mode-5 small-mode-6 small-mode-7"
      `,
    },
  },
  // 定义职能的网格的比例
  funClassifyBannerRatio: {
    big: {
      "big-mode": "2.156/1",
    },
    oneAndFour: {
      "big-mode": "2.156/1",
      "small-mode": "1.519/1",
    },
    oneAndSeven: {
      "big-mode": "1.4545",
      "small-mode": "1.519/1",
    },
  },
};

export default CONSTANT;
