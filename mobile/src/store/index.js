import Vue from "vue";
import Vuex from "vuex";
import home from "./modules/home";
import personal from "./modules/personal";
import study from "./modules/study";
import search from "./modules/search";
import searchBar from "./modules/searchBar";
import commonList from "./modules/commonList";
import scrollNavBar from "./modules/scrollNavBar";
import memberPoint from "./modules/memberPoint";
import monetaryMethods from "./modules/monetaryMethods";
import dominPage from "./modules/dominPage";
import { loginStore } from "enmore_common_mobile";
import dataActionStore from "@enmore/common-data-action/src/store";
Vue.use(Vuex);
const store = new Vuex.Store({
  state: {
    // 侧边栏的信息数据
    sliderInfoStatus: {
      contact: false,
      addxiaoyi: false,
      questionnaire: false,
      addweixin: false,
    },
    // 分享信息
    shareInfo: {},
    // 登录状态维护
    loginStatus: false,
    // 弹窗信息
    advertisement: {},
    isShowAd:false,
    isSignInStatus:false, // 控制签到成功的弹窗
    memberInfo: {}, // 查询会员信息
    pointInfos: {}, // 查询会员积分
    memberStatus: false, // 查询会员开关状态
    memCoinSettingForm: {}, // 易币设置信息
    getBerPointInfo: {}, // 个人认证信息
    getLayoutInfoForm: {}, // 商城配置信息
    getLayoutInfoBannerForm: {}, // banner图
    getProductInfoForm: {}, // 全部商品配置
    getProductInfoInfo: {}, // 获取查询商品详情信息
    showPopup: false, // 易享商城首页顶部筛选弹窗状态
    mallScrollNumber: 0,
    mallPopKeyString: "",
    adressShow: false,
    mallHomePopForm: {
      selectType: "综合推荐",
      selectId: 0,
      produName: null,
      productClassifyIds: [],
      productClassifyName: [],
      searType: null,
      jinShow: false,
    }, // 易享商城首页顶部导航栏筛选条件
    addressForm: {},
    // 签到信息
    userSignInInfo: {},
    userPopShow: false, // 审核弹窗全局状态
    pageMallForm: {
      pageNum: 1,
    }, // 易享商城手机端全部商品页码
    bioChinaShow: false, // 生物中国活动弹窗
    bioChinaImg: "", // 生物中国活动弹窗图片
    ...loginStore.state,
    allConfig: [],//所有配置
    bottomNavTab: [],//底部导航
    pageContent: [],//页面内容
  },
  mutations: {
    changeSliderInfoStatus(state, payload) {
      state.sliderInfoStatus = payload;
    },
    changeLoginStatus(state, payload) {
      state.loginStatus = payload;
    },
    changeShareInfo(state, payload) {
      state.shareInfo = payload;
    },
    //改变弹窗广告
    changeIsShowAd(state, payload) {
      state.isShowAd = payload;
    },
    changeAdvertisement(state, payload) {
      state.advertisement = payload || {};
    },
    changeIsSignInStatus(state,payload){
      state.isSignInStatus = payload
    },
    getMemberAuthorInfo(state,payload) {
      state.memberInfo = payload
    },
    getMemberStorePoints(state,payload) { // 查询会员积分
      if (payload.levelDesc) {
          payload.levelDesc = JSON.parse(payload.levelDesc);
      }
      state.pointInfos = payload
    },
    changeUserSignInInfo(state, payload) {
        state.userSignInInfo = payload;
    },
    changeUserPopShow(state, payload) {
      state.userPopShow = payload;
    },
    getMemberLevelSwitchStatus(state,payload) { // 查询会员开关状态
      state.memberStatus = payload
    },
    // 获取易币设置
    setMemberIntegral(state, payload) {
      state.memCoinSettingForm = payload;
    },
    // 获取个人认证信息
    getMemberAuthStatus(state, payload) {
      state.getBerPointInfo = payload;
    },
    // 最新上架、易享推荐
    getLayoutInfoList(state, payload) {
      state.getLayoutInfoForm = payload;
    },
    // 查询banner图
    getLayoutInfoBannerList(state, payload) {
      state.getLayoutInfoBannerForm = payload;
    },
    // 查询全部商品信息
    getProductInfoList(state, payload) {
      state.getProductInfoForm = payload;
    },
    // 获取查询商品详情信息
    getProductInfoDetail(state, payload) {
      state.getProductInfoInfo = payload;
    },
    // 获取易享商城页码
    pageMallFun(state, payload) {
      state.pageMallForm = payload;
    },
    // 列表滚动的距离
    mallScrollFun(state, payload) {
      state.mallScrollNumber = payload;
    },
    // 搜索页关键词存储
    mallPopKeyWordFun(state, payload) {
      state.mallPopKeyString = payload;
    },
    // 获取易享商城首页顶部筛选弹窗状态
    showPopoFun(state, payload) {
      state.showPopup = payload;
    },
    // 选择地址弹出层状态
    adressFuntion(state, payload) {
      state.adressShow = payload;
    },
    // 关闭地址弹出层回显的数据信息
    adressFunForm(state, payload) {
      state.addressForm = payload;
    },
    // 存储获取易享商城顶部导航栏筛选条件
    mallHomePopFun(state, payload) {
      state.mallHomePopForm = payload;
    },
    changeBioChinaShow(state,payload){
      state.bioChinaShow = payload
    },
    changeBioChinaImg(state,payload){
      state.bioChinaImg = payload
    },
    changeAllConfig(state,payload){
      state.allConfig = payload
    },
    changeBottomNavTab(state,payload){
      state.bottomNavTab = payload
    },
    changePageContent(state,payload){
      state.pageContent = payload
    },

    ...loginStore.mutations,
  },
  actions: {},
  modules: {
    home,
    personal,
    study,
    search,
    searchBar,
    commonList,
    scrollNavBar,
    dominPage,
    dataActionStore,
    memberPoint,
    monetaryMethods,
  },
});

export default store;
