import { getLayoutInfoList, getProductInfoList, getProductInfoDetail } from "@/api/monetary";
import Vue from "vue";
import Vuex from "vuex";
import router from "@/router";

import personProfile from "./personal";

Vue.use(Vuex);

const platformId = 3;

const memberActionMeth = {
  actions: {
    // 获取商品信息
    async getLayoutInfoList({ commit }, type) {
      let params = {
        platformId: platformId,
        sysCode: "system",
        modelTypes: type ? [type] : null,
      };
      if (process.env.VUE_APP_CURRENTMODE.indexOf("Preview") > -1) {
        params.modelTypes = params.modelTypes.join(",");
      }
      try {
        const res = await getLayoutInfoList(params);
        let deForm = res.data;
        if (type == "banner") {
          deForm.layoutInfoList[0].param = JSON.parse(deForm.layoutInfoList[0].param);
          commit("getLayoutInfoBannerList", deForm);
        } else {
          commit("getLayoutInfoList", deForm);
        }
      } catch (error) {}
    },
    // 获取全部商品信息api
    async getProductInfoList({ commit }, demo) {
      let params = {
        platformId: platformId,
        sysCode: "system",
        ...demo,
        moduleType: "goods",
      };
      try {
        const res = await getProductInfoList(params);
        let deForm = res.data;
        if (process.env.VUE_APP_CURRENTMODE.indexOf("Preview") > -1) {
        } else {
          if (deForm.list && deForm.list.length > 0) {
            deForm.list.forEach((item, index) => {
              deForm.list[index].logo = item.logo.split(",")[0];
            });
          } else {
            deForm.list = [];
          }
        }
        commit("getProductInfoList", deForm);
      } catch (error) {}
    },
    // 获取查询商品详情api
    async getProductInfoDetail({ commit }, demo) {
      let params = {
        productId: demo,
      };
      try {
        const res = await getProductInfoDetail(params);
        if (!res.data) {
          Vue.prototype.$toast("商品不存在或已下架");
          router.push({ path: "/mall" });
          return;
        }
        let deForm = JSON.parse(JSON.stringify(res.data));
        deForm.logo = res.data.logo ? res.data.logo.split(",") : [];
        deForm.description = res.data.description ? JSON.parse(res.data.description) : [];
        commit("getProductInfoDetail", deForm);
      } catch (error) {}
    },
  },
};

export default memberActionMeth;
