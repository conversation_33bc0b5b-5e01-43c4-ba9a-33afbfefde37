import { getMemberLevelInfo, getMemberPoints, getSettingApi, getMemberAuthStatus } from "@/api/memberApi";
import Vue from 'vue';
import Vuex from 'vuex';

import personProfile from "./personal"

Vue.use(Vuex);

const platformId = 3;

const memberActionMeth = {
    actions: {
        // 获取易币设置
        async getSettingApi({ commit }) {
            let params = {
                platformId: platformId,
            };
            try {
                const res = await getSettingApi(params);
                let deForm = res.data
                if (deForm.ruleDesc) {
                    deForm.ruleDesc = JSON.parse(deForm.ruleDesc);
                }
                commit("setMemberIntegral", deForm);
            } catch (error) {
                console.error("Error fetching setting API:", error);
            }
        },
        // 查询会员等级信息
        async getMemberLevelInfo({ commit, state }) {
            let params = {
                platformId: platformId,
            };
            try {
                const res = await getMemberLevelInfo(params);
                commit("getMemberAuthorInfo", res.data);
            } catch (error) {
                console.error("Error fetching setting API:", error);
            }
        },
        // 查询易币积分信息
        async getMemberPoints({ commit, state }) {
            let params = {
                platformId: platformId,
            };
            try {
                const res = await getMemberPoints(params);
                if (res.data.ruleDesc) {
                    res.data.ruleDesc = JSON.parse(res.data.ruleDesc);
                }
                commit("getMemberStorePoints", res.data);
            } catch (error) {
                console.error("Error fetching setting API:", error);
            }
        },
        // 获取实名认证信息
        async getMemberAuthStatus({ commit, state }) {
            let params = {
                platformId: platformId,
                memberId: personProfile.state.personProfile.id,
            };
            try {
                const res = await getMemberAuthStatus(params);
                commit("getMemberAuthStatus", res.data);
            } catch (error) {
                console.error("Error fetching setting API:", error);
            }
        },
    },
    
};

export default memberActionMeth;