import {searchAllModel} from '@/api/home';
const state = {
    // 视课的搜索数量
    courseNumber:'',

    //倾听的搜索数量
    listenNumber:'',

    //社区的搜索数量
    communityNumber:'',

    //  智库的搜索数量
    thankthinkNumber:'',

    // 伙伴的搜索数据
    partnerNumber:'',

    //  模块顶部的数量
    tabs:[
      {label:'视课'},
      {label:'倾听'},
      {label:'社区'},
      {label:'智库'},
      // {label:'伙伴'},
    ],

    //  进入页面进行选中操作
    selectedLabel:'视课',

    //  当前搜索的模块的类型  默认是视课的内容
    searchType:0,

    //  视课的搜索结果
    searchCourseData:[],
    
    //  倾听的搜索数据
    searchListenData:[],

    // 社区的搜索数据
    searchCommunityData:[],

    //  智库的搜索的数据
    searchThankthinkData:[],

    //  伙伴的搜索数据
    searchPartnerData:[],

    //  搜索的内容
    searchData:'',

    // 控制加载的数据
    noMoreSearch:false,

    //  全局控制搜索的页数
    searchPageNum:1,

     //  搜索总数
    searchTotal:0,
};
const mutations = {
  //  刚进入搜索页面的时候需要进行清空的操作
  clearSearchAllData(state){
    state.courseNumber='';
    state.listenNumber='';
    state.communityNumber='';
    state.thankthinkNumber='';
    state.partnerNumber='';
    state.tabs=[
      {label:'视课'},
      {label:'倾听'},
      {label:'社区'},
      {label:'智库'},
      // {label:'伙伴'},
    ]
  },

  //  清空搜索的数据
  clearSearchModelData(state){
    state.searchCourseData=[];
    state.searchListenData=[];
    state.searchCommunityData=[];
    state.searchThankthinkData=[];
    state.searchPartnerData=[];
  },

  //  模块进入进行选中值的操作
  changeSelectedLabel(state,payload){
    state.selectedLabel=payload;
    if( state.selectedLabel.indexOf('视课')!=-1){
      state.searchType=0;
    }else if(state.selectedLabel.indexOf('倾听')!=-1){
      state.searchType=1;
    }else if(state.selectedLabel.indexOf('社区')!=-1){
      state.searchType=2;
    }else if(state.selectedLabel.indexOf('智库')!=-1){
      state.searchType=3;
    }
    // }else if(state.selectedLabel.indexOf('伙伴')!=-1){
    //   state.searchType=4;
    // }
  },

  //  改变搜索的数量
  changeModelNumber(state,payload){
    state.tabs =[];
    let {audio,community,partner,thinkTank,video} = payload;
    state.courseNumber=video;
    state.listenNumber=audio;
    state.communityNumber=community;
    state.thankthinkNumber=thinkTank;
    state.partnerNumber=partner;
    state.tabs=[
      {label:`视课(${state.courseNumber})`},
      {label:`倾听(${state.listenNumber})`},
      {label:`社区(${state.communityNumber})`},
      {label:`智库(${state.thankthinkNumber})`},
      // {label:`伙伴(${state.partnerNumber})`},
    ]
      state.selectedLabel=state.tabs[state.searchType].label;
  },

  //  改变查询搜索的结果数据
  changeModelData(state,payload){
    if(state.searchType==0){
        state.searchCourseData=state.searchCourseData.concat(payload);
    }else if(state.searchType==1){
        state.searchListenData=state.searchListenData.concat(payload);
    }else if(state.searchType==2){
      state.searchCommunityData=state.searchCommunityData.concat(payload);
    }else if(state.searchType==3){
      state.searchThankthinkData=state.searchThankthinkData.concat(payload);
    }else if(state.searchType==4){
      state.searchPartnerData=state.searchPartnerData.concat(payload);
    }
  },

  //  改变搜索的数据
  changeSearchData(state,payload){
    state.searchData=payload;
  },

  //  改变搜索的页数
  changeSearchPageNum(state,payload){
    state.searchPageNum = payload;
  },

  //  改变搜索的总页数
  changeSearchTotal(state,payload){
    state.searchTotal=payload;
  } 

 
};
const actions = {
  //  请求搜索的数据
  getSearchContent({state,commit},payload){
    let params = Object.assign(payload,{pageSize:10});
    console.log(params)
    searchAllModel(params).then((res)=>{
      console.log('搜索查询结果',res);
      if(res.code=='001'){

        //  清空数据
        if(!params.isConcatData){
          commit('clearSearchModelData');
        }
    
        //改变搜索数量
        commit('changeModelNumber',res.data.totalList);

        //  查询数据进行展示
        commit('changeModelData',res.data.result.list);

        //   总页数的 更改
        commit('changeSearchTotal',res.data.result.total);
      }
    })
  }

}

export default {
    state,
    mutations,
    actions,
}