import { getConcern, getMyCollect, mySetting, headPortraitUpload } from "@/api/personal";
import  CONSTANT  from "@/config/config_constant";
import clearSingleCookie from "@/utils/delCookie";
const state = {
  // 我的关注
  myConcernData: [],
  //我的收藏
  myCollectData: [],
  // 我的关注的总页数
  myConcernDataTotal: 0,
  //  我的收藏的总页数
  myCollectDataTotal: 0,
  //设置的具体数据
  personProfile: {},
  //  修改个人信息
  setInfo: {},
  showSet: false,
};
const getters = {};
const mutations = {
  changeMyConcern(state, payload) {
    state.myConcernData = state.myConcernData.concat(payload);
  },
  changeMyCollect(state, payload) {
    state.myCollectData = state.myCollectData.concat(payload);
  },
  channgeConcernTotal(state, payload) {
    state.myConcernDataTotal = payload;
  },
  channgeCollectTotal(state, payload) {
    state.myCollectDataTotal = payload;
  },
  clearConcernData(state) {
    state.myConcernData = [];
  },
  clearMyCollect(state) {
    state.myCollectData = [];
  },
  changePersonPro(state, payload) {
    state.personProfile = payload;
    // 这里如果是客户被拉黑 就需要清除token 和 cookie
    if (payload.status === 0 ) {
      localStorage.removeItem(CONSTANT.token);
      // 清除cookie
      clearSingleCookie(CONSTANT.Authorization);
    }
  },
  clearnPersonPro(state, payload) {
    state.personProfile = {};
  },
  changeHeadImg(state, payload) {
    state.personProfile.headImg = payload;
  },
  changeSetInfo(state, payload) {
    state.setInfo = payload;
  },
  changeShowSet(state, payload) {
    state.showSet = payload;
  },
  changeMemberSave(state, payload) {
    state.personProfile[payload.key] = payload.content;
    headPortraitUpload(state.personProfile)
      .then((res) => {
        console.log(res);
      })
      .catch((err) => {
        console.log(err);
      });
  },
};
const actions = {
  //  个人页面的我的关注
  getMyConcern({ commit }, payload) {
    getConcern(payload)
      .then((res) => {
        if (res.code == "001") {
          if (payload.pageNum == 1) {
            commit("clearConcernData");
          }
          commit("channgeConcernTotal", res.data.total);
          let concernDatas = res.data.list.map((item) => {
            return {
              id: item.id,
              url: item.lecturerPic,
              name: item.lecturerName,
              detail: item.incumbent,
              collectCount: item.collectCount,
              type: 1,
              dataType: item.dataType,
            };
          });
          commit("changeMyConcern", concernDatas);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  },
  //  我的收藏的数据
  getMyCollects({ commit }, payload) {
    getMyCollect(payload)
      .then((res) => {
        if (res.code == "001") {
          if (payload.pageNum == 1) {
            commit("clearMyCollect");
          }
          commit("channgeCollectTotal", res.data.total);
          let collectDatas = res.data.list.map((item) => {
            return {
              id: item.id,
              url: item.coursePic,
              courseName: item.courseName,
              praiseCount: item.praiseCount,
              collect: item.collectCount,
              pageView: item.pageView,
              courseType: item.courseType,
            };
          });
          commit("changeMyCollect", collectDatas);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  },
  //  个人的具体信息的数据设置里面
  getPerprofile({ commit }) {
    mySetting()
      .then((res) => {
        if (res.code == "001") {
          commit("changePersonPro", res.data ? res.data : {});
        } else {
          window.vm.$toast(res.info);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  },
};
export default {
  state,
  getters,
  mutations,
  actions,
};
