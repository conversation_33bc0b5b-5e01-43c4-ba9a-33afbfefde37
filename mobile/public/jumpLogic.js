function jumpPage() {
  // pc环境 跳转pc页面
  if (!isMobile()  && window.parent === window) {
    const currentUrl = location.href;
    const platformId = getQueryVariable("platformId");
    const isProd = environment.includes("production");

    const domains = {
      3: {
        prod: {
          home: environment.includes("Preview") ? "https://preview-www.morebio.cn" : "https://www.morebio.cn",
          live: "https://stream.morebio.cn", 
          activity: "https://activity.morebio.cn",
          product: "https://mall.morebio.cn"
        },
        test: {
          home: environment.includes("Preview") ? "http://preview.testbio.ienmore.com:809" : "http://testbio.ienmore.com:809",
          live: "http://test-live.ienmore.com:809",
          activity: "http://test-event.ienmore.com:809",
          product: "http://test-mall.ienmore.com:809"
        }
      },
      5: {
        prod: {
          home: "https://www.enmoreauto.com",
          live: "https://stream.enmoreauto.com",
          activity: "https://activity.enmoreauto.com",
          product: "https://www.enmoreauto.com"
        },
        test: {
          home: "http://testcar.ienmore.com:809", 
          live: "http://testcar-live.ienmore.com:809",
          activity: "http://testcar-event.ienmore.com:809",
          product: "http://testcar.ienmore.com:809"
        }
      }
    };

    const thisOrigin = domains[platformId][isProd ? 'prod' : 'test'];

    const urlMaps = {
      userCenter: {
        "/home/<USER>/0/0": "/",
        "/userInfoSet": "/userInfo",
        "/live": "/live",
        "/activity": "/myActivity",
        "/inquiry": "/inquiry",
        "/collect": "/myCollect",
        "/missionCenter": "/missionCenter",
        "/myCoin": "/myCoin",
        "/myOrder": "/myOrder",
        "/mySpeech": "/mySpeech",
        "/discount": "/myDiscountCode",
        "/myBook": "/myBook",
        "/myInvoice": "/myInvoice",
        '/mall': '/mall',
        'myCareer': '/myCareer',
        '/mall/goods': '/mall/goods',
      },
      live: {
        "/home/<USER>/0/0": "/",
        "/liveRoom": "/liveRoom/" ,
      },
      activity: {
        "/medicineActivity": "/",
      },
      product:{
        '/home/<USER>/0/0': '/',
      },
    };

    const pcUrl = currentUrl.split("#")[1];
    let searchParams = new URLSearchParams(currentUrl);
    let invitationCodeId = searchParams.get("invitationCodeId");

    // 路径匹配规则
    const pathMatchRules = {
      '/mall/goods': (pcUrl, key) => pcUrl.startsWith(key + '/'),
      '/mall': (pcUrl, key) => pcUrl.split('?')[0] === key,
      '/live': (pcUrl, key) => pcUrl.split('?')[0] === key,
      '/liveRoom': (pcUrl, key) => pcUrl.startsWith(key + '/'),
      default: (pcUrl, key) => pcUrl.includes(key)
    };

    // 路径处理规则
    const pathProcessRules = {
      '/mall/goods': (pcUrl) => pcUrl.split('?')[0],
      '/liveRoom': (pcUrl) => {
        const match = pcUrl.match(/\/liveRoom\/(\d+)/);
        return match ? `/liveRoom/${match[1]}` : '/liveRoom';
      },
      default: (_, targetPath) => targetPath
    };
    // 获取重定向URL
    const getRedirectUrl = (type, targetPath) => {
      const baseUrl = type === 'userCenter' 
        ? `${thisOrigin.home}/userCenterMedical${targetPath}`
        : `${thisOrigin[type]}${targetPath}`;
        
      return invitationCodeId 
        ? `${baseUrl}?invitationCodeId=${invitationCodeId}` 
        : baseUrl;
      
    };

    // 查找匹配的路径并重定向
    for (const [type, map] of Object.entries(urlMaps)) {
      // 按路径长度从长到短排序键名，确保先匹配最具体的路径
      const sortedKeys = Object.keys(map).sort((a, b) => b.length - a.length);
      
      const matchedKey = sortedKeys.find(key => {
        const matchRule = pathMatchRules[key] || pathMatchRules.default;
        return matchRule(pcUrl, key);
      });
      
      
      if (matchedKey) {
        const processRule = pathProcessRules[matchedKey] || pathProcessRules.default;
        const targetPath = processRule(pcUrl, map[matchedKey]);
        const redirectUrl = getRedirectUrl(type, targetPath);
        window.location.replace(redirectUrl);
        return;
      }
    }
    // 默认跳转首页
    window.location.replace(thisOrigin.home);
  }
}
