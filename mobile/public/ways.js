// 判断手机端和移动端
function isMobile() {
    if (
        window.navigator.userAgent.match(
            /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
        )
    ) {
        return true // 移动端
    } else {
        return false // PC端
    }
}

// 微信禁止字体大小
function handleFontSize() {
    // 设置网页字体为默认大小
    WeixinJSBridge.invoke('setFontSizeCallback', {
        fontSize: 0,
    })
    // 重写设置网页字体大小的事件
    WeixinJSBridge.on('menu:setfont', function () {
        WeixinJSBridge.invoke('setFontSizeCallback', {
            fontSize: 0,
        })
    })
}

// 获取路径参数
// 兼容图标库
function getQueryVariable(key) {
    let after = window.location.search
    after = after.substr(1) || window.location.hash.split('?')[1]
    if (after) {
        let reg = new RegExp('(^|&)' + key + '=([^&]*)(&|$)')
        let r = after.match(reg)
        if (r != null) {
            return decodeURIComponent(r[2])
        } else {
            return null
        }
    }
}