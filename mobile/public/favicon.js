// 更改图标
function changeIcon() {
    let platformId = getQueryVariable('platformId');
    let headHTML = document.getElementsByTagName('head')[0].innerHTML;
    // 引入易贸医疗的图标，（行家活动不引入，因为会冲突）
    if (platformId == 3) {
        headHTML += '<link type="text/css" rel="stylesheet" href="//at.alicdn.com/t/c/font_2581894_qhnqzirr5r.css">';
        document.getElementsByTagName('head')[0].innerHTML = headHTML;
    }
    // 易贸医疗 设置图标 和 title
    let $favicon = document.createElement('link');
    $favicon.rel = 'shortcut icon';
    $favicon.type = 'image/x-icon';
    $favicon.href = platformId == 3 ? 'medicineFavicon.ico' : 'favicon.ico';
    document.getElementsByTagName('head')[0].appendChild($favicon);
}