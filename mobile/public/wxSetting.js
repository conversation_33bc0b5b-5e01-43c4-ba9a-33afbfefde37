function wxSet() {
    //android 禁止微信浏览器调整字体大小
    let isIOS = !!window.navigator.userAgent.match(
        /\(i[^;]+;( U;)? CPU.+Mac OS X/
    )
    if (!isIOS) {
        if (
            typeof WeixinJSBridge == 'object' &&
            typeof WeixinJSBridge.invoke == 'function'
        ) {
            handleFontSize();
        } else {
            if (document.addEventListener) {
                document.addEventListener(
                    'WeixinJSBridgeReady',
                    handleFontSize,
                    false
                )
            } else if (document.attachEvent) {
                document.attachEvent('WeixinJSBridgeReady', handleFontSize)
                document.attachEvent('onWeixinJSBridgeReady', handleFontSize)
            }
        }
    }
}